# Photo Assignment Feature

## Overview

The Photo Assignment feature provides a comprehensive drag-and-drop interface for assigning wildlife photos to species. This feature integrates seamlessly with the AI Dashboard and includes advanced capabilities like inline species editing, AI-powered suggestions, and bulk assignment operations.

## Features

### 🎯 Core Functionality
- **Drag-and-Drop Interface**: Intuitive photo-to-species assignment
- **Real-time Updates**: Immediate feedback on assignment changes
- **Comprehensive Logging**: All changes logged in `ai_override_log` table
- **Filtering Options**: Filter by assignment status (All/Assigned/Unassigned)
- **Search Functionality**: Search photos by title, description, or species name
- **Category Filtering**: Filter species by category (Mammal, Bird, etc.)

### 🖼️ Species Detail Modal (Inline Editing)
- **Edit Species Metadata**: Modify name, scientific name, category, description
- **Publication Status**: Toggle species published/draft status
- **Conservation Status**: Update IUCN conservation status
- **Real-time Validation**: Form validation and change tracking
- **Override Logging**: Automatic logging of metadata changes
- **Statistics Display**: Show photo counts and species statistics

### 🤖 AI Suggestion Overlays
- **Smart Suggestions**: AI-powered species recommendations during drag
- **Confidence Scores**: Visual confidence indicators (92%, 85%, etc.)
- **Visual Highlights**: Suggested species highlighted with blue glow
- **Real-time Analysis**: Suggestions based on photo metadata
- **Non-intrusive UI**: Subtle overlays that don't interfere with workflow

### 📦 Bulk Drag Actions
- **Multi-Select Mode**: Toggle between single and bulk selection
- **Select All/Clear**: Quick selection management
- **Visual Indicators**: Selected photos highlighted with blue ring
- **Bulk Assignment**: Assign multiple photos to species in one operation
- **Confirmation Modal**: Safety confirmation for bulk operations
- **Progress Tracking**: Real-time feedback during bulk operations

### 📊 Visual Indicators
- **Photo Status**: Published/Draft badges
- **Species Status**: Published/Unpublished indicators
- **Conservation Status**: Color-coded badges for endangered species
- **Drop Zone Feedback**: Visual feedback during drag operations
- **Processing States**: Loading indicators during assignment
- **Selection States**: Clear visual feedback for selected items

### 🔄 Integration
- **AI Dashboard Integration**: Available as a dedicated tab
- **Override Logging**: Automatic logging of all manual assignments
- **Real-time Updates**: Live data refresh after assignments
- **Toast Notifications**: Success/error feedback for user actions

## Architecture

### Components

#### `DraggablePhoto`
- **Purpose**: Renders individual photos as draggable elements
- **Features**: 
  - Drag preview with opacity changes
  - Photo metadata display
  - Status indicators
  - Error handling for missing images
  - Multi-select checkboxes
  - Selection state indicators
  - Drag start/end event handlers

#### `DropTargetSpecies`
- **Purpose**: Renders species as drop targets
- **Features**:
  - Visual feedback for valid/invalid drops
  - Processing states during assignment
  - Species metadata display
  - Photo count indicators
  - Edit button for inline species editing
  - AI suggestion overlays
  - Confidence score displays

#### `SpeciesDetailModal`
- **Purpose**: Inline species metadata editing
- **Features**:
  - Comprehensive form for species editing
  - Real-time validation
  - Publication status toggle
  - Conservation status selection
  - Statistics display
  - Change tracking
  - Override logging

#### `PhotoAssignment`
- **Purpose**: Main container component
- **Features**:
  - Tabbed interface (Photos/Species)
  - Filtering and search controls
  - Statistics dashboard
  - Grid/List view modes
  - Multi-select mode toggle
  - AI suggestions management
  - Bulk operation controls

#### `BulkAssignmentModal`
- **Purpose**: Confirmation dialog for bulk operations
- **Features**:
  - Clear action confirmation
  - Photo count display
  - Species information
  - Progress indicators
  - Safety warnings

### API Layer

#### `photoApi.ts`
```typescript
// Core assignment functions
reassignPhotoSpecies({
  photoId: number,
  newSpeciesId: string | null,
  oldSpeciesId: string | null,
  reason?: string
})

bulkReassignPhotos({
  photoIds: number[],
  newSpeciesId: string | null,
  reason?: string
})

// AI suggestions
getAISuggestions(photoId: number): Promise<AISuggestion[]>

// Data fetching functions
getPhotosWithFilter(filter: 'all' | 'assigned' | 'unassigned')
getAllSpecies()
getSpeciesWithPhotoCounts()
```

## Database Schema

### Required Tables

#### `photos` Table
```sql
CREATE TABLE photos (
  id SERIAL PRIMARY KEY,
  url TEXT,
  title TEXT,
  description TEXT,
  species_id UUID REFERENCES species(id),
  published BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### `species` Table
```sql
CREATE TABLE species (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  common_name TEXT,
  scientific_name TEXT,
  category TEXT,
  conservation_status TEXT,
  description TEXT,
  published BOOLEAN DEFAULT false
);
```

#### `ai_override_log` Table
```sql
CREATE TABLE ai_override_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name TEXT NOT NULL,
  record_id TEXT NOT NULL,
  field_name TEXT NOT NULL,
  old_value TEXT,
  new_value TEXT,
  override_type TEXT NOT NULL,
  override_reason TEXT,
  user_id UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Views

#### `species_photo_counts` View
```sql
CREATE VIEW species_photo_counts AS
SELECT 
  s.id,
  s.name,
  s.common_name,
  s.scientific_name,
  s.category,
  s.conservation_status,
  s.description,
  s.published,
  COUNT(p.id) as total_photos,
  COUNT(CASE WHEN p.published THEN 1 END) as published_photos
FROM species s
LEFT JOIN photos p ON s.id = p.species_id
GROUP BY s.id, s.name, s.common_name, s.scientific_name, s.category, s.conservation_status, s.description, s.published;
```

## Usage

### Basic Workflow

1. **Navigate to AI Dashboard**
   - Go to `/ai-dashboard`
   - Click on "Photo Assignment" tab

2. **Filter and Search Photos**
   - Use the filter dropdown to show All/Assigned/Unassigned photos
   - Use search to find specific photos
   - Switch between grid and list views

3. **Assign Photos**
   - Drag a photo from the Photos tab
   - Drop it onto a species in the Species tab
   - Confirm the assignment via toast notification

4. **Monitor Changes**
   - View assignment statistics in the header
   - Check the Overrides tab for assignment history
   - Use real-time updates to see live changes

### Advanced Features

#### Species Detail Modal
1. **Access Modal**: Click the edit icon (✏️) on any species card
2. **Edit Metadata**: Modify name, scientific name, category, description
3. **Update Status**: Toggle publication status
4. **Save Changes**: Click "Save Changes" to apply updates
5. **Monitor Logs**: Check override logs for metadata changes

#### AI Suggestion Overlays
1. **Start Dragging**: Begin dragging any photo
2. **View Suggestions**: AI-suggested species will show confidence badges
3. **Follow Guidance**: Higher confidence scores indicate better matches
4. **Make Decision**: Choose to follow AI suggestions or make manual assignments

#### Bulk Assignment
1. **Enable Multi-Select**: Click "Multi-select" toggle in photo controls
2. **Select Photos**: Use checkboxes to select multiple photos
3. **Drag Selection**: Drag any selected photo to a species
4. **Confirm Action**: Review the confirmation modal
5. **Complete Assignment**: All selected photos will be assigned

#### Category Filtering
- Filter species by category (Mammal, Bird, etc.)
- Useful for large species collections
- Helps focus on specific animal types

#### Search Functionality
- Search photos by title or description
- Search by current species assignment
- Real-time filtering as you type

#### View Modes
- **Grid View**: Compact photo display
- **List View**: Detailed photo information
- Toggle between modes for different workflows

## Configuration

### Dependencies

```json
{
  "react-dnd": "^16.0.1",
  "react-dnd-html5-backend": "^16.0.1",
  "@radix-ui/react-checkbox": "^1.0.0",
  "@radix-ui/react-dialog": "^1.0.0"
}
```

### Environment Variables

No additional environment variables required beyond standard Supabase configuration.

### Permissions

Ensure your Supabase RLS policies allow:
- Reading from `photos` and `species` tables
- Updating `photos.species_id` field
- Updating `species` metadata fields
- Inserting into `ai_override_log` table

## Error Handling

### Common Issues

1. **Drag Not Working**
   - Ensure `DndProvider` is wrapping the component
   - Check browser compatibility
   - Verify drag item type matches drop target

2. **Assignment Fails**
   - Check network connectivity
   - Verify database permissions
   - Review console for error details

3. **Photos Not Loading**
   - Check Supabase connection
   - Verify table permissions
   - Review API response format

4. **AI Suggestions Not Showing**
   - Check AI service connectivity
   - Verify photo metadata is available
   - Review suggestion API response

5. **Bulk Assignment Issues**
   - Ensure multi-select mode is enabled
   - Check photo selection state
   - Verify bulk API permissions

### Debug Mode

Enable debug logging by setting:
```typescript
localStorage.setItem('debug', 'react-dnd');
```

## Performance Considerations

### Optimization Strategies

1. **Pagination**: For large photo collections
2. **Virtual Scrolling**: For extensive species lists
3. **Debounced Search**: Reduce API calls during typing
4. **Caching**: Cache species data to reduce API calls
5. **Lazy Loading**: Load AI suggestions on demand
6. **Batch Operations**: Use bulk APIs for multiple assignments

### Memory Management

- Clean up drag previews on component unmount
- Limit concurrent drag operations
- Implement proper error boundaries
- Clear AI suggestions on drag end
- Reset selection state appropriately

## Future Enhancements

### Planned Features

1. **Advanced AI Integration**: Real AI analysis for photo content
2. **Keyboard Shortcuts**: Keyboard navigation support
3. **Undo/Redo**: Assignment history management
4. **Batch Processing**: Process large photo collections
5. **AI Training**: Learn from user assignments
6. **Export Functionality**: Export assignment reports
7. **Analytics Dashboard**: Assignment statistics and trends
8. **Collaborative Features**: Multi-user assignment workflows

### Integration Opportunities

1. **Photo Review Workflow**: Integration with review queue
2. **Species Validation**: Cross-reference with external databases
3. **Export Functionality**: Export assignment reports
4. **Analytics Dashboard**: Assignment statistics and trends
5. **Machine Learning**: Train models on user behavior
6. **API Integration**: Connect with external wildlife databases

## Troubleshooting

### Development Issues

1. **TypeScript Errors**
   - Ensure proper type definitions
   - Check API response types
   - Verify component prop interfaces

2. **Styling Issues**
   - Check Tailwind CSS classes
   - Verify component library imports
   - Review responsive design breakpoints

3. **State Management**
   - Verify React state updates
   - Check component re-rendering
   - Review effect dependencies

4. **Drag and Drop Issues**
   - Check DnD provider setup
   - Verify drag item types
   - Review drop target configuration

### Production Issues

1. **Performance Problems**
   - Monitor API response times
   - Check database query performance
   - Review bundle size and loading

2. **User Experience**
   - Test on different devices
   - Verify accessibility compliance
   - Check browser compatibility

3. **AI Integration**
   - Monitor AI service availability
   - Check suggestion accuracy
   - Review AI API performance

## Support

For issues or questions:
1. Check the console for error messages
2. Review the network tab for API failures
3. Verify database permissions and RLS policies
4. Test with minimal data to isolate issues
5. Check AI service connectivity
6. Review drag and drop configuration

## Contributing

When contributing to this feature:
1. Follow the existing component patterns
2. Maintain TypeScript type safety
3. Add proper error handling
4. Include comprehensive tests
5. Update documentation for new features
6. Test drag and drop functionality
7. Verify AI integration points
8. Check bulk operation performance 