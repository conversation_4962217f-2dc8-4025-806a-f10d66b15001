# 🧪 Testing Photo-Location Drag & Drop

## Quick Test Steps

### 1. **Access the Interface**
- Navigate to: `http://localhost:8082/admin/photo-assignment`
- Make sure you're logged in as an admin user

### 2. **Check for Unassigned Photos**
- Look for photos in the "Unassigned Photos" section at the top
- If no photos are visible, you may need to upload some test photos first

### 3. **Switch to Locations Tab**
- In the "Assignment Targets" section, click the "Locations" tab
- You should see location cards for:
  - Yellowstone National Park (Wyoming, USA)
  - Kruger National Park (Mpumalanga, South Africa)  
  - Banff National Park (Alberta, Canada)

### 4. **Test Drag & Drop**
1. **Click and hold** on any photo in the unassigned photos grid
2. **Drag** the photo over a location card
3. **Watch for visual feedback**: 
   - Photo should become semi-transparent while dragging
   - Location card should highlight when photo is over it
4. **Drop** the photo on the location card
5. **Check for success message**: Should see a toast notification

### 5. **Verify Assignment**
- Check the browser console for any errors
- Look for success toast message
- The photo's location field should be updated in the database

## Troubleshooting

### ❌ **No Photos Visible**
- Upload some test photos through the photo manager
- Make sure photos are published but not assigned to species

### ❌ **No Locations Visible**  
- Check if the test locations were created
- Run the database migration to add missing fields

### ❌ **Drag Not Working**
- Check browser console for JavaScript errors
- Ensure react-dnd packages are installed
- Try refreshing the page

### ❌ **Drop Fails with Error**
- Check browser console for detailed error message
- Verify database permissions
- Run the SQL migration to add missing fields to photos_v2

## Expected Behavior

### ✅ **Successful Drop**
- Photo becomes semi-transparent during drag
- Location card highlights when photo hovers over it
- Success toast appears: "1 photo assigned to [Location Name]!"
- Photo's location field updated in database

### ✅ **Visual Feedback**
- **Dragging**: Photo opacity reduces to 50%
- **Hover**: Location card background changes to green
- **Processing**: Location card shows "Assigning..." state
- **Success**: Green checkmark and success message

## Database Changes

When a photo is successfully assigned to a location:

```sql
UPDATE photos_v2 
SET location = 'Yellowstone National Park, Wyoming, United States'
WHERE id = 'photo-uuid';
```

## Console Commands for Testing

Open browser console and run:

```javascript
// Check if locations are loaded
console.log('Locations available:', window.locations);

// Check if photos are loaded  
console.log('Photos available:', window.photos);

// Test database connection
supabase.from('photos_v2').select('id, location').limit(5).then(console.log);
```

## Next Steps

After successful testing:
1. ✅ Verify location data appears in photo records
2. ✅ Test with multiple photos (future enhancement)
3. ✅ Add metadata field support (future enhancement)
4. ✅ Add GPS coordinate extraction (future enhancement)
