# 🧠 Fauna Focus AI Agent - Wildlife Photo Integration & Metadata Processing

This document describes the implementation of an AI agent that processes wildlife photos uploaded by users, integrating with the existing Supabase schema to enhance species and photo records with AI-generated metadata and tagging.

## 🏗️ Architecture Overview

The AI agent consists of several key components:

- **WildlifeAIAgent** (`src/lib/aiAgent.ts`) - Core AI processing logic
- **IntelligentUploader** (`src/components/IntelligentUploader.tsx`) - AI-powered upload interface
- **PhotoReviewDashboard** (`src/components/PhotoReviewDashboard.tsx`) - Review and approval workflow
- **AIWorkflow** (`src/pages/AIWorkflow.tsx`) - Main workflow page integrating all components

## 🗄️ Database Schema

### Photos Table Enhancements
```sql
ALTER TABLE public.photos 
ADD COLUMN ai_generated_metadata BOOLEAN DEFAULT FALSE,
ADD COLUMN needs_review BOOLEAN DEFAULT FALSE;
```

### Species Table Enhancements
```sql
ALTER TABLE public.species 
ADD COLUMN created_by_ai BOOLEAN DEFAULT FALSE,
ADD COLUMN ai_confidence DECIMAL(3,2) CHECK (ai_confidence >= 0 AND ai_confidence <= 1),
ADD COLUMN tags TEXT[] DEFAULT '{}';
```

## 🧬 Core AI Agent Logic

### Species Matching (Priority Order)
1. **Exact match by scientific_name**
2. **Exact match by common_name**
3. **Fuzzy match by scientific_name** (≥80% Levenshtein similarity)
4. **Fuzzy match by common_name** (≥70% similarity)
5. **Partial text match** in description fields (≥60% confidence)
6. **Create new species** if confidence ≥50%

### Photo Processing & Deduplication
1. **Hash image** before upload using SHA-256
2. **Check for duplicates** - skip if photo with same hash exists
3. **Process new photos**:
   - Create photo record
   - Assign most likely species_id (if confidence ≥75%)
   - Generate title, description, and tags
   - Extract location from image metadata

### Confidence Thresholds
```typescript
const CONFIDENCE_THRESHOLDS = {
  species_assignment: 0.75,    // Assign if ≥75%
  location_extraction: 0.60,   // Accept location string if ≥60%
  metadata_generation: 0.70,   // Accept tags, notes if ≥70%
  duplicate_detection: 0.90,   // Match photo if hash matches
  manual_review: 0.50          // Flag anything <50% confidence
};
```

## 🖼️ Photo Processing Workflow

### 1. Upload & Analysis
- User selects photos for upload
- AI agent processes each photo:
  - Generates SHA-256 hash for deduplication
  - Analyzes image content (placeholder for actual AI service)
  - Matches or creates species records
  - Extracts location information
  - Generates metadata (title, tags, notes)

### 2. Review & Approval
- Photos with low confidence or new species are flagged for review
- Review dashboard shows:
  - AI-generated metadata with confidence scores
  - Manual override options
  - Approve/reject actions
  - Statistics and filtering

### 3. Database Storage
- Photos are saved with AI-generated metadata
- `ai_generated_metadata` flag is set to true
- `needs_review` flag is set based on confidence thresholds
- New species are created with `created_by_ai` flag

## 🏷️ Tags & Metadata

### Standardized Tag Vocabulary
```typescript
const STANDARD_TAGS = {
  habitats: ['desert', 'forest', 'grassland', 'wetland', 'mountain', 'coastal', 'urban', 'suburban', 'rural'],
  behaviors: ['nocturnal', 'diurnal', 'migratory', 'resident', 'territorial', 'social', 'solitary', 'nesting', 'feeding'],
  conservation: ['endangered', 'threatened', 'vulnerable', 'near_threatened', 'least_concern', 'extinct_in_wild'],
  size: ['small', 'medium', 'large', 'tiny', 'huge'],
  activity: ['flying', 'perching', 'hunting', 'resting', 'feeding', 'displaying', 'courting']
};
```

### Location Handling
- Store location as plain text string in `photos.location`
- Extract human-readable place names from GPS metadata
- Standardize format to title case (e.g., "Red Rock Canyon")

## 📊 Output Format

Each AI operation returns a structured object:

```typescript
interface AIProcessingResult {
  photo_id?: number;
  hash: string;
  species_id?: string;
  species_name?: string;
  species_confidence: number;
  location?: string;
  metadata: {
    title: string;
    tags: string[];
    notes: string;
  };
  needs_review: boolean;
  warnings: string[];
  errors: string[];
}
```

## 🔄 Review Workflow & Audit Trail

### AI-Generated Records Include:
- `species.created_by_ai = true`
- `photos.ai_generated_metadata = true`
- `photos.needs_review = true` if confidence < 50%

### Review Process:
1. **Pending Review** - Photos flagged for manual review
2. **Approved** - Photos approved and published
3. **Rejected** - Photos rejected and unpublished
4. **Modified** - Photos with manual overrides

## 🔐 Error Handling & Fallbacks

### Graceful Error Handling:
- Handle failed uploads with detailed error logging
- Return warnings for:
  - Species reassignment
  - Borderline confidence scores
  - Duplicate detection
  - Low-confidence AI decisions

### Fallback Strategies:
- Use cached species dataset for efficient matching
- Provide manual override options for all AI decisions
- Maintain audit trail of all AI actions

## 🚀 Usage

### Accessing the AI Workflow
1. Navigate to `/ai-workflow` in the application
2. Use the "AI Upload" tab to process photos
3. Use the "Review Dashboard" tab to approve/reject AI-generated content

### API Integration
```typescript
import { WildlifeAIAgent } from '@/lib/aiAgent';

// Process a single photo
const result = await WildlifeAIAgent.processPhoto(file, existingMetadata);

// Save processed photo
const saveResult = await WildlifeAIAgent.saveProcessedPhoto(file, result);
```

## 🔧 Configuration

### Environment Variables
- Configure AI service endpoints (OpenAI, Google Vision, etc.)
- Set confidence thresholds
- Configure storage bucket names

### Database Migration
Run the migration to add AI-specific fields:
```bash
npx supabase db reset
```

## 🧪 Testing

### Mock AI Analysis
The current implementation includes mock AI analysis for testing:
```typescript
// Mock species suggestions
species_suggestions: [
  { name: 'Eastern Screech Owl', scientific_name: 'Megascops asio', confidence: 0.82 },
  { name: 'Great Horned Owl', scientific_name: 'Bubo virginianus', confidence: 0.15 },
  { name: 'Barred Owl', scientific_name: 'Strix varia', confidence: 0.03 }
]
```

### Integration Testing
- Test species matching algorithms
- Test duplicate detection
- Test confidence threshold logic
- Test review workflow

## 🔮 Future Enhancements

### Planned Features:
1. **Real AI Integration** - Connect to OpenAI Vision API or Google Vision API
2. **GPS Extraction** - Extract and reverse geocode GPS coordinates
3. **Advanced Species Matching** - Implement more sophisticated matching algorithms
4. **Batch Processing** - Process multiple photos simultaneously
5. **Performance Optimization** - Cache species data and optimize queries
6. **Machine Learning** - Train custom models for species identification

### AI Service Integration
To integrate with real AI services, replace the mock `analyzeImage` function:

```typescript
private static async analyzeImage(file: File): Promise<ImageAnalysis> {
  // Send image to AI service (e.g., OpenAI Vision, Google Vision API)
  // Get species identification, confidence scores, and image description
  // Extract GPS coordinates if available
  
  const response = await fetch('/api/ai/analyze', {
    method: 'POST',
    body: formData
  });
  
  return await response.json();
}
```

## 📝 Contributing

When contributing to the AI agent:

1. **Follow the confidence threshold guidelines**
2. **Maintain the audit trail** for all AI decisions
3. **Add comprehensive error handling**
4. **Update the review workflow** as needed
5. **Test with various image types and species**

## 📄 License

This AI agent implementation is part of the Fauna Focus project and follows the same licensing terms. 