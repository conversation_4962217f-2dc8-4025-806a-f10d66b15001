# Welcome to your Lovable project

## Project info

**URL**: https://lovable.dev/projects/56bd4dcb-cd74-49bd-8f93-21436d4ebd4e

## 🧠 AI Wildlife Photo Dashboard

This project includes a comprehensive AI monitoring and review system for wildlife photo content. The AI Dashboard provides:

- **Real-time monitoring** of AI-generated content
- **Review workflows** for photos and species
- **Audit trails** for manual overrides
- **Performance analytics** and metrics
- **Override logging** with full accountability

### Quick Start

1. **Access the Dashboard**: Navigate to `/ai-dashboard` in your application
2. **Review Content**: Use the inline editing interface to approve/reject AI-generated content
3. **Monitor Activity**: Track override patterns and system performance
4. **Audit Changes**: View complete history of manual modifications

### Documentation

- **[AI Dashboard README](AI_DASHBOARD_README.md)** - Comprehensive guide to the AI system
- **[AI Agent README](AI_AGENT_README.md)** - Technical details of the AI agent implementation
- **[SQL Queries](AI_DASHBOARD_QUERIES.sql)** - Useful queries for data auditing and analysis

### Key Features

- ✅ **Real-time Notifications** - Live updates for new review items and overrides
- ✅ **Inline Editing** - Direct editing of AI-generated metadata
- ✅ **Override Logging** - Complete audit trail of manual changes
- ✅ **Performance Metrics** - AI confidence and accuracy tracking
- ✅ **Review Queue Management** - Efficient workflow for content approval

## 🎯 Key Features

### AI-Powered Wildlife Management
- **AI Dashboard**: Comprehensive monitoring of AI-tagged photos and species
- **Photo Assignment**: Drag-and-drop interface for assigning photos to species
- **Real-time Monitoring**: Live tracking of AI assignments and manual overrides
- **Audit Trail**: Complete logging of all manual changes and AI decisions
- **Review Queue**: Streamlined workflow for reviewing AI-generated content

### Photo Management
- **Intelligent Upload**: AI-powered photo deduplication and organization
- **Species Registry**: Comprehensive wildlife species database
- **Photo Gallery**: Beautiful, responsive photo display
- **Data Consistency**: Automated data validation and cleanup tools

### Integration & Sync
- **Airtable Integration**: Seamless data synchronization
- **API Documentation**: Comprehensive API reference
- **Real-time Updates**: Live data synchronization across platforms

## 📊 AI Dashboard

The AI Dashboard provides comprehensive monitoring and management of AI-tagged wildlife photos and species. Access it at `/ai-dashboard`.

### Features
- **Overview**: Real-time statistics and metrics
- **Review Queue**: Photos and species needing human review
- **AI Species**: AI-generated species requiring validation
- **Overrides**: Complete audit trail of manual changes
- **Photo Assignment**: Drag-and-drop interface for photo-to-species assignment

### Real-time Monitoring
- Live notifications for new overrides
- Real-time review queue updates
- Instant feedback on manual changes
- Comprehensive audit logging

## 🖼️ Photo Assignment

The Photo Assignment feature provides an intuitive drag-and-drop interface for assigning wildlife photos to species. This feature is integrated into the AI Dashboard and includes comprehensive logging of all assignment changes.

### Key Features
- **Drag-and-Drop Interface**: Intuitive photo-to-species assignment
- **Real-time Updates**: Immediate feedback on assignment changes
- **Comprehensive Logging**: All changes logged in `ai_override_log` table
- **Filtering Options**: Filter by assignment status (All/Assigned/Unassigned)
- **Search Functionality**: Search photos by title, description, or species name
- **Category Filtering**: Filter species by category (Mammal, Bird, etc.)

### Usage
1. Navigate to AI Dashboard → Photo Assignment tab
2. Filter photos by assignment status
3. Drag photos from the Photos tab
4. Drop onto species in the Species tab
5. Monitor changes in real-time

For detailed documentation, see [PHOTO_ASSIGNMENT_README.md](./PHOTO_ASSIGNMENT_README.md).

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/56bd4dcb-cd74-49bd-8f93-21436d4ebd4e) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/56bd4dcb-cd74-49bd-8f93-21436d4ebd4e) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
