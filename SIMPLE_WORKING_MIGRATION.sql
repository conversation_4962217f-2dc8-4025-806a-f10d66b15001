-- SIMPLE WORKING MIGRATION
-- This avoids conflict issues and just adds what's missing

-- Step 1: Add missing columns to locations table
DO $$
BEGIN
    -- Add missing columns one by one, checking if they exist first
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'state_province') THEN
        ALTER TABLE locations ADD COLUMN state_province TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'county') THEN
        ALTER TABLE locations ADD COLUMN county TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'description') THEN
        ALTER TABLE locations ADD COLUMN description TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'directions') THEN
        ALTER TABLE locations ADD COLUMN directions TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'facilities') THEN
        ALTER TABLE locations ADD COLUMN facilities TEXT[];
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'best_time_to_visit') THEN
        ALTER TABLE locations ADD COLUMN best_time_to_visit TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'difficulty_level') THEN
        ALTER TABLE locations ADD COLUMN difficulty_level TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'access_type') THEN
        ALTER TABLE locations ADD COLUMN access_type TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'parking_info') THEN
        ALTER TABLE locations ADD COLUMN parking_info TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'entrance_fee') THEN
        ALTER TABLE locations ADD COLUMN entrance_fee DECIMAL(8,2);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'website_url') THEN
        ALTER TABLE locations ADD COLUMN website_url TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'contact_info') THEN
        ALTER TABLE locations ADD COLUMN contact_info TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'featured') THEN
        ALTER TABLE locations ADD COLUMN featured BOOLEAN DEFAULT FALSE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'photo_url') THEN
        ALTER TABLE locations ADD COLUMN photo_url TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'habitat_types') THEN
        ALTER TABLE locations ADD COLUMN habitat_types TEXT[];
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'target_species') THEN
        ALTER TABLE locations ADD COLUMN target_species TEXT[];
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'visitor_tips') THEN
        ALTER TABLE locations ADD COLUMN visitor_tips TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'published') THEN
        ALTER TABLE locations ADD COLUMN published BOOLEAN DEFAULT TRUE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'google_maps_url') THEN
        ALTER TABLE locations ADD COLUMN google_maps_url TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'apple_maps_url') THEN
        ALTER TABLE locations ADD COLUMN apple_maps_url TEXT;
    END IF;
END $$;

-- Step 2: Create other tables if they don't exist
CREATE TABLE IF NOT EXISTS species_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id UUID REFERENCES species_v2(id) ON DELETE CASCADE,
  location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
  abundance TEXT CHECK (abundance IN ('rare', 'uncommon', 'common', 'abundant', 'very_common')),
  seasonal_presence TEXT[],
  breeding_status TEXT CHECK (breeding_status IN ('non_breeding', 'possible', 'probable', 'confirmed')),
  best_months INTEGER[],
  notes TEXT,
  confidence_level TEXT CHECK (confidence_level IN ('low', 'medium', 'high', 'confirmed')),
  last_observed DATE,
  observation_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(species_id, location_id)
);

CREATE TABLE IF NOT EXISTS location_photos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
  photo_url TEXT NOT NULL,
  title TEXT,
  description TEXT,
  photographer TEXT,
  taken_date DATE,
  is_primary BOOLEAN DEFAULT FALSE,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS observations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id UUID REFERENCES species_v2(id) ON DELETE CASCADE,
  location_id UUID REFERENCES locations(id) ON DELETE SET NULL,
  observer_id TEXT,
  source TEXT NOT NULL CHECK (source IN ('ebird', 'inaturalist', 'manual')),
  external_id TEXT,
  observation_date DATE NOT NULL,
  observation_time TIME,
  count INTEGER DEFAULT 1,
  breeding_code TEXT,
  behavior TEXT,
  notes TEXT,
  confidence_level TEXT,
  photo_url TEXT,
  audio_url TEXT,
  weather_conditions TEXT,
  temperature_c INTEGER,
  wind_speed_kmh INTEGER,
  visibility_km DECIMAL(4,2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 3: Add basic indexes
CREATE INDEX IF NOT EXISTS idx_locations_featured ON locations(featured);
CREATE INDEX IF NOT EXISTS idx_locations_published ON locations(published);
CREATE INDEX IF NOT EXISTS idx_species_locations_species ON species_locations(species_id);
CREATE INDEX IF NOT EXISTS idx_species_locations_location ON species_locations(location_id);

-- Step 4: Enable RLS
ALTER TABLE locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE species_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE location_photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE observations ENABLE ROW LEVEL SECURITY;

-- Step 5: Create basic policies (drop first to avoid conflicts)
DROP POLICY IF EXISTS "Public read access for locations" ON locations;
DROP POLICY IF EXISTS "Admin full access for locations" ON locations;

CREATE POLICY "Public read access for locations" ON locations FOR SELECT USING (published = true);
CREATE POLICY "Admin full access for locations" ON locations FOR ALL USING (auth.email() = '<EMAIL>');

-- Step 6: Create simple view
DROP VIEW IF EXISTS hotspot_highlights CASCADE;

CREATE VIEW hotspot_highlights AS
SELECT 
  l.*,
  0 as total_species,
  0 as common_species,
  0 as rare_species,
  0 as breeding_species,
  ARRAY[]::text[] as categories_present,
  0 as total_observations,
  l.photo_url as primary_photo,
  NULL as primary_photo_title
FROM locations l
WHERE l.published = true
ORDER BY l.featured DESC, l.name;

-- Step 7: Insert sample data (simple insert, will error if duplicate but that's ok)
INSERT INTO locations (
  name, latitude, longitude, country, state_province, 
  description, featured, published
) 
SELECT 
  'Central Park - The Ramble', 
  40.7794, -73.9632, 
  'United States', 'New York',
  'A 36-acre woodland area in Central Park known for excellent bird watching.',
  true,
  true
WHERE NOT EXISTS (
  SELECT 1 FROM locations WHERE name = 'Central Park - The Ramble'
);

-- Step 8: Show what we have now
SELECT 'Migration completed! Tables created:' as status;
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('locations', 'species_locations', 'location_photos', 'observations');

SELECT 'Locations in database:' as info;
SELECT name, country, state_province FROM locations;
