-- First, let's check what we're working with
-- Run this to see the current state of your database

-- Check if locations table exists and what columns it has
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'locations' 
ORDER BY ordinal_position;

-- Check what other tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('locations', 'species_locations', 'location_photos', 'observations', 'checklists', 'species_occurrence');

-- Check if species_v2 table exists (needed for foreign keys)
SELECT table_name 
FROM information_schema.tables 
WHERE table_name = 'species_v2';
