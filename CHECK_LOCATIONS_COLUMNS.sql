-- Check the actual column names in the locations table
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'locations' 
ORDER BY ordinal_position;

-- Also check if the specific columns exist
SELECT 
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'description') 
    THEN 'description column exists' 
    ELSE 'description column MISSING' 
  END as description_status,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'visitor_tips') 
    THEN 'visitor_tips column exists' 
    ELSE 'visitor_tips column MISSING' 
  END as visitor_tips_status,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'best_times') 
    THEN 'best_times column exists' 
    ELSE 'best_times column MISSING' 
  END as best_times_status;
