-- =====================================================
-- FUN FACTS MIGRATION SQL
-- =====================================================
-- This file contains all SQL needed to fix the fun facts structure issue
-- Run this in your Supabase SQL Editor to create the table and migration functions

-- Step 1: Create the fun_facts table
CREATE TABLE IF NOT EXISTS fun_facts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id UUID REFERENCES species_v2(id) ON DELETE CASCADE NOT NULL,
  fact TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Step 2: Create index for better performance
CREATE INDEX IF NOT EXISTS idx_fun_facts_species_id ON fun_facts(species_id);

-- Step 3: Create migration function to move data from JSONB fields to the table
CREATE OR REPLACE FUNCTION migrate_fun_facts_to_table()
RETURNS TABLE (
  species_id UUID,
  species_name TEXT,
  migrated_facts_count INTEGER,
  status TEXT
) AS $$
DECLARE
  species_record RECORD;
  fact_text TEXT;
  facts_array TEXT[];
  fact_count INTEGER := 0;
  total_migrated INTEGER := 0;
BEGIN
  -- Loop through all species with fun facts data
  FOR species_record IN 
    SELECT id, name, ai_fun_facts, fun_facts_field 
    FROM species_v2 
    WHERE ai_fun_facts IS NOT NULL OR fun_facts_field IS NOT NULL
  LOOP
    fact_count := 0;
    facts_array := ARRAY[]::TEXT[];
    
    -- Process ai_fun_facts field
    IF species_record.ai_fun_facts IS NOT NULL THEN
      BEGIN
        -- Try to extract as array if it's a JSON array
        IF jsonb_typeof(species_record.ai_fun_facts) = 'array' THEN
          SELECT array_agg(value::text)
          INTO facts_array
          FROM jsonb_array_elements_text(species_record.ai_fun_facts);
        ELSE
          -- If it's a string, treat as single fact
          facts_array := facts_array || ARRAY[species_record.ai_fun_facts::text];
        END IF;
      EXCEPTION WHEN OTHERS THEN
        -- If JSON parsing fails, treat as plain text
        facts_array := facts_array || ARRAY[species_record.ai_fun_facts::text];
      END;
    END IF;
    
    -- Process fun_facts_field
    IF species_record.fun_facts_field IS NOT NULL THEN
      BEGIN
        -- Try to extract as array if it's a JSON array
        IF jsonb_typeof(species_record.fun_facts_field) = 'array' THEN
          SELECT array_agg(value::text)
          INTO facts_array
          FROM jsonb_array_elements_text(species_record.fun_facts_field)
          WHERE value::text NOT IN (SELECT unnest(facts_array));
        ELSE
          -- If it's a string, treat as single fact
          fact_text := species_record.fun_facts_field::text;
          IF fact_text NOT IN (SELECT unnest(facts_array)) THEN
            facts_array := facts_array || ARRAY[fact_text];
          END IF;
        END IF;
      EXCEPTION WHEN OTHERS THEN
        -- If JSON parsing fails, treat as plain text
        fact_text := species_record.fun_facts_field::text;
        IF fact_text NOT IN (SELECT unnest(facts_array)) THEN
          facts_array := facts_array || ARRAY[fact_text];
        END IF;
      END;
    END IF;
    
    -- Clean up and insert facts
    IF array_length(facts_array, 1) > 0 THEN
      -- Clear existing facts for this species
      DELETE FROM fun_facts WHERE fun_facts.species_id = species_record.id;
      
      -- Insert each fact
      FOREACH fact_text IN ARRAY facts_array
      LOOP
        -- Clean up the fact text
        fact_text := trim(fact_text);
        fact_text := regexp_replace(fact_text, '^[•\-\*\d+\.\s]+', '', 'g'); -- Remove bullet points and numbers
        fact_text := regexp_replace(fact_text, '"', '', 'g'); -- Remove quotes
        
        -- Only insert if fact is meaningful (more than 10 characters)
        IF length(fact_text) > 10 AND fact_text != 'null' AND fact_text != '' THEN
          INSERT INTO fun_facts (species_id, fact) 
          VALUES (species_record.id, fact_text);
          fact_count := fact_count + 1;
        END IF;
      END LOOP;
    END IF;
    
    total_migrated := total_migrated + fact_count;
    
    -- Return progress for this species
    RETURN QUERY SELECT 
      species_record.id,
      species_record.name,
      fact_count,
      CASE 
        WHEN fact_count > 0 THEN 'Migrated'
        ELSE 'No facts found'
      END;
  END LOOP;
  
  RAISE NOTICE 'Migration complete. Total facts migrated: %', total_migrated;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Create helper function to get fun facts for a species
CREATE OR REPLACE FUNCTION get_species_fun_facts(p_species_id UUID)
RETURNS TABLE (
  id UUID,
  fact TEXT,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT ff.id, ff.fact, ff.created_at
  FROM fun_facts ff
  WHERE ff.species_id = p_species_id
  ORDER BY ff.created_at ASC;
END;
$$ LANGUAGE plpgsql;

-- Step 5: Add helpful comments
COMMENT ON TABLE fun_facts IS 'Stores individual fun facts for species, replacing the JSONB fields approach';
COMMENT ON FUNCTION migrate_fun_facts_to_table() IS 'Migrates fun facts from ai_fun_facts and fun_facts_field JSONB columns to the dedicated fun_facts table';
COMMENT ON FUNCTION get_species_fun_facts(UUID) IS 'Returns all fun facts for a given species from the fun_facts table';

-- =====================================================
-- INSTRUCTIONS FOR USE:
-- =====================================================
-- 1. Copy and paste this entire file into the Supabase SQL Editor
-- 2. Click "Run" to create the table and functions
-- 3. After successful creation, run the migration with:
--    SELECT * FROM migrate_fun_facts_to_table();
-- 4. Verify the migration worked by checking:
--    SELECT COUNT(*) FROM fun_facts;
-- =====================================================
