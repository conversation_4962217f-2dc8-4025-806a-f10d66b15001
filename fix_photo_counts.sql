-- Fix photo counts in species_v2 table to match actual photos in photos_v2
-- This ensures the frontend displays correct photo counts

-- Update photo counts for all species based on published photos in photos_v2
UPDATE species_v2 
SET photo_count = (
    SELECT COUNT(*) 
    FROM photos_v2 
    WHERE photos_v2.species_id = species_v2.id 
    AND photos_v2.published = true
);

-- Verify the update worked
SELECT 
    s.name,
    s.photo_count as updated_count,
    COUNT(p.id) as actual_count,
    CASE 
        WHEN s.photo_count = COUNT(p.id) THEN '✅ Match'
        ELSE '❌ Still Mismatch'
    END as status
FROM species_v2 s
LEFT JOIN photos_v2 p ON s.id = p.species_id AND p.published = true
GROUP BY s.id, s.name, s.photo_count
HAVING COUNT(p.id) > 0
ORDER BY COUNT(p.id) DESC
LIMIT 10;
