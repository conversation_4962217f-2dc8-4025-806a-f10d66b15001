-- Simple duplicate removal for photos_v2
-- This version uses SELECT * to avoid column issues

-- Step 1: Create a backup table
CREATE TABLE photos_v2_backup AS 
SELECT * FROM photos_v2;

-- Step 2: Verify backup was created
SELECT COUNT(*) as backup_count FROM photos_v2_backup;

-- Step 3: Show sample duplicates (first 10 groups)
WITH duplicate_groups AS (
    SELECT 
        url,
        title,
        COUNT(*) as duplicate_count,
        array_agg(id ORDER BY created_at) as all_ids,
        (array_agg(id ORDER BY created_at))[1] as keep_id
    FROM photos_v2 
    WHERE url IS NOT NULL AND title IS NOT NULL
    GROUP BY url, title 
    HAVING COUNT(*) > 1
    ORDER BY duplicate_count DESC
    LIMIT 10
)
SELECT * FROM duplicate_groups;

-- Step 4: Create a clean table with unique records only
CREATE TABLE photos_v2_clean AS
WITH ranked_photos AS (
    SELECT 
        *,
        ROW_NUMBER() OVER (PARTITION BY url, title ORDER BY created_at) as rn
    FROM photos_v2
)
SELECT * 
FROM ranked_photos 
WHERE rn = 1;

-- Step 5: Verify the clean table
SELECT COUNT(*) as clean_count FROM photos_v2_clean;

-- Step 6: Check for duplicates in clean table (should be 0)
SELECT 
    url,
    title,
    COUNT(*) as duplicate_count
FROM photos_v2_clean 
WHERE url IS NOT NULL AND title IS NOT NULL
GROUP BY url, title 
HAVING COUNT(*) > 1;

-- Step 7: Show the difference
SELECT 
    (SELECT COUNT(*) FROM photos_v2) as original_count,
    (SELECT COUNT(*) FROM photos_v2_clean) as clean_count,
    (SELECT COUNT(*) FROM photos_v2) - (SELECT COUNT(*) FROM photos_v2_clean) as duplicates_removed;

-- Step 8: When ready, replace the original table
-- IMPORTANT: Only run these commands when you're satisfied with the results above

-- DROP TABLE photos_v2;
-- ALTER TABLE photos_v2_clean RENAME TO photos_v2;

-- Step 9: If you need to restore from backup
-- DROP TABLE photos_v2;
-- ALTER TABLE photos_v2_backup RENAME TO photos_v2;
