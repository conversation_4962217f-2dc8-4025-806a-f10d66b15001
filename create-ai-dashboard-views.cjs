const { config } = require('dotenv');
const { createClient } = require('@supabase/supabase-js');

config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

async function createAIDashboardViews() {
  console.log('🔧 Creating AI Dashboard views...\n');
  
  try {
    // 1. Create ai_assignment_stats view
    console.log('1. Creating ai_assignment_stats view...');
    const { error: statsError } = await supabase.rpc('exec', {
      sql: `
        DROP VIEW IF EXISTS ai_assignment_stats CASCADE;
        CREATE VIEW ai_assignment_stats AS
        SELECT
          COUNT(*) as total_photos,
          0 as ai_tagged,
          0 as flagged_for_review,
          0.0 as avg_confidence,
          SUM(CASE WHEN published THEN 1 ELSE 0 END) as published_photos,
          SUM(CASE WHEN NOT published THEN 1 ELSE 0 END) as unpublished_photos
        FROM photos_v2;
        
        GRANT SELECT ON ai_assignment_stats TO authenticated;
      `
    });
    
    if (statsError) {
      console.log('❌ Stats view failed:', statsError.message);
    } else {
      console.log('✅ ai_assignment_stats view created');
    }
    
    // 2. Create recent_ai_overrides view
    console.log('2. Creating recent_ai_overrides view...');
    const { error: overridesError } = await supabase.rpc('exec', {
      sql: `
        DROP VIEW IF EXISTS recent_ai_overrides CASCADE;
        CREATE VIEW recent_ai_overrides AS
        SELECT 
          id,
          table_name,
          record_id,
          field_name,
          old_value,
          new_value,
          override_type,
          override_reason,
          created_at,
          COALESCE(
            (SELECT title FROM photos_v2 WHERE id::text = record_id AND table_name = 'photos'),
            (SELECT name FROM species_v2 WHERE id::text = record_id AND table_name = 'species'),
            'Unknown'
          ) as record_title,
          CASE 
            WHEN table_name = 'photos' THEN (SELECT url FROM photos_v2 WHERE id::text = record_id)
            ELSE NULL
          END as photo_url
        FROM ai_override_log
        ORDER BY created_at DESC
        LIMIT 50;
        
        GRANT SELECT ON recent_ai_overrides TO authenticated;
      `
    });
    
    if (overridesError) {
      console.log('❌ Overrides view failed:', overridesError.message);
    } else {
      console.log('✅ recent_ai_overrides view created');
    }
    
    // 3. Create review_queue_summary view
    console.log('3. Creating review_queue_summary view...');
    const { error: reviewError } = await supabase.rpc('exec', {
      sql: `
        DROP VIEW IF EXISTS review_queue_summary CASCADE;
        CREATE VIEW review_queue_summary AS
        SELECT
          (SELECT COUNT(*) FROM photos_v2 WHERE published = false) as photos_needing_review,
          (SELECT COUNT(*) FROM species_v2 WHERE published = false) as species_needing_review,
          (SELECT COUNT(*) FROM photos_v2 WHERE published = false) + 
          (SELECT COUNT(*) FROM species_v2 WHERE published = false) as total_items_needing_review;
          
        GRANT SELECT ON review_queue_summary TO authenticated;
      `
    });
    
    if (reviewError) {
      console.log('❌ Review queue view failed:', reviewError.message);
    } else {
      console.log('✅ review_queue_summary view created');
    }
    
    // Test all views
    console.log('\n🧪 Testing created views...');
    
    // Test ai_assignment_stats
    try {
      const { data: statsData, error: statsTestError } = await supabase
        .from('ai_assignment_stats')
        .select('*')
        .single();
      
      if (statsTestError) throw statsTestError;
      console.log('✅ ai_assignment_stats working:', statsData);
    } catch (error) {
      console.log('❌ ai_assignment_stats test failed:', error.message);
    }
    
    // Test review_queue_summary
    try {
      const { data: reviewData, error: reviewTestError } = await supabase
        .from('review_queue_summary')
        .select('*')
        .single();
      
      if (reviewTestError) throw reviewTestError;
      console.log('✅ review_queue_summary working:', reviewData);
    } catch (error) {
      console.log('❌ review_queue_summary test failed:', error.message);
    }
    
    // Test recent_ai_overrides
    try {
      const { data: overridesData, error: overridesTestError } = await supabase
        .from('recent_ai_overrides')
        .select('*')
        .limit(5);
      
      if (overridesTestError) throw overridesTestError;
      console.log(`✅ recent_ai_overrides working: ${overridesData.length} records`);
    } catch (error) {
      console.log('❌ recent_ai_overrides test failed:', error.message);
    }
    
    console.log('\n🎉 AI Dashboard views setup complete!');
    console.log('📍 Check the AI Dashboard: http://localhost:8083/ai-dashboard');
    
  } catch (error) {
    console.error('❌ Error creating views:', error);
  }
}

createAIDashboardViews();
