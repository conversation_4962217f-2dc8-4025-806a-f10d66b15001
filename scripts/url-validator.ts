#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
import { writeFileSync } from 'fs';
import { join } from 'path';

// Load environment variables
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL!;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

interface Photo {
  id: number;
  url: string;
  title: string | null;
  species_id: string | null;
}

interface ValidationResult {
  photo_id: number;
  url: string;
  status: 'ok' | 'broken' | 'failed';
  http_status?: number;
  error?: string;
  response_time?: number;
}

async function validateUrl(url: string): Promise<{ status: 'ok' | 'broken' | 'failed'; http_status?: number; error?: string; response_time?: number }> {
  const startTime = Date.now();
  
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
    
    const response = await fetch(url, {
      method: 'HEAD', // Use HEAD to avoid downloading the full image
      signal: controller.signal,
      headers: {
        'User-Agent': 'FaunaFocus-URL-Validator/1.0'
      }
    });
    
    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;
    
    if (response.ok) {
      return { status: 'ok', http_status: response.status, response_time: responseTime };
    } else {
      return { status: 'broken', http_status: response.status, response_time: responseTime };
    }
  } catch (error) {
    const responseTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    if (error instanceof Error && error.name === 'AbortError') {
      return { status: 'failed', error: 'Timeout', response_time: responseTime };
    }
    
    return { status: 'failed', error: errorMessage, response_time: responseTime };
  }
}

async function validateAllPhotoUrls(): Promise<void> {
  console.log('🔍 Starting URL validation for all photos...');
  
  // Get all photos with URLs
  const { data: photos, error } = await supabase
    .from('photos')
    .select('id, url, title, species_id')
    .not('url', 'is', null)
    .neq('url', '');
  
  if (error) {
    console.error('❌ Error fetching photos:', error);
    return;
  }
  
  if (!photos || photos.length === 0) {
    console.log('ℹ️  No photos found with URLs');
    return;
  }
  
  console.log(`📸 Found ${photos.length} photos to validate`);
  
  const results: ValidationResult[] = [];
  const batchSize = 10; // Process in batches to avoid overwhelming the server
  
  for (let i = 0; i < photos.length; i += batchSize) {
    const batch = photos.slice(i, i + batchSize);
    console.log(`\n🔄 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(photos.length / batchSize)}`);
    
    const batchPromises = batch.map(async (photo) => {
      console.log(`  Checking: ${photo.url}`);
      const validation = await validateUrl(photo.url);
      
      const result: ValidationResult = {
        photo_id: photo.id,
        url: photo.url,
        ...validation
      };
      
      // Update the database with the result
      await supabase.rpc('update_photo_url_status', {
        photo_id: photo.id,
        status: validation.status === 'ok' ? 'ok' : 'broken'
      });
      
      return result;
    });
    
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
    
    // Small delay between batches to be respectful
    if (i + batchSize < photos.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  // Generate summary
  const summary = {
    total: results.length,
    ok: results.filter(r => r.status === 'ok').length,
    broken: results.filter(r => r.status === 'broken').length,
    failed: results.filter(r => r.status === 'failed').length,
    broken_urls: results.filter(r => r.status === 'broken').map(r => ({ id: r.photo_id, url: r.url, status: r.http_status })),
    failed_urls: results.filter(r => r.status === 'failed').map(r => ({ id: r.photo_id, url: r.url, error: r.error }))
  };
  
  console.log('\n📊 Validation Summary:');
  console.log(`  Total URLs: ${summary.total}`);
  console.log(`  ✅ Working: ${summary.ok}`);
  console.log(`  ❌ Broken: ${summary.broken}`);
  console.log(`  ⚠️  Failed to check: ${summary.failed}`);
  
  if (summary.broken > 0) {
    console.log('\n🔗 Broken URLs:');
    summary.broken_urls.forEach(({ id, url, status }) => {
      console.log(`  Photo ${id}: ${url} (HTTP ${status})`);
    });
  }
  
  if (summary.failed > 0) {
    console.log('\n⚠️  Failed to check:');
    summary.failed_urls.forEach(({ id, url, error }) => {
      console.log(`  Photo ${id}: ${url} (${error})`);
    });
  }
  
  // Save detailed results to file
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const resultsFile = join(process.cwd(), `url-validation-results-${timestamp}.json`);
  writeFileSync(resultsFile, JSON.stringify({ summary, results }, null, 2));
  
  console.log(`\n💾 Detailed results saved to: ${resultsFile}`);
}

async function validateSpecificPhoto(photoId: number): Promise<void> {
  console.log(`🔍 Validating photo ${photoId}...`);
  
  const { data: photo, error } = await supabase
    .from('photos')
    .select('id, url, title')
    .eq('id', photoId)
    .single();
  
  if (error || !photo) {
    console.error('❌ Photo not found:', error);
    return;
  }
  
  if (!photo.url) {
    console.log('ℹ️  Photo has no URL');
    return;
  }
  
  console.log(`  URL: ${photo.url}`);
  const validation = await validateUrl(photo.url);
  
  console.log(`  Status: ${validation.status}`);
  if (validation.http_status) console.log(`  HTTP Status: ${validation.http_status}`);
  if (validation.error) console.log(`  Error: ${validation.error}`);
  if (validation.response_time) console.log(`  Response Time: ${validation.response_time}ms`);
  
  // Update the database
  await supabase.rpc('update_photo_url_status', {
    photo_id: photo.id,
    status: validation.status === 'ok' ? 'ok' : 'broken'
  });
  
  console.log('✅ Database updated');
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('🔍 URL Validator for Fauna Focus');
    console.log('');
    console.log('Usage:');
    console.log('  npm run validate-urls                    # Validate all photo URLs');
    console.log('  npm run validate-urls <photo_id>         # Validate specific photo');
    console.log('');
    console.log('Examples:');
    console.log('  npm run validate-urls');
    console.log('  npm run validate-urls 123');
    return;
  }
  
  const photoId = parseInt(args[0]);
  
  if (isNaN(photoId)) {
    console.error('❌ Invalid photo ID. Please provide a number.');
    process.exit(1);
  }
  
  await validateSpecificPhoto(photoId);
}

// If run directly (not imported)
if (require.main === module) {
  if (process.argv.length === 2) {
    // No arguments - validate all URLs
    validateAllPhotoUrls().catch(console.error);
  } else {
    // Has arguments - validate specific photo
    main().catch(console.error);
  }
}

export { validateUrl, validateAllPhotoUrls, validateSpecificPhoto }; 