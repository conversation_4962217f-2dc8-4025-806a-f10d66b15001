#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
import { writeFileSync } from 'fs';
import { join } from 'path';

// Load environment variables
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL!;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

interface CleanupSummary {
  orphaned_photos: number;
  duplicate_photos: number;
  empty_species_unpublished: number;
  broken_urls: number;
  total_issues_fixed: number;
  details: {
    orphaned_photo_ids?: number[];
    duplicate_groups?: Array<{ hash?: string; url?: string; kept: number; deleted: number[] }>;
    unpublished_species_ids?: string[];
    broken_url_photo_ids?: number[];
  };
}

async function getDataIssuesSummary(): Promise<{
  orphaned_count: number;
  duplicate_hash_count: number;
  duplicate_url_count: number;
  invalid_species_count: number;
  broken_url_count: number;
  empty_species_count: number;
}> {
  console.log('🔍 Analyzing data issues...');
  
  const [
    { count: orphaned_count },
    { count: duplicate_hash_count },
    { count: duplicate_url_count },
    { count: invalid_species_count },
    { count: broken_url_count },
    { count: empty_species_count }
  ] = await Promise.all([
    supabase.from('orphaned_photos').select('*', { count: 'exact', head: true }),
    supabase.from('duplicate_photos_by_hash').select('*', { count: 'exact', head: true }),
    supabase.from('duplicate_photos_by_url').select('*', { count: 'exact', head: true }),
    supabase.from('invalid_species_references').select('*', { count: 'exact', head: true }),
    supabase.from('photos_with_missing_files').select('*', { count: 'exact', head: true }),
    supabase.from('species_with_no_photos').select('*', { count: 'exact', head: true })
  ]);
  
  return {
    orphaned_count: orphaned_count || 0,
    duplicate_hash_count: duplicate_hash_count || 0,
    duplicate_url_count: duplicate_url_count || 0,
    invalid_species_count: invalid_species_count || 0,
    broken_url_count: broken_url_count || 0,
    empty_species_count: empty_species_count || 0
  };
}

async function cleanupOrphanedPhotos(): Promise<{ deleted_count: number; deleted_ids: number[] }> {
  console.log('🗑️  Cleaning up orphaned photos...');
  
  // Get orphaned photo IDs before deletion
  const { data: orphanedPhotos } = await supabase
    .from('orphaned_photos')
    .select('id');
  
  if (!orphanedPhotos || orphanedPhotos.length === 0) {
    console.log('  ℹ️  No orphaned photos found');
    return { deleted_count: 0, deleted_ids: [] };
  }
  
  const orphanedIds = orphanedPhotos.map(p => p.id);
  
  // Run the cleanup function
  const { data: result, error } = await supabase.rpc('cleanup_orphaned_photos');
  
  if (error) {
    console.error('  ❌ Error cleaning up orphaned photos:', error);
    return { deleted_count: 0, deleted_ids: [] };
  }
  
  console.log(`  ✅ Deleted ${result} orphaned photos`);
  return { deleted_count: result, deleted_ids: orphanedIds };
}

async function cleanupDuplicatePhotos(): Promise<{ deleted_count: number; kept_photos: number[]; duplicateGroups: Array<{ hash?: string; url?: string; kept: number; deleted: number[] }> }> {
  console.log('🔄 Cleaning up duplicate photos...');
  
  // Get duplicate groups before cleanup
  const [hashDuplicates, urlDuplicates] = await Promise.all([
    supabase.from('duplicate_photos_by_hash').select('hash, photo_ids'),
    supabase.from('duplicate_photos_by_url').select('url, photo_ids')
  ]);
  
  const duplicateGroups: Array<{ hash?: string; url?: string; kept: number; deleted: number[] }> = [];
  
  // Process hash duplicates
  if (hashDuplicates.data) {
    hashDuplicates.data.forEach(group => {
      const kept = group.photo_ids[0];
      const deleted = group.photo_ids.slice(1);
      duplicateGroups.push({ hash: group.hash, kept, deleted });
    });
  }
  
  // Process URL duplicates
  if (urlDuplicates.data) {
    urlDuplicates.data.forEach(group => {
      const kept = group.photo_ids[0];
      const deleted = group.photo_ids.slice(1);
      duplicateGroups.push({ url: group.url, kept, deleted });
    });
  }
  
  if (duplicateGroups.length === 0) {
    console.log('  ℹ️  No duplicate photos found');
    return { deleted_count: 0, kept_photos: [], duplicateGroups: [] };
  }
  
  // Run the cleanup function
  const { data: result, error } = await supabase.rpc('cleanup_duplicate_photos');
  
  if (error) {
    console.error('  ❌ Error cleaning up duplicate photos:', error);
    return { deleted_count: 0, kept_photos: [], duplicateGroups: [] };
  }
  
  console.log(`  ✅ Deleted ${result[0].deleted_count} duplicate photos, kept ${result[0].kept_photos.length} photos`);
  return { 
    deleted_count: result[0].deleted_count, 
    kept_photos: result[0].kept_photos || [], 
    duplicateGroups 
  };
}

async function unpublishEmptySpecies(): Promise<{ updated_count: number; unpublished_ids: string[] }> {
  console.log('📝 Unpublishing species with no photos...');
  
  // Get species that will be unpublished
  const { data: emptySpecies } = await supabase
    .from('species_with_no_photos')
    .select('id')
    .eq('published', true);
  
  if (!emptySpecies || emptySpecies.length === 0) {
    console.log('  ℹ️  No published species without photos found');
    return { updated_count: 0, unpublished_ids: [] };
  }
  
  const unpublishedIds = emptySpecies.map(s => s.id);
  
  // Run the cleanup function
  const { data: result, error } = await supabase.rpc('unpublish_empty_species');
  
  if (error) {
    console.error('  ❌ Error unpublishing empty species:', error);
    return { updated_count: 0, unpublished_ids: [] };
  }
  
  console.log(`  ✅ Unpublished ${result} species`);
  return { updated_count: result, unpublished_ids: emptySpecies.map(s => s.id) };
}

async function getBrokenUrls(): Promise<{ count: number; photo_ids: number[] }> {
  console.log('🔗 Checking for broken URLs...');
  
  const { data: brokenPhotos, count } = await supabase
    .from('photos_with_missing_files')
    .select('id', { count: 'exact' });
  
  if (!brokenPhotos || brokenPhotos.length === 0) {
    console.log('  ℹ️  No broken URLs found');
    return { count: 0, photo_ids: [] };
  }
  
  console.log(`  ⚠️  Found ${count} photos with broken URLs`);
  return { count: count || 0, photo_ids: brokenPhotos.map(p => p.id) };
}

async function runFullCleanup(dryRun: boolean = false): Promise<CleanupSummary> {
  console.log(`🧹 Starting ${dryRun ? 'DRY RUN ' : ''}data cleanup...\n`);
  
  const summary: CleanupSummary = {
    orphaned_photos: 0,
    duplicate_photos: 0,
    empty_species_unpublished: 0,
    broken_urls: 0,
    total_issues_fixed: 0,
    details: {}
  };
  
  // Get initial issues summary
  const issues = await getDataIssuesSummary();
  
  console.log('📊 Current Issues Summary:');
  console.log(`  Orphaned photos: ${issues.orphaned_count}`);
  console.log(`  Duplicate photos (hash): ${issues.duplicate_hash_count}`);
  console.log(`  Duplicate photos (URL): ${issues.duplicate_url_count}`);
  console.log(`  Invalid species references: ${issues.invalid_species_count}`);
  console.log(`  Broken URLs: ${issues.broken_url_count}`);
  console.log(`  Empty species: ${issues.empty_species_count}\n`);
  
  if (dryRun) {
    console.log('🔍 This is a dry run - no changes will be made\n');
    return summary;
  }
  
  // Clean up orphaned photos
  const orphanedResult = await cleanupOrphanedPhotos();
  summary.orphaned_photos = orphanedResult.deleted_count;
  summary.details.orphaned_photo_ids = orphanedResult.deleted_ids;
  
  // Clean up duplicate photos
  const duplicateResult = await cleanupDuplicatePhotos();
  summary.duplicate_photos = duplicateResult.deleted_count;
  summary.details.duplicate_groups = duplicateResult.duplicateGroups;
  
  // Unpublish empty species
  const emptySpeciesResult = await unpublishEmptySpecies();
  summary.empty_species_unpublished = emptySpeciesResult.updated_count;
  summary.details.unpublished_species_ids = emptySpeciesResult.unpublished_ids;
  
  // Check broken URLs (no automatic fix, just reporting)
  const brokenUrlsResult = await getBrokenUrls();
  summary.broken_urls = brokenUrlsResult.count;
  summary.details.broken_url_photo_ids = brokenUrlsResult.photo_ids;
  
  // Calculate total issues fixed
  summary.total_issues_fixed = summary.orphaned_photos + summary.duplicate_photos + summary.empty_species_unpublished;
  
  return summary;
}

async function generateCleanupReport(summary: CleanupSummary): Promise<void> {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportFile = join(process.cwd(), `cleanup-report-${timestamp}.json`);
  
  const report = {
    timestamp: new Date().toISOString(),
    summary,
    recommendations: [
      summary.broken_urls > 0 ? `Run URL validation to fix ${summary.broken_urls} broken URLs` : null,
      summary.orphaned_photos > 0 ? `Orphaned photos have been cleaned up` : null,
      summary.duplicate_photos > 0 ? `Duplicate photos have been cleaned up` : null,
      summary.empty_species_unpublished > 0 ? `Empty species have been unpublished` : null
    ].filter(Boolean)
  };
  
  writeFileSync(reportFile, JSON.stringify(report, null, 2));
  console.log(`\n💾 Cleanup report saved to: ${reportFile}`);
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run') || args.includes('-d');
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log('🧹 Data Cleanup Tool for Fauna Focus');
    console.log('');
    console.log('Usage:');
    console.log('  npm run cleanup-data                    # Run full cleanup');
    console.log('  npm run cleanup-data --dry-run          # Dry run (no changes)');
    console.log('  npm run cleanup-data -d                 # Short form for dry run');
    console.log('');
    console.log('Operations:');
    console.log('  • Delete orphaned photos (no valid species)');
    console.log('  • Remove duplicate photos (keep oldest)');
    console.log('  • Unpublish species with no photos');
    console.log('  • Report broken URLs (requires manual fix)');
    console.log('');
    console.log('Examples:');
    console.log('  npm run cleanup-data');
    console.log('  npm run cleanup-data --dry-run');
    return;
  }
  
  try {
    const summary = await runFullCleanup(dryRun);
    
    console.log('\n📊 Cleanup Summary:');
    console.log(`  Orphaned photos deleted: ${summary.orphaned_photos}`);
    console.log(`  Duplicate photos deleted: ${summary.duplicate_photos}`);
    console.log(`  Empty species unpublished: ${summary.empty_species_unpublished}`);
    console.log(`  Broken URLs found: ${summary.broken_urls}`);
    console.log(`  Total issues fixed: ${summary.total_issues_fixed}`);
    
    if (!dryRun) {
      await generateCleanupReport(summary);
    }
    
    console.log('\n✅ Cleanup completed successfully!');
  } catch (error) {
    console.error('\n❌ Cleanup failed:', error);
    process.exit(1);
  }
}

// If run directly (not imported)
if (require.main === module) {
  main().catch(console.error);
}

export { runFullCleanup, getDataIssuesSummary, cleanupOrphanedPhotos, cleanupDuplicatePhotos, unpublishEmptySpecies }; 