#!/usr/bin/env node

/**
 * Cleanup script for deprecated components after navigation consolidation
 * 
 * This script identifies and optionally removes deprecated components,
 * unused imports, and redundant code after the navigation consolidation.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Components that have been deprecated/consolidated
const DEPRECATED_COMPONENTS = [
  'PhotoAssignment.tsx',
  'PhotoRematch.tsx', 
  'PhotoGalleryManager.tsx'
];

// Pages that are now legacy
const LEGACY_PAGES = [
  'Index.tsx', // Now legacy, replaced by WildlifeExplorer
  'PublicWildlifeSite.tsx', // Now legacy, replaced by WildlifeExplorer
  'SpeciesPage.tsx' // Now legacy, replaced by WildlifeExplorer
];

// Routes that have been consolidated
const DEPRECATED_ROUTES = [
  '/explore',
  '/species',
  '/admin/photo-assignment',
  '/admin/photo-review',
  '/admin/species-photo-matrix'
];

class DeprecationAnalyzer {
  constructor() {
    this.srcDir = path.join(__dirname, '../src');
    this.findings = {
      deprecatedComponents: [],
      unusedImports: [],
      legacyRoutes: [],
      redundantCode: [],
      performanceIssues: []
    };
  }

  analyze() {
    console.log('🔍 Analyzing codebase for deprecated components...\n');
    
    this.findDeprecatedComponents();
    this.findUnusedImports();
    this.findLegacyRoutes();
    this.findRedundantCode();
    this.checkPerformanceIssues();
    
    this.generateReport();
  }

  findDeprecatedComponents() {
    console.log('📦 Checking for deprecated components...');
    
    const componentsDir = path.join(this.srcDir, 'components');
    
    DEPRECATED_COMPONENTS.forEach(component => {
      const componentPath = path.join(componentsDir, component);
      if (fs.existsSync(componentPath)) {
        const content = fs.readFileSync(componentPath, 'utf8');
        
        // Check if it's already marked as deprecated
        const isMarkedDeprecated = content.includes('@deprecated') || content.includes('deprecated');
        
        this.findings.deprecatedComponents.push({
          file: component,
          path: componentPath,
          isMarkedDeprecated,
          size: fs.statSync(componentPath).size
        });
      }
    });
  }

  findUnusedImports() {
    console.log('📥 Checking for unused imports...');
    
    const checkFile = (filePath) => {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      const imports = [];
      const usages = new Set();
      
      lines.forEach((line, index) => {
        // Find import statements
        const importMatch = line.match(/import\s+(?:{([^}]+)}|\*\s+as\s+(\w+)|(\w+))\s+from\s+['"]([^'"]+)['"]/);
        if (importMatch) {
          const [, namedImports, namespaceImport, defaultImport, source] = importMatch;
          
          if (namedImports) {
            namedImports.split(',').forEach(imp => {
              const name = imp.trim();
              imports.push({ name, line: index + 1, source, type: 'named' });
            });
          } else if (namespaceImport) {
            imports.push({ name: namespaceImport, line: index + 1, source, type: 'namespace' });
          } else if (defaultImport) {
            imports.push({ name: defaultImport, line: index + 1, source, type: 'default' });
          }
        }
        
        // Find usages (simple heuristic)
        const words = line.match(/\b\w+\b/g) || [];
        words.forEach(word => usages.add(word));
      });
      
      // Check for unused imports
      const unusedImports = imports.filter(imp => {
        // Skip if it's a type import or has side effects
        if (imp.source.includes('.css') || imp.source.includes('.scss')) return false;
        
        return !usages.has(imp.name);
      });
      
      if (unusedImports.length > 0) {
        this.findings.unusedImports.push({
          file: path.relative(this.srcDir, filePath),
          unusedImports
        });
      }
    };

    this.walkDirectory(this.srcDir, checkFile, ['.tsx', '.ts']);
  }

  findLegacyRoutes() {
    console.log('🛣️ Checking for legacy routes...');
    
    const routeFiles = [
      path.join(this.srcDir, 'App.tsx'),
      path.join(this.srcDir, 'components/ui/mobile-navigation.tsx')
    ];
    
    routeFiles.forEach(filePath => {
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        
        DEPRECATED_ROUTES.forEach(route => {
          if (content.includes(route)) {
            this.findings.legacyRoutes.push({
              file: path.relative(this.srcDir, filePath),
              route,
              stillReferenced: true
            });
          }
        });
      }
    });
  }

  findRedundantCode() {
    console.log('🔄 Checking for redundant code patterns...');
    
    const checkFile = (filePath) => {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Check for duplicate component patterns
      const componentMatches = content.match(/const\s+(\w+)\s*=\s*\(/g) || [];
      const functionMatches = content.match(/function\s+(\w+)\s*\(/g) || [];
      
      if (componentMatches.length > 10 || functionMatches.length > 15) {
        this.findings.redundantCode.push({
          file: path.relative(this.srcDir, filePath),
          issue: 'Large file with many components/functions',
          components: componentMatches.length,
          functions: functionMatches.length,
          suggestion: 'Consider splitting into smaller files'
        });
      }
      
      // Check for duplicate imports
      const importLines = content.split('\n').filter(line => line.trim().startsWith('import'));
      const uniqueImports = new Set(importLines);
      
      if (importLines.length !== uniqueImports.size) {
        this.findings.redundantCode.push({
          file: path.relative(this.srcDir, filePath),
          issue: 'Duplicate imports detected',
          suggestion: 'Remove duplicate import statements'
        });
      }
    };

    this.walkDirectory(this.srcDir, checkFile, ['.tsx', '.ts']);
  }

  checkPerformanceIssues() {
    console.log('⚡ Checking for performance issues...');
    
    const checkFile = (filePath) => {
      const content = fs.readFileSync(filePath, 'utf8');
      const fileSize = fs.statSync(filePath).size;
      
      // Check file size
      if (fileSize > 50000) { // 50KB
        this.findings.performanceIssues.push({
          file: path.relative(this.srcDir, filePath),
          issue: 'Large file size',
          size: `${Math.round(fileSize / 1024)}KB`,
          suggestion: 'Consider code splitting or lazy loading'
        });
      }
      
      // Check for missing React.memo or useMemo
      if (content.includes('useState') || content.includes('useEffect')) {
        if (!content.includes('React.memo') && !content.includes('useMemo') && !content.includes('useCallback')) {
          this.findings.performanceIssues.push({
            file: path.relative(this.srcDir, filePath),
            issue: 'Missing performance optimizations',
            suggestion: 'Consider using React.memo, useMemo, or useCallback'
          });
        }
      }
    };

    this.walkDirectory(this.srcDir, checkFile, ['.tsx']);
  }

  walkDirectory(dir, callback, extensions = []) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        this.walkDirectory(filePath, callback, extensions);
      } else if (extensions.length === 0 || extensions.some(ext => file.endsWith(ext))) {
        callback(filePath);
      }
    });
  }

  generateReport() {
    console.log('\n📊 DEPRECATION ANALYSIS REPORT');
    console.log('================================\n');
    
    // Deprecated Components
    if (this.findings.deprecatedComponents.length > 0) {
      console.log('🗑️ DEPRECATED COMPONENTS:');
      this.findings.deprecatedComponents.forEach(comp => {
        const status = comp.isMarkedDeprecated ? '✅ Marked' : '❌ Not marked';
        console.log(`  • ${comp.file} (${Math.round(comp.size / 1024)}KB) - ${status}`);
      });
      console.log();
    }
    
    // Unused Imports
    if (this.findings.unusedImports.length > 0) {
      console.log('📥 UNUSED IMPORTS:');
      this.findings.unusedImports.forEach(file => {
        console.log(`  • ${file.file}:`);
        file.unusedImports.forEach(imp => {
          console.log(`    - ${imp.name} from "${imp.source}" (line ${imp.line})`);
        });
      });
      console.log();
    }
    
    // Legacy Routes
    if (this.findings.legacyRoutes.length > 0) {
      console.log('🛣️ LEGACY ROUTES STILL REFERENCED:');
      this.findings.legacyRoutes.forEach(route => {
        console.log(`  • ${route.route} in ${route.file}`);
      });
      console.log();
    }
    
    // Redundant Code
    if (this.findings.redundantCode.length > 0) {
      console.log('🔄 REDUNDANT CODE:');
      this.findings.redundantCode.forEach(issue => {
        console.log(`  • ${issue.file}: ${issue.issue}`);
        console.log(`    Suggestion: ${issue.suggestion}`);
      });
      console.log();
    }
    
    // Performance Issues
    if (this.findings.performanceIssues.length > 0) {
      console.log('⚡ PERFORMANCE ISSUES:');
      this.findings.performanceIssues.forEach(issue => {
        console.log(`  • ${issue.file}: ${issue.issue}`);
        if (issue.size) console.log(`    Size: ${issue.size}`);
        console.log(`    Suggestion: ${issue.suggestion}`);
      });
      console.log();
    }
    
    // Summary
    const totalIssues = Object.values(this.findings).reduce((sum, arr) => sum + arr.length, 0);
    console.log(`📈 SUMMARY: Found ${totalIssues} items to review`);
    
    if (totalIssues === 0) {
      console.log('🎉 No issues found! Codebase is clean.');
    } else {
      console.log('\n💡 RECOMMENDATIONS:');
      console.log('  1. Remove or properly mark deprecated components');
      console.log('  2. Clean up unused imports to reduce bundle size');
      console.log('  3. Update route references to use new consolidated paths');
      console.log('  4. Refactor large files for better maintainability');
      console.log('  5. Add performance optimizations where needed');
    }
  }
}

// Run the analysis
if (import.meta.url === `file://${process.argv[1]}`) {
  const analyzer = new DeprecationAnalyzer();
  analyzer.analyze();
}
