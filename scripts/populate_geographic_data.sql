-- Populate sample geographic data for testing the new hierarchical filtering
-- This script adds countries, states/provinces, and regions to existing species

-- First, let's check if the geographic columns exist
-- If not, add them (this should already be done by the migration)
ALTER TABLE species_v2 
ADD COLUMN IF NOT EXISTS countries TEXT[],
ADD COLUMN IF NOT EXISTS states_provinces TEXT[],
ADD COLUMN IF NOT EXISTS primary_region TEXT,
ADD COLUMN IF NOT EXISTS geographic_scope TEXT DEFAULT 'regional';

-- Update some sample species with geographic data for testing
-- Note: This is sample data for testing purposes

-- Australian species
UPDATE species_v2 
SET 
  countries = ARRAY['Australia'],
  states_provinces = ARRAY['Queensland', 'New South Wales', 'Victoria'],
  primary_region = 'Australia',
  geographic_scope = 'national'
WHERE name ILIKE '%kangaroo%' OR name ILIKE '%koala%' OR name ILIKE '%wombat%' OR name ILIKE '%emu%';

-- North American species  
UPDATE species_v2 
SET 
  countries = ARRAY['United States', 'Canada'],
  states_provinces = ARRAY['California', 'Texas', 'Florida', 'British Columbia', 'Ontario'],
  primary_region = 'North America',
  geographic_scope = 'continental'
WHERE name ILIKE '%eagle%' OR name ILIKE '%bear%' OR name ILIKE '%deer%' OR name ILIKE '%wolf%';

-- European species
UPDATE species_v2 
SET 
  countries = ARRAY['United Kingdom', 'France', 'Germany', 'Spain'],
  states_provinces = ARRAY['England', 'Scotland', 'Wales'],
  primary_region = 'Europe',
  geographic_scope = 'continental'
WHERE name ILIKE '%fox%' OR name ILIKE '%badger%' OR name ILIKE '%robin%';

-- African species
UPDATE species_v2 
SET 
  countries = ARRAY['Kenya', 'Tanzania', 'South Africa', 'Botswana'],
  states_provinces = ARRAY['Serengeti', 'Masai Mara', 'Kruger'],
  primary_region = 'Africa',
  geographic_scope = 'continental'
WHERE name ILIKE '%lion%' OR name ILIKE '%elephant%' OR name ILIKE '%giraffe%' OR name ILIKE '%zebra%';

-- Asian species
UPDATE species_v2 
SET 
  countries = ARRAY['India', 'China', 'Thailand', 'Indonesia'],
  states_provinces = ARRAY['Bengal', 'Sichuan', 'Sumatra'],
  primary_region = 'Asia',
  geographic_scope = 'continental'
WHERE name ILIKE '%tiger%' OR name ILIKE '%panda%' OR name ILIKE '%orangutan%';

-- Global/widespread species
UPDATE species_v2 
SET 
  countries = ARRAY['Global'],
  primary_region = 'Worldwide',
  geographic_scope = 'global'
WHERE name ILIKE '%sparrow%' OR name ILIKE '%pigeon%' OR name ILIKE '%crow%';

-- Add some fallback data for species without specific geographic info
UPDATE species_v2 
SET 
  countries = ARRAY['Unknown'],
  primary_region = 'Unspecified',
  geographic_scope = 'regional'
WHERE countries IS NULL AND published = true;

-- Create an index for better performance on geographic queries
CREATE INDEX IF NOT EXISTS idx_species_v2_countries ON species_v2 USING GIN (countries);
CREATE INDEX IF NOT EXISTS idx_species_v2_primary_region ON species_v2 (primary_region);
CREATE INDEX IF NOT EXISTS idx_species_v2_geographic_scope ON species_v2 (geographic_scope);
