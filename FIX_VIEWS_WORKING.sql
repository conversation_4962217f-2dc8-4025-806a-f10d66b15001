-- Fix missing views and add species mapping functionality (WORKING VERSION)

-- 1. Create the missing species_location_details view
CREATE OR REPLACE VIEW species_location_details AS
SELECT 
  s.id as species_id,
  s.name as species_name,
  s.common_name,
  s.scientific_name,
  s.category,
  s.conservation_status,
  l.id as location_id,
  l.name as location_name,
  l.state_province,
  l.country,
  l.latitude,
  l.longitude,
  sl.abundance,
  sl.seasonal_presence,
  sl.breeding_status,
  sl.best_months,
  sl.notes,
  sl.confidence_level,
  sl.last_observed,
  sl.observation_count,
  COUNT(p.id) as photo_count
FROM species_v2 s
JOIN species_locations sl ON s.id = sl.species_id
JOIN locations l ON sl.location_id = l.id
LEFT JOIN photos_v2 p ON s.id = p.species_id
WHERE s.published = true AND l.published = true
GROUP BY s.id, s.name, s.common_name, s.scientific_name, s.category, s.conservation_status,
         l.id, l.name, l.state_province, l.country, l.latitude, l.longitude,
         sl.abundance, sl.seasonal_presence, sl.breeding_status, sl.best_months,
         sl.notes, sl.confidence_level, sl.last_observed, sl.observation_count;

-- 2. Update the location_species_summary view to handle empty data
CREATE OR REPLACE VIEW location_species_summary AS
SELECT 
  l.id as location_id,
  l.name as location_name,
  l.latitude,
  l.longitude,
  l.state_province,
  l.country,
  l.habitat_types,
  l.featured,
  l.published,
  COALESCE(COUNT(sl.species_id), 0) as total_species,
  COALESCE(COUNT(CASE WHEN sl.abundance IN ('common', 'abundant', 'very_common') THEN 1 END), 0) as common_species,
  COALESCE(COUNT(CASE WHEN sl.abundance = 'rare' THEN 1 END), 0) as rare_species,
  COALESCE(COUNT(CASE WHEN sl.breeding_status = 'confirmed' THEN 1 END), 0) as breeding_species,
  COALESCE(ARRAY_AGG(DISTINCT s.category) FILTER (WHERE s.category IS NOT NULL), ARRAY[]::text[]) as categories_present,
  MAX(sl.last_observed) as last_observation_date,
  COALESCE(SUM(sl.observation_count), 0) as total_observations
FROM locations l
LEFT JOIN species_locations sl ON l.id = sl.location_id
LEFT JOIN species_v2 s ON sl.species_id = s.id
WHERE l.published = true
GROUP BY l.id, l.name, l.latitude, l.longitude, l.state_province, l.country, l.habitat_types, l.featured, l.published;

-- 3. Update hotspot_highlights view to use the corrected location_species_summary
CREATE OR REPLACE VIEW hotspot_highlights AS
SELECT 
  l.*,
  COALESCE(lss.total_species, 0) as total_species,
  COALESCE(lss.common_species, 0) as common_species,
  COALESCE(lss.rare_species, 0) as rare_species,
  COALESCE(lss.breeding_species, 0) as breeding_species,
  COALESCE(lss.categories_present, ARRAY[]::text[]) as categories_present,
  COALESCE(lss.total_observations, 0) as total_observations,
  l.photo_url as primary_photo,
  NULL as primary_photo_title
FROM locations l
LEFT JOIN location_species_summary lss ON l.id = lss.location_id
WHERE l.published = true
ORDER BY l.featured DESC, COALESCE(lss.total_species, 0) DESC, l.name;

-- 4. Add some sample species-location relationships for testing
DO $$
DECLARE
    central_park_id UUID;
    species_ids UUID[];
    species_id UUID;
BEGIN
    -- Get Central Park location ID
    SELECT id INTO central_park_id FROM locations WHERE name = 'Central Park - The Ramble' LIMIT 1;
    
    -- Get some species IDs (first 5 published species)
    SELECT ARRAY(SELECT id FROM species_v2 WHERE published = true LIMIT 5) INTO species_ids;
    
    -- Only proceed if we have both location and species
    IF central_park_id IS NOT NULL AND array_length(species_ids, 1) > 0 THEN
        -- Add sample species-location relationships
        FOREACH species_id IN ARRAY species_ids
        LOOP
            INSERT INTO species_locations (
                species_id, 
                location_id, 
                abundance, 
                seasonal_presence, 
                breeding_status, 
                best_months, 
                confidence_level, 
                observation_count
            ) VALUES (
                species_id,
                central_park_id,
                CASE (random() * 4)::int 
                    WHEN 0 THEN 'rare'
                    WHEN 1 THEN 'uncommon' 
                    WHEN 2 THEN 'common'
                    WHEN 3 THEN 'abundant'
                    ELSE 'very_common'
                END,
                ARRAY['spring', 'summer', 'fall'],
                CASE (random() * 3)::int
                    WHEN 0 THEN 'non_breeding'
                    WHEN 1 THEN 'possible'
                    WHEN 2 THEN 'probable'
                    ELSE 'confirmed'
                END,
                ARRAY[4, 5, 6, 7, 8, 9, 10], -- April through October
                'medium',
                (random() * 50)::int + 1
            ) ON CONFLICT (species_id, location_id) DO NOTHING;
        END LOOP;
    END IF;
END $$;

-- 5. Create a view for species that don't have location assignments yet
CREATE OR REPLACE VIEW unassigned_species AS
SELECT 
    s.id,
    s.name,
    s.common_name,
    s.scientific_name,
    s.category,
    s.conservation_status,
    COUNT(p.id) as photo_count
FROM species_v2 s
LEFT JOIN photos_v2 p ON s.id = p.species_id
WHERE s.published = true 
AND s.id NOT IN (SELECT DISTINCT species_id FROM species_locations WHERE species_id IS NOT NULL)
GROUP BY s.id, s.name, s.common_name, s.scientific_name, s.category, s.conservation_status
ORDER BY s.name;

-- 6. Create a view for locations that don't have species assignments yet
CREATE OR REPLACE VIEW locations_without_species AS
SELECT 
    l.id,
    l.name,
    l.state_province,
    l.country,
    l.latitude,
    l.longitude,
    l.habitat_types
FROM locations l
WHERE l.published = true 
AND l.id NOT IN (SELECT DISTINCT location_id FROM species_locations WHERE location_id IS NOT NULL)
ORDER BY l.name;

-- 7. Drop existing policies and create new ones (without IF NOT EXISTS)
DROP POLICY IF EXISTS "Public read access for species occurrence" ON species_occurrence;
DROP POLICY IF EXISTS "Admin full access for species occurrence" ON species_occurrence;
DROP POLICY IF EXISTS "Public read access for observations" ON observations;
DROP POLICY IF EXISTS "Admin full access for observations" ON observations;

CREATE POLICY "Public read access for species occurrence" ON species_occurrence FOR SELECT USING (
  EXISTS (SELECT 1 FROM locations l WHERE l.id = location_id AND l.published = true)
);

CREATE POLICY "Admin full access for species occurrence" ON species_occurrence FOR ALL USING (auth.email() = '<EMAIL>');

CREATE POLICY "Public read access for observations" ON observations FOR SELECT USING (
  EXISTS (SELECT 1 FROM locations l WHERE l.id = location_id AND l.published = true)
);

CREATE POLICY "Admin full access for observations" ON observations FOR ALL USING (auth.email() = '<EMAIL>');

-- 8. Show results
SELECT 'Views created successfully!' as status;

SELECT 'Sample species-location relationships:' as info;
SELECT 
    s.name as species_name,
    l.name as location_name,
    sl.abundance,
    sl.breeding_status
FROM species_locations sl
JOIN species_v2 s ON sl.species_id = s.id
JOIN locations l ON sl.location_id = l.id
LIMIT 10;

SELECT 'Unassigned species count:' as info;
SELECT COUNT(*) as count FROM unassigned_species;

SELECT 'Locations without species count:' as info;
SELECT COUNT(*) as count FROM locations_without_species;
