# Wildlife API Documentation

This project provides a comprehensive API for wildlife species and photo data, built with Supabase Edge Functions and a React admin interface.

## 🚀 Quick Start

### API Base URL
```
https://raqxptgrugnmxdunbuty.supabase.co/functions/v1/api/v1
```

### Example Usage
```javascript
// Get all birds
const response = await fetch('https://raqxptgrugnmxdunbuty.supabase.co/functions/v1/api/v1/species?category=Birds');
const data = await response.json();

// Search for species
const searchResponse = await fetch('https://raqxptgrugnmxdunbuty.supabase.co/functions/v1/api/v1/search?q=goldfinch');
const searchData = await searchResponse.json();
```

## 📋 API Endpoints

### Species Endpoints

#### GET /species
Get all species with pagination and filtering.

**Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20, max: 100)
- `category` (string): Filter by category (e.g., "Birds", "Mammals", "Reptiles")
- `conservation_status` (string): Filter by conservation status
- `featured` (boolean): Filter featured species only
- `published` (boolean): Filter published species only (default: true)
- `search` (string): Search in name, common_name, scientific_name, description
- `sort_by` (string): Sort field - "name", "created_at", "photo_count" (default: "name")
- `sort_order` (string): Sort direction - "asc" or "desc" (default: "asc")

**Example Request:**
```bash
curl "https://raqxptgrugnmxdunbuty.supabase.co/functions/v1/api/v1/species?category=Birds&limit=10&sort_by=name&sort_order=asc"
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "american-goldfinch",
      "name": "American Goldfinch",
      "common_name": "Eastern Goldfinch",
      "scientific_name": "Spinus tristis",
      "category": "Birds",
      "conservation_status": "Least Concern",
      "description": "A small North American bird in the finch family...",
      "habitat": "Open woodlands, fields, gardens",
      "diet": "Seeds, especially thistle and sunflower",
      "behavior": "Social, often found in flocks",
      "photo_count": 15,
      "published": true,
      "featured": false,
      "created_at": "2024-01-15T10:30:00Z",
      "photos": [
        {
          "id": 1,
          "url": "https://example.com/photo1.jpg",
          "title": "Goldfinch in flight",
          "description": "Beautiful shot of a goldfinch",
          "photographer": "John Doe",
          "location": "Central Park, NY",
          "camera_model": "Canon EOS R5",
          "lens": "RF 100-500mm f/4.5-7.1L IS USM",
          "published": true,
          "created_at": "2024-01-15T10:30:00Z"
        }
      ]
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 150,
    "totalPages": 15
  }
}
```

#### GET /species/{id}
Get a specific species with photos.

**Parameters:**
- `id` (string, required): Species ID

**Example Request:**
```bash
curl "https://raqxptgrugnmxdunbuty.supabase.co/functions/v1/api/v1/species/american-goldfinch"
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "id": "american-goldfinch",
    "name": "American Goldfinch",
    "common_name": "Eastern Goldfinch",
    "scientific_name": "Spinus tristis",
    "category": "Birds",
    "conservation_status": "Least Concern",
    "description": "A small North American bird in the finch family...",
    "habitat": "Open woodlands, fields, gardens",
    "diet": "Seeds, especially thistle and sunflower",
    "behavior": "Social, often found in flocks",
    "photo_count": 15,
    "published": true,
    "featured": false,
    "created_at": "2024-01-15T10:30:00Z",
    "photos": [...]
  }
}
```

#### POST /species
Create a new species (admin only).

**Required fields:**
- `name` (string): Species name

**Optional fields:**
- `common_name` (string): Common name
- `scientific_name` (string): Scientific name
- `category` (string): Category
- `conservation_status` (string): Conservation status
- `description` (string): Description
- `habitat` (string): Habitat
- `diet` (string): Diet
- `behavior` (string): Behavior
- `published` (boolean): Published status (default: false)
- `featured` (boolean): Featured status (default: false)

**Example Request:**
```bash
curl -X POST "https://raqxptgrugnmxdunbuty.supabase.co/functions/v1/api/v1/species" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "New Species",
    "category": "Birds",
    "description": "A new species description",
    "published": true
  }'
```

### Photo Endpoints

#### GET /photos
Get all photos with filtering.

**Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20, max: 100)
- `species_id` (string): Filter by species ID
- `photographer` (string): Filter by photographer name
- `location` (string): Filter by location
- `camera_model` (string): Filter by camera model
- `lens` (string): Filter by lens
- `published` (boolean): Filter published photos only (default: true)
- `search` (string): Search in title, description
- `sort_by` (string): Sort field - "created_at", "title" (default: "created_at")
- `sort_order` (string): Sort direction - "asc" or "desc" (default: "desc")

**Example Request:**
```bash
curl "https://raqxptgrugnmxdunbuty.supabase.co/functions/v1/api/v1/photos?species_id=american-goldfinch&limit=10&camera_model=Canon"
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "url": "https://example.com/photo1.jpg",
      "title": "Goldfinch in flight",
      "description": "Beautiful shot of a goldfinch",
      "photographer": "John Doe",
      "location": "Central Park, NY",
      "camera_model": "Canon EOS R5",
      "lens": "RF 100-500mm f/4.5-7.1L IS USM",
      "species_id": "american-goldfinch",
      "published": true,
      "created_at": "2024-01-15T10:30:00Z",
      "species": {
        "id": "american-goldfinch",
        "name": "American Goldfinch",
        "common_name": "Eastern Goldfinch",
        "category": "Birds",
        "conservation_status": "Least Concern"
      }
    }
  ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 500,
    "totalPages": 50
  }
}
```

#### GET /photos/{id}
Get a specific photo with species details.

**Parameters:**
- `id` (number, required): Photo ID

**Example Request:**
```bash
curl "https://raqxptgrugnmxdunbuty.supabase.co/functions/v1/api/v1/photos/1"
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "url": "https://example.com/photo1.jpg",
    "title": "Goldfinch in flight",
    "description": "Beautiful shot of a goldfinch",
    "photographer": "John Doe",
    "location": "Central Park, NY",
    "camera_model": "Canon EOS R5",
    "lens": "RF 100-500mm f/4.5-7.1L IS USM",
    "species_id": "american-goldfinch",
    "published": true,
    "created_at": "2024-01-15T10:30:00Z",
    "species": {
      "id": "american-goldfinch",
      "name": "American Goldfinch",
      "common_name": "Eastern Goldfinch",
      "category": "Birds",
      "conservation_status": "Least Concern"
    }
  }
}
```

#### POST /photos
Upload a new photo (admin only).

**Required fields:**
- `url` (string): Photo URL
- `species_id` (string): Species ID

**Optional fields:**
- `title` (string): Photo title
- `description` (string): Photo description
- `photographer` (string): Photographer name
- `location` (string): Location
- `camera_model` (string): Camera model
- `lens` (string): Lens
- `published` (boolean): Published status (default: false)

**Example Request:**
```bash
curl -X POST "https://raqxptgrugnmxdunbuty.supabase.co/functions/v1/api/v1/photos" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "url": "https://example.com/new-photo.jpg",
    "species_id": "american-goldfinch",
    "title": "New Photo",
    "photographer": "Jane Smith",
    "location": "Central Park, NY",
    "published": true
  }'
```

### Search Endpoints

#### GET /search
Search across species and photos.

**Parameters:**
- `q` (string, required): Search query
- `type` (string): Search type - "species", "photos", or "all" (default: "all")
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20)
- `category` (string): Filter by category
- `conservation_status` (string): Filter by conservation status
- `location` (string): Filter by location
- `camera_model` (string): Filter by camera model
- `lens` (string): Filter by lens

**Example Request:**
```bash
curl "https://raqxptgrugnmxdunbuty.supabase.co/functions/v1/api/v1/search?q=goldfinch&type=all&limit=20&category=Birds"
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "species": [
      {
        "id": "american-goldfinch",
        "name": "American Goldfinch",
        "common_name": "Eastern Goldfinch",
        "category": "Birds",
        "conservation_status": "Least Concern",
        "photo_count": 15
      }
    ],
    "photos": [
      {
        "id": 1,
        "url": "https://example.com/photo1.jpg",
        "title": "Goldfinch in flight",
        "photographer": "John Doe",
        "location": "Central Park, NY",
        "species": {
          "id": "american-goldfinch",
          "name": "American Goldfinch"
        }
      }
    ]
  },
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 25,
    "totalPages": 2
  }
}
```

### Metadata Endpoints

#### GET /categories
Get all available categories with counts.

**Example Request:**
```bash
curl "https://raqxptgrugnmxdunbuty.supabase.co/functions/v1/api/v1/categories"
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "category": "Birds",
      "count": 45
    },
    {
      "category": "Mammals",
      "count": 32
    },
    {
      "category": "Reptiles",
      "count": 18
    },
    {
      "category": "Amphibians",
      "count": 12
    },
    {
      "category": "Fish",
      "count": 8
    }
  ]
}
```

#### GET /conservation-statuses
Get all conservation statuses with counts.

**Example Request:**
```bash
curl "https://raqxptgrugnmxdunbuty.supabase.co/functions/v1/api/v1/conservation-statuses"
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "conservation_status": "Least Concern",
      "count": 89
    },
    {
      "conservation_status": "Near Threatened",
      "count": 15
    },
    {
      "conservation_status": "Vulnerable",
      "count": 8
    },
    {
      "conservation_status": "Endangered",
      "count": 3
    },
    {
      "conservation_status": "Critically Endangered",
      "count": 1
    }
  ]
}
```

## 📊 Response Format

All API endpoints return JSON responses with a consistent format:

### Success Response
```json
{
  "success": true,
  "data": {
    // Actual data here
  },
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "code": "ERROR_CODE",
    "details": {
      // Additional error details
    }
  }
}
```

### Common Error Codes
- `400`: Bad Request - Invalid parameters
- `401`: Unauthorized - Missing or invalid authentication
- `403`: Forbidden - Insufficient permissions
- `404`: Not Found - Resource not found
- `422`: Unprocessable Entity - Validation error
- `500`: Internal Server Error - Server error

## 🔄 Data Flow

```
┌─────────────┐    ┌──────────────┐    ┌─────────────┐    ┌─────────────┐
│   Frontend  │───▶│  Supabase    │───▶│ Edge        │───▶│   Database  │
│   (/explore)│    │   Client     │    │ Functions   │    │   (PostgreSQL)│
└─────────────┘    └──────────────┘    └─────────────┘    └─────────────┘
       ▲                   │                   │                   │
       │                   │                   │                   │
       └───────────────────┴───────────────────┴───────────────────┘
                              Real-time updates
```

### Data Flow Steps:
1. **Frontend** (`/explore`) makes API requests via Supabase client
2. **Supabase Client** handles authentication and request routing
3. **Edge Functions** process requests and apply business logic
4. **Database** stores and retrieves data with proper indexing
5. **Real-time updates** flow back to frontend via Supabase subscriptions

## 🧱 Unused Fields

The following fields exist in the Supabase schema but are not currently displayed in the public UI:

### Species Table
- `lifespan` (string): Average lifespan of the species
- `ai_model` (string): AI model used for species identification
- `behavior` (string): Detailed behavior patterns (partially used)
- `image_hash` (string): Hash for image deduplication
- `updated_at` (timestamp): Last update timestamp
- `ai_confidence` (float): AI identification confidence score
- `ai_tags` (jsonb): AI-generated tags
- `ai_description` (text): AI-generated description

### Photos Table
- `ai_tags` (jsonb): AI-generated tags for the photo
- `ai_confidence` (float): AI identification confidence
- `ai_model` (string): AI model used for analysis
- `image_hash` (string): Hash for deduplication
- `file_size` (integer): File size in bytes
- `width` (integer): Image width in pixels
- `height` (integer): Image height in pixels
- `exif_data` (jsonb): EXIF metadata
- `updated_at` (timestamp): Last update timestamp
- `deleted_at` (timestamp): Soft delete timestamp

### Potential Future Uses:
- **AI Analysis**: Display AI confidence scores and tags
- **Image Metadata**: Show EXIF data, file sizes, dimensions
- **Deduplication**: Use image hashes to prevent duplicates
- **Analytics**: Track species lifespan and behavior patterns
- **Search Enhancement**: Use AI tags for better search results

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+
- Supabase CLI
- Supabase project

### Local Development

1. **Clone the repository:**
```bash
git clone <repository-url>
cd fauna-focus-frontend
```

2. **Install dependencies:**
```bash
npm install
```

3. **Start Supabase locally:**
```bash
supabase start
```

4. **Deploy Edge Functions:**
```bash
supabase functions deploy api/v1/species
supabase functions deploy api/v1/photos
supabase functions deploy api/v1/search
supabase functions deploy api/v1/categories
supabase functions deploy api/v1/conservation-statuses
```

5. **Start the development server:**
```bash
npm run dev
```

### Environment Variables
```bash
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Admin Configuration
VITE_ADMIN_EMAIL=<EMAIL>

# API Configuration
VITE_API_BASE_URL=https://your-project.supabase.co/functions/v1/api/v1
```

## 🔒 Authentication

### Public Endpoints
- `GET /species` (with published=true filter)
- `GET /photos` (with published=true filter)
- `GET /search`
- `GET /categories`
- `GET /conservation-statuses`

### Protected Endpoints (Admin Only)
- `POST /species`
- `PUT /species/{id}`
- `DELETE /species/{id}`
- `POST /photos`
- `PUT /photos/{id}`
- `DELETE /photos/{id}`

### Authentication Method
```javascript
// Include in request headers
headers: {
  'Authorization': `Bearer ${supabase.auth.session()?.access_token}`
}
```

## 📈 Performance Considerations

### Database Indexes
- `species(published, category, conservation_status)`
- `species(name, common_name, scientific_name)` (for search)
- `photos(published, species_id, location)`
- `photos(created_at)` (for sorting)

### Caching Strategy
- Use Supabase's built-in caching for frequently accessed data
- Implement client-side caching for metadata (categories, statuses)
- Consider CDN for photo assets

### Pagination
- Default limit: 20 items per page
- Maximum limit: 100 items per page
- Use cursor-based pagination for large datasets

## 🚀 Production Deployment

### Supabase Edge Functions
```bash
# Deploy all functions
supabase functions deploy --project-ref your-project-ref

# Deploy specific function
supabase functions deploy api/v1/species --project-ref your-project-ref
```

### Frontend Deployment
```bash
# Build for production
npm run build

# Deploy to Vercel/Netlify/etc.
```

## 📞 Support

For API support and questions:
- Check the [Supabase documentation](https://supabase.com/docs)
- Review the [Edge Functions guide](https://supabase.com/docs/guides/functions)
- Open an issue in the project repository

## 🔄 API Versioning

Current API version: `v1`

To access a specific version:
```
https://raqxptgrugnmxdunbuty.supabase.co/functions/v1/api/v1/endpoint
```

Future versions will be available at:
```
https://raqxptgrugnmxdunbuty.supabase.co/functions/v1/api/v2/endpoint
``` 