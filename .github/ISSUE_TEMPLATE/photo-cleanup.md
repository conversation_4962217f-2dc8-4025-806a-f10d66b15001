---
name: Photo Cleanup & Recovery
about: Track and manage the photo URL cleanup and recovery workflow
labels: [data-cleanup, photo, supabase]
---

## Photo Cleanup & Recovery Issue

### Checklist
- [ ] Migrations applied (`needs_recovery` column, `photos_requiring_review` view)
- [ ] Supabase types regenerated
- [ ] Test data inserted (optional)
- [ ] Normalization script run (dry-run)
- [ ] Normalization script run (applied)
- [ ] Photos reviewed in admin UI

### Description
Describe any issues, blockers, or notes related to the photo cleanup process. 