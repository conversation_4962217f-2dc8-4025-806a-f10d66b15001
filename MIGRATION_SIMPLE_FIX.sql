-- SIMPL<PERSON> HOTSPOTS MIGRATION FIX
-- This handles the case where locations table already exists but is missing columns

-- 1. First, let's check what columns exist and add missing ones
DO $$
BEGIN
    -- Add missing columns to locations table if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'ebird_location_id') THEN
        ALTER TABLE locations ADD COLUMN ebird_location_id TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'inat_place_id') THEN
        ALTER TABLE locations ADD COLUMN inat_place_id INTEGER;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'elevation_m') THEN
        ALTER TABLE locations ADD COLUMN elevation_m INTEGER;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'habitat_type') THEN
        ALTER TABLE locations ADD COLUMN habitat_type TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'description') THEN
        ALTER TABLE locations ADD COLUMN description TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'directions') THEN
        ALTER TABLE locations ADD COLUMN directions TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'facilities') THEN
        ALTER TABLE locations ADD COLUMN facilities TEXT[];
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'best_time_to_visit') THEN
        ALTER TABLE locations ADD COLUMN best_time_to_visit TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'difficulty_level') THEN
        ALTER TABLE locations ADD COLUMN difficulty_level TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'access_type') THEN
        ALTER TABLE locations ADD COLUMN access_type TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'parking_info') THEN
        ALTER TABLE locations ADD COLUMN parking_info TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'entrance_fee') THEN
        ALTER TABLE locations ADD COLUMN entrance_fee DECIMAL(8,2);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'website_url') THEN
        ALTER TABLE locations ADD COLUMN website_url TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'contact_info') THEN
        ALTER TABLE locations ADD COLUMN contact_info TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'featured') THEN
        ALTER TABLE locations ADD COLUMN featured BOOLEAN DEFAULT FALSE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'photo_url') THEN
        ALTER TABLE locations ADD COLUMN photo_url TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'habitat_types') THEN
        ALTER TABLE locations ADD COLUMN habitat_types TEXT[];
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'target_species') THEN
        ALTER TABLE locations ADD COLUMN target_species TEXT[];
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'seasonal_highlights') THEN
        ALTER TABLE locations ADD COLUMN seasonal_highlights JSONB;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'visitor_tips') THEN
        ALTER TABLE locations ADD COLUMN visitor_tips TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'published') THEN
        ALTER TABLE locations ADD COLUMN published BOOLEAN DEFAULT TRUE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'google_maps_url') THEN
        ALTER TABLE locations ADD COLUMN google_maps_url TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'apple_maps_url') THEN
        ALTER TABLE locations ADD COLUMN apple_maps_url TEXT;
    END IF;
END $$;

-- 2. Add constraints if they don't exist
DO $$
BEGIN
    -- Add difficulty level constraint if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'locations_difficulty_level_check') THEN
        ALTER TABLE locations ADD CONSTRAINT locations_difficulty_level_check 
        CHECK (difficulty_level IN ('easy', 'moderate', 'difficult', 'expert'));
    END IF;
    
    -- Add access type constraint if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints WHERE constraint_name = 'locations_access_type_check') THEN
        ALTER TABLE locations ADD CONSTRAINT locations_access_type_check 
        CHECK (access_type IN ('public', 'private', 'permit_required', 'restricted'));
    END IF;
END $$;

-- 3. Create other tables if they don't exist
CREATE TABLE IF NOT EXISTS observations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id UUID REFERENCES species_v2(id) ON DELETE CASCADE,
  location_id UUID REFERENCES locations(id) ON DELETE SET NULL,
  observer_id TEXT,
  source TEXT NOT NULL CHECK (source IN ('ebird', 'inaturalist', 'manual')),
  external_id TEXT,
  observation_date DATE NOT NULL,
  observation_time TIME,
  count INTEGER DEFAULT 1,
  breeding_code TEXT,
  behavior TEXT,
  notes TEXT,
  confidence_level TEXT,
  photo_url TEXT,
  audio_url TEXT,
  weather_conditions TEXT,
  temperature_c INTEGER,
  wind_speed_kmh INTEGER,
  visibility_km DECIMAL(4,2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(external_id, source)
);

CREATE TABLE IF NOT EXISTS species_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id UUID REFERENCES species_v2(id) ON DELETE CASCADE,
  location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
  abundance TEXT CHECK (abundance IN ('rare', 'uncommon', 'common', 'abundant', 'very_common')),
  seasonal_presence TEXT[],
  breeding_status TEXT CHECK (breeding_status IN ('non_breeding', 'possible', 'probable', 'confirmed')),
  best_months INTEGER[],
  notes TEXT,
  confidence_level TEXT CHECK (confidence_level IN ('low', 'medium', 'high', 'confirmed')),
  last_observed DATE,
  observation_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(species_id, location_id)
);

CREATE TABLE IF NOT EXISTS location_photos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
  photo_url TEXT NOT NULL,
  title TEXT,
  description TEXT,
  photographer TEXT,
  taken_date DATE,
  is_primary BOOLEAN DEFAULT FALSE,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS checklists (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ebird_checklist_id TEXT UNIQUE NOT NULL,
  location_id UUID REFERENCES locations(id) ON DELETE SET NULL,
  observer_id TEXT NOT NULL,
  checklist_date DATE NOT NULL,
  start_time TIME,
  duration_minutes INTEGER,
  distance_km DECIMAL(5,2),
  effort_type TEXT,
  protocol TEXT,
  number_observers INTEGER DEFAULT 1,
  all_species_reported BOOLEAN DEFAULT FALSE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS species_occurrence (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id UUID REFERENCES species_v2(id) ON DELETE CASCADE,
  location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
  month INTEGER CHECK (month >= 1 AND month <= 12),
  frequency DECIMAL(5,4),
  abundance_category TEXT,
  first_observed DATE,
  last_observed DATE,
  total_observations INTEGER DEFAULT 0,
  breeding_evidence BOOLEAN DEFAULT FALSE,
  migration_status TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(species_id, location_id, month)
);

-- 4. Add indexes
CREATE INDEX IF NOT EXISTS idx_observations_species_id ON observations(species_id);
CREATE INDEX IF NOT EXISTS idx_observations_location_id ON observations(location_id);
CREATE INDEX IF NOT EXISTS idx_observations_date ON observations(observation_date);
CREATE INDEX IF NOT EXISTS idx_observations_source ON observations(source);
CREATE INDEX IF NOT EXISTS idx_observations_external_id ON observations(external_id);

CREATE INDEX IF NOT EXISTS idx_locations_coords ON locations(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_locations_ebird_id ON locations(ebird_location_id);
CREATE INDEX IF NOT EXISTS idx_locations_inat_id ON locations(inat_place_id);
CREATE INDEX IF NOT EXISTS idx_locations_featured ON locations(featured);
CREATE INDEX IF NOT EXISTS idx_locations_published ON locations(published);
CREATE INDEX IF NOT EXISTS idx_locations_access_type ON locations(access_type);
CREATE INDEX IF NOT EXISTS idx_locations_difficulty ON locations(difficulty_level);
CREATE INDEX IF NOT EXISTS idx_locations_habitat_types ON locations USING GIN(habitat_types);

CREATE INDEX IF NOT EXISTS idx_species_locations_species ON species_locations(species_id);
CREATE INDEX IF NOT EXISTS idx_species_locations_location ON species_locations(location_id);
CREATE INDEX IF NOT EXISTS idx_species_locations_abundance ON species_locations(abundance);
CREATE INDEX IF NOT EXISTS idx_species_locations_seasonal ON species_locations USING GIN(seasonal_presence);
CREATE INDEX IF NOT EXISTS idx_species_locations_months ON species_locations USING GIN(best_months);

CREATE INDEX IF NOT EXISTS idx_location_photos_location ON location_photos(location_id);
CREATE INDEX IF NOT EXISTS idx_location_photos_primary ON location_photos(is_primary);
CREATE INDEX IF NOT EXISTS idx_location_photos_sort ON location_photos(sort_order);

CREATE INDEX IF NOT EXISTS idx_checklists_date ON checklists(checklist_date);
CREATE INDEX IF NOT EXISTS idx_checklists_location ON checklists(location_id);
CREATE INDEX IF NOT EXISTS idx_checklists_observer ON checklists(observer_id);

CREATE INDEX IF NOT EXISTS idx_species_occurrence_species ON species_occurrence(species_id);
CREATE INDEX IF NOT EXISTS idx_species_occurrence_location ON species_occurrence(location_id);
CREATE INDEX IF NOT EXISTS idx_species_occurrence_month ON species_occurrence(month);

-- 5. Enable RLS
ALTER TABLE locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE observations ENABLE ROW LEVEL SECURITY;
ALTER TABLE checklists ENABLE ROW LEVEL SECURITY;
ALTER TABLE species_occurrence ENABLE ROW LEVEL SECURITY;
ALTER TABLE species_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE location_photos ENABLE ROW LEVEL SECURITY;

-- 6. Drop and recreate policies to avoid conflicts
DROP POLICY IF EXISTS "Public read access for locations" ON locations;
DROP POLICY IF EXISTS "Public read access for species occurrence" ON species_occurrence;
DROP POLICY IF EXISTS "Public read access for species_locations" ON species_locations;
DROP POLICY IF EXISTS "Public read access for location_photos" ON location_photos;
DROP POLICY IF EXISTS "Admin full access for locations" ON locations;
DROP POLICY IF EXISTS "Admin full access for observations" ON observations;
DROP POLICY IF EXISTS "Admin full access for checklists" ON checklists;
DROP POLICY IF EXISTS "Admin full access for species occurrence" ON species_occurrence;
DROP POLICY IF EXISTS "Admin full access for species_locations" ON species_locations;
DROP POLICY IF EXISTS "Admin full access for location_photos" ON location_photos;

-- Create new policies
CREATE POLICY "Public read access for locations" ON locations FOR SELECT USING (published = true);
CREATE POLICY "Public read access for species occurrence" ON species_occurrence FOR SELECT USING (
  EXISTS (SELECT 1 FROM locations l WHERE l.id = location_id AND l.published = true)
);
CREATE POLICY "Public read access for species_locations" ON species_locations FOR SELECT USING (
  EXISTS (SELECT 1 FROM locations l WHERE l.id = location_id AND l.published = true)
);
CREATE POLICY "Public read access for location_photos" ON location_photos FOR SELECT USING (
  EXISTS (SELECT 1 FROM locations l WHERE l.id = location_id AND l.published = true)
);

CREATE POLICY "Admin full access for locations" ON locations FOR ALL USING (auth.email() = '<EMAIL>');
CREATE POLICY "Admin full access for observations" ON observations FOR ALL USING (auth.email() = '<EMAIL>');
CREATE POLICY "Admin full access for checklists" ON checklists FOR ALL USING (auth.email() = '<EMAIL>');
CREATE POLICY "Admin full access for species occurrence" ON species_occurrence FOR ALL USING (auth.email() = '<EMAIL>');
CREATE POLICY "Admin full access for species_locations" ON species_locations FOR ALL USING (auth.email() = '<EMAIL>');
CREATE POLICY "Admin full access for location_photos" ON location_photos FOR ALL USING (auth.email() = '<EMAIL>');

-- 7. Create views
CREATE OR REPLACE VIEW location_species_summary AS
SELECT
  l.id as location_id,
  l.name as location_name,
  l.latitude,
  l.longitude,
  l.state_province,
  l.country,
  l.habitat_types,
  l.featured,
  l.published,
  COUNT(sl.species_id) as total_species,
  COUNT(CASE WHEN sl.abundance IN ('common', 'abundant', 'very_common') THEN 1 END) as common_species,
  COUNT(CASE WHEN sl.abundance = 'rare' THEN 1 END) as rare_species,
  COUNT(CASE WHEN sl.breeding_status = 'confirmed' THEN 1 END) as breeding_species,
  ARRAY_AGG(DISTINCT s.category) FILTER (WHERE s.category IS NOT NULL) as categories_present,
  MAX(sl.last_observed) as last_observation_date,
  SUM(sl.observation_count) as total_observations
FROM locations l
LEFT JOIN species_locations sl ON l.id = sl.location_id
LEFT JOIN species_v2 s ON sl.species_id = s.id
WHERE l.published = true
GROUP BY l.id, l.name, l.latitude, l.longitude, l.state_province, l.country, l.habitat_types, l.featured, l.published;

CREATE OR REPLACE VIEW species_location_details AS
SELECT
  s.id as species_id,
  s.name as species_name,
  s.common_name,
  s.scientific_name,
  s.category,
  s.conservation_status,
  l.id as location_id,
  l.name as location_name,
  l.state_province,
  l.country,
  l.latitude,
  l.longitude,
  sl.abundance,
  sl.seasonal_presence,
  sl.breeding_status,
  sl.best_months,
  sl.notes,
  sl.confidence_level,
  sl.last_observed,
  sl.observation_count,
  COUNT(p.id) as photo_count
FROM species_v2 s
JOIN species_locations sl ON s.id = sl.species_id
JOIN locations l ON sl.location_id = l.id
LEFT JOIN photos_v2 p ON s.id = p.species_id
WHERE s.published = true AND l.published = true
GROUP BY s.id, s.name, s.common_name, s.scientific_name, s.category, s.conservation_status,
         l.id, l.name, l.state_province, l.country, l.latitude, l.longitude,
         sl.abundance, sl.seasonal_presence, sl.breeding_status, sl.best_months,
         sl.notes, sl.confidence_level, sl.last_observed, sl.observation_count;

CREATE OR REPLACE VIEW hotspot_highlights AS
SELECT
  l.*,
  lss.total_species,
  lss.common_species,
  lss.rare_species,
  lss.breeding_species,
  lss.categories_present,
  lss.total_observations,
  lp.photo_url as primary_photo,
  lp.title as primary_photo_title
FROM locations l
LEFT JOIN location_species_summary lss ON l.id = lss.location_id
LEFT JOIN location_photos lp ON l.id = lp.location_id AND lp.is_primary = true
WHERE l.published = true
ORDER BY l.featured DESC, lss.total_species DESC NULLS LAST;

-- 8. Insert sample data
INSERT INTO locations (
  name, latitude, longitude, country, state_province,
  description, directions, facilities, best_time_to_visit,
  difficulty_level, access_type, habitat_types, published, featured
) VALUES
(
  'Central Park - The Ramble',
  40.7794, -73.9632,
  'United States', 'New York',
  'A 36-acre woodland area in Central Park known for excellent bird watching, especially during migration seasons.',
  'Enter Central Park at 79th Street and Central Park West. Walk east to The Ramble area near the Lake.',
  ARRAY['restrooms', 'benches', 'trails', 'visitor_center'],
  'Early morning (6-10 AM) during spring and fall migration (April-May, September-October)',
  'easy',
  'public',
  ARRAY['deciduous_forest', 'wetland', 'urban_park'],
  true,
  true
) ON CONFLICT DO NOTHING;
