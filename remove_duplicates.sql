-- Remove duplicate photos from photos_v2 table
-- This script will keep the oldest record (lowest ID) for each duplicate group

-- Step 1: First, let's see what we're working with
SELECT
    url,
    title,
    COUNT(*) as duplicate_count,
    array_agg(id ORDER BY created_at) as photo_ids,
    (array_agg(id ORDER BY created_at))[1] as keep_id,
    (array_agg(id ORDER BY created_at))[2:] as delete_ids
FROM photos_v2
WHERE url IS NOT NULL AND title IS NOT NULL
GROUP BY url, title
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC
LIMIT 10;

-- Step 2: Create a temporary table with IDs to delete
CREATE TEMP TABLE duplicate_ids AS
WITH duplicates AS (
    SELECT
        id,
        ROW_NUMBER() OVER (PARTITION BY url, title ORDER BY created_at) as rn
    FROM photos_v2
    WHERE url IS NOT NULL AND title IS NOT NULL
)
SELECT id
FROM duplicates
WHERE rn > 1;

-- Step 3: Show how many records will be deleted
SELECT COUNT(*) as records_to_delete FROM duplicate_ids;

-- Step 4: Delete the duplicate records (UNCOMMENT WHEN READY)
-- DELETE FROM photos_v2 WHERE id IN (SELECT id FROM duplicate_ids);

-- Step 5: Verify the cleanup (run after deletion)
-- SELECT COUNT(*) as remaining_photos FROM photos_v2;

-- Step 6: Check for any remaining duplicates (should return 0 rows)
-- SELECT 
--     url,
--     title,
--     COUNT(*) as duplicate_count
-- FROM photos_v2 
-- WHERE url IS NOT NULL AND title IS NOT NULL
-- GROUP BY url, title 
-- HAVING COUNT(*) > 1;
