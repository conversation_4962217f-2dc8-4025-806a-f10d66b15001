-- =====================================================
-- AI DASHBOARD VIEWS FIX
-- =====================================================
-- This fixes the missing database views that the AI Dashboard depends on
-- Run this in Supabase SQL Editor after the Fun Facts migration

-- Drop existing views if they exist
DROP VIEW IF EXISTS ai_assignment_stats CASCADE;
DROP VIEW IF EXISTS recent_ai_overrides CASCADE;
DROP VIEW IF EXISTS review_queue_summary CASCADE;

-- 1. AI Assignment Stats - Statistical overview using photos_v2
CREATE VIEW ai_assignment_stats AS
SELECT
  COUNT(*) as total_photos,
  0 as ai_tagged,  -- Placeholder for future AI fields
  0 as flagged_for_review,  -- Placeholder for future AI fields
  0.0 as avg_confidence,  -- Placeholder for future AI fields
  SUM(CASE WHEN published THEN 1 ELSE 0 END) as published_photos,
  SUM(CASE WHEN NOT published THEN 1 ELSE 0 END) as unpublished_photos
FROM photos_v2;

-- 2. Recent AI Overrides - Recent manual overrides with details
CREATE VIEW recent_ai_overrides AS
SELECT 
    l.id,
    l.table_name,
    l.record_id,
    l.field_name,
    l.old_value,
    l.new_value,
    l.override_type,
    l.override_reason,
    l.created_at,
    CASE
        WHEN l.table_name IN ('photos', 'photos_v2') THEN p.title
        WHEN l.table_name IN ('species', 'species_v2') THEN s.name
        ELSE 'Unknown'
    END AS record_title,
    CASE
        WHEN l.table_name IN ('photos', 'photos_v2') THEN p.url
        ELSE NULL
    END AS photo_url
FROM ai_override_log l
LEFT JOIN photos_v2 p ON l.table_name IN ('photos', 'photos_v2') AND l.record_id = p.id::text
LEFT JOIN species_v2 s ON l.table_name IN ('species', 'species_v2') AND l.record_id = s.id::text
ORDER BY l.created_at DESC
LIMIT 100;

-- 3. Review Queue Summary - Summary of items needing review using v2 tables
CREATE VIEW review_queue_summary AS
SELECT
  COUNT(*) as total_items_needing_review,
  SUM(CASE WHEN content_type = 'photo' THEN 1 ELSE 0 END) as photos_needing_review,
  SUM(CASE WHEN content_type = 'species' THEN 1 ELSE 0 END) as species_needing_review,
  0.0 as avg_confidence,  -- Placeholder for future AI fields
  MIN(created_at) as oldest_item_date,
  MAX(created_at) as newest_item_date
FROM (
  SELECT 
    'photo' as content_type,
    p.created_at
  FROM photos_v2 p
  WHERE p.published = false  -- Unpublished photos need review
  
  UNION ALL
  
  SELECT 
    'species' as content_type,
    s.created_at
  FROM species_v2 s
  WHERE s.published = false  -- Unpublished species need review
) review_items;

-- Add comments for documentation
COMMENT ON VIEW ai_assignment_stats IS 'Statistical overview of photo processing using photos_v2 table';
COMMENT ON VIEW recent_ai_overrides IS 'Recent manual overrides with details, supporting both v1 and v2 table references';
COMMENT ON VIEW review_queue_summary IS 'Summary of items needing review using v2 tables';

-- Grant permissions for authenticated users
GRANT SELECT ON ai_assignment_stats TO authenticated;
GRANT SELECT ON recent_ai_overrides TO authenticated;
GRANT SELECT ON review_queue_summary TO authenticated;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================
-- Run these after creating the views to verify they work:

-- Test ai_assignment_stats
-- SELECT * FROM ai_assignment_stats;

-- Test recent_ai_overrides  
-- SELECT * FROM recent_ai_overrides LIMIT 5;

-- Test review_queue_summary
-- SELECT * FROM review_queue_summary;

-- =====================================================
-- INSTRUCTIONS:
-- =====================================================
-- 1. Run the Fun Facts migration first
-- 2. Then copy and paste this entire file into Supabase SQL Editor
-- 3. Click "Run" to create the missing views
-- 4. Refresh your AI Dashboard page - errors should be gone!
-- =====================================================
