-- CLEAN HOTSPOTS MIGRATION - Starting Fresh
-- Since no tables exist, we can create everything cleanly

-- 1. Create locations table with all columns
CREATE TABLE locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  latitude DECIMAL(10, 8) NOT NULL,
  longitude DECIMAL(11, 8) NOT NULL,
  country TEXT,
  state_province TEXT,
  county TEXT,
  locality TEXT,
  ebird_location_id TEXT,
  inat_place_id INTEGER,
  elevation_m INTEGER,
  habitat_type TEXT,
  description TEXT,
  directions TEXT,
  facilities TEXT[],
  best_time_to_visit TEXT,
  difficulty_level TEXT CHECK (difficulty_level IN ('easy', 'moderate', 'difficult', 'expert')),
  access_type TEXT CHECK (access_type IN ('public', 'private', 'permit_required', 'restricted')),
  parking_info TEXT,
  entrance_fee DECIMAL(8,2),
  website_url TEXT,
  contact_info TEXT,
  featured BOOLEAN DEFAULT FALSE,
  photo_url TEXT,
  habitat_types TEXT[],
  target_species TEXT[],
  seasonal_highlights JSONB,
  visitor_tips TEXT,
  published BOOLEAN DEFAULT TRUE,
  google_maps_url TEXT,
  apple_maps_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(latitude, longitude, name)
);

-- 2. Create species_locations junction table
CREATE TABLE species_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id UUID REFERENCES species_v2(id) ON DELETE CASCADE,
  location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
  abundance TEXT CHECK (abundance IN ('rare', 'uncommon', 'common', 'abundant', 'very_common')),
  seasonal_presence TEXT[],
  breeding_status TEXT CHECK (breeding_status IN ('non_breeding', 'possible', 'probable', 'confirmed')),
  best_months INTEGER[],
  notes TEXT,
  confidence_level TEXT CHECK (confidence_level IN ('low', 'medium', 'high', 'confirmed')),
  last_observed DATE,
  observation_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(species_id, location_id)
);

-- 3. Create location_photos table
CREATE TABLE location_photos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
  photo_url TEXT NOT NULL,
  title TEXT,
  description TEXT,
  photographer TEXT,
  taken_date DATE,
  is_primary BOOLEAN DEFAULT FALSE,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create observations table
CREATE TABLE observations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id UUID REFERENCES species_v2(id) ON DELETE CASCADE,
  location_id UUID REFERENCES locations(id) ON DELETE SET NULL,
  observer_id TEXT,
  source TEXT NOT NULL CHECK (source IN ('ebird', 'inaturalist', 'manual')),
  external_id TEXT,
  observation_date DATE NOT NULL,
  observation_time TIME,
  count INTEGER DEFAULT 1,
  breeding_code TEXT,
  behavior TEXT,
  notes TEXT,
  confidence_level TEXT,
  photo_url TEXT,
  audio_url TEXT,
  weather_conditions TEXT,
  temperature_c INTEGER,
  wind_speed_kmh INTEGER,
  visibility_km DECIMAL(4,2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(external_id, source)
);

-- 5. Create checklists table
CREATE TABLE checklists (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ebird_checklist_id TEXT UNIQUE NOT NULL,
  location_id UUID REFERENCES locations(id) ON DELETE SET NULL,
  observer_id TEXT NOT NULL,
  checklist_date DATE NOT NULL,
  start_time TIME,
  duration_minutes INTEGER,
  distance_km DECIMAL(5,2),
  effort_type TEXT,
  protocol TEXT,
  number_observers INTEGER DEFAULT 1,
  all_species_reported BOOLEAN DEFAULT FALSE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Create species_occurrence table
CREATE TABLE species_occurrence (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id UUID REFERENCES species_v2(id) ON DELETE CASCADE,
  location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
  month INTEGER CHECK (month >= 1 AND month <= 12),
  frequency DECIMAL(5,4),
  abundance_category TEXT,
  first_observed DATE,
  last_observed DATE,
  total_observations INTEGER DEFAULT 0,
  breeding_evidence BOOLEAN DEFAULT FALSE,
  migration_status TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(species_id, location_id, month)
);

-- 7. Create indexes for performance
CREATE INDEX idx_observations_species_id ON observations(species_id);
CREATE INDEX idx_observations_location_id ON observations(location_id);
CREATE INDEX idx_observations_date ON observations(observation_date);
CREATE INDEX idx_observations_source ON observations(source);
CREATE INDEX idx_observations_external_id ON observations(external_id);

CREATE INDEX idx_locations_coords ON locations(latitude, longitude);
CREATE INDEX idx_locations_ebird_id ON locations(ebird_location_id);
CREATE INDEX idx_locations_inat_id ON locations(inat_place_id);
CREATE INDEX idx_locations_featured ON locations(featured);
CREATE INDEX idx_locations_published ON locations(published);
CREATE INDEX idx_locations_access_type ON locations(access_type);
CREATE INDEX idx_locations_difficulty ON locations(difficulty_level);
CREATE INDEX idx_locations_habitat_types ON locations USING GIN(habitat_types);

CREATE INDEX idx_species_locations_species ON species_locations(species_id);
CREATE INDEX idx_species_locations_location ON species_locations(location_id);
CREATE INDEX idx_species_locations_abundance ON species_locations(abundance);
CREATE INDEX idx_species_locations_seasonal ON species_locations USING GIN(seasonal_presence);
CREATE INDEX idx_species_locations_months ON species_locations USING GIN(best_months);

CREATE INDEX idx_location_photos_location ON location_photos(location_id);
CREATE INDEX idx_location_photos_primary ON location_photos(is_primary);
CREATE INDEX idx_location_photos_sort ON location_photos(sort_order);

CREATE INDEX idx_checklists_date ON checklists(checklist_date);
CREATE INDEX idx_checklists_location ON checklists(location_id);
CREATE INDEX idx_checklists_observer ON checklists(observer_id);

CREATE INDEX idx_species_occurrence_species ON species_occurrence(species_id);
CREATE INDEX idx_species_occurrence_location ON species_occurrence(location_id);
CREATE INDEX idx_species_occurrence_month ON species_occurrence(month);

-- 8. Enable Row Level Security
ALTER TABLE locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE observations ENABLE ROW LEVEL SECURITY;
ALTER TABLE checklists ENABLE ROW LEVEL SECURITY;
ALTER TABLE species_occurrence ENABLE ROW LEVEL SECURITY;
ALTER TABLE species_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE location_photos ENABLE ROW LEVEL SECURITY;

-- 9. Create RLS policies
CREATE POLICY "Public read access for locations" ON locations FOR SELECT USING (published = true);
CREATE POLICY "Public read access for species occurrence" ON species_occurrence FOR SELECT USING (
  EXISTS (SELECT 1 FROM locations l WHERE l.id = location_id AND l.published = true)
);
CREATE POLICY "Public read access for species_locations" ON species_locations FOR SELECT USING (
  EXISTS (SELECT 1 FROM locations l WHERE l.id = location_id AND l.published = true)
);
CREATE POLICY "Public read access for location_photos" ON location_photos FOR SELECT USING (
  EXISTS (SELECT 1 FROM locations l WHERE l.id = location_id AND l.published = true)
);

CREATE POLICY "Admin full access for locations" ON locations FOR ALL USING (auth.email() = '<EMAIL>');
CREATE POLICY "Admin full access for observations" ON observations FOR ALL USING (auth.email() = '<EMAIL>');
CREATE POLICY "Admin full access for checklists" ON checklists FOR ALL USING (auth.email() = '<EMAIL>');
CREATE POLICY "Admin full access for species occurrence" ON species_occurrence FOR ALL USING (auth.email() = '<EMAIL>');
CREATE POLICY "Admin full access for species_locations" ON species_locations FOR ALL USING (auth.email() = '<EMAIL>');
CREATE POLICY "Admin full access for location_photos" ON location_photos FOR ALL USING (auth.email() = '<EMAIL>');

-- 10. Create helpful views
CREATE VIEW location_species_summary AS
SELECT
  l.id as location_id,
  l.name as location_name,
  l.latitude,
  l.longitude,
  l.state_province,
  l.country,
  l.habitat_types,
  l.featured,
  l.published,
  COUNT(sl.species_id) as total_species,
  COUNT(CASE WHEN sl.abundance IN ('common', 'abundant', 'very_common') THEN 1 END) as common_species,
  COUNT(CASE WHEN sl.abundance = 'rare' THEN 1 END) as rare_species,
  COUNT(CASE WHEN sl.breeding_status = 'confirmed' THEN 1 END) as breeding_species,
  ARRAY_AGG(DISTINCT s.category) FILTER (WHERE s.category IS NOT NULL) as categories_present,
  MAX(sl.last_observed) as last_observation_date,
  SUM(sl.observation_count) as total_observations
FROM locations l
LEFT JOIN species_locations sl ON l.id = sl.location_id
LEFT JOIN species_v2 s ON sl.species_id = s.id
WHERE l.published = true
GROUP BY l.id, l.name, l.latitude, l.longitude, l.state_province, l.country, l.habitat_types, l.featured, l.published;

CREATE VIEW species_location_details AS
SELECT
  s.id as species_id,
  s.name as species_name,
  s.common_name,
  s.scientific_name,
  s.category,
  s.conservation_status,
  l.id as location_id,
  l.name as location_name,
  l.state_province,
  l.country,
  l.latitude,
  l.longitude,
  sl.abundance,
  sl.seasonal_presence,
  sl.breeding_status,
  sl.best_months,
  sl.notes,
  sl.confidence_level,
  sl.last_observed,
  sl.observation_count,
  COUNT(p.id) as photo_count
FROM species_v2 s
JOIN species_locations sl ON s.id = sl.species_id
JOIN locations l ON sl.location_id = l.id
LEFT JOIN photos_v2 p ON s.id = p.species_id
WHERE s.published = true AND l.published = true
GROUP BY s.id, s.name, s.common_name, s.scientific_name, s.category, s.conservation_status,
         l.id, l.name, l.state_province, l.country, l.latitude, l.longitude,
         sl.abundance, sl.seasonal_presence, sl.breeding_status, sl.best_months,
         sl.notes, sl.confidence_level, sl.last_observed, sl.observation_count;

CREATE VIEW hotspot_highlights AS
SELECT
  l.*,
  lss.total_species,
  lss.common_species,
  lss.rare_species,
  lss.breeding_species,
  lss.categories_present,
  lss.total_observations,
  lp.photo_url as primary_photo,
  lp.title as primary_photo_title
FROM locations l
LEFT JOIN location_species_summary lss ON l.id = lss.location_id
LEFT JOIN location_photos lp ON l.id = lp.location_id AND lp.is_primary = true
WHERE l.published = true
ORDER BY l.featured DESC, lss.total_species DESC NULLS LAST;

-- 11. Insert sample data for testing
INSERT INTO locations (
  name, latitude, longitude, country, state_province,
  description, directions, facilities, best_time_to_visit,
  difficulty_level, access_type, habitat_types, published, featured
) VALUES
(
  'Central Park - The Ramble',
  40.7794, -73.9632,
  'United States', 'New York',
  'A 36-acre woodland area in Central Park known for excellent bird watching, especially during migration seasons.',
  'Enter Central Park at 79th Street and Central Park West. Walk east to The Ramble area near the Lake.',
  ARRAY['restrooms', 'benches', 'trails', 'visitor_center'],
  'Early morning (6-10 AM) during spring and fall migration (April-May, September-October)',
  'easy',
  'public',
  ARRAY['deciduous_forest', 'wetland', 'urban_park'],
  true,
  true
),
(
  'Point Pelee National Park',
  42.2044, -82.5110,
  'Canada', 'Ontario',
  'Canadas southernmost point, famous for bird migration and diverse habitats including marshes, forests, and beaches.',
  'Located at 407 Monarch Lane, Leamington, ON. Follow Highway 3 south to Point Pelee Drive.',
  ARRAY['restrooms', 'parking', 'trails', 'visitor_center', 'benches'],
  'Peak migration: May and September. Summer for breeding birds. Winter for waterfowl.',
  'easy',
  'public',
  ARRAY['deciduous_forest', 'wetland', 'coastal', 'grassland'],
  true,
  true
);

-- 12. Add table comments for documentation
COMMENT ON TABLE locations IS 'Wildlife viewing locations and hotspots with detailed metadata';
COMMENT ON TABLE observations IS 'Individual species observations from various sources';
COMMENT ON TABLE checklists IS 'eBird checklist metadata for tracking observation effort';
COMMENT ON TABLE species_occurrence IS 'Derived patterns showing species occurrence by location and month';
COMMENT ON TABLE species_locations IS 'Many-to-many relationship between species and locations with abundance data';
COMMENT ON TABLE location_photos IS 'Photo gallery for each location/hotspot';
COMMENT ON VIEW location_species_summary IS 'Summary statistics for each location';
COMMENT ON VIEW species_location_details IS 'Detailed species-location relationships';
COMMENT ON VIEW hotspot_highlights IS 'Featured locations with statistics for public display';
