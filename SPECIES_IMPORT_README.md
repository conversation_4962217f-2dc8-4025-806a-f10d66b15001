# 🚀 Species Import Tool

A comprehensive AI-powered tool for importing and enriching species metadata into the Fauna Focus database.

## Overview

The Species Import Tool allows administrators to:
- Upload species names via text input or file upload (CSV/JSON)
- Auto-populate missing metadata using AI (OpenAI GPT-3.5-turbo)
- Review and edit generated data before import
- Insert enriched species into Supabase with proper tracking
- Export data to CSV for backup or review

## Features

### 📝 Input Methods
- **Text Input**: Paste species names (one per line)
- **File Upload**: Support for CSV and JSON formats
  - CSV: First column should contain species names
  - JSON: Array of strings or objects with 'name' field

### 🤖 AI Metadata Generation
- Uses OpenAI GPT-3.5-turbo for intelligent metadata generation
- Falls back to dummy data if OpenAI API key is not available
- Generates all required fields:
  - Scientific name
  - Description
  - Habitat
  - Diet
  - Conservation status
  - Category
  - Related locations

### ✏️ Review & Edit Interface
- Editable table view with inline editing
- Detailed edit panel for comprehensive field editing
- Visual indicators for AI-generated vs manually edited content
- Bulk publish/unpublish functionality
- Individual row deletion

### 🗄️ Database Integration
- Direct insertion into Supabase species table
- Proper field mapping and validation
- Sets `ai_generated = true` for AI-created records
- Sets `published = false` by default (user configurable)
- Auto-handles `id`, `created_at`, and `updated_at` fields

### 🔧 Advanced Features
- **Dry Run Mode**: Test imports without writing to database
- **CSV Export**: Download enriched data for review
- **Bulk Operations**: Publish/unpublish multiple species at once
- **Status Tracking**: Visual indicators for AI-generated content
- **Error Handling**: Comprehensive error messages and fallbacks

## Usage

### 1. Access the Tool
Navigate to `/species-import` in the application.

### 2. Input Species Names
Choose between:
- **Text Input**: Paste species names directly
- **File Upload**: Upload CSV or JSON file

### 3. Generate Metadata
Click "Fetch Metadata with AI" to populate missing fields.

### 4. Review & Edit
- Review generated data in the table
- Click edit icons to modify individual fields
- Use bulk publish toggle for mass operations
- Delete unwanted entries

### 5. Import to Database
- Enable/disable dry run mode as needed
- Click "Insert to Supabase" to save to database
- Review success/error messages

## File Format Examples

### CSV Format
```csv
Species Name
African Elephant
Bengal Tiger
Bald Eagle
Green Sea Turtle
Giant Panda
```

### JSON Format
```json
[
  "African Elephant",
  "Bengal Tiger",
  "Bald Eagle",
  "Green Sea Turtle",
  "Giant Panda"
]
```

Or with objects:
```json
[
  {"name": "African Elephant"},
  {"name": "Bengal Tiger"},
  {"name": "Bald Eagle"}
]
```

## Database Schema

The tool inserts data into the `species` table with the following fields:

| Field | Type | Description | Auto-handled |
|-------|------|-------------|--------------|
| `id` | string | Unique identifier | ✅ |
| `name` | string | Common name | ❌ |
| `scientific_name` | string | Scientific name | ❌ |
| `description` | string | Species description | ❌ |
| `habitat` | string | Natural habitat | ❌ |
| `diet` | string | Dietary information | ❌ |
| `conservation_status` | string | IUCN status | ❌ |
| `category` | string | Animal kingdom category | ❌ |
| `related_locations` | string[] | Geographic locations | ❌ |
| `ai_generated` | boolean | AI creation flag | ❌ |
| `published` | boolean | Publication status | ❌ |
| `created_at` | string | Creation timestamp | ✅ |
| `updated_at` | string | Update timestamp | ✅ |

## Configuration

### OpenAI Integration
To enable AI metadata generation, set the environment variable:
```bash
VITE_OPENAI_API_KEY=your_openai_api_key_here
```

Without this key, the tool will use predefined dummy data for common species.

### Fallback Data
The tool includes dummy data for popular species:
- African Elephant
- Bengal Tiger
- Bald Eagle
- Green Sea Turtle
- Giant Panda

For unknown species, it generates random but realistic metadata.

## Error Handling

The tool handles various error scenarios:
- **File parsing errors**: Clear error messages for invalid formats
- **AI API errors**: Graceful fallback to dummy data
- **Database errors**: Detailed error reporting
- **Network issues**: Retry mechanisms and user feedback

## Best Practices

1. **Start Small**: Test with a few species before bulk imports
2. **Use Dry Run**: Always test with dry run mode first
3. **Review Data**: Check AI-generated content for accuracy
4. **Backup**: Export CSV before large imports
5. **Validate**: Ensure scientific names and conservation statuses are correct

## Troubleshooting

### Common Issues

**"OpenAI API key not found"**
- Set `VITE_OPENAI_API_KEY` environment variable
- Tool will use dummy data as fallback

**"Failed to parse file"**
- Check file format (CSV or JSON)
- Ensure proper structure
- Remove any special characters

**"Database error"**
- Check Supabase connection
- Verify table permissions
- Review field constraints

**"No species to import"**
- Add species names via text or file
- Ensure names are not empty
- Check file encoding

## Development

### File Structure
```
src/
├── pages/
│   └── SpeciesImport.tsx          # Main page component
├── components/
│   └── SpeciesImportTable.tsx     # Editable table component
└── utils/
    └── aiSpeciesGenerator.ts      # AI and database utilities
```

### Key Components

- **SpeciesImport**: Main page with input methods and workflow
- **SpeciesImportTable**: Editable table for review and editing
- **aiSpeciesGenerator**: AI integration and database operations

### Adding New Features

1. **New Input Formats**: Extend parsing functions in `aiSpeciesGenerator.ts`
2. **Additional AI Fields**: Modify OpenAI prompt and data structures
3. **Custom Validation**: Add validation logic to insert functions
4. **Export Formats**: Extend export functionality beyond CSV

## Support

For issues or questions:
1. Check the browser console for detailed error messages
2. Review the network tab for API call failures
3. Verify environment variables are set correctly
4. Test with dry run mode to isolate issues

---

**Note**: This tool is designed for administrative use and should be used with appropriate permissions and data validation procedures. 