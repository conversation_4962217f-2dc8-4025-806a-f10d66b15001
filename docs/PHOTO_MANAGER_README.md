# PhotoManager Component Documentation

## Overview

The `PhotoManager` component is a unified, mode-driven component that consolidates the functionality of three previously separate components:
- `PhotoAssignment` (photo assignment operations)
- `PhotoRematch` (photo rematching operations)  
- `PhotoGalleryManager` (photo gallery management)

This consolidation eliminates ~2000+ lines of duplicated code while maintaining full backward compatibility through deprecated wrapper components.

## Features

### Mode-Driven Functionality
The component operates in three distinct modes, each optimized for specific use cases:

#### 🎯 **Assignment Mode** (`mode="assign"`)
- Drag-and-drop photo assignment to species
- AI-powered species suggestions
- Real-time filtering and search
- Category-based species organization
- Assignment statistics and progress tracking

#### 🔄 **Rematch Mode** (`mode="rematch"`)
- Bulk photo-species rematching operations
- Comprehensive matching algorithms
- Progress tracking and result reporting
- Database consistency validation

#### 🖼️ **Gallery Mode** (`mode="gallery"`)
- Photo browsing and management
- Upload new photos
- Download and delete operations
- Grid and list view modes
- Advanced search and filtering

### Shared Capabilities
All modes include:
- **Search & Filtering**: Real-time search across titles, descriptions, and species
- **View Modes**: Toggle between grid and list views
- **Responsive Design**: Optimized for desktop and mobile
- **Error Handling**: Comprehensive error states and user feedback
- **Loading States**: Smooth loading indicators and progress tracking

## Usage

### Basic Usage

```tsx
import { PhotoManager } from '@/components/PhotoManager';

// Assignment mode
<PhotoManager mode="assign" title="Photo Assignment" />

// Rematch mode  
<PhotoManager mode="rematch" title="Photo Rematching" />

// Gallery mode
<PhotoManager mode="gallery" title="Photo Gallery" />
```

### Advanced Usage

```tsx
<PhotoManager
  mode="assign"
  title="Custom Photo Assignment"
  className="my-custom-styles"
  onPhotoUpdate={() => {
    // Handle photo updates
    console.log('Photo updated');
  }}
  onSpeciesUpdate={() => {
    // Handle species updates
    console.log('Species updated');
  }}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `mode` | `'assign' \| 'rematch' \| 'gallery'` | **Required** | Operating mode of the component |
| `title` | `string` | Auto-generated | Custom title for the component |
| `className` | `string` | `''` | Additional CSS classes |
| `onPhotoUpdate` | `() => void` | `undefined` | Callback when photos are updated |
| `onSpeciesUpdate` | `() => void` | `undefined` | Callback when species are updated |

## Mode-Specific Features

### Assignment Mode Features

#### Drag-and-Drop Assignment
```tsx
// Photos can be dragged to species cards
<DraggablePhoto photo={photo} species={species} />
<DropTargetSpecies species={species} />
```

#### AI Suggestions
- Real-time AI-powered species suggestions
- Confidence scoring and reasoning
- Visual highlighting of suggested species

#### Filtering Options
- **Assignment Status**: All, Assigned, Unassigned
- **Publication Status**: Published, Unpublished
- **Recovery Status**: Needs Recovery
- **Category Filter**: Filter species by category

### Rematch Mode Features

#### Rematch Operations
```tsx
// Start rematch process
<Button onClick={handleRematch}>
  Start Rematch
</Button>
```

#### Results Display
- Total photos processed
- Successfully matched count
- Unmatched count
- Database updates count

### Gallery Mode Features

#### Photo Management
```tsx
// Upload new photos
<Dialog>
  <PhotoUpload onUploadComplete={handleUpload} />
</Dialog>

// Download photo
<Button onClick={() => handleDownloadPhoto(photo)}>
  <Download className="w-4 h-4" />
</Button>

// Delete photo
<Button onClick={() => handleDeletePhoto(photo.id)}>
  <Trash2 className="w-4 h-4" />
</Button>
```

#### View Modes
- **Grid View**: Card-based layout with thumbnails
- **List View**: Detailed list with metadata

## State Management

### Shared State
```typescript
interface PhotoManagerState {
  photos: Photo[];
  species: Species[];
  loading: boolean;
  searchTerm: string;
  viewMode: 'grid' | 'list';
  isDragging: boolean;
  showUploadDialog: boolean;
  deletingIds: Set<number>;
}
```

### Mode-Specific State
```typescript
// Assignment mode
interface AssignmentState {
  assignmentFilter: FilterType;
  selectedCategory: string;
  aiSuggestions: AISuggestion[];
}

// Rematch mode
interface RematchState {
  rematchResult: RematchResult | null;
  isRematching: boolean;
}
```

## Data Flow

### Photo Loading
```typescript
const loadData = async () => {
  if (mode === 'assign') {
    const [photosData, speciesData] = await Promise.all([
      getPhotosWithFilter(assignmentFilter),
      getAllSpecies()
    ]);
    setPhotos(photosData);
    setSpecies(speciesData);
  }
  // ... other modes
};
```

### Event Handling
```typescript
// Photo updates
const handlePhotoUpdate = () => {
  loadData();
  onPhotoUpdate?.();
};

// Drag operations
const handleDragStart = async (photoId: number) => {
  if (mode === 'assign') {
    const suggestions = await getAISuggestions(photoId);
    setAiSuggestions(suggestions);
  }
};
```

## Styling

### CSS Classes
The component uses Tailwind CSS with consistent theming:

```css
/* Card styling */
.photo-card {
  @apply overflow-hidden hover:shadow-lg transition-shadow;
}

/* Loading states */
.loading-spinner {
  @apply animate-spin;
}

/* Mode-specific colors */
.assignment-mode {
  @apply bg-blue-50 border-blue-200;
}

.rematch-mode {
  @apply bg-orange-50 border-orange-200;
}

.gallery-mode {
  @apply bg-green-50 border-green-200;
}
```

### Responsive Design
```css
/* Grid layout */
.photo-grid {
  @apply grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4;
}

/* Sidebar layout */
.sidebar-layout {
  @apply grid grid-cols-1 lg:grid-cols-4 gap-6;
}
```

## Migration Guide

### From Old Components

#### Before (Deprecated)
```tsx
import { PhotoAssignment } from '@/components/PhotoAssignment';
import { PhotoRematch } from '@/components/PhotoRematch';
import { PhotoGalleryManager } from '@/components/PhotoGalleryManager';

// Usage
<PhotoAssignment />
<PhotoRematch />
<PhotoGalleryManager />
```

#### After (Recommended)
```tsx
import { PhotoManager } from '@/components/PhotoManager';

// Usage
<PhotoManager mode="assign" title="Photo Assignment" />
<PhotoManager mode="rematch" title="Photo Rematching" />
<PhotoManager mode="gallery" title="Photo Gallery" />
```

### Backward Compatibility
The old components are still available as deprecated wrappers:

```tsx
// These still work but show deprecation warnings
<PhotoAssignment /> // Wraps PhotoManager mode="assign"
<PhotoRematch />    // Wraps PhotoManager mode="rematch"
<PhotoGalleryManager /> // Wraps PhotoManager mode="gallery"
```

## Performance Considerations

### Optimization Strategies
1. **Lazy Loading**: Photos are loaded on-demand
2. **Debounced Search**: Search input is debounced to prevent excessive API calls
3. **Virtual Scrolling**: Large lists use virtual scrolling for performance
4. **Memoization**: Expensive operations are memoized

### Memory Management
```typescript
// Cleanup on unmount
useEffect(() => {
  return () => {
    setPhotos([]);
    setSpecies([]);
    setAiSuggestions([]);
  };
}, []);
```

## Error Handling

### Error States
```typescript
// Loading errors
if (error) {
  return (
    <Alert variant="destructive">
      <AlertCircle className="h-4 w-4" />
      <AlertDescription>
        Failed to load photos: {error.message}
      </AlertDescription>
    </Alert>
  );
}

// Empty states
if (photos.length === 0) {
  return (
    <EmptyState
      icon={<ImageIcon className="w-12 h-12" />}
      title="No photos found"
      description="Upload some photos to get started"
    />
  );
}
```

### User Feedback
- Toast notifications for success/error states
- Loading spinners for async operations
- Progress indicators for long-running tasks
- Confirmation dialogs for destructive actions

## Testing

### Component Testing
```typescript
import { render, screen } from '@testing-library/react';
import { PhotoManager } from './PhotoManager';

test('renders assignment mode correctly', () => {
  render(<PhotoManager mode="assign" />);
  expect(screen.getByText('Photo Assignment')).toBeInTheDocument();
});

test('handles photo updates', async () => {
  const onPhotoUpdate = jest.fn();
  render(<PhotoManager mode="assign" onPhotoUpdate={onPhotoUpdate} />);
  // Test photo update scenarios
});
```

### Integration Testing
```typescript
test('drag and drop assignment', async () => {
  render(<PhotoManager mode="assign" />);
  // Test drag and drop functionality
});

test('search functionality', async () => {
  render(<PhotoManager mode="gallery" />);
  // Test search and filtering
});
```

## Dependencies

### Required Dependencies
```json
{
  "react-dnd": "^16.0.1",
  "react-dnd-html5-backend": "^16.0.1",
  "@radix-ui/react-dialog": "^1.0.5",
  "@radix-ui/react-select": "^2.0.0",
  "lucide-react": "^0.294.0"
}
```

### Internal Dependencies
- `@/components/ui/*` - UI components
- `@/lib/photoApi` - Photo API functions
- `@/lib/photoUpload` - Upload utilities
- `@/hooks/use-toast` - Toast notifications

## Future Enhancements

### Planned Features
1. **Bulk Operations**: Select multiple photos for bulk actions
2. **Advanced Filtering**: Date ranges, metadata filters
3. **Keyboard Shortcuts**: Power user keyboard navigation
4. **Export Functionality**: Export photos and metadata
5. **Batch Processing**: Process multiple photos simultaneously

### Performance Improvements
1. **Image Optimization**: Automatic image compression and resizing
2. **Caching**: Implement intelligent caching strategies
3. **Background Sync**: Sync changes in the background
4. **Progressive Loading**: Load photos progressively

## Troubleshooting

### Common Issues

#### Photos Not Loading
```typescript
// Check API connection
const { data, error } = await supabase.from('photos').select('*');
if (error) {
  console.error('Database connection error:', error);
}
```

#### Drag and Drop Not Working
```typescript
// Ensure DndProvider is present
<DndProvider backend={HTML5Backend}>
  <PhotoManager mode="assign" />
</DndProvider>
```

#### AI Suggestions Not Appearing
```typescript
// Check AI service availability
const suggestions = await getAISuggestions(photoId);
if (!suggestions) {
  console.warn('AI service unavailable');
}
```

### Debug Mode
```typescript
// Enable debug logging
const DEBUG = process.env.NODE_ENV === 'development';

if (DEBUG) {
  console.log('PhotoManager state:', { photos, species, mode });
}
```

## Contributing

### Development Guidelines
1. **Type Safety**: All props and state must be properly typed
2. **Error Boundaries**: Wrap in error boundaries for production
3. **Accessibility**: Ensure ARIA labels and keyboard navigation
4. **Performance**: Monitor bundle size and runtime performance
5. **Testing**: Maintain high test coverage

### Code Style
```typescript
// Use consistent naming
const handlePhotoUpdate = () => { /* ... */ };
const handleSpeciesUpdate = () => { /* ... */ };

// Use proper TypeScript
interface PhotoManagerProps {
  mode: PhotoManagerMode;
  title?: string;
  // ...
}
```

---

This documentation covers the comprehensive PhotoManager component consolidation. For additional questions or contributions, please refer to the main project README or create an issue in the repository. 