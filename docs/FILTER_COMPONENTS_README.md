# 🎛️ Filter Components

The filter components have been consolidated into a single, reusable `MultiSelectFilter` component that eliminates code duplication and provides consistent behavior across the application.

## 📁 Components

### `MultiSelectFilter` (New)
- **Location**: `src/components/MultiSelectFilter.tsx`
- **Purpose**: Generic multi-select filter component
- **Features**: Toggle options, select all, clear all, customizable colors

### `CategoryFilter` (Refactored)
- **Location**: `src/components/CategoryFilter.tsx`
- **Status**: Deprecated wrapper around `MultiSelectFilter`
- **Purpose**: Backward compatibility for category filtering

### `ConservationStatusFilter` (Refactored)
- **Location**: `src/components/ConservationStatusFilter.tsx`
- **Status**: Deprecated wrapper around `MultiSelectFilter`
- **Purpose**: Backward compatibility for conservation status filtering

## 🚀 Usage

### Using MultiSelectFilter Directly (Recommended)

```tsx
import { MultiSelectFilter } from '@/components/MultiSelectFilter';

// Basic usage
<MultiSelectFilter
  options={['Mammals', 'Birds', 'Reptiles']}
  selected={selectedCategories}
  onChange={setSelectedCategories}
  label="Filter by Category"
/>

// With custom color scheme
<MultiSelectFilter
  options={['High', 'Medium', 'Low']}
  selected={selectedPriorities}
  onChange={setSelectedPriorities}
  label="Filter by Priority"
  colorScheme="red"
/>

// Conservation status (automatic color coding)
<MultiSelectFilter
  options={['Critically Endangered', 'Endangered', 'Vulnerable']}
  selected={selectedStatuses}
  onChange={setSelectedStatuses}
  label="Filter by Conservation Status"
  colorScheme="default" // Automatic conservation status colors
/>
```

### Using Legacy Components (Deprecated)

```tsx
import { CategoryFilter } from '@/components/CategoryFilter';
import { ConservationStatusFilter } from '@/components/ConservationStatusFilter';

// These still work but are deprecated
<CategoryFilter
  categories={categories}
  selectedCategories={selectedCategories}
  onCategoryChange={setSelectedCategories}
/>

<ConservationStatusFilter
  statuses={statuses}
  selectedStatuses={selectedStatuses}
  onStatusChange={setSelectedStatuses}
/>
```

## 🎨 Color Schemes

### Available Color Schemes
- `'default'` - Standard blue/gray colors
- `'green'` - Green theme (used by CategoryFilter)
- `'red'` - Red theme for high-priority items
- `'orange'` - Orange theme for medium-priority items
- `'yellow'` - Yellow theme for low-priority items

### Conservation Status Colors (Automatic)
When `colorScheme="default"` and options look like conservation statuses, the component automatically applies appropriate colors:

- **Critically Endangered**: Red (dark)
- **Endangered**: Red (medium)
- **Vulnerable**: Orange
- **Near Threatened**: Yellow
- **Least Concern**: Green

## 📋 Props

### MultiSelectFilterProps

```typescript
interface MultiSelectFilterProps {
  /** Array of available options to select from */
  options: string[];
  /** Currently selected options */
  selected: string[];
  /** Callback when selection changes */
  onChange: (updated: string[]) => void;
  /** Optional label to display above the filter */
  label?: string;
  /** Whether all options should be selected by default */
  defaultAllSelected?: boolean;
  /** Optional color scheme for the filter buttons */
  colorScheme?: 'default' | 'green' | 'red' | 'orange' | 'yellow';
  /** Optional CSS class for the container */
  className?: string;
}
```

## 🔄 Migration Guide

### From CategoryFilter

**Before:**
```tsx
<CategoryFilter
  categories={categories}
  selectedCategories={selectedCategories}
  onCategoryChange={setSelectedCategories}
/>
```

**After:**
```tsx
<MultiSelectFilter
  options={categories}
  selected={selectedCategories}
  onChange={setSelectedCategories}
  label="Filter by Category"
  colorScheme="green"
/>
```

### From ConservationStatusFilter

**Before:**
```tsx
<ConservationStatusFilter
  statuses={statuses}
  selectedStatuses={selectedStatuses}
  onStatusChange={setSelectedStatuses}
/>
```

**After:**
```tsx
<MultiSelectFilter
  options={statuses}
  selected={selectedStatuses}
  onChange={setSelectedStatuses}
  label="Filter by Conservation Status"
  colorScheme="default" // Automatic conservation status colors
/>
```

## 🧪 Testing Examples

### Basic Functionality
```tsx
// Test single selection
const [selected, setSelected] = useState<string[]>([]);
<MultiSelectFilter
  options={['Option 1', 'Option 2', 'Option 3']}
  selected={selected}
  onChange={setSelected}
  label="Test Filter"
/>
```

### All Selected State
```tsx
// Test select all functionality
const [selected, setSelected] = useState<string[]>(['Option 1', 'Option 2', 'Option 3']);
<MultiSelectFilter
  options={['Option 1', 'Option 2', 'Option 3']}
  selected={selected}
  onChange={setSelected}
  label="All Selected Test"
/>
```

### Conservation Status Colors
```tsx
// Test automatic conservation status colors
<MultiSelectFilter
  options={['Critically Endangered', 'Endangered', 'Vulnerable']}
  selected={['Endangered']}
  onChange={() => {}}
  label="Conservation Status"
  colorScheme="default"
/>
```

## 🎯 Benefits

1. **No Code Duplication** - Single component handles all multi-select filtering
2. **Consistent UI** - Same layout, spacing, and interactions across all filters
3. **Flexible Styling** - Multiple color schemes and automatic conservation status colors
4. **Easy Maintenance** - Fix bugs or add features in one location
5. **Backward Compatibility** - Existing components still work with deprecation warnings
6. **Type Safety** - Full TypeScript support with proper interfaces

## 🔮 Future Enhancements

Potential improvements for the MultiSelectFilter:

1. **Search/Filter** - Add search functionality for long option lists
2. **Grouping** - Support for grouped options
3. **Custom Rendering** - Allow custom option rendering
4. **Keyboard Navigation** - Full keyboard accessibility
5. **Virtual Scrolling** - Handle very long option lists efficiently

## 📝 Notes

- The legacy components (`CategoryFilter`, `ConservationStatusFilter`) are marked as deprecated but still functional
- They will be removed in a future version after all usages have been migrated
- The `MultiSelectFilter` component is fully backward compatible with the existing interfaces
- Conservation status color coding is automatically detected and applied 