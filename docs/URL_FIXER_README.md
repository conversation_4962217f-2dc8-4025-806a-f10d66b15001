# 🔧 UrlFixer Utility

The `UrlFixer` utility class consolidates all photo URL validation and fixing logic that was previously duplicated across multiple scripts.

## 📁 Location
- **Utility Class**: `src/utils/UrlFixer.ts`
- **Unified Script**: `src/scripts/runUrlFixer.ts`
- **Updated Scripts**: All existing scripts now use the UrlFixer utility

## 🚀 Usage

### Using the Unified Script (Recommended)

```bash
# Fix all URL issues
npx tsx src/scripts/runUrlFixer.ts

# Fix only newline issues
npx tsx src/scripts/runUrlFixer.ts --fix-type newlines

# Fix only bucket name issues
npx tsx src/scripts/runUrlFixer.ts --fix-type bucket

# Fix only encoding issues
npx tsx src/scripts/runUrlFixer.ts --fix-type encoding

# Normalize URLs (sanitize and flag broken ones)
npx tsx src/scripts/runUrlFixer.ts --fix-type normalize

# Dry run to see what would be fixed
npx tsx src/scripts/runUrlFixer.ts --dry-run

# Check accessibility of fixed URLs
npx tsx src/scripts/runUrlFixer.ts --check

# Limit processing to first 100 photos
npx tsx src/scripts/runUrlFixer.ts --limit 100

# Process from input file
npx tsx src/scripts/runUrlFixer.ts --input-file broken_photos.json

# Log detailed changes
npx tsx src/scripts/runUrlFixer.ts --log-changes
```

### Using the Utility Class Directly

```typescript
import { UrlFixer } from '../utils/UrlFixer.js';

// Check if URL is broken
if (UrlFixer.isBrokenUrl(url)) {
  console.log('URL needs recovery');
}

// Fix newlines in URL
const fixedUrl = UrlFixer.fixNewlines(url);

// Check URL accessibility
const result = await UrlFixer.checkAccessibility(url);
if (result.accessible) {
  console.log('URL is working');
}

// Try different bucket variations
const bucketResult = await UrlFixer.tryBucketVariations(url);
if (bucketResult) {
  console.log(`Found working URL with bucket: ${bucketResult.bucket}`);
}
```

## 🔧 Available Methods

### URL Validation
- `isBrokenUrl(url)` - Checks if URL is broken or invalid
- `hasNewlines(url)` - Checks if URL contains newline characters
- `isUrlFixable(url)` - Checks if URL has fixable issues

### URL Fixing
- `sanitizeUrl(url)` - Removes newlines, tabs, and trims whitespace
- `fixNewlines(url)` - Fixes newline characters in URLs
- `fixPhotoUrl(url)` - Fixes bucket name from 'species' to 'photos'
- `fixCommonUrlIssues(url)` - Applies multiple encoding fixes
- `decodeAndSanitizeUrl(url)` - Decodes newlines and sanitizes

### URL Testing
- `checkAccessibility(url)` - Tests if URL is accessible via HTTP HEAD
- `tryBucketVariations(url)` - Tests different bucket names
- `getBucketVariations()` - Returns array of bucket names to try

### URL Encoding
- `encodeUrlPath(url)` - Properly encodes URL path segments

## 📊 Fix Types

### `newlines`
- Removes `\n`, `\r`, `\t` characters
- Replaces multiple spaces with single space
- Trims whitespace

### `bucket`
- Replaces `/storage/v1/object/public/species/` with `/storage/v1/object/public/photos/`
- Only applies if URL contains the species bucket pattern

### `encoding`
- Applies `sanitizeUrl()`
- Applies `encodeUrlPath()`
- Fixes double encoding issues (`%25` → `%`)

### `normalize`
- Flags broken URLs for recovery
- Sanitizes fixable URLs
- Updates database with `needs_recovery` flag

### `all`
- Tries all fix strategies in order: newlines → bucket → encoding
- Uses the first successful fix

## 🔄 Migration from Old Scripts

### Before (Multiple Scripts)
```bash
# Fix newlines
npx tsx src/scripts/fixNewlineUrls.ts

# Fix bucket names
npx tsx src/scripts/fixPhotoUrls.ts

# Comprehensive fix
npx tsx src/scripts/comprehensiveUrlFix.ts

# Normalize URLs
npx tsx src/scripts/normalizePhotoUrls.ts
```

### After (Unified Script)
```bash
# All fixes in one command
npx tsx src/scripts/runUrlFixer.ts --fix-type all

# Or specific fixes
npx tsx src/scripts/runUrlFixer.ts --fix-type newlines
npx tsx src/scripts/runUrlFixer.ts --fix-type bucket
npx tsx src/scripts/runUrlFixer.ts --fix-type encoding
npx tsx src/scripts/runUrlFixer.ts --fix-type normalize
```

## 📝 Output

The script generates a `fixed_urls.json` file with the following structure:

```json
[
  {
    "id": "123",
    "newUrl": "https://fixed-url.com/image.jpg",
    "fixType": "newlines",
    "originalUrl": "https://broken-url.com/image.jpg\n"
  }
]
```

## ⚠️ Deprecated Scripts

The following scripts have been updated to use UrlFixer but are maintained for backward compatibility:

- `src/scripts/fixPhotoUrls.ts` - Now uses `UrlFixer.fixPhotoUrl()`
- `src/scripts/fixNewlineUrls.ts` - Now uses `UrlFixer.fixNewlines()`
- `src/scripts/normalizePhotoUrls.ts` - Now uses `UrlFixer.isBrokenUrl()` and `UrlFixer.decodeAndSanitizeUrl()`

## 🎯 Benefits

1. **No Code Duplication** - All URL fixing logic centralized in one place
2. **Consistent Behavior** - Same validation and fixing logic across all scripts
3. **Easy Maintenance** - Fix bugs or add features in one location
4. **Flexible Usage** - Use as utility class or unified script
5. **Better Testing** - Single utility class is easier to test thoroughly

## 🧪 Testing

```typescript
// Test URL validation
expect(UrlFixer.isBrokenUrl('http://localhost/image.jpg')).toBe(true);
expect(UrlFixer.isBrokenUrl('https://valid-url.com/image.jpg')).toBe(false);

// Test URL fixing
expect(UrlFixer.fixNewlines('url\nwith\rnewlines\t')).toBe('url with newlines ');
expect(UrlFixer.fixPhotoUrl('https://supabase.co/storage/v1/object/public/species/image.jpg'))
  .toBe('https://supabase.co/storage/v1/object/public/photos/image.jpg');
``` 