# SOP: Photo Cleanup & Recovery Workflow

## Overview
This SOP describes the process for cleaning up and flagging malformed photo URLs in the `photos` table using Supabase and Node.js scripts. The workflow is non-destructive and supports admin review.

## Steps

1. **Apply Migrations**
   - Ensure the following migrations exist and are applied:
     - `20250622043819_add_needs_recovery_to_photos.sql`
     - `20250622043906_create_photos_requiring_review_view.sql`
     - (and any subsequent fixes)

2. **Regenerate Supabase Types**
   ```sh
   npx supabase gen types typescript --local > src/integrations/supabase/types.ts
   ```

3. **Insert Test Data (Optional)**
   ```sh
   DATABASE_URL="..." npx tsx src/scripts/addTestData.ts
   ```

4. **Run Normalization Script**
   - Dry-run:
     ```sh
     DATABASE_URL="..." npx tsx src/scripts/normalizePhotoUrls.ts --dry-run
     ```
   - Apply changes:
     ```sh
     DATABASE_URL="..." npx tsx src/scripts/normalizePhotoUrls.ts
     ```

5. **Check Photos Requiring Review**
   ```sh
   DATABASE_URL="..." npx tsx src/scripts/checkPhotosRequiringReview.ts
   ```

6. **Review in Admin UI**
   - Use the `photos_requiring_review` view to filter and review flagged photos.

## Notes
- No rows are deleted; only `needs_recovery` is set or URLs are sanitized.
- Safe for production use with admin oversight. 