-- Add geographic columns to species_v2 table
-- This ensures the AI-generated geographic data can be saved

-- Add the geographic columns if they don't exist
ALTER TABLE species_v2 
ADD COLUMN IF NOT EXISTS countries TEXT[], -- Array of countries
ADD COLUMN IF NOT EXISTS states_provinces TEXT[], -- Array of states/provinces  
ADD COLUMN IF NOT EXISTS geographic_scope TEXT DEFAULT 'regional', -- 'global', 'continental', 'national', 'regional', 'local'
ADD COLUMN IF NOT EXISTS primary_region TEXT; -- Main geographic region for the species

-- Create indexes for geographic searches
CREATE INDEX IF NOT EXISTS idx_species_v2_countries ON species_v2 USING gin(countries);
CREATE INDEX IF NOT EXISTS idx_species_v2_states_provinces ON species_v2 USING gin(states_provinces);
CREATE INDEX IF NOT EXISTS idx_species_v2_geographic_scope ON species_v2(geographic_scope);
CREATE INDEX IF NOT EXISTS idx_species_v2_primary_region ON species_v2(primary_region);

-- Add helpful comments
COMMENT ON COLUMN species_v2.countries IS 'Array of countries where this species is found';
COMMENT ON COLUMN species_v2.states_provinces IS 'Array of states/provinces for more granular location data';
COMMENT ON COLUMN species_v2.geographic_scope IS 'Scope of species distribution: global, continental, national, regional, local';
COMMENT ON COLUMN species_v2.primary_region IS 'Primary geographic region where species is most commonly found';

-- Verify the columns were added
SELECT 
  'Geographic columns added successfully' as status,
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns 
WHERE table_name = 'species_v2' 
  AND column_name IN ('countries', 'states_provinces', 'geographic_scope', 'primary_region')
ORDER BY column_name;
