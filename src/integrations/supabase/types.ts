export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      fun_facts: {
        Row: {
          created_at: string | null
          fact: string
          id: string
          species_id: string
        }
        Insert: {
          created_at?: string | null
          fact: string
          id?: string
          species_id: string
        }
        Update: {
          created_at?: string | null
          fact?: string
          id?: string
          species_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fun_facts_species_id_fkey"
            columns: ["species_id"]
            isOneToOne: false
            referencedRelation: "species_needing_review"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fun_facts_species_id_fkey"
            columns: ["species_id"]
            isOneToOne: false
            referencedRelation: "species_photo_counts_v2"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fun_facts_species_id_fkey"
            columns: ["species_id"]
            isOneToOne: false
            referencedRelation: "species_v2"
            referencedColumns: ["id"]
          },
        ]
      }
      location_species: {
        Row: {
          location_id: string
          photo_count: number | null
          species_id: string
        }
        Insert: {
          location_id: string
          photo_count?: number | null
          species_id: string
        }
        Update: {
          location_id?: string
          photo_count?: number | null
          species_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "location_species_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "location_species_species_id_fkey"
            columns: ["species_id"]
            isOneToOne: false
            referencedRelation: "species_needing_review"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "location_species_species_id_fkey"
            columns: ["species_id"]
            isOneToOne: false
            referencedRelation: "species_photo_counts_v2"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "location_species_species_id_fkey"
            columns: ["species_id"]
            isOneToOne: false
            referencedRelation: "species_v2"
            referencedColumns: ["id"]
          },
        ]
      }
      locations: {
        Row: {
          country: string | null
          created_at: string | null
          description: string | null
          ebird_hotspot_url: string | null
          id: string
          latitude: number | null
          longitude: number | null
          name: string
          region: string | null
          slug: string | null
          state: string | null
          url: string | null
        }
        Insert: {
          country?: string | null
          created_at?: string | null
          description?: string | null
          ebird_hotspot_url?: string | null
          id?: string
          latitude?: number | null
          longitude?: number | null
          name: string
          region?: string | null
          slug?: string | null
          state?: string | null
          url?: string | null
        }
        Update: {
          country?: string | null
          created_at?: string | null
          description?: string | null
          ebird_hotspot_url?: string | null
          id?: string
          latitude?: number | null
          longitude?: number | null
          name?: string
          region?: string | null
          slug?: string | null
          state?: string | null
          url?: string | null
        }
        Relationships: []
      }
      photos: {
        Row: {
          ai_confidence: number | null
          ai_reviewed: boolean | null
          ai_suggested_id: string | null
          airtable_id: string | null
          camera_settings: string | null
          created_at: string | null
          description: string | null
          hash: string | null
          id: number
          location: string | null
          notes: string | null
          photographer: string | null
          published: boolean | null
          species_id: string | null
          tags: string[] | null
          time_of_day: string | null
          title: string | null
          url: string
          weather_conditions: string | null
        }
        Insert: {
          ai_confidence?: number | null
          ai_reviewed?: boolean | null
          ai_suggested_id?: string | null
          airtable_id?: string | null
          camera_settings?: string | null
          created_at?: string | null
          description?: string | null
          hash?: string | null
          id?: number
          location?: string | null
          notes?: string | null
          photographer?: string | null
          published?: boolean | null
          species_id?: string | null
          tags?: string[] | null
          time_of_day?: string | null
          title?: string | null
          url: string
          weather_conditions?: string | null
        }
        Update: {
          ai_confidence?: number | null
          ai_reviewed?: boolean | null
          ai_suggested_id?: string | null
          airtable_id?: string | null
          camera_settings?: string | null
          created_at?: string | null
          description?: string | null
          hash?: string | null
          id?: number
          location?: string | null
          notes?: string | null
          photographer?: string | null
          published?: boolean | null
          species_id?: string | null
          tags?: string[] | null
          time_of_day?: string | null
          title?: string | null
          url?: string
          weather_conditions?: string | null
        }
        Relationships: []
      }
      photos_v2: {
        Row: {
          ai_confidence: number | null
          ai_reviewed: boolean | null
          ai_suggested_id: string | null
          airtable_id: string | null
          camera_settings: string | null
          created_at: string | null
          description: string | null
          hash: string | null
          id: string | null
          location: string | null
          location_id: string | null
          notes: string | null
          photographer: string | null
          published: boolean | null
          rn: number | null
          species_id: string | null
          tags: string[] | null
          time_of_day: string | null
          title: string | null
          url: string | null
          weather_conditions: string | null
        }
        Insert: {
          ai_confidence?: number | null
          ai_reviewed?: boolean | null
          ai_suggested_id?: string | null
          airtable_id?: string | null
          camera_settings?: string | null
          created_at?: string | null
          description?: string | null
          hash?: string | null
          id?: string | null
          location?: string | null
          location_id?: string | null
          notes?: string | null
          photographer?: string | null
          published?: boolean | null
          rn?: number | null
          species_id?: string | null
          tags?: string[] | null
          time_of_day?: string | null
          title?: string | null
          url?: string | null
          weather_conditions?: string | null
        }
        Update: {
          ai_confidence?: number | null
          ai_reviewed?: boolean | null
          ai_suggested_id?: string | null
          airtable_id?: string | null
          camera_settings?: string | null
          created_at?: string | null
          description?: string | null
          hash?: string | null
          id?: string | null
          location?: string | null
          location_id?: string | null
          notes?: string | null
          photographer?: string | null
          published?: boolean | null
          rn?: number | null
          species_id?: string | null
          tags?: string[] | null
          time_of_day?: string | null
          title?: string | null
          url?: string | null
          weather_conditions?: string | null
        }
        Relationships: []
      }
      photos_v2_backup: {
        Row: {
          ai_confidence: number | null
          ai_reviewed: boolean | null
          ai_suggested_id: string | null
          airtable_id: string | null
          camera_settings: string | null
          created_at: string | null
          description: string | null
          hash: string | null
          id: string | null
          location: string | null
          location_id: string | null
          notes: string | null
          photographer: string | null
          published: boolean | null
          species_id: string | null
          tags: string[] | null
          time_of_day: string | null
          title: string | null
          url: string | null
          weather_conditions: string | null
        }
        Insert: {
          ai_confidence?: number | null
          ai_reviewed?: boolean | null
          ai_suggested_id?: string | null
          airtable_id?: string | null
          camera_settings?: string | null
          created_at?: string | null
          description?: string | null
          hash?: string | null
          id?: string | null
          location?: string | null
          location_id?: string | null
          notes?: string | null
          photographer?: string | null
          published?: boolean | null
          species_id?: string | null
          tags?: string[] | null
          time_of_day?: string | null
          title?: string | null
          url?: string | null
          weather_conditions?: string | null
        }
        Update: {
          ai_confidence?: number | null
          ai_reviewed?: boolean | null
          ai_suggested_id?: string | null
          airtable_id?: string | null
          camera_settings?: string | null
          created_at?: string | null
          description?: string | null
          hash?: string | null
          id?: string | null
          location?: string | null
          location_id?: string | null
          notes?: string | null
          photographer?: string | null
          published?: boolean | null
          species_id?: string | null
          tags?: string[] | null
          time_of_day?: string | null
          title?: string | null
          url?: string | null
          weather_conditions?: string | null
        }
        Relationships: []
      }
      species: {
        Row: {
          ai_fun_facts: string | null
          airtable_id: string | null
          avibase_id: string | null
          behavior: string | null
          breeding_season: string | null
          category: string | null
          common_group: string | null
          common_name: string | null
          conservation_actions: string | null
          conservation_status: string | null
          created_at: string | null
          description: string | null
          diet: string | null
          ebird_code: string | null
          family: string | null
          featured: boolean | null
          fun_facts: string | null
          fun_facts_field: string | null
          gbif_id: string | null
          habitat: string | null
          id: string
          inat_id: string | null
          itis_tsn: string | null
          lifespan_years: number | null
          migration_pattern: string | null
          name: string
          notes: string | null
          photo_count: number | null
          population_trend: string | null
          published: boolean | null
          related_groups: string | null
          scientific_name: string | null
          size_cm: number | null
          size_description: string | null
          tags: string[] | null
          taxonomy_genus: string | null
          taxonomy_order: string | null
          taxonomy_subfamily: string | null
          threat_level: string | null
          updated_at: string | null
          weight_g: number | null
        }
        Insert: {
          ai_fun_facts?: string | null
          airtable_id?: string | null
          avibase_id?: string | null
          behavior?: string | null
          breeding_season?: string | null
          category?: string | null
          common_group?: string | null
          common_name?: string | null
          conservation_actions?: string | null
          conservation_status?: string | null
          created_at?: string | null
          description?: string | null
          diet?: string | null
          ebird_code?: string | null
          family?: string | null
          featured?: boolean | null
          fun_facts?: string | null
          fun_facts_field?: string | null
          gbif_id?: string | null
          habitat?: string | null
          id: string
          inat_id?: string | null
          itis_tsn?: string | null
          lifespan_years?: number | null
          migration_pattern?: string | null
          name: string
          notes?: string | null
          photo_count?: number | null
          population_trend?: string | null
          published?: boolean | null
          related_groups?: string | null
          scientific_name?: string | null
          size_cm?: number | null
          size_description?: string | null
          tags?: string[] | null
          taxonomy_genus?: string | null
          taxonomy_order?: string | null
          taxonomy_subfamily?: string | null
          threat_level?: string | null
          updated_at?: string | null
          weight_g?: number | null
        }
        Update: {
          ai_fun_facts?: string | null
          airtable_id?: string | null
          avibase_id?: string | null
          behavior?: string | null
          breeding_season?: string | null
          category?: string | null
          common_group?: string | null
          common_name?: string | null
          conservation_actions?: string | null
          conservation_status?: string | null
          created_at?: string | null
          description?: string | null
          diet?: string | null
          ebird_code?: string | null
          family?: string | null
          featured?: boolean | null
          fun_facts?: string | null
          fun_facts_field?: string | null
          gbif_id?: string | null
          habitat?: string | null
          id?: string
          inat_id?: string | null
          itis_tsn?: string | null
          lifespan_years?: number | null
          migration_pattern?: string | null
          name?: string
          notes?: string | null
          photo_count?: number | null
          population_trend?: string | null
          published?: boolean | null
          related_groups?: string | null
          scientific_name?: string | null
          size_cm?: number | null
          size_description?: string | null
          tags?: string[] | null
          taxonomy_genus?: string | null
          taxonomy_order?: string | null
          taxonomy_subfamily?: string | null
          threat_level?: string | null
          updated_at?: string | null
          weight_g?: number | null
        }
        Relationships: []
      }
      species_locations: {
        Row: {
          location_id: string
          species_id: string
        }
        Insert: {
          location_id: string
          species_id: string
        }
        Update: {
          location_id?: string
          species_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "species_locations_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "species_locations_species_id_fkey"
            columns: ["species_id"]
            isOneToOne: false
            referencedRelation: "species_needing_review"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "species_locations_species_id_fkey"
            columns: ["species_id"]
            isOneToOne: false
            referencedRelation: "species_photo_counts_v2"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "species_locations_species_id_fkey"
            columns: ["species_id"]
            isOneToOne: false
            referencedRelation: "species_v2"
            referencedColumns: ["id"]
          },
        ]
      }
      species_v2: {
        Row: {
          ai_confidence: number | null
          ai_fun_facts: Json | null
          ai_generated: boolean | null
          airtable_id: string | null
          avibase_id: string | null
          behavior: string | null
          breeding_season: string | null
          category: string | null
          common_group: string | null
          common_name: string | null
          conservation_actions: string | null
          conservation_status: string | null
          created_at: string | null
          description: string | null
          diet: string | null
          ebird_code: string | null
          family: string | null
          featured: boolean | null
          fun_facts_field: Json | null
          gbif_id: string | null
          habitat: string | null
          id: string
          inat_id: string | null
          is_placeholder: boolean | null
          itis_tsn: string | null
          legacy_id: string | null
          lifespan_years: number | null
          migration_pattern: string | null
          name: string | null
          notes: string | null
          photo_count: number | null
          population_trend: string | null
          published: boolean | null
          regions: string | null
          related_groups: string[] | null
          scientific_name: string | null
          size_cm: string | null
          size_description: string | null
          tags: string[] | null
          taxonomy_genus: string | null
          taxonomy_order: string | null
          taxonomy_subfamily: string | null
          threat_level: string | null
          updated_at: string | null
          weight_g: string | null
          // Enhanced geographic fields
          countries: string[] | null
          states_provinces: string[] | null
          geographic_scope: string | null
          primary_region: string | null
          habitat_specificity: string | null
        }
        Insert: {
          ai_confidence?: number | null
          ai_fun_facts?: Json | null
          ai_generated?: boolean | null
          airtable_id?: string | null
          avibase_id?: string | null
          behavior?: string | null
          breeding_season?: string | null
          category?: string | null
          common_group?: string | null
          common_name?: string | null
          conservation_actions?: string | null
          conservation_status?: string | null
          created_at?: string | null
          description?: string | null
          diet?: string | null
          ebird_code?: string | null
          family?: string | null
          featured?: boolean | null
          fun_facts_field?: Json | null
          gbif_id?: string | null
          habitat?: string | null
          id?: string
          inat_id?: string | null
          is_placeholder?: boolean | null
          itis_tsn?: string | null
          legacy_id?: string | null
          lifespan_years?: number | null
          migration_pattern?: string | null
          name?: string | null
          notes?: string | null
          photo_count?: number | null
          population_trend?: string | null
          published?: boolean | null
          regions?: string | null
          related_groups?: string[] | null
          scientific_name?: string | null
          size_cm?: string | null
          size_description?: string | null
          tags?: string[] | null
          taxonomy_genus?: string | null
          taxonomy_order?: string | null
          taxonomy_subfamily?: string | null
          threat_level?: string | null
          updated_at?: string | null
          weight_g?: string | null
          // Enhanced geographic fields
          countries?: string[] | null
          states_provinces?: string[] | null
          geographic_scope?: string | null
          primary_region?: string | null
          habitat_specificity?: string | null
        }
        Update: {
          ai_confidence?: number | null
          ai_fun_facts?: Json | null
          ai_generated?: boolean | null
          airtable_id?: string | null
          avibase_id?: string | null
          behavior?: string | null
          breeding_season?: string | null
          category?: string | null
          common_group?: string | null
          common_name?: string | null
          conservation_actions?: string | null
          conservation_status?: string | null
          created_at?: string | null
          description?: string | null
          diet?: string | null
          ebird_code?: string | null
          family?: string | null
          featured?: boolean | null
          fun_facts_field?: Json | null
          gbif_id?: string | null
          habitat?: string | null
          id?: string
          inat_id?: string | null
          is_placeholder?: boolean | null
          itis_tsn?: string | null
          legacy_id?: string | null
          lifespan_years?: number | null
          migration_pattern?: string | null
          name?: string | null
          notes?: string | null
          photo_count?: number | null
          population_trend?: string | null
          published?: boolean | null
          regions?: string | null
          related_groups?: string[] | null
          scientific_name?: string | null
          size_cm?: string | null
          size_description?: string | null
          tags?: string[] | null
          taxonomy_genus?: string | null
          taxonomy_order?: string | null
          taxonomy_subfamily?: string | null
          threat_level?: string | null
          updated_at?: string | null
          weight_g?: string | null
          // Enhanced geographic fields
          countries?: string[] | null
          states_provinces?: string[] | null
          geographic_scope?: string | null
          primary_region?: string | null
          habitat_specificity?: string | null
        }
        Relationships: []
      }
      sync_state: {
        Row: {
          base_id: string
          bucket_name: string | null
          completed_at: string | null
          current_offset: number | null
          error_count: number | null
          error_details: Json | null
          id: number
          last_updated: string | null
          metadata: Json | null
          photos_inserted: number | null
          started_at: string | null
          status: string | null
          sync_type: string
          table_name: string
          total_records: number | null
        }
        Insert: {
          base_id: string
          bucket_name?: string | null
          completed_at?: string | null
          current_offset?: number | null
          error_count?: number | null
          error_details?: Json | null
          id?: number
          last_updated?: string | null
          metadata?: Json | null
          photos_inserted?: number | null
          started_at?: string | null
          status?: string | null
          sync_type: string
          table_name: string
          total_records?: number | null
        }
        Update: {
          base_id?: string
          bucket_name?: string | null
          completed_at?: string | null
          current_offset?: number | null
          error_count?: number | null
          error_details?: Json | null
          id?: number
          last_updated?: string | null
          metadata?: Json | null
          photos_inserted?: number | null
          started_at?: string | null
          status?: string | null
          sync_type?: string
          table_name?: string
          total_records?: number | null
        }
        Relationships: []
      }
    }
    Views: {
      orphaned_photo_urls: {
        Row: {
          ai_confidence: number | null
          ai_reviewed: boolean | null
          ai_suggested_id: string | null
          airtable_id: string | null
          camera_settings: string | null
          created_at: string | null
          description: string | null
          hash: string | null
          id: number | null
          location: string | null
          notes: string | null
          photographer: string | null
          published: boolean | null
          species_id: string | null
          tags: string[] | null
          time_of_day: string | null
          title: string | null
          url: string | null
          weather_conditions: string | null
        }
        Relationships: []
      }
      photos_requiring_review: {
        Row: {
          ai_confidence: number | null
          ai_reviewed: boolean | null
          ai_suggested_id: string | null
          ai_suggested_name: string | null
          datetaken: string | null
          id: number | null
          location: string | null
          published: boolean | null
          species_id: string | null
          species_name: string | null
          url: string | null
        }
        Relationships: []
      }
      species_needing_review: {
        Row: {
          behavior: string | null
          conservation_actions: string | null
          description: string | null
          family: string | null
          id: string | null
          name: string | null
          size_description: string | null
        }
        Insert: {
          behavior?: string | null
          conservation_actions?: string | null
          description?: string | null
          family?: string | null
          id?: string | null
          name?: string | null
          size_description?: string | null
        }
        Update: {
          behavior?: string | null
          conservation_actions?: string | null
          description?: string | null
          family?: string | null
          id?: string | null
          name?: string | null
          size_description?: string | null
        }
        Relationships: []
      }
      species_photo_counts_v2: {
        Row: {
          category: string | null
          common_name: string | null
          conservation_status: string | null
          created_at: string | null
          id: string | null
          name: string | null
          published_photos: number | null
          scientific_name: string | null
          total_photos: number | null
          unpublished_photos: number | null
          updated_at: string | null
        }
        Relationships: []
      }
      species_photo_summary: {
        Row: {
          category: string | null
          common_name: string | null
          conservation_status: string | null
          id: string | null
          latest_photo_date: string | null
          name: string | null
          photo_count: number | null
          published_photo_count: number | null
        }
        Relationships: []
      }
    }
    Functions: {
      cleanup_orphaned_records: {
        Args: Record<PropertyKey, never>
        Returns: {
          orphaned_photos_removed: number
          empty_species_unpublished: number
        }[]
      }
      get_duplicate_photos: {
        Args: Record<PropertyKey, never>
        Returns: {
          hash_value: string
          duplicate_count: number
          photo_ids: number[]
        }[]
      }
      get_species_fun_facts: {
        Args: { p_species_id: string }
        Returns: {
          id: string
          fact: string
          created_at: string
        }[]
      }
      get_species_photo_matrix: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_species_with_photo_stats: {
        Args: Record<PropertyKey, never>
        Returns: {
          species_id: string
          species_name: string
          total_photos: number
          published_photos: number
          airtable_photos: number
          uploaded_photos: number
        }[]
      }
      is_valid_uuid: {
        Args: { id: string }
        Returns: boolean
      }
      migrate_fun_facts_to_table: {
        Args: Record<PropertyKey, never>
        Returns: {
          species_id: string
          species_name: string
          migrated_facts_count: number
          status: string
        }[]
      }
      recalculate_all_species_photo_counts: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      refresh_species_photo_summary: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      update_species_photo_count: {
        Args: { species_uuid: string }
        Returns: boolean
      }
      update_species_photo_counts: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      update_species_published_status: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
