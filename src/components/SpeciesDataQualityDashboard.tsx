import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  BarChart3,
  AlertTriangle,
  CheckCircle,
  XCircle,
  TrendingUp,
  Database,
  FileText,
  Image,
  MapPin,
  Info,
  ExternalLink,
  Eye
} from "lucide-react";
import { useSpeciesData } from "@/hooks/useSpeciesData";
import { SpeciesDataImprovement } from "./SpeciesDataImprovement";
import { DetailedSpeciesDataAnalysis } from "./DetailedSpeciesDataAnalysis";
import { runPhotoAssignmentMigration, recalculateAllPhotoCounts } from "@/scripts/runMigration";

interface FieldQuality {
  field: string;
  label: string;
  filled: number;
  empty: number;
  total: number;
  percentage: number;
  priority: 'high' | 'medium' | 'low';
  category: 'basic' | 'detailed' | 'media' | 'location';
}

interface DataQualityStats {
  totalSpecies: number;
  publishedSpecies: number;
  unpublishedSpecies: number;
  speciesWithPhotos: number;
  speciesWithoutPhotos: number;
  averageCompleteness: number;
  fieldQuality: FieldQuality[];
  criticalIssues: string[];
}

export const SpeciesDataQualityDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const navigate = useNavigate();
  const { data: allSpecies, isLoading } = useSpeciesData({
    mode: 'all-for-analysis' // Use special mode that gets ALL species without pagination
  });

  // Debug logging
  console.log('Species data in Data Quality Dashboard:', {
    isLoading,
    speciesCount: allSpecies?.length || 0,
    firstFewSpecies: allSpecies?.slice(0, 3).map(s => ({ id: s.id, name: s.name, published: s.published }))
  });

  const qualityStats = useMemo((): DataQualityStats => {
    if (!allSpecies || allSpecies.length === 0) {
      return {
        totalSpecies: 0,
        publishedSpecies: 0,
        unpublishedSpecies: 0,
        speciesWithPhotos: 0,
        speciesWithoutPhotos: 0,
        averageCompleteness: 0,
        fieldQuality: [],
        criticalIssues: []
      };
    }

    const total = allSpecies.length;
    const published = allSpecies.filter(s => s.published).length;
    const unpublished = total - published;
    const withPhotos = allSpecies.filter(s => s.photo_count && s.photo_count > 0).length;
    const withoutPhotos = total - withPhotos;

    // Define fields to analyze
    const fieldsToAnalyze = [
      { field: 'name', label: 'Species Name', priority: 'high' as const, category: 'basic' as const },
      { field: 'scientific_name', label: 'Scientific Name', priority: 'high' as const, category: 'basic' as const },
      { field: 'description', label: 'Description', priority: 'high' as const, category: 'basic' as const },
      { field: 'conservation_status', label: 'Conservation Status', priority: 'high' as const, category: 'basic' as const },
      { field: 'habitat', label: 'Habitat', priority: 'medium' as const, category: 'detailed' as const },
      { field: 'diet', label: 'Diet', priority: 'medium' as const, category: 'detailed' as const },
      { field: 'behavior', label: 'Behavior', priority: 'medium' as const, category: 'detailed' as const },
      { field: 'size_cm', label: 'Size (cm)', priority: 'medium' as const, category: 'detailed' as const },
      { field: 'weight_g', label: 'Weight (g)', priority: 'medium' as const, category: 'detailed' as const },
      { field: 'lifespan_years', label: 'Lifespan', priority: 'medium' as const, category: 'detailed' as const },
      { field: 'regions', label: 'Regions', priority: 'medium' as const, category: 'location' as const },
      { field: 'migration_pattern', label: 'Migration Pattern', priority: 'low' as const, category: 'detailed' as const },
      { field: 'breeding_season', label: 'Breeding Season', priority: 'low' as const, category: 'detailed' as const },
      { field: 'ai_fun_facts', label: 'Fun Facts', priority: 'low' as const, category: 'detailed' as const },
      { field: 'conservation_actions', label: 'Conservation Actions', priority: 'medium' as const, category: 'detailed' as const },
    ];

    const fieldQuality: FieldQuality[] = fieldsToAnalyze.map(fieldDef => {
      const filled = allSpecies.filter(species => {
        const value = species[fieldDef.field as keyof typeof species];
        return value !== null && 
               value !== undefined && 
               value !== '' && 
               value !== 'Information not available' &&
               value !== 'Varied diet' &&
               !(typeof value === 'string' && value.includes('{"state"'));
      }).length;
      
      const empty = total - filled;
      const percentage = total > 0 ? Math.round((filled / total) * 100) : 0;

      return {
        field: fieldDef.field,
        label: fieldDef.label,
        filled,
        empty,
        total,
        percentage,
        priority: fieldDef.priority,
        category: fieldDef.category
      };
    });

    // Calculate average completeness
    const totalFields = fieldQuality.length;
    const averageCompleteness = totalFields > 0 
      ? Math.round(fieldQuality.reduce((sum, field) => sum + field.percentage, 0) / totalFields)
      : 0;

    // Identify critical issues
    const criticalIssues: string[] = [];
    
    if (unpublished > published) {
      criticalIssues.push(`More unpublished species (${unpublished}) than published (${published})`);
    }
    
    if (withoutPhotos > withPhotos) {
      criticalIssues.push(`${withoutPhotos} species have no photos`);
    }

    const lowQualityFields = fieldQuality.filter(f => f.priority === 'high' && f.percentage < 80);
    if (lowQualityFields.length > 0) {
      criticalIssues.push(`${lowQualityFields.length} high-priority fields have <80% completion`);
    }

    return {
      totalSpecies: total,
      publishedSpecies: published,
      unpublishedSpecies: unpublished,
      speciesWithPhotos: withPhotos,
      speciesWithoutPhotos: withoutPhotos,
      averageCompleteness,
      fieldQuality,
      criticalIssues
    };
  }, [allSpecies]);

  const getQualityColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getQualityBadge = (percentage: number) => {
    if (percentage >= 80) return <Badge className="bg-green-100 text-green-800">Good</Badge>;
    if (percentage >= 60) return <Badge className="bg-yellow-100 text-yellow-800">Fair</Badge>;
    return <Badge className="bg-red-100 text-red-800">Poor</Badge>;
  };

  // Navigation helpers
  const navigateToSpeciesList = (filter?: string) => {
    // Navigate to the unified CMS which has species management
    navigate('/admin/cms');
  };

  const navigateToSpeciesWithoutPhotos = () => {
    // Navigate to the unified CMS which shows species without photos
    navigate('/admin/cms');
  };

  const navigateToUnpublishedSpecies = () => {
    // Navigate to the unified CMS which can filter unpublished species
    navigate('/admin/cms');
  };

  const navigateToPhotoAssignment = () => {
    navigate('/admin/photo-assignment');
  };

  const navigateToSpeciesMatrix = () => {
    navigate('/admin/species-photo-matrix');
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="text-center">Loading species data quality analysis...</div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Species Data Quality Dashboard</h2>
          <p className="text-muted-foreground">
            Analyze and improve the completeness of your species database
          </p>
        </div>
        <Badge variant="outline" className="text-lg px-3 py-1">
          {qualityStats.averageCompleteness}% Complete
        </Badge>
      </div>

      {/* Critical Issues Alert */}
      {qualityStats.criticalIssues.length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-700">
              <AlertTriangle className="w-5 h-5" />
              Critical Issues Detected
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {qualityStats.criticalIssues.map((issue, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-red-50 rounded border border-red-200">
                  <div className="flex items-center gap-2 text-red-600">
                    <XCircle className="w-4 h-4" />
                    <span className="text-sm">{issue}</span>
                  </div>
                  <div className="flex gap-2">
                    {issue.includes('no photos') && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-7 text-xs"
                        onClick={navigateToPhotoAssignment}
                      >
                        <Image className="w-3 h-3 mr-1" />
                        Assign Photos
                      </Button>
                    )}
                    {issue.includes('unpublished') && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-7 text-xs"
                        onClick={navigateToSpeciesList}
                      >
                        <Eye className="w-3 h-3 mr-1" />
                        Manage Species
                      </Button>
                    )}
                    {issue.includes('completion') && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-7 text-xs"
                        onClick={() => setActiveTab('tools')}
                      >
                        <TrendingUp className="w-3 h-3 mr-1" />
                        Use AI Tools
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="fields">Field Analysis</TabsTrigger>
          <TabsTrigger value="categories">By Category</TabsTrigger>
          <TabsTrigger value="detailed">Detailed Analysis</TabsTrigger>
          <TabsTrigger value="actions">Improvement Actions</TabsTrigger>
          <TabsTrigger value="tools">Improvement Tools</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => navigateToSpeciesList()}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Species</CardTitle>
                <div className="flex items-center gap-1">
                  <Database className="h-4 w-4 text-muted-foreground" />
                  <ExternalLink className="h-3 w-3 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{qualityStats.totalSpecies}</div>
                <div className="flex items-center justify-between">
                  <p className="text-xs text-muted-foreground">
                    {qualityStats.publishedSpecies} published, {qualityStats.unpublishedSpecies} draft
                  </p>
                  <Button variant="ghost" size="sm" className="h-6 text-xs">
                    Manage Species
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={navigateToSpeciesWithoutPhotos}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">With Photos</CardTitle>
                <div className="flex items-center gap-1">
                  <Image className="h-4 w-4 text-muted-foreground" />
                  <ExternalLink className="h-3 w-3 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{qualityStats.speciesWithPhotos}</div>
                <div className="flex items-center justify-between">
                  <p className="text-xs text-muted-foreground">
                    {qualityStats.speciesWithoutPhotos} need photos
                  </p>
                  <Button variant="ghost" size="sm" className="h-6 text-xs">
                    Manage Photos
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('fields')}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Data Completeness</CardTitle>
                <div className="flex items-center gap-1">
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  <Eye className="h-3 w-3 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{qualityStats.averageCompleteness}%</div>
                <div className="flex items-center justify-between mt-2">
                  <Progress value={qualityStats.averageCompleteness} className="flex-1" />
                  <Button variant="ghost" size="sm" className="h-6 text-xs ml-2">
                    Details
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => setActiveTab('actions')}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Critical Issues</CardTitle>
                <div className="flex items-center gap-1">
                  <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                  <Eye className="h-3 w-3 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{qualityStats.criticalIssues.length}</div>
                <div className="flex items-center justify-between">
                  <p className="text-xs text-muted-foreground">
                    Require immediate attention
                  </p>
                  <Button variant="ghost" size="sm" className="h-6 text-xs">
                    Fix Issues
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="fields" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Field Completion Analysis</CardTitle>
              <p className="text-sm text-muted-foreground">
                Detailed breakdown of data completeness for each field
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {qualityStats.fieldQuality
                  .sort((a, b) => {
                    // Sort by priority first, then by percentage
                    const priorityOrder = { high: 0, medium: 1, low: 2 };
                    if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
                      return priorityOrder[a.priority] - priorityOrder[b.priority];
                    }
                    return b.percentage - a.percentage;
                  })
                  .map((field) => (
                    <div key={field.field} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex items-center gap-3 flex-1">
                        <div className="flex-1">
                          <div className="font-medium">{field.label}</div>
                          <div className="text-sm text-muted-foreground">
                            {field.filled} of {field.total} species ({field.percentage}%)
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={field.priority === 'high' ? 'destructive' : field.priority === 'medium' ? 'default' : 'secondary'}>
                          {field.priority}
                        </Badge>
                        {getQualityBadge(field.percentage)}
                        <div className="w-24">
                          <Progress value={field.percentage} />
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 text-xs"
                          onClick={navigateToSpeciesMatrix}
                        >
                          <Eye className="w-3 h-3 mr-1" />
                          Species Matrix
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          {['basic', 'detailed', 'location', 'media'].map(category => {
            const categoryFields = qualityStats.fieldQuality.filter(f => f.category === category);
            const avgCompleteness = categoryFields.length > 0 
              ? Math.round(categoryFields.reduce((sum, f) => sum + f.percentage, 0) / categoryFields.length)
              : 0;

            return (
              <Card key={category}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="capitalize">{category} Information</span>
                    <Badge variant="outline">{avgCompleteness}% Complete</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {categoryFields.map(field => (
                      <div key={field.field} className="flex items-center justify-between">
                        <span className="text-sm">{field.label}</span>
                        <div className="flex items-center gap-2">
                          <span className={`text-sm font-medium ${getQualityColor(field.percentage)}`}>
                            {field.percentage}%
                          </span>
                          <div className="w-16">
                            <Progress value={field.percentage} className="h-2" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </TabsContent>

        <TabsContent value="actions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recommended Improvement Actions</CardTitle>
              <p className="text-sm text-muted-foreground">
                Prioritized actions to improve your species database quality
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="p-4 border rounded-lg bg-red-50 border-red-200">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-red-800">🚨 High Priority</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-7 text-xs"
                      onClick={() => setActiveTab('tools')}
                    >
                      <TrendingUp className="w-3 h-3 mr-1" />
                      Use AI Tools
                    </Button>
                  </div>
                  <div className="space-y-2">
                    {qualityStats.fieldQuality
                      .filter(f => f.priority === 'high' && f.percentage < 80)
                      .map(f => (
                        <div key={f.field} className="flex items-center justify-between text-sm">
                          <span className="text-red-700">• Fix {f.label} - only {f.percentage}% complete</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 text-xs text-red-600 hover:text-red-800"
                            onClick={navigateToSpeciesMatrix}
                          >
                            Species Matrix
                          </Button>
                        </div>
                      ))}
                    {qualityStats.speciesWithoutPhotos > 0 && (
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-red-700">• Add photos to {qualityStats.speciesWithoutPhotos} species</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 text-xs text-red-600 hover:text-red-800"
                          onClick={navigateToPhotoAssignment}
                        >
                          Assign Photos
                        </Button>
                      </div>
                    )}
                  </div>
                </div>

                <div className="p-4 border rounded-lg bg-yellow-50 border-yellow-200">
                  <h4 className="font-medium text-yellow-800 mb-2">⚠️ Medium Priority</h4>
                  <ul className="space-y-1 text-sm text-yellow-700">
                    {qualityStats.fieldQuality
                      .filter(f => f.priority === 'medium' && f.percentage < 60)
                      .map(f => (
                        <li key={f.field}>• Improve {f.label} - {f.percentage}% complete</li>
                      ))}
                  </ul>
                </div>

                <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
                  <h4 className="font-medium text-blue-800 mb-2">💡 Enhancement Opportunities</h4>
                  <ul className="space-y-1 text-sm text-blue-700">
                    <li>• Use AI to generate missing descriptions and fun facts</li>
                    <li>• Bulk import conservation status data from IUCN</li>
                    <li>• Standardize habitat and diet information</li>
                    <li>• Add size and weight data from scientific sources</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="detailed">
          <DetailedSpeciesDataAnalysis />
        </TabsContent>

        <TabsContent value="tools">
          <SpeciesDataImprovement />
        </TabsContent>
      </Tabs>
    </div>
  );
};
