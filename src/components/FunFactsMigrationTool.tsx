import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Lightbulb, Play, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface MigrationResult {
  species_id: string;
  species_name: string;
  migrated_facts_count: number;
  status: string;
}

export const FunFactsMigrationTool: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<MigrationResult[]>([]);
  const [currentState, setCurrentState] = useState<{
    funFactsCount: number;
    speciesWithJsonbCount: number;
  } | null>(null);
  const [error, setError] = useState<string | null>(null);

  const checkCurrentState = async () => {
    try {
      setError(null);
      
      // Check current fun facts count
      const { count: funFactsCount, error: funFactsError } = await supabase
        .from('fun_facts')
        .select('*', { count: 'exact', head: true });

      if (funFactsError) throw funFactsError;

      // Check species with JSONB fun facts
      const { data: speciesWithJsonb, error: speciesError } = await supabase
        .from('species_v2')
        .select('id')
        .or('ai_fun_facts.not.is.null,fun_facts_field.not.is.null');

      if (speciesError) throw speciesError;

      setCurrentState({
        funFactsCount: funFactsCount || 0,
        speciesWithJsonbCount: speciesWithJsonb?.length || 0
      });

    } catch (err: any) {
      setError(`Error checking current state: ${err.message}`);
    }
  };

  const runMigration = async () => {
    setIsRunning(true);
    setError(null);
    setResults([]);

    try {
      console.log('🚀 Running fun facts migration...');
      
      const { data: migrationResults, error: migrationError } = await supabase
        .rpc('migrate_fun_facts_to_table');

      if (migrationError) {
        throw new Error(`Migration failed: ${migrationError.message}`);
      }

      setResults(migrationResults || []);
      
      // Refresh current state
      await checkCurrentState();
      
      console.log('✅ Migration completed successfully');

    } catch (err: any) {
      setError(err.message);
      console.error('❌ Migration failed:', err);
    } finally {
      setIsRunning(false);
    }
  };

  const addTestFunFacts = async () => {
    setIsRunning(true);
    setError(null);

    try {
      console.log('🎯 Adding test fun facts...');

      // Get some published species
      const { data: species, error: speciesError } = await supabase
        .from('species_v2')
        .select('id, name, category')
        .eq('published', true)
        .limit(3);

      if (speciesError) throw speciesError;

      if (!species || species.length === 0) {
        throw new Error('No published species found');
      }

      let totalAdded = 0;

      for (const sp of species) {
        // Clear existing facts
        await supabase.from('fun_facts').delete().eq('species_id', sp.id);

        // Add test facts
        const testFacts = [
          `${sp.name} is perfectly adapted to its natural habitat`,
          `${sp.name} plays an important role in the local ecosystem`,
          `${sp.name} has fascinating behaviors that scientists are still studying`,
          `${sp.name} faces various conservation challenges in the wild`,
          `${sp.name} is an indicator species for environmental health`
        ];

        const factsToInsert = testFacts.map(fact => ({
          species_id: sp.id,
          fact: fact
        }));

        const { data: insertedFacts, error: insertError } = await supabase
          .from('fun_facts')
          .insert(factsToInsert)
          .select();

        if (insertError) {
          console.error(`Error inserting facts for ${sp.name}:`, insertError);
        } else {
          totalAdded += insertedFacts?.length || 0;
        }
      }

      console.log(`✅ Added ${totalAdded} test fun facts`);
      
      // Refresh current state
      await checkCurrentState();

    } catch (err: any) {
      setError(err.message);
      console.error('❌ Adding test facts failed:', err);
    } finally {
      setIsRunning(false);
    }
  };

  React.useEffect(() => {
    checkCurrentState();
  }, []);

  const totalMigrated = results.reduce((sum, result) => sum + (result.migrated_facts_count || 0), 0);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="w-5 h-5 text-amber-500" />
          Fun Facts Migration Tool
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current State */}
        {currentState && (
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{currentState.funFactsCount}</div>
              <div className="text-sm text-blue-700">Fun Facts in Table</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{currentState.speciesWithJsonbCount}</div>
              <div className="text-sm text-green-700">Species with JSONB Facts</div>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-red-500" />
            <span className="text-red-700">{error}</span>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button 
            onClick={runMigration} 
            disabled={isRunning}
            className="flex items-center gap-2"
          >
            {isRunning ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Play className="w-4 h-4" />}
            Run Migration
          </Button>
          
          <Button 
            onClick={addTestFunFacts} 
            disabled={isRunning}
            variant="outline"
            className="flex items-center gap-2"
          >
            {isRunning ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Lightbulb className="w-4 h-4" />}
            Add Test Facts
          </Button>

          <Button 
            onClick={checkCurrentState} 
            disabled={isRunning}
            variant="ghost"
            className="flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
        </div>

        {/* Migration Results */}
        {results.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="font-medium">Migration Results ({totalMigrated} facts migrated)</span>
            </div>
            <div className="max-h-40 overflow-y-auto space-y-1">
              {results.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm">
                  <span className="font-medium">{result.species_name}</span>
                  <div className="flex items-center gap-2">
                    <Badge variant={result.migrated_facts_count > 0 ? "default" : "secondary"}>
                      {result.migrated_facts_count} facts
                    </Badge>
                    <Badge variant="outline">{result.status}</Badge>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
