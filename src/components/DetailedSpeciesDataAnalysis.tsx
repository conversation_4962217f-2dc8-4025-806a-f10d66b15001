import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import {
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info,
  Search,
  Filter,
  Eye,
  Edit,
  Image,
  MapPin,
  FileText,
  Database,
  TrendingUp,
  TrendingDown,
  Minus,
  Star,
  Calendar,
  Globe,
  Dna,
  Scale,
  Clock,
  Heart,
  Shield,
  Users,
  Wand2,
  <PERSON>ader2,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>
} from "lucide-react";
import { useSpeciesData } from "@/hooks/useSpeciesData";
import { improveSpeciesWithAI } from "@/api/ai-improve-species";
import { DuplicatePhotoManager } from "@/components/DuplicatePhotoManager";

// Define comprehensive field analysis schema
interface FieldAnalysis {
  field: string;
  displayName: string;
  category: 'core' | 'taxonomy' | 'physical' | 'behavioral' | 'conservation' | 'metadata' | 'geographic' | 'external';
  priority: 'critical' | 'high' | 'medium' | 'low';
  dataType: 'text' | 'number' | 'boolean' | 'array' | 'json' | 'date';
  icon: React.ComponentType<any>;
  qualityLevels: {
    excellent: { min: number; description: string };
    good: { min: number; description: string };
    fair: { min: number; description: string };
    poor: { description: string };
  };
  validator: (value: any, species?: any) => {
    score: number;
    level: 'excellent' | 'good' | 'fair' | 'poor' | 'missing';
    issues: string[];
    suggestions: string[];
  };
}

// Comprehensive field definitions based on species_v2 schema
const FIELD_DEFINITIONS: FieldAnalysis[] = [
  // Core Fields
  {
    field: 'name',
    displayName: 'Species Name',
    category: 'core',
    priority: 'critical',
    dataType: 'text',
    icon: FileText,
    qualityLevels: {
      excellent: { min: 90, description: 'Clear, descriptive name' },
      good: { min: 70, description: 'Adequate name' },
      fair: { min: 50, description: 'Basic name present' },
      poor: { description: 'Missing or very poor name' }
    },
    validator: (value) => {
      if (!value || value.trim().length === 0) {
        return { score: 0, level: 'missing', issues: ['Name is required'], suggestions: ['Add a clear species name'] };
      }
      
      const length = value.trim().length;
      const hasNumbers = /\d/.test(value);
      const hasSpecialChars = /[^a-zA-Z\s\-']/.test(value);
      const issues = [];
      const suggestions = [];
      
      if (length < 3) {
        issues.push('Name too short');
        suggestions.push('Use a more descriptive name');
      }
      if (hasNumbers) {
        issues.push('Contains numbers');
        suggestions.push('Remove numbers from species name');
      }
      if (hasSpecialChars) {
        issues.push('Contains special characters');
      }
      
      let score = 100;
      if (length < 5) score -= 20;
      if (length < 10) score -= 10;
      if (hasNumbers) score -= 30;
      if (hasSpecialChars) score -= 20;
      
      const level = score >= 90 ? 'excellent' : score >= 70 ? 'good' : score >= 50 ? 'fair' : 'poor';
      return { score, level, issues, suggestions };
    }
  },
  {
    field: 'common_name',
    displayName: 'Alternative Common Name',
    category: 'core',
    priority: 'medium',
    dataType: 'text',
    icon: Users,
    qualityLevels: {
      excellent: { min: 90, description: 'Additional common name provided' },
      good: { min: 70, description: 'Alternative name present' },
      fair: { min: 50, description: 'Basic alternative name' },
      poor: { description: 'No alternative common name' }
    },
    validator: (value, species) => {
      // common_name is optional - it's an alternative to the main name
      if (!value || value.trim().length === 0) {
        return { score: 70, level: 'good', issues: [], suggestions: ['Consider adding an alternative common name if one exists'] };
      }

      const length = value.trim().length;
      const words = value.trim().split(/\s+/).length;
      const issues = [];
      const suggestions = [];

      let score = 100;
      if (length < 5) {
        score -= 20;
        issues.push('Very short alternative name');
        suggestions.push('Use a more complete alternative name');
      }
      if (words === 1) {
        score -= 10;
        issues.push('Single word alternative name');
        suggestions.push('Consider adding descriptive words');
      }
      if (words > 5) {
        score -= 10;
        issues.push('Very long alternative name');
        suggestions.push('Consider shortening to most common usage');
      }

      // Check if it's the same as the main name
      if (species?.name && value.trim().toLowerCase() === species.name.trim().toLowerCase()) {
        score -= 30;
        issues.push('Same as main species name');
        suggestions.push('Use a different common name or leave blank if no alternative exists');
      }

      const level = score >= 90 ? 'excellent' : score >= 70 ? 'good' : score >= 50 ? 'fair' : 'poor';
      return { score, level, issues, suggestions };
    }
  },
  {
    field: 'scientific_name',
    displayName: 'Scientific Name',
    category: 'taxonomy',
    priority: 'critical',
    dataType: 'text',
    icon: Dna,
    qualityLevels: {
      excellent: { min: 95, description: 'Proper binomial nomenclature' },
      good: { min: 80, description: 'Valid scientific format' },
      fair: { min: 60, description: 'Basic scientific name' },
      poor: { description: 'Invalid or missing scientific name' }
    },
    validator: (value) => {
      if (!value || value.trim().length === 0) {
        return { score: 0, level: 'missing', issues: ['Scientific name required'], suggestions: ['Add proper binomial nomenclature (Genus species)'] };
      }
      
      const trimmed = value.trim();
      const words = trimmed.split(/\s+/);
      const issues = [];
      const suggestions = [];
      
      // Check for proper binomial format
      const isBinomial = words.length === 2;
      const hasCapitalizedGenus = words[0] && words[0][0] === words[0][0].toUpperCase();
      const hasLowercaseSpecies = words[1] && words[1] === words[1].toLowerCase();
      const hasItalicIndicators = /\*|_/.test(trimmed);
      
      let score = 100;
      
      if (!isBinomial) {
        score -= 40;
        issues.push('Not proper binomial format');
        suggestions.push('Use format: Genus species');
      }
      if (!hasCapitalizedGenus) {
        score -= 20;
        issues.push('Genus not capitalized');
        suggestions.push('Capitalize the genus name');
      }
      if (!hasLowercaseSpecies && words[1]) {
        score -= 20;
        issues.push('Species not lowercase');
        suggestions.push('Make species name lowercase');
      }
      if (hasItalicIndicators) {
        score -= 5;
        issues.push('Contains formatting characters');
        suggestions.push('Remove italic markers (* or _)');
      }
      
      const level = score >= 95 ? 'excellent' : score >= 80 ? 'good' : score >= 60 ? 'fair' : 'poor';
      return { score, level, issues, suggestions };
    }
  },
  {
    field: 'category',
    displayName: 'Category',
    category: 'core',
    priority: 'high',
    dataType: 'text',
    icon: Database,
    qualityLevels: {
      excellent: { min: 100, description: 'Standard category (Birds, Mammals, etc.)' },
      good: { min: 80, description: 'Valid category' },
      fair: { min: 60, description: 'Acceptable category' },
      poor: { description: 'Invalid or missing category' }
    },
    validator: (value) => {
      if (!value || value.trim().length === 0) {
        return { score: 0, level: 'missing', issues: ['Category missing'], suggestions: ['Add species category (Birds, Mammals, Reptiles, etc.)'] };
      }

      const standardCategories = ['Birds', 'Mammals', 'Reptiles', 'Amphibians', 'Fish', 'Insects', 'Arachnids', 'Mollusks', 'Crustaceans', 'Other'];
      const isStandard = standardCategories.includes(value.trim());
      const issues = [];
      const suggestions = [];

      let score = 100;
      if (!isStandard) {
        score -= 20;
        issues.push('Non-standard category');
        suggestions.push('Use standard categories: ' + standardCategories.join(', '));
      }

      const level = score >= 100 ? 'excellent' : score >= 80 ? 'good' : score >= 60 ? 'fair' : 'poor';
      return { score, level, issues, suggestions };
    }
  },
  {
    field: 'family',
    displayName: 'Family',
    category: 'taxonomy',
    priority: 'medium',
    dataType: 'text',
    icon: Dna,
    qualityLevels: {
      excellent: { min: 90, description: 'Proper taxonomic family' },
      good: { min: 70, description: 'Valid family name' },
      fair: { min: 50, description: 'Basic family info' },
      poor: { description: 'Missing or invalid family' }
    },
    validator: (value) => {
      if (!value || value.trim().length === 0) {
        return { score: 0, level: 'missing', issues: ['Family missing'], suggestions: ['Add taxonomic family'] };
      }

      const trimmed = value.trim();
      const endsWithIdae = trimmed.endsWith('idae') || trimmed.endsWith('inae');
      const isCapitalized = trimmed[0] === trimmed[0].toUpperCase();
      const issues = [];
      const suggestions = [];

      let score = 100;
      if (!endsWithIdae) {
        score -= 20;
        issues.push('Does not follow standard family naming');
        suggestions.push('Family names typically end in -idae');
      }
      if (!isCapitalized) {
        score -= 10;
        issues.push('Not capitalized');
        suggestions.push('Capitalize family name');
      }

      const level = score >= 90 ? 'excellent' : score >= 70 ? 'good' : score >= 50 ? 'fair' : 'poor';
      return { score, level, issues, suggestions };
    }
  },
  {
    field: 'habitat',
    displayName: 'Habitat',
    category: 'behavioral',
    priority: 'high',
    dataType: 'text',
    icon: Globe,
    qualityLevels: {
      excellent: { min: 85, description: 'Detailed habitat description' },
      good: { min: 70, description: 'Good habitat info' },
      fair: { min: 50, description: 'Basic habitat' },
      poor: { description: 'Missing or vague habitat' }
    },
    validator: (value) => {
      if (!value || value.trim().length === 0) {
        return { score: 0, level: 'missing', issues: ['Habitat missing'], suggestions: ['Add habitat information'] };
      }

      const length = value.trim().length;
      const habitats = value.split(',').map(h => h.trim()).filter(h => h.length > 0);
      const issues = [];
      const suggestions = [];

      let score = 100;
      if (length < 20) {
        score -= 30;
        issues.push('Very brief habitat description');
        suggestions.push('Add more detailed habitat information');
      }
      if (habitats.length === 1) {
        score -= 15;
        issues.push('Single habitat type');
        suggestions.push('Consider adding multiple habitat types if applicable');
      }

      const level = score >= 85 ? 'excellent' : score >= 70 ? 'good' : score >= 50 ? 'fair' : 'poor';
      return { score, level, issues, suggestions };
    }
  },
  {
    field: 'diet',
    displayName: 'Diet',
    category: 'behavioral',
    priority: 'medium',
    dataType: 'text',
    icon: Heart,
    qualityLevels: {
      excellent: { min: 85, description: 'Detailed diet information' },
      good: { min: 70, description: 'Good diet description' },
      fair: { min: 50, description: 'Basic diet info' },
      poor: { description: 'Missing or vague diet' }
    },
    validator: (value) => {
      if (!value || value.trim().length === 0) {
        return { score: 0, level: 'missing', issues: ['Diet missing'], suggestions: ['Add diet information'] };
      }

      const length = value.trim().length;
      const dietTypes = value.split(',').map(d => d.trim()).filter(d => d.length > 0);
      const issues = [];
      const suggestions = [];

      let score = 100;
      if (length < 15) {
        score -= 25;
        issues.push('Very brief diet description');
        suggestions.push('Add more detailed diet information');
      }
      if (dietTypes.length === 1 && length < 30) {
        score -= 15;
        issues.push('Single diet type with minimal detail');
        suggestions.push('Add more specific diet details or additional food sources');
      }

      const level = score >= 85 ? 'excellent' : score >= 70 ? 'good' : score >= 50 ? 'fair' : 'poor';
      return { score, level, issues, suggestions };
    }
  },
  {
    field: 'conservation_status',
    displayName: 'Conservation Status',
    category: 'conservation',
    priority: 'high',
    dataType: 'text',
    icon: Shield,
    qualityLevels: {
      excellent: { min: 100, description: 'IUCN Red List status' },
      good: { min: 80, description: 'Valid conservation status' },
      fair: { min: 60, description: 'Basic status info' },
      poor: { description: 'Missing or invalid status' }
    },
    validator: (value) => {
      if (!value || value.trim().length === 0) {
        return { score: 0, level: 'missing', issues: ['Conservation status missing'], suggestions: ['Add IUCN Red List status'] };
      }

      const iucnStatuses = [
        'Least Concern', 'Near Threatened', 'Vulnerable', 'Endangered',
        'Critically Endangered', 'Extinct in the Wild', 'Extinct', 'Data Deficient'
      ];
      const isIUCN = iucnStatuses.includes(value.trim());
      const issues = [];
      const suggestions = [];

      let score = 100;
      if (!isIUCN) {
        score -= 20;
        issues.push('Non-IUCN status');
        suggestions.push('Use IUCN Red List categories: ' + iucnStatuses.join(', '));
      }

      const level = score >= 100 ? 'excellent' : score >= 80 ? 'good' : score >= 60 ? 'fair' : 'poor';
      return { score, level, issues, suggestions };
    }
  },
  {
    field: 'fun_facts',
    displayName: 'Fun Facts',
    category: 'behavioral',
    priority: 'medium',
    dataType: 'array',
    icon: Star,
    qualityLevels: {
      excellent: { min: 90, description: '3+ interesting fun facts' },
      good: { min: 70, description: '2-3 fun facts' },
      fair: { min: 50, description: '1-2 fun facts' },
      poor: { description: 'No fun facts' }
    },
    validator: (value, species) => {
      // Fun facts might be in a separate table, so check if we have any
      const funFactsCount = Array.isArray(value) ? value.length : 0;
      const issues = [];
      const suggestions = [];

      if (funFactsCount === 0) {
        return { score: 0, level: 'missing', issues: ['No fun facts'], suggestions: ['Add interesting facts about this species'] };
      }

      let score = 100;
      if (funFactsCount < 2) {
        score -= 30;
        issues.push('Only one fun fact');
        suggestions.push('Add more interesting facts');
      }
      if (funFactsCount < 3) {
        score -= 15;
        suggestions.push('Consider adding one more fun fact');
      }

      const level = score >= 90 ? 'excellent' : score >= 70 ? 'good' : score >= 50 ? 'fair' : 'poor';
      return { score, level, issues, suggestions };
    }
  },
  {
    field: 'published',
    displayName: 'Publication Status',
    category: 'metadata',
    priority: 'high',
    dataType: 'boolean',
    icon: Eye,
    qualityLevels: {
      excellent: { min: 100, description: 'Published and visible' },
      good: { min: 80, description: 'Ready for publication' },
      fair: { min: 60, description: 'Draft status' },
      poor: { description: 'Unpublished' }
    },
    validator: (value, species) => {
      if (value === true) {
        return { score: 100, level: 'excellent', issues: [], suggestions: [] };
      } else {
        return { score: 50, level: 'fair', issues: ['Species not published'], suggestions: ['Publish species when data is complete'] };
      }
    }
  },
  {
    field: 'photo_count',
    displayName: 'Photo Count',
    category: 'metadata',
    priority: 'high',
    dataType: 'number',
    icon: Image,
    qualityLevels: {
      excellent: { min: 90, description: '5+ photos' },
      good: { min: 70, description: '3-4 photos' },
      fair: { min: 50, description: '1-2 photos' },
      poor: { description: 'No photos' }
    },
    validator: (value, species) => {
      const photoCount = parseInt(value) || 0;
      const issues = [];
      const suggestions = [];

      if (photoCount === 0) {
        return { score: 0, level: 'missing', issues: ['No photos'], suggestions: ['Add photos of this species'] };
      }

      let score = 100;
      if (photoCount < 3) {
        score -= 30;
        issues.push('Few photos');
        suggestions.push('Add more photos to showcase the species');
      }
      if (photoCount < 5) {
        score -= 15;
        suggestions.push('Consider adding more photos for better representation');
      }

      const level = score >= 90 ? 'excellent' : score >= 70 ? 'good' : score >= 50 ? 'fair' : 'poor';
      return { score, level, issues, suggestions };
    }
  },
  {
    field: 'description',
    displayName: 'Description',
    category: 'core',
    priority: 'high',
    dataType: 'text',
    icon: FileText,
    qualityLevels: {
      excellent: { min: 90, description: 'Comprehensive, detailed description (300+ chars)' },
      good: { min: 75, description: 'Good description (150-300 chars)' },
      fair: { min: 50, description: 'Basic description (50-150 chars)' },
      poor: { description: 'Very short or missing description' }
    },
    validator: (value) => {
      if (!value || value.trim().length === 0) {
        return { score: 0, level: 'missing', issues: ['Description missing'], suggestions: ['Add detailed species description'] };
      }
      
      const length = value.trim().length;
      const sentences = value.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
      const words = value.trim().split(/\s+/).length;
      const issues = [];
      const suggestions = [];
      
      let score = 100;
      
      if (length < 50) {
        score -= 50;
        issues.push('Very short description');
        suggestions.push('Expand with more details about appearance, behavior, habitat');
      } else if (length < 150) {
        score -= 25;
        issues.push('Short description');
        suggestions.push('Add more descriptive details');
      }
      
      if (sentences < 2) {
        score -= 20;
        issues.push('Single sentence description');
        suggestions.push('Break into multiple sentences for better readability');
      }
      
      if (words < 10) {
        score -= 30;
        issues.push('Very few words');
        suggestions.push('Add more descriptive content');
      }
      
      // Check for quality indicators
      const hasPhysicalDescription = /size|color|length|weight|appearance|look|feature/i.test(value);
      const hasBehaviorDescription = /behavior|habit|feed|nest|migrate|social/i.test(value);
      const hasHabitatDescription = /habitat|live|found|environment|ecosystem/i.test(value);
      
      if (!hasPhysicalDescription) {
        score -= 10;
        suggestions.push('Add physical description details');
      }
      if (!hasBehaviorDescription) {
        score -= 10;
        suggestions.push('Add behavioral information');
      }
      if (!hasHabitatDescription) {
        score -= 10;
        suggestions.push('Add habitat information');
      }
      
      const level = score >= 90 ? 'excellent' : score >= 75 ? 'good' : score >= 50 ? 'fair' : 'poor';
      return { score, level, issues, suggestions };
    }
  }
];

export const DetailedSpeciesDataAnalysis: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [qualityFilter, setQualityFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('score');
  const [selectedSpecies, setSelectedSpecies] = useState<Set<string>>(new Set());
  const [isAiImproving, setIsAiImproving] = useState(false);
  const [improvingSpecies, setImprovingSpecies] = useState<Set<string>>(new Set());
  const navigate = useNavigate();
  
  const { data: allSpecies, isLoading } = useSpeciesData({
    mode: 'all-for-analysis'
  });

  // AI Improvement Functions
  const handleAiImproveSpecies = async (speciesIds: string[]) => {
    setIsAiImproving(true);
    setImprovingSpecies(new Set(speciesIds));

    let successCount = 0;
    let failureCount = 0;

    try {
      for (const speciesId of speciesIds) {
        const species = allSpecies?.find(s => s.id === speciesId);
        if (!species) {
          failureCount++;
          continue;
        }

        toast.info(`🤖 Improving ${species.name || 'species'} with Gemini AI...`);

        // Determine which fields need improvement based on current data
        const fieldsToImprove = [];
        if (!species.description || species.description.length < 50) fieldsToImprove.push('description');
        if (!species.habitat) fieldsToImprove.push('habitat');
        if (!species.diet) fieldsToImprove.push('diet');
        if (!species.conservation_status) fieldsToImprove.push('conservation_status');
        if (!species.family) fieldsToImprove.push('family');
        if (!species.size_description) fieldsToImprove.push('size');
        if (!species.weight_g) fieldsToImprove.push('weight');
        if (!species.lifespan_years) fieldsToImprove.push('lifespan');
        if (!species.behavior) fieldsToImprove.push('behavior');
        if (!species.breeding_season) fieldsToImprove.push('reproduction');
        // For fun_facts, we'll check if there are any in the fun_facts table (this is simplified for now)
        fieldsToImprove.push('fun_facts');

        if (fieldsToImprove.length === 0) {
          toast.info(`${species.name || 'species'} already has complete data`);
          successCount++;
          continue;
        }

        // Call AI improvement function
        const result = await improveSpeciesWithAI(speciesId, fieldsToImprove);

        if (result.success) {
          toast.success(`✅ Successfully improved ${species.name || 'species'} (${fieldsToImprove.length} fields updated)`);
          successCount++;
        } else {
          toast.error(`❌ Failed to improve ${species.name || 'species'}: ${result.error || 'Unknown error'}`);
          failureCount++;
        }

        // Small delay between requests to avoid rate limiting
        if (speciesIds.length > 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // Final summary
      if (successCount > 0 && failureCount === 0) {
        toast.success(`🎉 Successfully improved ${successCount} species with AI!`);
      } else if (successCount > 0 && failureCount > 0) {
        toast.info(`✅ Improved ${successCount} species, ${failureCount} failed`);
      } else if (failureCount > 0) {
        toast.error(`❌ Failed to improve ${failureCount} species`);
      }

    } catch (error) {
      toast.error('❌ AI improvement process failed');
      console.error('AI improvement error:', error);
    } finally {
      setIsAiImproving(false);
      setImprovingSpecies(new Set());

      // Refresh the data to show improvements
      // Note: In a real app, you'd want to invalidate the query cache
      window.location.reload();
    }
  };

  const handleSelectAll = () => {
    if (selectedSpecies.size === filteredSpecies.length) {
      setSelectedSpecies(new Set());
    } else {
      setSelectedSpecies(new Set(filteredSpecies.map(s => s.species.id)));
    }
  };

  const handleSelectSpecies = (speciesId: string) => {
    const newSelected = new Set(selectedSpecies);
    if (newSelected.has(speciesId)) {
      newSelected.delete(speciesId);
    } else {
      newSelected.add(speciesId);
    }
    setSelectedSpecies(newSelected);
  };

  // Analyze each species in detail
  const speciesAnalysis = useMemo(() => {
    if (!allSpecies) return [];
    
    return allSpecies.map(species => {
      const fieldAnalyses = FIELD_DEFINITIONS.map(fieldDef => {
        const fieldValue = species[fieldDef.field as keyof typeof species];
        const analysis = fieldDef.validator(fieldValue, species);

        return {
          ...fieldDef,
          value: fieldValue,
          analysis
        };
      });
      
      // Calculate overall quality score
      const totalScore = fieldAnalyses.reduce((sum, field) => {
        const weight = field.priority === 'critical' ? 3 : field.priority === 'high' ? 2 : field.priority === 'medium' ? 1.5 : 1;
        return sum + (field.analysis.score * weight);
      }, 0);
      
      const totalWeight = fieldAnalyses.reduce((sum, field) => {
        const weight = field.priority === 'critical' ? 3 : field.priority === 'high' ? 2 : field.priority === 'medium' ? 1.5 : 1;
        return sum + (100 * weight);
      }, 0);
      
      const overallScore = Math.round(totalScore / totalWeight * 100);
      const overallLevel = overallScore >= 90 ? 'excellent' : overallScore >= 75 ? 'good' : overallScore >= 50 ? 'fair' : 'poor';
      
      return {
        species,
        fieldAnalyses,
        overallScore,
        overallLevel,
        criticalIssues: fieldAnalyses.filter(f => f.priority === 'critical' && f.analysis.level === 'missing').length,
        totalIssues: fieldAnalyses.reduce((sum, f) => sum + f.analysis.issues.length, 0),
        completedFields: fieldAnalyses.filter(f => f.analysis.level !== 'missing').length,
        totalFields: fieldAnalyses.length
      };
    });
  }, [allSpecies]);

  // Filter and sort species
  const filteredSpecies = useMemo(() => {
    let filtered = speciesAnalysis;
    
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(item => 
        item.species.name?.toLowerCase().includes(term) ||
        item.species.common_name?.toLowerCase().includes(term) ||
        item.species.scientific_name?.toLowerCase().includes(term)
      );
    }
    
    if (qualityFilter !== 'all') {
      filtered = filtered.filter(item => item.overallLevel === qualityFilter);
    }
    
    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'score':
          return b.overallScore - a.overallScore;
        case 'issues':
          return b.totalIssues - a.totalIssues;
        case 'name':
          return (a.species.name || '').localeCompare(b.species.name || '');
        case 'completion':
          return (b.completedFields / b.totalFields) - (a.completedFields / a.totalFields);
        default:
          return 0;
      }
    });
    
    return filtered;
  }, [speciesAnalysis, searchTerm, qualityFilter, sortBy]);

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="text-center">Loading detailed species analysis...</div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Detailed Species Data Analysis</h2>
          <p className="text-muted-foreground">
            Comprehensive field-by-field analysis of species data quality
          </p>
        </div>
        <Badge variant="outline" className="text-lg px-3 py-1">
          {speciesAnalysis.length} Species Analyzed
        </Badge>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="species-list">Species Analysis</TabsTrigger>
          <TabsTrigger value="field-breakdown">Field Breakdown</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="duplicates">Duplicate Photos</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Quality Distribution */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {['excellent', 'good', 'fair', 'poor'].map(level => {
              const count = speciesAnalysis.filter(s => s.overallLevel === level).length;
              const percentage = Math.round((count / speciesAnalysis.length) * 100);
              const color = level === 'excellent' ? 'text-green-600' :
                           level === 'good' ? 'text-blue-600' :
                           level === 'fair' ? 'text-yellow-600' : 'text-red-600';
              const bgColor = level === 'excellent' ? 'bg-green-50 border-green-200' :
                             level === 'good' ? 'bg-blue-50 border-blue-200' :
                             level === 'fair' ? 'bg-yellow-50 border-yellow-200' : 'bg-red-50 border-red-200';

              return (
                <Card key={level} className={`${bgColor} cursor-pointer hover:shadow-md transition-shadow`}
                      onClick={() => setQualityFilter(level)}>
                  <CardHeader className="pb-2">
                    <CardTitle className={`text-sm font-medium ${color} capitalize`}>
                      {level} Quality
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className={`text-2xl font-bold ${color}`}>{count}</div>
                    <div className="text-xs text-muted-foreground">
                      {percentage}% of species
                    </div>
                    <Progress value={percentage} className="mt-2 h-2" />
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Critical Issues Summary */}
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <CardTitle className="text-red-800 flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                Critical Issues Requiring Immediate Attention
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {speciesAnalysis
                  .filter(s => s.criticalIssues > 0)
                  .slice(0, 5)
                  .map(item => (
                    <div key={item.species.id} className="flex items-center justify-between p-3 bg-white rounded border">
                      <div>
                        <div className="font-medium">{item.species.name || 'Unnamed Species'}</div>
                        <div className="text-sm text-muted-foreground">
                          {item.criticalIssues} critical field(s) missing
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="destructive">{item.overallScore}%</Badge>
                        <Button size="sm" variant="outline">
                          <Edit className="w-4 h-4 mr-1" />
                          Fix
                        </Button>
                      </div>
                    </div>
                  ))}
                {speciesAnalysis.filter(s => s.criticalIssues > 0).length > 5 && (
                  <div className="text-center pt-2">
                    <Button variant="ghost" onClick={() => setActiveTab('species-list')}>
                      View all {speciesAnalysis.filter(s => s.criticalIssues > 0).length} species with critical issues
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Field Completion Overview */}
          <Card>
            <CardHeader>
              <CardTitle>Field Completion Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {FIELD_DEFINITIONS.map(fieldDef => {
                  const fieldAnalyses = speciesAnalysis.map(s =>
                    s.fieldAnalyses.find(f => f.field === fieldDef.field)
                  ).filter(Boolean);

                  const completionRate = Math.round(
                    (fieldAnalyses.filter(f => f?.analysis.level !== 'missing').length / fieldAnalyses.length) * 100
                  );

                  const avgScore = Math.round(
                    fieldAnalyses.reduce((sum, f) => sum + (f?.analysis.score || 0), 0) / fieldAnalyses.length
                  );

                  const Icon = fieldDef.icon;
                  const priorityColor = fieldDef.priority === 'critical' ? 'text-red-600' :
                                      fieldDef.priority === 'high' ? 'text-orange-600' :
                                      fieldDef.priority === 'medium' ? 'text-yellow-600' : 'text-gray-600';

                  return (
                    <div key={fieldDef.field} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex items-center gap-3">
                        <Icon className={`w-5 h-5 ${priorityColor}`} />
                        <div>
                          <div className="font-medium">{fieldDef.displayName}</div>
                          <div className="text-sm text-muted-foreground capitalize">
                            {fieldDef.priority} priority • {fieldDef.category}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className="font-medium">{completionRate}% complete</div>
                          <div className="text-sm text-muted-foreground">Avg quality: {avgScore}%</div>
                        </div>
                        <Progress value={completionRate} className="w-24" />
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="species-list" className="space-y-4">
          {/* Filters and Search */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    <Input
                      placeholder="Search species by name..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={qualityFilter} onValueChange={setQualityFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by quality" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Quality Levels</SelectItem>
                    <SelectItem value="excellent">Excellent</SelectItem>
                    <SelectItem value="good">Good</SelectItem>
                    <SelectItem value="fair">Fair</SelectItem>
                    <SelectItem value="poor">Poor</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="score">Quality Score</SelectItem>
                    <SelectItem value="issues">Issue Count</SelectItem>
                    <SelectItem value="name">Species Name</SelectItem>
                    <SelectItem value="completion">Completion Rate</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Bulk Actions */}
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Checkbox
                      checked={selectedSpecies.size === filteredSpecies.length && filteredSpecies.length > 0}
                      onCheckedChange={handleSelectAll}
                      className="data-[state=checked]:bg-blue-600"
                    />
                    <span className="text-sm font-medium">
                      {selectedSpecies.size > 0
                        ? `${selectedSpecies.size} species selected`
                        : 'Select all species'
                      }
                    </span>
                  </div>
                  {selectedSpecies.size > 0 && (
                    <Badge variant="secondary">
                      {selectedSpecies.size} selected
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {selectedSpecies.size > 0 && (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedSpecies(new Set())}
                      >
                        Clear Selection
                      </Button>
                      <Button
                        onClick={() => {
                          if (selectedSpecies.size > 5) {
                            if (!confirm(`You're about to improve ${selectedSpecies.size} species with AI. This may take several minutes and use API quota. Continue?`)) {
                              return;
                            }
                          }
                          handleAiImproveSpecies(Array.from(selectedSpecies));
                        }}
                        disabled={isAiImproving || selectedSpecies.size === 0}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        {isAiImproving ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Improving with Gemini...
                          </>
                        ) : (
                          <>
                            <Wand2 className="w-4 h-4 mr-2" />
                            AI Improve Selected ({selectedSpecies.size})
                          </>
                        )}
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Species Analysis List */}
          <div className="space-y-4">
            {filteredSpecies.map(item => {
              const qualityColor = item.overallLevel === 'excellent' ? 'border-green-200 bg-green-50' :
                                  item.overallLevel === 'good' ? 'border-blue-200 bg-blue-50' :
                                  item.overallLevel === 'fair' ? 'border-yellow-200 bg-yellow-50' : 'border-red-200 bg-red-50';

              const scoreColor = item.overallScore >= 90 ? 'text-green-600' :
                                item.overallScore >= 75 ? 'text-blue-600' :
                                item.overallScore >= 50 ? 'text-yellow-600' : 'text-red-600';

              return (
                <Card key={item.species.id} className={`${qualityColor} border-l-4`}>
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        <Checkbox
                          checked={selectedSpecies.has(item.species.id)}
                          onCheckedChange={() => handleSelectSpecies(item.species.id)}
                          className="mt-1"
                        />
                        <div className="flex-1">
                          <CardTitle className="text-lg">
                            {item.species.name || 'Unnamed Species'}
                          </CardTitle>
                          <div className="text-sm text-muted-foreground space-y-1">
                            {item.species.common_name && (
                              <div>Common: {item.species.common_name}</div>
                            )}
                            {item.species.scientific_name && (
                              <div>Scientific: <em>{item.species.scientific_name}</em></div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="text-right">
                          <div className={`text-2xl font-bold ${scoreColor}`}>
                            {item.overallScore}%
                          </div>
                          <Badge variant={item.overallLevel === 'excellent' ? 'default' :
                                        item.overallLevel === 'good' ? 'secondary' :
                                        item.overallLevel === 'fair' ? 'outline' : 'destructive'}>
                            {item.overallLevel}
                          </Badge>
                        </div>
                        <div className="flex flex-col gap-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => navigate('/admin/cms')}
                          >
                            <Edit className="w-4 h-4 mr-1" />
                            Edit
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleAiImproveSpecies([item.species.id])}
                            disabled={improvingSpecies.has(item.species.id)}
                            className="bg-blue-50 hover:bg-blue-100 border-blue-200"
                            title={`AI improve missing fields: ${
                              [
                                !item.species.description || item.species.description.length < 50 ? 'description' : null,
                                !item.species.habitat ? 'habitat' : null,
                                !item.species.diet ? 'diet' : null,
                                !item.species.conservation_status ? 'conservation' : null,
                                !item.species.family ? 'family' : null,
                                !item.species.size_description ? 'size' : null,
                                !item.species.weight_g ? 'weight' : null,
                                !item.species.lifespan_years ? 'lifespan' : null,
                                !item.species.behavior ? 'behavior' : null,
                                !item.species.breeding_season ? 'reproduction' : null,
                                'fun facts' // Always include since we can't easily check the fun_facts table here
                              ].filter(Boolean).join(', ') || 'All fields complete'
                            }`}
                          >
                            {improvingSpecies.has(item.species.id) ? (
                              <Loader2 className="w-4 h-4 animate-spin" />
                            ) : (
                              <Wand2 className="w-4 h-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {/* Field Analysis Summary */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span className="text-sm">
                          {item.completedFields}/{item.totalFields} fields completed
                        </span>
                      </div>
                      {item.criticalIssues > 0 && (
                        <div className="flex items-center gap-2">
                          <AlertTriangle className="w-4 h-4 text-red-600" />
                          <span className="text-sm text-red-600">
                            {item.criticalIssues} critical issues
                          </span>
                        </div>
                      )}
                      {item.totalIssues > 0 && (
                        <div className="flex items-center gap-2">
                          <Info className="w-4 h-4 text-yellow-600" />
                          <span className="text-sm text-yellow-600">
                            {item.totalIssues} total issues
                          </span>
                        </div>
                      )}
                    </div>

                    {/* All Issues */}
                    {item.totalIssues > 0 && (
                      <div className="mb-4 space-y-3">
                        {/* Critical Issues */}
                        {item.criticalIssues > 0 && (
                          <div className="p-3 bg-red-100 border border-red-200 rounded">
                            <div className="font-medium text-red-800 mb-2">Critical Issues:</div>
                            <div className="space-y-1">
                              {item.fieldAnalyses
                                .filter(f => f.priority === 'critical' && f.analysis.level === 'missing')
                                .map(field => (
                                  <div key={field.field} className="text-sm text-red-700">
                                    • {field.displayName} is missing
                                  </div>
                                ))}
                            </div>
                          </div>
                        )}

                        {/* All Other Issues */}
                        {item.fieldAnalyses.some(f => f.analysis.issues.length > 0 && !(f.priority === 'critical' && f.analysis.level === 'missing')) && (
                          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded">
                            <div className="font-medium text-yellow-800 mb-2">Other Issues:</div>
                            <div className="space-y-1">
                              {item.fieldAnalyses
                                .filter(f => f.analysis.issues.length > 0 && !(f.priority === 'critical' && f.analysis.level === 'missing'))
                                .map(field =>
                                  field.analysis.issues.map(issue => (
                                    <div key={`${field.field}-${issue}`} className="text-sm text-yellow-700">
                                      • {field.displayName}: {issue}
                                    </div>
                                  ))
                                )}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Field Quality Breakdown */}
                    <div className="space-y-2">
                      <div className="text-sm font-medium">Field Quality Breakdown:</div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {item.fieldAnalyses.map(field => {
                          const Icon = field.icon;
                          const levelColor = field.analysis.level === 'excellent' ? 'text-green-600' :
                                           field.analysis.level === 'good' ? 'text-blue-600' :
                                           field.analysis.level === 'fair' ? 'text-yellow-600' :
                                           field.analysis.level === 'poor' ? 'text-red-600' : 'text-gray-400';

                          return (
                            <div key={field.field} className="flex items-center gap-2 p-2 bg-white rounded border">
                              <Icon className={`w-4 h-4 ${levelColor}`} />
                              <div className="flex-1 min-w-0">
                                <div className="text-xs font-medium truncate">{field.displayName}</div>
                                <div className={`text-xs ${levelColor}`}>
                                  {field.analysis.level === 'missing' ? 'Missing' : `${field.analysis.score}%`}
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {filteredSpecies.length === 0 && (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground">No species match your current filters.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>



        <TabsContent value="field-breakdown" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {FIELD_DEFINITIONS.map(fieldDef => {
              const fieldAnalyses = speciesAnalysis.map(s =>
                s.fieldAnalyses.find(f => f.field === fieldDef.field)
              ).filter(Boolean);

              const completionRate = Math.round(
                (fieldAnalyses.filter(f => f?.analysis.level !== 'missing').length / fieldAnalyses.length) * 100
              );

              const avgScore = Math.round(
                fieldAnalyses.reduce((sum, f) => sum + (f?.analysis.score || 0), 0) / fieldAnalyses.length
              );

              const levelCounts = {
                excellent: fieldAnalyses.filter(f => f?.analysis.level === 'excellent').length,
                good: fieldAnalyses.filter(f => f?.analysis.level === 'good').length,
                fair: fieldAnalyses.filter(f => f?.analysis.level === 'fair').length,
                poor: fieldAnalyses.filter(f => f?.analysis.level === 'poor').length,
                missing: fieldAnalyses.filter(f => f?.analysis.level === 'missing').length
              };

              const Icon = fieldDef.icon;
              const priorityColor = fieldDef.priority === 'critical' ? 'border-red-200 bg-red-50' :
                                  fieldDef.priority === 'high' ? 'border-orange-200 bg-orange-50' :
                                  fieldDef.priority === 'medium' ? 'border-yellow-200 bg-yellow-50' : 'border-gray-200 bg-gray-50';

              return (
                <Card key={fieldDef.field} className={priorityColor}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Icon className="w-5 h-5" />
                      {fieldDef.displayName}
                      <Badge variant="outline" className="ml-auto">
                        {fieldDef.priority} priority
                      </Badge>
                    </CardTitle>
                    <div className="text-sm text-muted-foreground">
                      {fieldDef.category} • {fieldDef.dataType}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Completion Stats */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="text-2xl font-bold">{completionRate}%</div>
                        <div className="text-sm text-muted-foreground">Completion Rate</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold">{avgScore}%</div>
                        <div className="text-sm text-muted-foreground">Average Quality</div>
                      </div>
                    </div>

                    {/* Quality Distribution */}
                    <div className="space-y-2">
                      <div className="text-sm font-medium">Quality Distribution:</div>
                      {Object.entries(levelCounts).map(([level, count]) => {
                        const percentage = Math.round((count / fieldAnalyses.length) * 100);
                        const color = level === 'excellent' ? 'bg-green-500' :
                                     level === 'good' ? 'bg-blue-500' :
                                     level === 'fair' ? 'bg-yellow-500' :
                                     level === 'poor' ? 'bg-red-500' : 'bg-gray-400';

                        return (
                          <div key={level} className="flex items-center justify-between text-sm">
                            <div className="flex items-center gap-2">
                              <div className={`w-3 h-3 rounded ${color}`}></div>
                              <span className="capitalize">{level}</span>
                            </div>
                            <span>{count} ({percentage}%)</span>
                          </div>
                        );
                      })}
                    </div>

                    {/* Common Issues */}
                    {fieldAnalyses.length > 0 && (
                      <div className="space-y-2">
                        <div className="text-sm font-medium">Common Issues:</div>
                        <div className="text-xs space-y-1">
                          {(() => {
                            const issueCount = fieldAnalyses
                              .flatMap(f => f?.analysis.issues || [])
                              .reduce((acc, issue) => {
                                acc[issue] = (acc[issue] || 0) + 1;
                                return acc;
                              }, {} as Record<string, number>);

                            return Object.entries(issueCount)
                              .sort(([,a], [,b]) => b - a)
                              .slice(0, 3)
                              .map(([issue, count]) => (
                                <div key={issue} className="text-muted-foreground">
                                  • {issue} ({count} species)
                                </div>
                              ));
                          })()}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          {/* Priority Actions */}
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <CardTitle className="text-red-800 flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                Immediate Priority Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Critical Missing Fields */}
              <div className="space-y-3">
                <div className="font-medium">Critical Fields Missing:</div>
                {FIELD_DEFINITIONS
                  .filter(f => f.priority === 'critical')
                  .map(fieldDef => {
                    const missingCount = speciesAnalysis.filter(s =>
                      s.fieldAnalyses.find(fa => fa.field === fieldDef.field)?.analysis.level === 'missing'
                    ).length;

                    if (missingCount === 0) return null;

                    return (
                      <div key={fieldDef.field} className="flex items-center justify-between p-3 bg-white rounded border">
                        <div className="flex items-center gap-2">
                          <fieldDef.icon className="w-4 h-4 text-red-600" />
                          <span>{fieldDef.displayName}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="destructive">{missingCount} species</Badge>
                          <Button size="sm" variant="outline">Fix Now</Button>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </CardContent>
          </Card>

          {/* Improvement Opportunities */}
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="text-blue-800 flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Improvement Opportunities
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="font-medium">Quick Wins:</div>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      Add missing conservation statuses (standardized IUCN categories)
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      Standardize category names across all species
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      Expand short descriptions to include more detail
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      Add scientific names for species missing them
                    </li>
                  </ul>
                </div>
                <div className="space-y-3">
                  <div className="font-medium">Long-term Goals:</div>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center gap-2">
                      <Star className="w-4 h-4 text-yellow-600" />
                      Enhance all descriptions to 300+ characters
                    </li>
                    <li className="flex items-center gap-2">
                      <Star className="w-4 h-4 text-yellow-600" />
                      Add comprehensive habitat information
                    </li>
                    <li className="flex items-center gap-2">
                      <Star className="w-4 h-4 text-yellow-600" />
                      Include detailed diet and behavior data
                    </li>
                    <li className="flex items-center gap-2">
                      <Star className="w-4 h-4 text-yellow-600" />
                      Complete taxonomic family information
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Data Quality Targets */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Data Quality Targets
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  Recommended quality targets for a comprehensive wildlife database:
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {[
                    { label: 'Critical Fields', target: '100%', current: Math.round((speciesAnalysis.filter(s => s.criticalIssues === 0).length / speciesAnalysis.length) * 100) },
                    { label: 'Overall Quality', target: '85%', current: Math.round(speciesAnalysis.reduce((sum, s) => sum + s.overallScore, 0) / speciesAnalysis.length) },
                    { label: 'Complete Descriptions', target: '90%', current: Math.round((speciesAnalysis.filter(s => s.fieldAnalyses.find(f => f.field === 'description')?.analysis.level !== 'missing').length / speciesAnalysis.length) * 100) },
                    { label: 'Excellent Species', target: '60%', current: Math.round((speciesAnalysis.filter(s => s.overallLevel === 'excellent').length / speciesAnalysis.length) * 100) }
                  ].map(metric => (
                    <div key={metric.label} className="p-3 border rounded">
                      <div className="text-sm font-medium">{metric.label}</div>
                      <div className="text-2xl font-bold">{metric.current}%</div>
                      <div className="text-sm text-muted-foreground">Target: {metric.target}</div>
                      <Progress value={metric.current} className="mt-2" />
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="duplicates" className="space-y-4">
          <DuplicatePhotoManager />
        </TabsContent>
      </Tabs>
    </div>
  );
};
