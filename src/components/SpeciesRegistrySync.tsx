import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Database } from 'lucide-react';

export function SpeciesRegistrySync() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="w-5 h-5 text-blue-600" />
          Species Registry Sync
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600">
          Synchronization tools for species registry data. This component is currently under development.
        </p>
      </CardContent>
    </Card>
  );
}
