import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, DialogClose } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { Loader2, Save, X } from 'lucide-react';

interface EditSpeciesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  species: {
    id: string;
    name: string;
    category: string;
    conservation_status: string;
    // Extended fields for comprehensive editing
    scientific_name?: string;
    common_name?: string;
    description?: string;
    habitat?: string;
    diet?: string;
    behavior?: string;
    family?: string;
    size_cm?: number;
    weight_g?: number;
    lifespan_years?: number;
    migration_pattern?: string;
    breeding_season?: string;
    threat_level?: string;
    population_trend?: string;
    featured?: boolean;
    published?: boolean;
    regions?: string;
    size_description?: string;
    notes?: string;
  };
  onSave: (updated: any) => void;
}

const CATEGORY_OPTIONS = [
  'Mammals', 'Birds', 'Reptiles', 'Amphibians', 'Fish', 'Invertebrates', 'Plants', 'Other'
];
const STATUS_OPTIONS = [
  'Least Concern', 'Near Threatened', 'Vulnerable', 'Endangered', 'Critically Endangered', 'Extinct in the Wild', 'Extinct'
];
const THREAT_LEVEL_OPTIONS = [
  'Low', 'Medium', 'High', 'Critical'
];
const POPULATION_TREND_OPTIONS = [
  'Increasing', 'Stable', 'Decreasing', 'Unknown'
];

export function EditSpeciesModal({ open, onOpenChange, species, onSave }: EditSpeciesModalProps) {
  const [form, setForm] = useState({
    name: species.name,
    scientific_name: species.scientific_name || '',
    common_name: species.common_name || '',
    category: species.category,
    conservation_status: species.conservation_status,
    description: species.description || '',
    habitat: species.habitat || '',
    diet: species.diet || '',
    behavior: species.behavior || '',
    family: species.family || '',
    size_cm: species.size_cm || '',
    weight_g: species.weight_g || '',
    lifespan_years: species.lifespan_years || '',
    migration_pattern: species.migration_pattern || '',
    breeding_season: species.breeding_season || '',
    threat_level: species.threat_level || '',
    population_trend: species.population_trend || '',
    featured: species.featured || false,
    published: species.published !== false, // Default to true if undefined
    regions: species.regions || '',
    size_description: species.size_description || '',
    notes: species.notes || '',
  });
  const [loading, setLoading] = useState(false);

  // Reset form when species changes
  React.useEffect(() => {
    setForm({
      name: species.name,
      scientific_name: species.scientific_name || '',
      common_name: species.common_name || '',
      category: species.category,
      conservation_status: species.conservation_status,
      description: species.description || '',
      habitat: species.habitat || '',
      diet: species.diet || '',
      behavior: species.behavior || '',
      family: species.family || '',
      size_cm: species.size_cm || '',
      weight_g: species.weight_g || '',
      lifespan_years: species.lifespan_years || '',
      migration_pattern: species.migration_pattern || '',
      breeding_season: species.breeding_season || '',
      threat_level: species.threat_level || '',
      population_trend: species.population_trend || '',
      featured: species.featured || false,
      published: species.published !== false,
      regions: species.regions || '',
      size_description: species.size_description || '',
      notes: species.notes || '',
    });
  }, [species]);

  const handleChange = (field: keyof typeof form, value: string | number | boolean) => {
    setForm((prev) => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    if (!form.name.trim()) {
      toast.error('Species name is required');
      return;
    }

    setLoading(true);
    try {
      // Prepare update data with proper type conversion
      const updateData = {
        name: form.name.trim(),
        scientific_name: form.scientific_name.trim() || null,
        common_name: form.common_name.trim() || null,
        category: form.category,
        conservation_status: form.conservation_status,
        description: form.description.trim() || null,
        habitat: form.habitat.trim() || null,
        diet: form.diet.trim() || null,
        behavior: form.behavior.trim() || null,
        family: form.family.trim() || null,
        size_cm: form.size_cm ? Number(form.size_cm) : null,
        weight_g: form.weight_g ? Number(form.weight_g) : null,
        lifespan_years: form.lifespan_years ? Number(form.lifespan_years) : null,
        migration_pattern: form.migration_pattern.trim() || null,
        breeding_season: form.breeding_season.trim() || null,
        threat_level: form.threat_level || null,
        population_trend: form.population_trend || null,
        featured: form.featured,
        published: form.published,
        regions: form.regions.trim() || null,
        size_description: form.size_description.trim() || null,
        notes: form.notes.trim() || null,
        updated_at: new Date().toISOString(),
      };

      const { error } = await supabase
        .from('species_v2')
        .update(updateData)
        .eq('id', species.id);

      if (error) throw error;

      toast.success('Species updated successfully!');
      onSave({ id: species.id, ...updateData });
      onOpenChange(false);
    } catch (err: unknown) {
      const message = err instanceof Error ? err.message : String(err);
      toast.error('Failed to update species: ' + message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            Edit Species: {species.name}
            <div className="flex gap-2">
              <Badge variant={form.published ? "default" : "secondary"}>
                {form.published ? "Published" : "Draft"}
              </Badge>
              {form.featured && <Badge variant="outline">Featured</Badge>}
            </div>
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="biology">Biology</TabsTrigger>
            <TabsTrigger value="status">Status</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={form.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  placeholder="Species name"
                  disabled={loading}
                />
              </div>
              <div>
                <Label htmlFor="common_name">Common Name</Label>
                <Input
                  id="common_name"
                  value={form.common_name}
                  onChange={(e) => handleChange('common_name', e.target.value)}
                  placeholder="Common name"
                  disabled={loading}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="scientific_name">Scientific Name</Label>
              <Input
                id="scientific_name"
                value={form.scientific_name}
                onChange={(e) => handleChange('scientific_name', e.target.value)}
                placeholder="Scientific name (e.g., Homo sapiens)"
                disabled={loading}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category">Category</Label>
                <Select
                  value={form.category}
                  onValueChange={(v) => handleChange('category', v)}
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {CATEGORY_OPTIONS.map((cat) => (
                      <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="family">Family</Label>
                <Input
                  id="family"
                  value={form.family}
                  onChange={(e) => handleChange('family', e.target.value)}
                  placeholder="Taxonomic family"
                  disabled={loading}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={form.description}
                onChange={(e) => handleChange('description', e.target.value)}
                placeholder="Detailed description of the species..."
                rows={4}
                disabled={loading}
              />
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="published"
                  checked={form.published}
                  onCheckedChange={(checked) => handleChange('published', checked)}
                  disabled={loading}
                />
                <Label htmlFor="published">Published</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="featured"
                  checked={form.featured}
                  onCheckedChange={(checked) => handleChange('featured', checked)}
                  disabled={loading}
                />
                <Label htmlFor="featured">Featured</Label>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="details" className="space-y-4 mt-4">
            <div>
              <Label htmlFor="habitat">Habitat</Label>
              <Textarea
                id="habitat"
                value={form.habitat}
                onChange={(e) => handleChange('habitat', e.target.value)}
                placeholder="Natural habitat and environment..."
                rows={3}
                disabled={loading}
              />
            </div>

            <div>
              <Label htmlFor="diet">Diet</Label>
              <Textarea
                id="diet"
                value={form.diet}
                onChange={(e) => handleChange('diet', e.target.value)}
                placeholder="Feeding habits and diet..."
                rows={3}
                disabled={loading}
              />
            </div>

            <div>
              <Label htmlFor="behavior">Behavior</Label>
              <Textarea
                id="behavior"
                value={form.behavior}
                onChange={(e) => handleChange('behavior', e.target.value)}
                placeholder="Behavioral patterns and characteristics..."
                rows={3}
                disabled={loading}
              />
            </div>

            <div>
              <Label htmlFor="regions">Regions</Label>
              <Input
                id="regions"
                value={form.regions}
                onChange={(e) => handleChange('regions', e.target.value)}
                placeholder="Geographic regions (comma-separated)"
                disabled={loading}
              />
            </div>

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={form.notes}
                onChange={(e) => handleChange('notes', e.target.value)}
                placeholder="Additional notes and observations..."
                rows={3}
                disabled={loading}
              />
            </div>
          </TabsContent>

          <TabsContent value="biology" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="size_cm">Size (cm)</Label>
                <Input
                  id="size_cm"
                  type="number"
                  value={form.size_cm}
                  onChange={(e) => handleChange('size_cm', e.target.value)}
                  placeholder="Length in cm"
                  disabled={loading}
                />
              </div>
              <div>
                <Label htmlFor="weight_g">Weight (g)</Label>
                <Input
                  id="weight_g"
                  type="number"
                  value={form.weight_g}
                  onChange={(e) => handleChange('weight_g', e.target.value)}
                  placeholder="Weight in grams"
                  disabled={loading}
                />
              </div>
              <div>
                <Label htmlFor="lifespan_years">Lifespan (years)</Label>
                <Input
                  id="lifespan_years"
                  type="number"
                  value={form.lifespan_years}
                  onChange={(e) => handleChange('lifespan_years', e.target.value)}
                  placeholder="Average lifespan"
                  disabled={loading}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="size_description">Size Description</Label>
              <Input
                id="size_description"
                value={form.size_description}
                onChange={(e) => handleChange('size_description', e.target.value)}
                placeholder="Descriptive size information"
                disabled={loading}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="migration_pattern">Migration Pattern</Label>
                <Input
                  id="migration_pattern"
                  value={form.migration_pattern}
                  onChange={(e) => handleChange('migration_pattern', e.target.value)}
                  placeholder="Migration behavior"
                  disabled={loading}
                />
              </div>
              <div>
                <Label htmlFor="breeding_season">Breeding Season</Label>
                <Input
                  id="breeding_season"
                  value={form.breeding_season}
                  onChange={(e) => handleChange('breeding_season', e.target.value)}
                  placeholder="Breeding period"
                  disabled={loading}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="status" className="space-y-4 mt-4">
            <div>
              <Label htmlFor="conservation_status">Conservation Status</Label>
              <Select
                value={form.conservation_status}
                onValueChange={(v) => handleChange('conservation_status', v)}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {STATUS_OPTIONS.map((status) => (
                    <SelectItem key={status} value={status}>{status}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="threat_level">Threat Level</Label>
                <Select
                  value={form.threat_level}
                  onValueChange={(v) => handleChange('threat_level', v)}
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select threat level" />
                  </SelectTrigger>
                  <SelectContent>
                    {THREAT_LEVEL_OPTIONS.map((level) => (
                      <SelectItem key={level} value={level}>{level}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="population_trend">Population Trend</Label>
                <Select
                  value={form.population_trend}
                  onValueChange={(v) => handleChange('population_trend', v)}
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select trend" />
                  </SelectTrigger>
                  <SelectContent>
                    {POPULATION_TREND_OPTIONS.map((trend) => (
                      <SelectItem key={trend} value={trend}>{trend}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-6 flex gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={loading || !form.name.trim()}>
            {loading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <Save className="w-4 h-4 mr-2" />}
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}