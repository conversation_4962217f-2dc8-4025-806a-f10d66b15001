import React from 'react';
import { PhotoReviewItem } from '@/hooks/usePhotoReviewData';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Check, X, AlertTriangle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface PhotoReviewGridProps {
  data?: PhotoReviewItem[];
  isLoading?: boolean;
  error?: unknown;
  onAccept: (photo: PhotoReviewItem) => void;
  onReject: (photo: PhotoReviewItem) => void;
}

const ConfidenceBadge: React.FC<{ confidence: number | null }> = ({ confidence }) => {
  if (confidence === null) {
    return null;
  }

  const confidencePercent = Math.round(confidence * 100);
  const badgeColor =
    confidencePercent > 85
      ? 'bg-green-100 text-green-800 border-green-200'
      : confidencePercent > 60
      ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
      : 'bg-red-100 text-red-800 border-red-200';

  return (
    <Badge variant="outline" className={cn('font-semibold', badgeColor)}>
      {confidencePercent}% Confidence
    </Badge>
  );
};

const PhotoCard: React.FC<{ photo: PhotoReviewItem; onAccept: (photo: PhotoReviewItem) => void; onReject: (photo: PhotoReviewItem) => void; }> = ({ photo, onAccept, onReject }) => {
  const [isImageError, setIsImageError] = React.useState(false);
  const fileName = photo.url.split('/').pop() || 'Untitled';

  return (
    <div className="relative group border rounded-lg overflow-hidden bg-white shadow-sm transition-all hover:shadow-md">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="aspect-square w-full bg-gray-50">
              {isImageError ? (
                <div className="w-full h-full flex flex-col items-center justify-center bg-gray-100 text-gray-500">
                  <AlertTriangle className="w-8 h-8 mb-2" />
                  <span className="text-xs">Image not available</span>
                </div>
              ) : (
                <img
                  src={photo.url}
                  alt={`AI suggestion: ${photo.ai_suggested_name}`}
                  className="w-full h-full object-cover"
                  onError={() => setIsImageError(true)}
                />
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-xs">
              <p><strong>File:</strong> {fileName}</p>
              <p><strong>Location:</strong> {photo.location || 'N/A'}</p>
              <p><strong>Date:</strong> {photo.dateTaken ? new Date(photo.dateTaken).toLocaleDateString() : 'N/A'}</p>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <div className="p-3">
        <div className="mb-2">
          <p className="text-xs text-gray-500">AI Suggestion:</p>
          <p className="font-semibold truncate" title={photo.ai_suggested_name || ''}>{photo.ai_suggested_name || 'Unknown'}</p>
          <ConfidenceBadge confidence={photo.ai_confidence} />
        </div>
        
        <div className="mb-3">
          <p className="text-xs text-gray-500">Current Assignment:</p>
          <p className="font-medium text-sm truncate" title={photo.species_name || ''}>
            {photo.species_name || <span className="italic text-gray-400">Unassigned</span>}
          </p>
        </div>
        
        <div className="grid grid-cols-2 gap-2">
          <Button variant="outline" size="sm" className="w-full border-red-500 text-red-500 hover:bg-red-50 hover:text-red-600" onClick={() => onReject(photo)}>
            <X className="w-4 h-4 mr-2" /> Reject
          </Button>
          <Button variant="outline" size="sm" className="w-full border-green-600 text-green-600 hover:bg-green-50 hover:text-green-700" onClick={() => onAccept(photo)}>
            <Check className="w-4 h-4 mr-2" /> Accept
          </Button>
        </div>
      </div>
    </div>
  );
};


export const PhotoReviewGrid: React.FC<PhotoReviewGridProps> = ({ data = [], isLoading, error, onAccept, onReject }) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {Array.from({ length: 10 }).map((_, i) => (
          <div key={i} className="aspect-square bg-gray-100 animate-pulse rounded-lg" />
        ))}
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 font-semibold mb-2">Error loading photos for review.</div>
        <div className="text-sm text-gray-500">Please try refreshing the page or contact support if the problem persists.</div>
      </div>
    );
  }
  
  if (!data.length) {
    return (
      <div className="text-center py-12 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-medium text-gray-900">Queue Clear!</h3>
        <p className="text-sm text-gray-500 mt-1">
          There are no photos with AI suggestions pending review.
        </p>
      </div>
    );
  }
  
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
      {data.map(photo => (
        <PhotoCard key={photo.id} photo={photo} onAccept={onAccept} onReject={onReject} />
      ))}
    </div>
  );
}; 