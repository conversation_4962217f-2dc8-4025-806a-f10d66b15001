import { Link } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Loader2, AlertTriangle } from 'lucide-react';
import { useSpeciesNeedingReview } from '@/hooks/useSpeciesData';
import { type Database } from '@/integrations/supabase/types';

type SpeciesNeedingReview = Database['public']['Views']['species_needing_review']['Row'];

export default function SpeciesNeedingReview() {
  const { data: species, isLoading, error } = useSpeciesNeedingReview();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-6 h-6 animate-spin mr-2" />
        <span>Loading species needing review...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8 text-red-600">
        <AlertTriangle className="w-6 h-6 mr-2" />
        <span>Error loading data: {error.message}</span>
      </div>
    );
  }
  
  if (!species || species.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>All Species Reviewed</CardTitle>
        </CardHeader>
        <CardContent>
          <p>No species currently require data review. Great job!</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Species Needing Data Review
          <Badge variant="secondary" className="ml-2">
            {species.length} species
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Species Name</TableHead>
              <TableHead>Missing Fields</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {species.map((s: SpeciesNeedingReview) => {
              const missingFields = [
                !s.description && 'Description',
                !s.behavior && 'Behavior',
                !s.family && 'Family',
                !s.conservation_actions && 'Conservation',
                !s.size_description && 'Size'
              ].filter(Boolean) as string[];

              return (
                <TableRow key={s.id}>
                  <TableCell className="font-medium">{s.name}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {missingFields.map((field) => (
                        <Badge key={field} variant="destructive" className="text-xs">
                          {field}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Link
                      to={`/species/${s.id}`}
                      className="text-blue-600 hover:underline text-sm font-medium"
                      target="_blank"
                      rel="noreferrer"
                    >
                      View & Edit →
                    </Link>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
} 