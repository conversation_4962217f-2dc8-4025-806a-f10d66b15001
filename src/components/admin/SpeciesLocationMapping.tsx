import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search,
  Plus,
  Trash2,
  MapPin,
  Bird,
  Link,
  Unlink,
  Filter,
  Save,
  X
} from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { LoadingSpinner } from '@/components/public/LoadingSpinner';

interface Species {
  id: string;
  name: string;
  common_name?: string;
  scientific_name?: string;
  category?: string;
  conservation_status?: string;
  photo_count: number;
}

interface Location {
  id: string;
  name: string;
  state_province?: string;
  country?: string;
  latitude: number;
  longitude: number;
  habitat_types?: string[];
}

interface SpeciesLocationMapping {
  id: string;
  species_id: string;
  location_id: string;
  abundance: string;
  seasonal_presence: string[];
  breeding_status: string;
  best_months: number[];
  notes?: string;
  confidence_level: string;
  observation_count: number;
  species_name: string;
  location_name: string;
}

interface MappingFormData {
  species_id: string;
  location_id: string;
  abundance: string;
  seasonal_presence: string[];
  breeding_status: string;
  best_months: number[];
  notes: string;
  confidence_level: string;
  observation_count: number;
}

const initialFormData: MappingFormData = {
  species_id: '',
  location_id: '',
  abundance: 'common',
  seasonal_presence: [],
  breeding_status: 'non_breeding',
  best_months: [],
  notes: '',
  confidence_level: 'medium',
  observation_count: 0
};

export default function SpeciesLocationMapping() {
  const [unassignedSpecies, setUnassignedSpecies] = useState<Species[]>([]);
  const [locationsWithoutSpecies, setLocationsWithoutSpecies] = useState<Location[]>([]);
  const [existingMappings, setExistingMappings] = useState<SpeciesLocationMapping[]>([]);
  const [allSpecies, setAllSpecies] = useState<Species[]>([]);
  const [allLocations, setAllLocations] = useState<Location[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState<MappingFormData>(initialFormData);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('');

  const abundanceOptions = ['rare', 'uncommon', 'common', 'abundant', 'very_common'];
  const breedingStatusOptions = ['non_breeding', 'possible', 'probable', 'confirmed'];
  const confidenceLevels = ['low', 'medium', 'high', 'confirmed'];
  const seasonOptions = ['spring', 'summer', 'fall', 'winter'];
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Fetch unassigned species
      const { data: unassigned, error: unassignedError } = await supabase
        .from('unassigned_species')
        .select('*')
        .order('name');
      
      if (unassignedError) throw unassignedError;
      setUnassignedSpecies(unassigned || []);

      // Fetch locations without species
      const { data: locationsWithout, error: locationsError } = await supabase
        .from('locations_without_species')
        .select('*')
        .order('name');
      
      if (locationsError) throw locationsError;
      setLocationsWithoutSpecies(locationsWithout || []);

      // Fetch existing mappings
      const { data: mappings, error: mappingsError } = await supabase
        .from('species_location_details')
        .select('*')
        .order('species_name');
      
      if (mappingsError) throw mappingsError;
      setExistingMappings(mappings || []);

      // Fetch all species for dropdown
      const { data: species, error: speciesError } = await supabase
        .from('species_v2')
        .select('id, name, common_name, scientific_name, category, conservation_status')
        .eq('published', true)
        .order('name');
      
      if (speciesError) throw speciesError;
      setAllSpecies(species || []);

      // Fetch all locations for dropdown
      const { data: locations, error: locationsAllError } = await supabase
        .from('locations')
        .select('id, name, state_province, country, latitude, longitude, habitat_types')
        .eq('published', true)
        .order('name');
      
      if (locationsAllError) throw locationsAllError;
      setAllLocations(locations || []);

    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateMapping = async () => {
    try {
      const { error } = await supabase
        .from('species_locations')
        .insert({
          species_id: formData.species_id,
          location_id: formData.location_id,
          abundance: formData.abundance,
          seasonal_presence: formData.seasonal_presence,
          breeding_status: formData.breeding_status,
          best_months: formData.best_months,
          notes: formData.notes,
          confidence_level: formData.confidence_level,
          observation_count: formData.observation_count
        });

      if (error) throw error;
      
      setShowForm(false);
      setFormData(initialFormData);
      await fetchData();
    } catch (error) {
      console.error('Error creating mapping:', error);
      alert('Failed to create species-location mapping');
    }
  };

  const handleDeleteMapping = async (speciesId: string, locationId: string) => {
    if (!window.confirm('Are you sure you want to remove this species-location mapping?')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('species_locations')
        .delete()
        .eq('species_id', speciesId)
        .eq('location_id', locationId);

      if (error) throw error;
      await fetchData();
    } catch (error) {
      console.error('Error deleting mapping:', error);
      alert('Failed to delete mapping');
    }
  };

  const updateFormData = (field: keyof MappingFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const toggleArrayItem = (field: 'seasonal_presence' | 'best_months', item: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].includes(item)
        ? prev[field].filter(i => i !== item)
        : [...prev[field], item]
    }));
  };

  const filteredMappings = existingMappings.filter(mapping => {
    const matchesSearch = !searchTerm || 
      mapping.species_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mapping.location_name.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesSearch;
  });

  if (loading) return <LoadingSpinner />;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Species-Location Mapping</h2>
          <p className="text-gray-600 mt-1">
            Manage which species are found at each location with abundance and seasonal data
          </p>
        </div>
        <Button onClick={() => setShowForm(true)} disabled={showForm}>
          <Plus className="w-4 h-4 mr-2" />
          Add Mapping
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{existingMappings.length}</div>
            <div className="text-sm text-gray-600">Total Mappings</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{unassignedSpecies.length}</div>
            <div className="text-sm text-gray-600">Unassigned Species</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{locationsWithoutSpecies.length}</div>
            <div className="text-sm text-gray-600">Locations Without Species</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {new Set(existingMappings.map(m => m.location_id)).size}
            </div>
            <div className="text-sm text-gray-600">Locations with Species</div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="mappings" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="mappings">Current Mappings ({existingMappings.length})</TabsTrigger>
          <TabsTrigger value="unassigned">Unassigned Species ({unassignedSpecies.length})</TabsTrigger>
          <TabsTrigger value="empty-locations">Empty Locations ({locationsWithoutSpecies.length})</TabsTrigger>
        </TabsList>

        {/* Current Mappings Tab */}
        <TabsContent value="mappings" className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search species or locations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredMappings.map((mapping) => (
              <Card key={`${mapping.species_id}-${mapping.location_id}`} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900 line-clamp-1">
                          {mapping.species_name}
                        </h4>
                        <p className="text-sm text-gray-600 line-clamp-1">
                          📍 {mapping.location_name}
                        </p>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeleteMapping(mapping.species_id, mapping.location_id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>

                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {mapping.abundance}
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {mapping.breeding_status}
                      </Badge>
                    </div>

                    {mapping.seasonal_presence && mapping.seasonal_presence.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {mapping.seasonal_presence.map((season) => (
                          <Badge key={season} variant="outline" className="text-xs">
                            {season}
                          </Badge>
                        ))}
                      </div>
                    )}

                    {mapping.observation_count > 0 && (
                      <p className="text-xs text-gray-500">
                        {mapping.observation_count} observations
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Unassigned Species Tab */}
        <TabsContent value="unassigned" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {unassignedSpecies.map((species) => (
              <Card key={species.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-semibold text-gray-900 line-clamp-1">
                        {species.name}
                      </h4>
                      {species.common_name && species.common_name !== species.name && (
                        <p className="text-sm text-gray-600 line-clamp-1">
                          {species.common_name}
                        </p>
                      )}
                      {species.scientific_name && (
                        <p className="text-sm italic text-gray-500 line-clamp-1">
                          {species.scientific_name}
                        </p>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      {species.category && (
                        <Badge variant="outline" className="text-xs">
                          {species.category}
                        </Badge>
                      )}
                      {species.photo_count > 0 && (
                        <Badge variant="secondary" className="text-xs">
                          {species.photo_count} photos
                        </Badge>
                      )}
                    </div>

                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        setFormData(prev => ({ ...prev, species_id: species.id }));
                        setShowForm(true);
                      }}
                      className="w-full"
                    >
                      <Link className="w-3 h-3 mr-1" />
                      Assign to Location
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Empty Locations Tab */}
        <TabsContent value="empty-locations" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {locationsWithoutSpecies.map((location) => (
              <Card key={location.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-semibold text-gray-900 line-clamp-1">
                        {location.name}
                      </h4>
                      <p className="text-sm text-gray-600 line-clamp-1">
                        📍 {location.state_province && location.country
                          ? `${location.state_province}, ${location.country}`
                          : location.country || 'Location not specified'
                        }
                      </p>
                    </div>

                    {location.habitat_types && location.habitat_types.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {location.habitat_types.slice(0, 3).map((habitat, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {habitat.replace('_', ' ')}
                          </Badge>
                        ))}
                        {location.habitat_types.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{location.habitat_types.length - 3} more
                          </Badge>
                        )}
                      </div>
                    )}

                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        setFormData(prev => ({ ...prev, location_id: location.id }));
                        setShowForm(true);
                      }}
                      className="w-full"
                    >
                      <Link className="w-3 h-3 mr-1" />
                      Add Species
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Mapping Form Modal */}
      {showForm && (
        <Card className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Create Species-Location Mapping
                <Button variant="ghost" size="sm" onClick={() => setShowForm(false)}>
                  <X className="w-4 h-4" />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Species Selection */}
                <div>
                  <Label htmlFor="species">Species *</Label>
                  <Select value={formData.species_id} onValueChange={(value) => updateFormData('species_id', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select species" />
                    </SelectTrigger>
                    <SelectContent>
                      {allSpecies.map((species) => (
                        <SelectItem key={species.id} value={species.id}>
                          {species.name} {species.common_name && `(${species.common_name})`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Location Selection */}
                <div>
                  <Label htmlFor="location">Location *</Label>
                  <Select value={formData.location_id} onValueChange={(value) => updateFormData('location_id', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select location" />
                    </SelectTrigger>
                    <SelectContent>
                      {allLocations.map((location) => (
                        <SelectItem key={location.id} value={location.id}>
                          {location.name} ({location.state_province}, {location.country})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Abundance */}
                <div>
                  <Label htmlFor="abundance">Abundance *</Label>
                  <Select value={formData.abundance} onValueChange={(value) => updateFormData('abundance', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select abundance" />
                    </SelectTrigger>
                    <SelectContent>
                      {abundanceOptions.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option.replace('_', ' ')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Breeding Status */}
                <div>
                  <Label htmlFor="breeding">Breeding Status</Label>
                  <Select value={formData.breeding_status} onValueChange={(value) => updateFormData('breeding_status', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select breeding status" />
                    </SelectTrigger>
                    <SelectContent>
                      {breedingStatusOptions.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option.replace('_', ' ')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Seasonal Presence */}
              <div>
                <Label>Seasonal Presence</Label>
                <div className="flex gap-2 mt-2">
                  {seasonOptions.map((season) => (
                    <div key={season} className="flex items-center space-x-2">
                      <Checkbox
                        id={`season-${season}`}
                        checked={formData.seasonal_presence.includes(season)}
                        onCheckedChange={() => toggleArrayItem('seasonal_presence', season)}
                      />
                      <label htmlFor={`season-${season}`} className="text-sm capitalize">
                        {season}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Best Months */}
              <div>
                <Label>Best Months for Observation</Label>
                <div className="grid grid-cols-6 gap-2 mt-2">
                  {monthNames.map((month, index) => (
                    <div key={month} className="flex items-center space-x-1">
                      <Checkbox
                        id={`month-${index + 1}`}
                        checked={formData.best_months.includes(index + 1)}
                        onCheckedChange={() => toggleArrayItem('best_months', index + 1)}
                      />
                      <label htmlFor={`month-${index + 1}`} className="text-xs">
                        {month}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Additional Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="confidence">Confidence Level</Label>
                  <Select value={formData.confidence_level} onValueChange={(value) => updateFormData('confidence_level', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select confidence" />
                    </SelectTrigger>
                    <SelectContent>
                      {confidenceLevels.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="observations">Observation Count</Label>
                  <Input
                    id="observations"
                    type="number"
                    min="0"
                    value={formData.observation_count}
                    onChange={(e) => updateFormData('observation_count', parseInt(e.target.value) || 0)}
                  />
                </div>
              </div>

              {/* Notes */}
              <div>
                <Label htmlFor="notes">Notes</Label>
                <Input
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => updateFormData('notes', e.target.value)}
                  placeholder="Additional notes about this species at this location..."
                />
              </div>

              {/* Form Actions */}
              <div className="flex items-center gap-2 pt-4 border-t">
                <Button onClick={handleCreateMapping} disabled={!formData.species_id || !formData.location_id}>
                  <Save className="w-4 h-4 mr-2" />
                  Create Mapping
                </Button>
                <Button variant="outline" onClick={() => setShowForm(false)}>
                  Cancel
                </Button>
              </div>
            </CardContent>
          </div>
        </Card>
      )}
    </div>
  );
}
