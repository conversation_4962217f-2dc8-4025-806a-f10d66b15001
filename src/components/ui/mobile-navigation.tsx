import { useState } from "react";
import { Menu, X, Shield } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { NavigationLink } from "@/components/ui/navigation-link";
import { useAdminAuth } from "@/hooks/useAdminAuth";

export function MobileNavigation() {
  const [open, setOpen] = useState(false);
  const { isAdmin } = useAdminAuth();

  const closeSheet = () => setOpen(false);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="sm" className="md:hidden">
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-[300px] sm:w-[400px]">
        <div className="flex flex-col space-y-4 mt-4">
          <div className="flex items-center space-x-2 pb-4 border-b">
            <span className="font-bold text-lg">Fauna Focus</span>
          </div>
          
          {/* Main Navigation */}
          <nav className="flex flex-col space-y-3">
            <NavigationLink to="/" exactMatch>
              <div onClick={closeSheet} className="block w-full text-left">
                🌿 Explore Wildlife
              </div>
            </NavigationLink>
            <NavigationLink to="/photos">
              <div onClick={closeSheet} className="block w-full text-left">
                📸 Photo Manager
              </div>
            </NavigationLink>
            <NavigationLink to="/hotspots">
              <div onClick={closeSheet} className="block w-full text-left">
                📍 Hotspots
              </div>
            </NavigationLink>
          </nav>

          {/* Admin Navigation */}
          {isAdmin && (
            <>
              <div className="border-t pt-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Shield className="w-4 h-4" />
                  <span className="font-semibold">Admin</span>
                </div>
                <nav className="flex flex-col space-y-3 pl-6">
                  <NavigationLink to="/admin/cms">
                    <div onClick={closeSheet} className="block w-full text-left">
                      🎛️ CMS Hub
                    </div>
                  </NavigationLink>
                  <NavigationLink to="/admin/observation-import">
                    <div onClick={closeSheet} className="block w-full text-left">
                      📥 Data Import
                    </div>
                  </NavigationLink>
                  <NavigationLink to="/admin/dashboard">
                    <div onClick={closeSheet} className="block w-full text-left">
                      📊 System Monitor
                    </div>
                  </NavigationLink>
                </nav>
              </div>
            </>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
