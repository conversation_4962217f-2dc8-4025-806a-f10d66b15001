import React from 'react';
import { cn } from '@/lib/utils';

// Screen reader only text component
export const ScreenReaderOnly: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <span className="sr-only">{children}</span>
);

// Focus trap component for modals and dialogs
export const FocusTrap: React.FC<{ 
  children: React.ReactNode;
  enabled?: boolean;
  className?: string;
}> = ({ children, enabled = true, className }) => {
  const trapRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (!enabled || !trapRef.current) return;

    const focusableElements = trapRef.current.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement?.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement?.focus();
          e.preventDefault();
        }
      }
    };

    document.addEventListener('keydown', handleTabKey);
    firstElement?.focus();

    return () => {
      document.removeEventListener('keydown', handleTabKey);
    };
  }, [enabled]);

  return (
    <div ref={trapRef} className={className}>
      {children}
    </div>
  );
};

// Skip link component for keyboard navigation
export const SkipLink: React.FC<{
  href: string;
  children: React.ReactNode;
  className?: string;
}> = ({ href, children, className }) => (
  <a
    href={href}
    className={cn(
      "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50",
      "bg-wildlife-primary text-white px-4 py-2 rounded-md font-medium",
      "focus:outline-none focus:ring-2 focus:ring-wildlife-primary focus:ring-offset-2",
      className
    )}
  >
    {children}
  </a>
);

// Accessible loading indicator
export const LoadingIndicator: React.FC<{
  size?: 'sm' | 'md' | 'lg';
  label?: string;
  className?: string;
}> = ({ size = 'md', label = 'Loading...', className }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  return (
    <div className={cn('flex items-center justify-center', className)}>
      <div
        className={cn(
          'animate-spin rounded-full border-2 border-wildlife-primary border-t-transparent',
          sizeClasses[size]
        )}
        role="status"
        aria-label={label}
      />
      <ScreenReaderOnly>{label}</ScreenReaderOnly>
    </div>
  );
};

// Accessible error message component
export const ErrorMessage: React.FC<{
  children: React.ReactNode;
  className?: string;
  id?: string;
}> = ({ children, className, id }) => (
  <div
    id={id}
    role="alert"
    aria-live="polite"
    className={cn(
      'text-destructive text-sm font-medium flex items-center gap-2',
      className
    )}
  >
    <span aria-hidden="true">⚠️</span>
    {children}
  </div>
);

// Accessible success message component
export const SuccessMessage: React.FC<{
  children: React.ReactNode;
  className?: string;
  id?: string;
}> = ({ children, className, id }) => (
  <div
    id={id}
    role="status"
    aria-live="polite"
    className={cn(
      'text-wildlife-success text-sm font-medium flex items-center gap-2',
      className
    )}
  >
    <span aria-hidden="true">✅</span>
    {children}
  </div>
);

// Accessible form field wrapper
export const FormField: React.FC<{
  label: string;
  children: React.ReactNode;
  error?: string;
  required?: boolean;
  description?: string;
  className?: string;
}> = ({ label, children, error, required, description, className }) => {
  const fieldId = React.useId();
  const errorId = error ? `${fieldId}-error` : undefined;
  const descriptionId = description ? `${fieldId}-description` : undefined;

  return (
    <div className={cn('space-y-2', className)}>
      <label
        htmlFor={fieldId}
        className="text-sm font-medium text-foreground"
      >
        {label}
        {required && (
          <span className="text-destructive ml-1" aria-label="required">
            *
          </span>
        )}
      </label>
      
      {description && (
        <p id={descriptionId} className="text-sm text-muted-foreground">
          {description}
        </p>
      )}
      
      <div>
        {React.cloneElement(children as React.ReactElement, {
          id: fieldId,
          'aria-describedby': [descriptionId, errorId].filter(Boolean).join(' ') || undefined,
          'aria-invalid': error ? 'true' : undefined,
          required
        })}
      </div>
      
      {error && (
        <ErrorMessage id={errorId}>
          {error}
        </ErrorMessage>
      )}
    </div>
  );
};

// Accessible button with loading state
export const AccessibleButton: React.FC<{
  children: React.ReactNode;
  loading?: boolean;
  loadingText?: string;
  disabled?: boolean;
  className?: string;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'default' | 'wildlife' | 'outline' | 'ghost';
}> = ({ 
  children, 
  loading = false, 
  loadingText = 'Loading...', 
  disabled = false,
  className,
  onClick,
  type = 'button',
  variant = 'default'
}) => {
  const isDisabled = disabled || loading;

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={isDisabled}
      aria-disabled={isDisabled}
      className={cn(
        'inline-flex items-center justify-center gap-2 px-4 py-2 rounded-md font-medium',
        'focus:outline-none focus:ring-2 focus:ring-offset-2',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        'transition-colors duration-200',
        {
          'bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-primary': variant === 'default',
          'bg-wildlife-primary text-white hover:bg-wildlife-primary-dark focus:ring-wildlife-primary': variant === 'wildlife',
          'border border-input bg-background hover:bg-accent hover:text-accent-foreground focus:ring-ring': variant === 'outline',
          'hover:bg-accent hover:text-accent-foreground focus:ring-ring': variant === 'ghost',
        },
        className
      )}
    >
      {loading && (
        <LoadingIndicator size="sm" label="" className="mr-2" />
      )}
      <span aria-live="polite">
        {loading ? loadingText : children}
      </span>
    </button>
  );
};

// Accessible modal/dialog wrapper
export const AccessibleModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  className?: string;
}> = ({ isOpen, onClose, title, children, className }) => {
  React.useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
    >
      <div
        className="fixed inset-0 bg-black/50"
        onClick={onClose}
        aria-hidden="true"
      />
      <FocusTrap className={cn(
        'relative bg-background rounded-lg shadow-lg max-w-md w-full mx-4 p-6',
        className
      )}>
        <h2 id="modal-title" className="text-lg font-semibold mb-4">
          {title}
        </h2>
        {children}
      </FocusTrap>
    </div>
  );
};
