import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";

interface NavigationLinkProps {
  to: string;
  children: React.ReactNode;
  className?: string;
  exactMatch?: boolean;
}

export function NavigationLink({ 
  to, 
  children, 
  className = "", 
  exactMatch = false 
}: NavigationLinkProps) {
  const location = useLocation();
  
  const isActive = exactMatch 
    ? location.pathname === to
    : location.pathname.startsWith(to);
  
  return (
    <Link
      to={to}
      className={cn(
        "text-sm font-medium transition-colors hover:text-primary",
        isActive 
          ? "text-black dark:text-white" 
          : "text-muted-foreground",
        className
      )}
    >
      {children}
    </Link>
  );
}
