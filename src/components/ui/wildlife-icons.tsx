import React from 'react';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

// Modern Wildlife Icon System
// Using a combination of Lucide icons and custom SVGs for wildlife-specific needs

interface WildlifeIconProps {
  className?: string;
  size?: number;
}

// Category Icons - Using emojis for better clarity
export const CategoryIcons = {
  Mammal: ({ className, size = 24 }: WildlifeIconProps) => (
    <span className={cn("text-amber-600", className)} style={{ fontSize: size }}>🦌</span>
  ),

  Bird: ({ className, size = 24 }: WildlifeIconProps) => (
    <span className={cn("text-sky-600", className)} style={{ fontSize: size }}>🐦</span>
  ),

  Reptile: ({ className, size = 24 }: WildlifeIconProps) => (
    <span className={cn("text-green-600", className)} style={{ fontSize: size }}>🦎</span>
  ),

  Amphibian: ({ className, size = 24 }: WildlifeIconProps) => (
    <span className={cn("text-emerald-600", className)} style={{ fontSize: size }}>🐸</span>
  ),

  Fish: ({ className, size = 24 }: WildlifeIconProps) => (
    <span className={cn("text-blue-600", className)} style={{ fontSize: size }}>🐟</span>
  ),

  Insect: ({ className, size = 24 }: WildlifeIconProps) => (
    <span className={cn("text-purple-600", className)} style={{ fontSize: size }}>🦋</span>
  ),

  Marine: ({ className, size = 24 }: WildlifeIconProps) => (
    <span className={cn("text-cyan-600", className)} style={{ fontSize: size }}>🐋</span>
  ),

  Plant: ({ className, size = 24 }: WildlifeIconProps) => (
    <span className={cn("text-green-500", className)} style={{ fontSize: size }}>🌿</span>
  )
};

// Action Icons - Modern and intuitive
export const ActionIcons = {
  Upload: ({ className, size = 24 }: WildlifeIconProps) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" className={cn("text-blue-600", className)}>
      <path d="M12 2L8 6h3v8h2V6h3l-4-4z" fill="currentColor"/>
      <path d="M4 18h16v2H4v-2z" fill="currentColor"/>
    </svg>
  ),
  
  Camera: ({ className, size = 24 }: WildlifeIconProps) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" className={cn("text-gray-700", className)}>
      <rect x="2" y="8" width="20" height="12" rx="2" fill="currentColor"/>
      <path d="M7 8V6a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v2" stroke="white" strokeWidth="1.5" fill="none"/>
      <circle cx="12" cy="14" r="3" stroke="white" strokeWidth="1.5" fill="none"/>
    </svg>
  ),
  
  AI: ({ className, size = 24 }: WildlifeIconProps) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" className={cn("text-purple-600", className)}>
      <circle cx="12" cy="12" r="8" fill="currentColor"/>
      <path d="M8 12h8M12 8v8M10 10l4 4M14 10l-4 4" stroke="white" strokeWidth="1.5"/>
    </svg>
  ),
  
  Analytics: ({ className, size = 24 }: WildlifeIconProps) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" className={cn("text-indigo-600", className)}>
      <rect x="3" y="12" width="4" height="8" fill="currentColor"/>
      <rect x="10" y="8" width="4" height="12" fill="currentColor"/>
      <rect x="17" y="4" width="4" height="16" fill="currentColor"/>
    </svg>
  ),
  
  Location: ({ className, size = 24 }: WildlifeIconProps) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" className={cn("text-red-600", className)}>
      <path d="M12 2C8.5 2 6 4.5 6 8c0 5.5 6 12 6 12s6-6.5 6-12c0-3.5-2.5-6-6-6z" fill="currentColor"/>
      <circle cx="12" cy="8" r="2" fill="white"/>
    </svg>
  )
};

// Status Icons - Clear visual hierarchy
export const StatusIcons = {
  Published: ({ className, size = 24 }: WildlifeIconProps) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" className={cn("text-green-600", className)}>
      <circle cx="12" cy="12" r="10" fill="currentColor"/>
      <path d="M8 12l3 3 5-6" stroke="white" strokeWidth="2" fill="none"/>
    </svg>
  ),
  
  Draft: ({ className, size = 24 }: WildlifeIconProps) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" className={cn("text-yellow-600", className)}>
      <circle cx="12" cy="12" r="10" fill="currentColor"/>
      <path d="M12 8v4M12 16h.01" stroke="white" strokeWidth="2"/>
    </svg>
  ),
  
  Review: ({ className, size = 24 }: WildlifeIconProps) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" className={cn("text-orange-600", className)}>
      <circle cx="12" cy="12" r="10" fill="currentColor"/>
      <circle cx="12" cy="12" r="3" fill="white"/>
    </svg>
  )
};

// Conservation Status Icons
export const ConservationIcons = {
  'Least Concern': ({ className, size = 24 }: WildlifeIconProps) => (
    <div className={cn("w-6 h-6 rounded-full bg-green-500", className)} style={{width: size, height: size}} />
  ),
  
  'Near Threatened': ({ className, size = 24 }: WildlifeIconProps) => (
    <div className={cn("w-6 h-6 rounded-full bg-yellow-500", className)} style={{width: size, height: size}} />
  ),
  
  'Vulnerable': ({ className, size = 24 }: WildlifeIconProps) => (
    <div className={cn("w-6 h-6 rounded-full bg-orange-500", className)} style={{width: size, height: size}} />
  ),
  
  'Endangered': ({ className, size = 24 }: WildlifeIconProps) => (
    <div className={cn("w-6 h-6 rounded-full bg-red-500", className)} style={{width: size, height: size}} />
  ),
  
  'Critically Endangered': ({ className, size = 24 }: WildlifeIconProps) => (
    <div className={cn("w-6 h-6 rounded-full bg-red-700", className)} style={{width: size, height: size}} />
  ),
  
  'Extinct': ({ className, size = 24 }: WildlifeIconProps) => (
    <div className={cn("w-6 h-6 rounded-full bg-gray-800", className)} style={{width: size, height: size}} />
  )
};

// Helper function to get category icon
export const getCategoryIcon = (category: string | null) => {
  if (!category) return CategoryIcons.Plant;
  
  const normalizedCategory = category.toLowerCase();
  if (normalizedCategory.includes('mammal')) return CategoryIcons.Mammal;
  if (normalizedCategory.includes('bird')) return CategoryIcons.Bird;
  if (normalizedCategory.includes('reptile')) return CategoryIcons.Reptile;
  if (normalizedCategory.includes('amphibian')) return CategoryIcons.Amphibian;
  if (normalizedCategory.includes('fish')) return CategoryIcons.Fish;
  if (normalizedCategory.includes('insect') || normalizedCategory.includes('arthropod')) return CategoryIcons.Insect;
  if (normalizedCategory.includes('marine')) return CategoryIcons.Marine;
  
  return CategoryIcons.Plant;
};

// Helper function to get conservation status icon
export const getConservationIcon = (status: string | null) => {
  if (!status) return ConservationIcons['Least Concern'];
  
  const normalizedStatus = status.toLowerCase();
  if (normalizedStatus.includes('least concern')) return ConservationIcons['Least Concern'];
  if (normalizedStatus.includes('near threatened')) return ConservationIcons['Near Threatened'];
  if (normalizedStatus.includes('vulnerable')) return ConservationIcons['Vulnerable'];
  if (normalizedStatus.includes('endangered') && !normalizedStatus.includes('critically')) return ConservationIcons['Endangered'];
  if (normalizedStatus.includes('critically endangered')) return ConservationIcons['Critically Endangered'];
  if (normalizedStatus.includes('extinct')) return ConservationIcons['Extinct'];
  
  return ConservationIcons['Least Concern'];
};
