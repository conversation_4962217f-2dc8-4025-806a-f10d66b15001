import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Lightbulb, Sparkles, ChevronDown, ChevronUp } from "lucide-react";

interface FunFactsProps {
  facts: string[];
  title?: string;
}

export function FunFacts({ facts, title = "Fun Facts" }: FunFactsProps) {
  const [showAll, setShowAll] = useState(false);

  if (!facts?.length) return null;

  // Filter out empty/short facts
  const validFacts = facts.filter(fact => fact && fact.trim().length > 15);

  if (!validFacts.length) return null;

  // Show first 4 facts by default, all facts when expanded
  const displayFacts = showAll ? validFacts : validFacts.slice(0, 4);
  const hasMoreFacts = validFacts.length > 4;

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Sparkles className="w-6 h-6 text-amber-500" />
        <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {displayFacts.map((fact, idx) => (
          <Card
            key={idx}
            className="bg-gradient-to-br from-amber-50 to-yellow-50 border-amber-200 hover:shadow-md hover:scale-[1.02] transition-all duration-200"
          >
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center mt-0.5">
                  <Lightbulb className="w-4 h-4 text-amber-600" />
                </div>
                <p className="text-sm text-gray-700 leading-relaxed font-medium">
                  {fact}
                </p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {hasMoreFacts && (
        <div className="flex flex-col items-center gap-2 mt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAll(!showAll)}
            className="flex items-center gap-2"
          >
            {showAll ? (
              <>
                <ChevronUp className="w-4 h-4" />
                Show Less
              </>
            ) : (
              <>
                <ChevronDown className="w-4 h-4" />
                Show {validFacts.length - 4} More Facts
              </>
            )}
          </Button>
          <p className="text-sm text-gray-500 text-center italic">
            Showing {displayFacts.length} of {validFacts.length} fun facts
          </p>
        </div>
      )}
    </div>
  );
}