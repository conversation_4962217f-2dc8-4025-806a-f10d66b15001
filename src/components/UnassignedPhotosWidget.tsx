import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ImageIcon, ArrowRight, AlertCircle } from 'lucide-react';
import { useUnassignedPhotos } from '@/hooks/useUnassignedPhotos';

interface UnassignedPhotosWidgetProps {
  className?: string;
}

export function UnassignedPhotosWidget({ className = '' }: UnassignedPhotosWidgetProps) {
  const { count, loading } = useUnassignedPhotos();

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <ImageIcon className="w-5 h-5" />
            Unassigned Photos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-8 bg-gray-200 rounded w-full"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <ImageIcon className="w-5 h-5" />
          Unassigned Photos
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold flex items-center gap-2">
                {count}
                {count > 0 && (
                  <AlertCircle className="w-5 h-5 text-orange-500" />
                )}
              </div>
              <p className="text-sm text-muted-foreground">
                {count === 0 
                  ? 'All photos are assigned!' 
                  : `${count} photo${count !== 1 ? 's' : ''} need${count === 1 ? 's' : ''} assignment`
                }
              </p>
            </div>
            {count > 0 && (
              <Badge variant="outline" className="text-orange-600 border-orange-200">
                Action needed
              </Badge>
            )}
          </div>

          <Link to="/photo-assignment">
            <Button 
              className="w-full" 
              variant={count > 0 ? "default" : "outline"}
              size="sm"
            >
              {count > 0 ? (
                <>
                  <ImageIcon className="w-4 h-4 mr-2" />
                  Assign {count} Photo{count !== 1 ? 's' : ''}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </>
              ) : (
                <>
                  <ImageIcon className="w-4 h-4 mr-2" />
                  Photo Assignment
                </>
              )}
            </Button>
          </Link>

          {count > 0 && (
            <div className="text-xs text-muted-foreground">
              💡 Tip: Use drag & drop or AI suggestions to quickly assign photos to species
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
