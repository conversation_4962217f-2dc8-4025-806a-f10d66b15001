import { Button } from "@/components/ui/button";

/**
 * MultiSelectFilter - A reusable component for multi-select filtering
 * 
 * Consolidates logic from CategoryFilter and ConservationStatusFilter components.
 * Provides consistent UI for selecting multiple options with toggle, select all, and clear all functionality.
 * 
 * @example
 * ```tsx
 * <MultiSelectFilter
 *   options={['Mammals', 'Birds', 'Reptiles']}
 *   selected={selectedCategories}
 *   onChange={setSelectedCategories}
 *   label="Filter by Category"
 * />
 * ```
 */
interface MultiSelectFilterProps {
  /** Array of available options to select from */
  options: string[];
  /** Currently selected options */
  selected: string[];
  /** Callback when selection changes */
  onChange: (updated: string[]) => void;
  /** Optional label to display above the filter */
  label?: string;
  /** Whether all options should be selected by default */
  defaultAllSelected?: boolean;
  /** Optional color scheme for the filter buttons */
  colorScheme?: 'default' | 'green' | 'red' | 'orange' | 'yellow';
  /** Optional CSS class for the container */
  className?: string;
}

/**
 * Get the appropriate color classes for a filter option based on the color scheme
 */
const getColorClasses = (colorScheme: string, isSelected: boolean, option: string) => {
  if (isSelected) {
    switch (colorScheme) {
      case 'green':
        return 'bg-green-600 hover:bg-green-700 text-white';
      case 'red':
        return 'bg-red-600 hover:bg-red-700 text-white';
      case 'orange':
        return 'bg-orange-500 hover:bg-orange-600 text-white';
      case 'yellow':
        return 'bg-yellow-500 hover:bg-yellow-600 text-black';
      default:
        return 'bg-blue-600 hover:bg-blue-700 text-white';
    }
  } else {
    switch (colorScheme) {
      case 'green':
        return 'border-green-300 text-green-700 hover:bg-green-50';
      case 'red':
        return 'border-red-300 text-red-700 hover:bg-red-50';
      case 'orange':
        return 'border-orange-300 text-orange-700 hover:bg-orange-50';
      case 'yellow':
        return 'border-yellow-300 text-yellow-700 hover:bg-yellow-50';
      default:
        return 'border-gray-300 text-gray-700 hover:bg-gray-50';
    }
  }
};

/**
 * Get color classes for conservation status options
 * This maintains the original color coding from ConservationStatusFilter
 */
const getConservationStatusColor = (status: string, isSelected: boolean) => {
  const statusLower = status.toLowerCase();
  
  if (isSelected) {
    switch (statusLower) {
      case 'critically endangered':
        return 'bg-red-600 hover:bg-red-700 text-white';
      case 'endangered':
        return 'bg-red-500 hover:bg-red-600 text-white';
      case 'vulnerable':
        return 'bg-orange-500 hover:bg-orange-600 text-white';
      case 'near threatened':
        return 'bg-yellow-500 hover:bg-yellow-600 text-black';
      case 'least concern':
        return 'bg-green-500 hover:bg-green-600 text-white';
      default:
        return 'bg-gray-500 hover:bg-gray-600 text-white';
    }
  } else {
    switch (statusLower) {
      case 'critically endangered':
        return 'border-red-600 text-red-700 hover:bg-red-50';
      case 'endangered':
        return 'border-red-500 text-red-600 hover:bg-red-50';
      case 'vulnerable':
        return 'border-orange-500 text-orange-600 hover:bg-orange-50';
      case 'near threatened':
        return 'border-yellow-500 text-yellow-700 hover:bg-yellow-50';
      case 'least concern':
        return 'border-green-500 text-green-600 hover:bg-green-50';
      default:
        return 'border-gray-500 text-gray-600 hover:bg-gray-50';
    }
  }
};

export const MultiSelectFilter = ({ 
  options, 
  selected, 
  onChange, 
  label,
  defaultAllSelected = false,
  colorScheme = 'default',
  className = ''
}: MultiSelectFilterProps) => {
  
  /**
   * Toggle a single option in the selection
   */
  const toggleOption = (option: string) => {
    if (selected.includes(option)) {
      onChange(selected.filter(item => item !== option));
    } else {
      onChange([...selected, option]);
    }
  };

  /**
   * Clear all selected options
   */
  const clearAll = () => {
    onChange([]);
  };

  /**
   * Select all available options
   */
  const selectAll = () => {
    onChange([...options]);
  };

  /**
   * Determine if all options are currently selected
   */
  const allSelected = selected.length === options.length;

  /**
   * Get the appropriate color classes for an option
   */
  const getOptionColorClasses = (option: string, isSelected: boolean) => {
    // Special handling for conservation status colors
    if (colorScheme === 'default' && isConservationStatus(option)) {
      return getConservationStatusColor(option, isSelected);
    }
    
    return getColorClasses(colorScheme, isSelected, option);
  };

  /**
   * Check if an option looks like a conservation status
   */
  const isConservationStatus = (option: string): boolean => {
    const conservationStatuses = [
      'critically endangered', 'endangered', 'vulnerable', 
      'near threatened', 'least concern', 'data deficient'
    ];
    return conservationStatuses.includes(option.toLowerCase());
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 border border-green-200 ${className}`}>
      {label && (
        <h3 className="text-lg font-semibold text-green-900 mb-4">{label}</h3>
      )}
      
      <div className="flex flex-wrap gap-2 mb-4">
        {options.map((option) => (
          <Button
            key={option}
            variant={selected.includes(option) ? "default" : "outline"}
            size="sm"
            onClick={() => toggleOption(option)}
            className={`transition-all duration-200 ${
              getOptionColorClasses(option, selected.includes(option))
            }`}
          >
            {option}
          </Button>
        ))}
      </div>
      
      <div className="flex gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={selectAll}
          className="text-green-600 hover:text-green-700 hover:bg-green-50"
        >
          Select All
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={clearAll}
          className="text-gray-600 hover:text-gray-700 hover:bg-gray-50"
        >
          Clear All
        </Button>
      </div>
    </div>
  );
}; 