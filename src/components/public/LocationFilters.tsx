import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MapPin, ArrowRight, Loader2, Globe, Mountain } from 'lucide-react';
import { locationContentService } from '@/services/locationContentService';
import { usePublicSpecies } from '@/hooks/usePublicWildlifeData';

interface LocationContent {
  name: string;
  description: string;
  highlights: string[];
  speciesCount?: number;
}

interface LocationFiltersProps {
  onLocationSelect?: (location: string) => void;
}

export const LocationFilters: React.FC<LocationFiltersProps> = ({ onLocationSelect }) => {
  const [locationContents, setLocationContents] = useState<LocationContent[]>([]);
  const [loading, setLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);

  // Get species counts for each location
  const { data: allSpecies = [] } = usePublicSpecies({ limit: 10000 });

  useEffect(() => {
    const generateLocationContent = async () => {
      if (isGenerating) return; // Prevent multiple simultaneous generations

      setIsGenerating(true);

      try {
        const locations = locationContentService.getFeaturedLocations();

        // First, set up basic content immediately to prevent flashing
        const basicContents = locations.map(location => ({
          name: location.name,
          description: `Discover the unique wildlife and natural beauty of ${location.name}.`,
          highlights: ['Diverse wildlife', 'Natural habitats', 'Conservation areas'],
          speciesCount: allSpecies.filter(species =>
            species.location &&
            species.location.toLowerCase().includes(location.searchTerm.toLowerCase())
          ).length
        }));

        setLocationContents(basicContents);
        setLoading(false);

        // Then enhance with AI content in the background
        const enhancedContents = await Promise.all(
          locations.map(async (location) => {
            const speciesCount = allSpecies.filter(species =>
              species.location &&
              species.location.toLowerCase().includes(location.searchTerm.toLowerCase())
            ).length;

            return await locationContentService.generateLocationContent(location, speciesCount);
          })
        );

        // Update with enhanced content
        setLocationContents(enhancedContents);
      } catch (error) {
        console.error('Error generating location content:', error);
        // Keep the basic content if AI generation fails
      } finally {
        setIsGenerating(false);
      }
    };

    if (allSpecies.length > 0 && !isGenerating) {
      generateLocationContent();
    }
  }, [allSpecies, isGenerating]);

  const handleLocationClick = (locationName: string) => {
    if (onLocationSelect) {
      onLocationSelect(locationName);
    }
  };

  const getLocationIcon = (locationName: string) => {
    const stateLocations = ['Oregon', 'Nevada'];
    return stateLocations.includes(locationName) ? Mountain : Globe;
  };

  if (loading && locationContents.length === 0) {
    return (
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              🌍 Explore Wildlife by Location
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover amazing species from around the world
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(5)].map((_, index) => (
              <Card key={index} className="animate-pulse border-0 shadow-lg">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-9 h-9 bg-gray-200 rounded-lg" />
                      <div className="w-24 h-5 bg-gray-200 rounded" />
                    </div>
                    <div className="w-16 h-5 bg-gray-200 rounded" />
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="w-full h-4 bg-gray-200 rounded" />
                    <div className="w-3/4 h-4 bg-gray-200 rounded" />
                  </div>
                  <div className="space-y-2">
                    <div className="w-20 h-3 bg-gray-200 rounded" />
                    <div className="flex flex-wrap gap-2">
                      {[...Array(3)].map((_, i) => (
                        <div key={i} className="w-16 h-6 bg-gray-200 rounded" />
                      ))}
                    </div>
                  </div>
                  <div className="w-full h-10 bg-gray-200 rounded" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            🌍 Explore Wildlife by Location
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover amazing species from diverse ecosystems around the world
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {locationContents.map((location) => {
            const IconComponent = getLocationIcon(location.name);
            
            return (
              <Card 
                key={location.name}
                className="group hover:shadow-xl transition-all duration-300 cursor-pointer border-0 shadow-lg hover:scale-105"
                onClick={() => handleLocationClick(location.name)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-emerald-100 rounded-lg group-hover:bg-emerald-200 transition-colors">
                        <IconComponent className="w-5 h-5 text-emerald-600" />
                      </div>
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        {location.name}
                      </CardTitle>
                    </div>
                    {location.speciesCount !== undefined && (
                      <Badge variant="secondary" className="bg-emerald-50 text-emerald-700 border-emerald-200">
                        {location.speciesCount} species
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <p className="text-gray-600 leading-relaxed">
                    {location.description}
                  </p>
                  
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-900">Wildlife Highlights:</h4>
                    <div className="flex flex-wrap gap-2">
                      {location.highlights.map((highlight, index) => (
                        <Badge 
                          key={index}
                          variant="outline" 
                          className="text-xs bg-white border-gray-200 text-gray-700"
                        >
                          {highlight}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <Button 
                    variant="ghost" 
                    className="w-full mt-4 group-hover:bg-emerald-50 group-hover:text-emerald-700 transition-colors"
                  >
                    <MapPin className="w-4 h-4 mr-2" />
                    Explore {location.name} Wildlife
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default LocationFilters;
