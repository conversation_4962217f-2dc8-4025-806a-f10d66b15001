import React from "react";
import { Link } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Camera, MapPin, Star, Eye, Heart, Edit, Trash2 } from "lucide-react";
import { CategoryIcons, ActionIcons, getCategoryIcon, getConservationIcon } from "@/components/ui/wildlife-icons";
import type { PublicSpecies } from "@/hooks/usePublicWildlifeData";
import placeholder from '/placeholder.svg';

interface PublicSpeciesCardProps {
  species: PublicSpecies;
  onViewDetails?: (species: PublicSpecies) => void;
  showFavorite?: boolean;
  isFavorite?: boolean;
  onToggleFavorite?: (speciesId: string) => void;
  showAdminActions?: boolean;
  onEdit?: (species: PublicSpecies) => void;
  onDelete?: (species: PublicSpecies) => void;
}

const getConservationStatusColor = (status: string) => {
  switch (status) {
    case 'Least Concern': return 'bg-green-100 text-green-800 border-green-200';
    case 'Near Threatened': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'Vulnerable': return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'Endangered': return 'bg-red-100 text-red-800 border-red-200';
    case 'Critically Endangered': return 'bg-red-200 text-red-900 border-red-300';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
  const target = e.target as HTMLImageElement;
  target.src = placeholder;
};

export const PublicSpeciesCard: React.FC<PublicSpeciesCardProps> = ({
  species,
  onViewDetails,
  showFavorite = false,
  isFavorite = false,
  onToggleFavorite,
  showAdminActions = false,
  onEdit,
  onDelete
}) => {
  const mainPhoto = species.photos?.[0];
  const photoCount = species.photos?.length || species.photo_count || 0;
  
  // Extract first location from photos
  const primaryLocation = species.photos?.find(p => p.location)?.location?.split(',')[0];

  const handleViewDetails = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    if (onViewDetails) {
      onViewDetails(species);
    }
  };

  const handleToggleFavorite = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    if (onToggleFavorite) {
      onToggleFavorite(species.id);
    }
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    if (onEdit) {
      onEdit(species);
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    if (onDelete) {
      onDelete(species);
    }
  };

  return (
    <div className="relative group">
      <Link to={`/wildlife/${species.id}`} className="block">
        <Card variant="interactive" className="overflow-hidden h-full bg-white border-gray-200 hover:border-green-300 hover:shadow-xl">
          {/* Image Section */}
          <div className="relative h-52 overflow-hidden bg-gradient-to-br from-green-50 to-blue-50">
            {mainPhoto ? (
              <img
                src={mainPhoto.url}
                alt={species.name || 'Species photo'}
                className="w-full h-52 object-cover group-hover:scale-110 transition-transform duration-500"
                onError={handleImageError}
                loading="lazy"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-green-100 to-green-200 flex items-center justify-center">
                <div className="text-center text-green-600">
                  <Camera className="w-12 h-12 mx-auto mb-2" />
                  <p className="text-sm font-medium">No photo available</p>
                </div>
              </div>
            )}
            
            {/* Gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
            
            {/* Top badges */}
            <div className="absolute top-3 left-3 flex flex-col gap-2">
              <Badge variant="secondary" className="bg-white/95 text-gray-800 backdrop-blur-sm border-0 shadow-sm">
                {React.createElement(getCategoryIcon(species.category), { size: 16, className: "mr-2" })}
                {species.category}
              </Badge>
              {species.featured && (
                <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white border-0 shadow-sm">
                  <Star className="w-3 h-3 mr-1 fill-current" />
                  Featured
                </Badge>
              )}
            </div>

            {/* Top right actions */}
            <div className="absolute top-3 right-3 flex flex-col gap-2">
              {showFavorite && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 bg-white/20 backdrop-blur-sm hover:bg-white/30"
                  onClick={handleToggleFavorite}
                >
                  <Heart className={`w-4 h-4 ${isFavorite ? 'fill-red-500 text-red-500' : 'text-white'}`} />
                </Button>
              )}
              <Badge variant="secondary" className="bg-black/50 text-white backdrop-blur-sm">
                <Camera className="w-3 h-3 mr-1" />
                {photoCount}
              </Badge>
            </div>

            {/* Bottom info overlay */}
            <div className="absolute bottom-3 left-3 right-3 flex items-center justify-between">
              {species.conservation_status && (
                <div className="flex items-center gap-2">
                  {React.createElement(getConservationIcon(species.conservation_status), { size: 16 })}
                  <Badge className={`${getConservationStatusColor(species.conservation_status)} backdrop-blur-sm border-0 shadow-sm`}>
                    {species.conservation_status}
                  </Badge>
                </div>
              )}
            </div>
          </div>

          {/* Content Section */}
          <CardContent className="p-4">
            <div className="space-y-3">
              {/* Title */}
              <div>
                <h3 className="font-bold text-lg text-gray-900 line-clamp-1">
                  {species.name}
                </h3>
                {species.common_name && species.common_name !== species.name && (
                  <p className="text-sm text-gray-600 line-clamp-1">
                    {species.common_name}
                  </p>
                )}
                {species.scientific_name && (
                  <p className="text-sm italic text-gray-500 line-clamp-1">
                    {species.scientific_name}
                  </p>
                )}
              </div>

              {/* Description */}
              {species.description && (
                <p className="text-sm text-gray-600 line-clamp-2">
                  {species.description.replace(/AI Facts: \[object Object\]/g, '')}
                </p>
              )}

              {/* Location and Action */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1 text-sm text-gray-500 min-w-0 flex-1">
                  {primaryLocation && (
                    <>
                      <MapPin className="w-4 h-4 flex-shrink-0" />
                      <span className="truncate">{primaryLocation}</span>
                    </>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {showAdminActions && (
                    <>
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-8 w-8 p-0 border-blue-200 hover:bg-blue-50"
                        onClick={handleEdit}
                        title="Edit species"
                      >
                        <Edit className="w-3 h-3 text-blue-600" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-8 w-8 p-0 border-red-200 hover:bg-red-50"
                        onClick={handleDelete}
                        title="Delete species"
                      >
                        <Trash2 className="w-3 h-3 text-red-600" />
                      </Button>
                    </>
                  )}
                  <Button
                    size="sm"
                    className="h-8 px-3 rounded-lg bg-green-600 hover:bg-green-700 text-white flex-shrink-0"
                    onClick={handleViewDetails}
                  >
                    <Eye className="w-3 h-3 mr-1" />
                    View
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </Link>
    </div>
  );
};

export default PublicSpeciesCard;
