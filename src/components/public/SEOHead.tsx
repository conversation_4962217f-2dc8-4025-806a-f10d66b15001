import React from 'react';
import { Helmet } from 'react-helmet-async';
import type { PublicSpecies } from '@/hooks/usePublicWildlifeData';

interface SEOHeadProps {
  title?: string;
  description?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article';
  species?: PublicSpecies;
  structuredData?: object;
}

export const SEOHead: React.FC<SEOHeadProps> = ({
  title = 'Wildlife Explorer - Discover Amazing Species',
  description = 'Explore our collection of stunning wildlife photography and learn about incredible species from around the world.',
  image = '/wildlife-hero.jpg',
  url = window.location.href,
  type = 'website',
  species,
  structuredData
}) => {
  // Generate species-specific metadata
  const speciesTitle = species ? `${species.name} - Wildlife Explorer` : title;
  const speciesDescription = species 
    ? `Learn about ${species.name}${species.common_name ? ` (${species.common_name})` : ''}, a ${species.category?.toLowerCase()} species. ${species.description?.slice(0, 150)}...`
    : description;
  const speciesImage = species?.photos?.[0]?.url || image;

  // Generate structured data for species
  const speciesStructuredData = species ? {
    "@context": "https://schema.org",
    "@type": "Animal",
    "name": species.name,
    "alternateName": species.common_name,
    "scientificName": species.scientific_name,
    "description": species.description,
    "image": species.photos?.map(photo => photo.url) || [],
    "habitat": species.habitat,
    "diet": species.diet,
    "conservationStatus": species.conservation_status,
    "category": species.category,
    "url": url,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": url
    }
  } : null;

  // Default structured data for the website
  const defaultStructuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Wildlife Explorer",
    "description": "Discover amazing wildlife species and their habitats through stunning photography",
    "url": "https://wildlife-explorer.com",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://wildlife-explorer.com/wildlife?search={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };

  const finalStructuredData = structuredData || speciesStructuredData || defaultStructuredData;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{speciesTitle}</title>
      <meta name="description" content={speciesDescription} />
      <meta name="keywords" content={`wildlife, nature, photography, conservation, ${species?.category || 'animals'}, ${species?.name || 'species'}`} />
      <meta name="author" content="Wildlife Explorer" />
      <meta name="robots" content="index, follow" />
      <link rel="canonical" href={url} />

      {/* Open Graph Meta Tags */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={speciesTitle} />
      <meta property="og:description" content={speciesDescription} />
      <meta property="og:image" content={speciesImage} />
      <meta property="og:url" content={url} />
      <meta property="og:site_name" content="Wildlife Explorer" />
      <meta property="og:locale" content="en_US" />

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={speciesTitle} />
      <meta name="twitter:description" content={speciesDescription} />
      <meta name="twitter:image" content={speciesImage} />
      <meta name="twitter:site" content="@WildlifeExplorer" />

      {/* Additional Meta Tags for Mobile */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="theme-color" content="#16a34a" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="Wildlife Explorer" />

      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(finalStructuredData)}
      </script>

      {/* Preconnect to external domains for performance */}
      <link rel="preconnect" href="https://images.unsplash.com" />
      <link rel="preconnect" href="https://cdn.jsdelivr.net" />
      
      {/* DNS Prefetch for better performance */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
    </Helmet>
  );
};

export default SEOHead;
