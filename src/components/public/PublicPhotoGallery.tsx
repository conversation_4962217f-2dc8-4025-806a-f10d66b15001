import React, { useState, useCallback } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import {
  ChevronLeft,
  ChevronRight,
  Share2,
  Camera,
  MapPin,
  Calendar,
  User,
  X,
  Maximize2,
  Info
} from 'lucide-react';
import type { PublicPhoto } from '@/hooks/usePublicWildlifeData';
import placeholder from '/placeholder.svg';

interface PublicPhotoGalleryProps {
  photos: PublicPhoto[];
  viewMode?: 'grid' | 'masonry';
  onPhotoClick?: (photo: PublicPhoto, index: number) => void;
  showSpeciesInfo?: boolean;
  className?: string;
}

interface PhotoLightboxProps {
  photo: PublicPhoto;
  photos: PublicPhoto[];
  currentIndex: number;
  isOpen: boolean;
  onClose: () => void;
  onNext: () => void;
  onPrevious: () => void;
}

const getConservationStatusColor = (status: string) => {
  switch (status) {
    case 'Least Concern': return 'bg-green-100 text-green-800';
    case 'Near Threatened': return 'bg-yellow-100 text-yellow-800';
    case 'Vulnerable': return 'bg-orange-100 text-orange-800';
    case 'Endangered': return 'bg-red-100 text-red-800';
    case 'Critically Endangered': return 'bg-red-200 text-red-900';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
  const target = e.target as HTMLImageElement;
  target.src = placeholder;
};

const PhotoLightbox: React.FC<PhotoLightboxProps> = ({
  photo,
  photos,
  currentIndex,
  isOpen,
  onClose,
  onNext,
  onPrevious
}) => {
  const [showInfo, setShowInfo] = useState(false);



  const handleShare = useCallback(async () => {
    if (navigator.share && photo.url) {
      try {
        await navigator.share({
          title: photo.title || 'Wildlife Photo',
          text: photo.description || 'Check out this amazing wildlife photo!',
          url: photo.url,
        });
      } catch (error) {
        // Fallback to copying URL
        navigator.clipboard.writeText(photo.url);
      }
    } else if (photo.url) {
      navigator.clipboard.writeText(photo.url);
    }
  }, [photo]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[95vh] p-0 overflow-hidden">
        <div className="relative h-full">
          {/* Main Image */}
          <div className="relative h-[80vh] bg-black flex items-center justify-center">
            <img
              src={photo.url}
              alt={photo.title || 'Wildlife photo'}
              className="max-w-full max-h-full object-contain"
              onError={handleImageError}
            />
            
            {/* Navigation Controls */}
            <Button
              variant="ghost"
              size="sm"
              className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 text-white hover:bg-black/70"
              onClick={onPrevious}
              disabled={photos.length <= 1}
            >
              <ChevronLeft className="w-6 h-6" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 text-white hover:bg-black/70"
              onClick={onNext}
              disabled={photos.length <= 1}
            >
              <ChevronRight className="w-6 h-6" />
            </Button>

            {/* Top Controls */}
            <div className="absolute top-4 right-4 flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="bg-black/50 text-white hover:bg-black/70"
                onClick={() => setShowInfo(!showInfo)}
              >
                <Info className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="bg-black/50 text-white hover:bg-black/70"
                onClick={handleShare}
              >
                <Share2 className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="bg-black/50 text-white hover:bg-black/70"
                onClick={onClose}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* Photo Counter */}
            <div className="absolute bottom-4 left-4">
              <Badge variant="secondary" className="bg-black/50 text-white">
                {currentIndex + 1} of {photos.length}
              </Badge>
            </div>
          </div>

          {/* Photo Information Panel */}
          {showInfo && (
            <div className="absolute bottom-0 left-0 right-0 bg-white p-6 max-h-[20vh] overflow-y-auto">
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-lg">{photo.title || 'Untitled'}</h3>
                  {photo.description && (
                    <p className="text-gray-600 mt-1">{photo.description}</p>
                  )}
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  {photo.photographer && (
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4 text-gray-500" />
                      <span>{photo.photographer}</span>
                    </div>
                  )}
                  {photo.location && (
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-gray-500" />
                      <span>{photo.location}</span>
                    </div>
                  )}
                  {photo.camera_settings && (
                    <div className="flex items-center gap-2">
                      <Camera className="w-4 h-4 text-gray-500" />
                      <span>{photo.camera_settings}</span>
                    </div>
                  )}
                  {photo.created_at && (
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-gray-500" />
                      <span>{new Date(photo.created_at).toLocaleDateString()}</span>
                    </div>
                  )}
                </div>

                {photo.species && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">Species:</span>
                    <Badge variant="outline">{photo.species.name}</Badge>
                    <Badge variant="outline">{photo.species.category}</Badge>
                    {photo.species.conservation_status && (
                      <Badge className={getConservationStatusColor(photo.species.conservation_status)}>
                        {photo.species.conservation_status}
                      </Badge>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export const PublicPhotoGallery: React.FC<PublicPhotoGalleryProps> = ({
  photos,
  viewMode = 'grid',
  onPhotoClick,
  showSpeciesInfo = true,
  className = ''
}) => {
  const [selectedPhoto, setSelectedPhoto] = useState<PublicPhoto | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [lightboxOpen, setLightboxOpen] = useState(false);

  const handlePhotoClick = useCallback((photo: PublicPhoto, index: number) => {
    if (onPhotoClick) {
      onPhotoClick(photo, index);
    } else {
      setSelectedPhoto(photo);
      setCurrentIndex(index);
      setLightboxOpen(true);
    }
  }, [onPhotoClick]);

  const handleNext = useCallback(() => {
    const nextIndex = (currentIndex + 1) % photos.length;
    setCurrentIndex(nextIndex);
    setSelectedPhoto(photos[nextIndex]);
  }, [currentIndex, photos]);

  const handlePrevious = useCallback(() => {
    const prevIndex = currentIndex === 0 ? photos.length - 1 : currentIndex - 1;
    setCurrentIndex(prevIndex);
    setSelectedPhoto(photos[prevIndex]);
  }, [currentIndex, photos]);

  if (!photos || photos.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-gray-500">
        <Camera className="w-12 h-12 mb-4" />
        <p className="text-lg font-medium">No photos available</p>
        <p className="text-sm">Check back later for new wildlife photos!</p>
      </div>
    );
  }

  return (
    <>
      <div className={`${className} ${
        viewMode === 'grid' 
          ? 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4' 
          : 'columns-2 sm:columns-3 md:columns-4 lg:columns-6 gap-4'
      }`}>
        {photos.map((photo, index) => (
          <Card
            key={photo.id}
            className={`relative group cursor-pointer overflow-hidden hover:shadow-lg transition-all duration-200 ${
              viewMode === 'masonry' ? 'break-inside-avoid mb-4' : ''
            }`}
            onClick={() => handlePhotoClick(photo, index)}
          >
            <div className="relative">
              <img
                src={photo.url}
                alt={photo.title || 'Wildlife photo'}
                className={`w-full object-cover transition-transform duration-200 group-hover:scale-105 ${
                  viewMode === 'grid' ? 'h-32' : 'h-auto'
                }`}
                onError={handleImageError}
                loading="lazy"
              />
              
              {/* Hover Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-200 flex flex-col justify-between p-2">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                  {showSpeciesInfo && photo.species && (
                    <Badge variant="secondary" className="bg-white/90 text-gray-800 text-xs">
                      {photo.species.name}
                    </Badge>
                  )}
                </div>
                
                <div className="opacity-0 group-hover:opacity-100 transition-opacity text-white text-center">
                  <p className="text-sm font-semibold line-clamp-1">{photo.title || 'Untitled'}</p>
                  {photo.location && (
                    <p className="text-xs line-clamp-1">{photo.location}</p>
                  )}
                  {photo.photographer && (
                    <p className="text-xs line-clamp-1">by {photo.photographer}</p>
                  )}
                </div>
              </div>

              {/* Expand Icon */}
              <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="bg-black/50 rounded-full p-1">
                  <Maximize2 className="w-4 h-4 text-white" />
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Lightbox */}
      {selectedPhoto && (
        <PhotoLightbox
          photo={selectedPhoto}
          photos={photos}
          currentIndex={currentIndex}
          isOpen={lightboxOpen}
          onClose={() => setLightboxOpen(false)}
          onNext={handleNext}
          onPrevious={handlePrevious}
        />
      )}
    </>
  );
};

export default PublicPhotoGallery;
