import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Search,
  Camera,
  Leaf,
  Globe,
  Shield,
  Star,
  ArrowRight,
  Play,
  Users,
  TrendingUp,
  MapPin,
  Eye,
  Sparkles,
  Heart,
  Award
} from 'lucide-react';
import { usePublicSpecies, usePublicPhotos, usePublicMetadata } from '@/hooks/usePublicWildlifeData';
import { PublicSpeciesCard } from './PublicSpeciesCard';
import { PublicPhotoGallery } from './PublicPhotoGallery';
import { LocationFilters } from './LocationFilters';

interface PublicHomepageProps {
  onSearch?: (query: string) => void;
  onViewAllSpecies?: () => void;
  onViewAllPhotos?: () => void;
  onLocationSelect?: (location: string) => void;
}

export const PublicHomepage: React.FC<PublicHomepageProps> = ({
  onSearch,
  onViewAllSpecies,
  onViewAllPhotos,
  onLocationSelect
}) => {
  const [searchQuery, setSearchQuery] = React.useState('');

  // Fetch featured species
  const { data: featuredSpecies = [], isLoading: featuredLoading } = usePublicSpecies({
    featured: true,
    limit: 6
  });

  // Fetch all species for accurate count
  const { data: allSpecies = [] } = usePublicSpecies({
    limit: 10000 // High limit to get all species for accurate count
  });

  // Fetch hero photos for visual impact
  const { data: heroPhotos = [], isLoading: heroLoading } = usePublicPhotos({
    limit: 6,
    sortBy: 'newest'
  });

  // Fetch recent photos
  const { data: recentPhotos = [], isLoading: photosLoading } = usePublicPhotos({
    limit: 8,
    sortBy: 'newest'
  });

  // Fetch all photos for accurate count
  const { data: allPhotos = [] } = usePublicPhotos({
    limit: 10000 // High limit to get all photos for accurate count
  });

  // Fetch metadata for stats
  const { data: metadata } = usePublicMetadata();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch && searchQuery.trim()) {
      onSearch(searchQuery.trim());
    }
  };

  // Calculate accurate counts
  const totalSpecies = allSpecies.length;
  const totalPhotos = allPhotos.length;
  const totalCategories = metadata?.categories.length || 0;
  const totalLocations = (metadata?.countries.length || 0) + (metadata?.regions.length || 0);

  return (
    <div className="min-h-screen bg-black">
      {/* Hero Section with Photo Background */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Photo Background Grid */}
        {!heroLoading && heroPhotos.length > 0 && (
          <div className="absolute inset-0 grid grid-cols-3 gap-1 opacity-40">
            {heroPhotos.slice(0, 6).map((photo, index) => (
              <div key={photo.id} className="relative overflow-hidden">
                <img
                  src={photo.url}
                  alt={photo.species_name || 'Wildlife'}
                  className="w-full h-full object-cover"
                  style={{ height: 'calc(100vh / 2)' }}
                />
                <div className="absolute inset-0 bg-black/60" />
              </div>
            ))}
          </div>
        )}

        {/* Fallback gradient background */}
        {(heroLoading || heroPhotos.length === 0) && (
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-900 via-gray-900 to-blue-900" />
        )}

        {/* Content Overlay */}
        <div className="relative z-10 max-w-6xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <Badge className="bg-white/10 backdrop-blur-sm text-white border-white/20 px-4 py-2 text-sm font-medium mb-6">
              <Camera className="w-4 h-4 mr-2" />
              Wildlife Photography Collection
            </Badge>
            <h1 className="text-4xl sm:text-6xl lg:text-7xl xl:text-8xl font-bold text-white mb-6 leading-tight">
              Nature's
              <span className="block bg-gradient-to-r from-emerald-400 to-blue-400 bg-clip-text text-transparent">
                Masterpieces
              </span>
            </h1>
            <p className="text-xl sm:text-2xl text-gray-200 max-w-4xl mx-auto mb-12 leading-relaxed">
              Stunning wildlife photography from around the globe. Discover, explore, and connect with nature's most incredible moments.
            </p>
          </div>

          {/* Enhanced Search Bar */}
          <form onSubmit={handleSearch} className="max-w-3xl mx-auto mb-16">
            <div className="relative">
              <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-2xl">
                <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 text-white/60 w-6 h-6" />
                <Input
                  type="text"
                  placeholder="Search species, locations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-16 pr-32 h-16 text-lg border-0 rounded-2xl focus:ring-2 focus:ring-emerald-400 bg-transparent text-white placeholder:text-white/60"
                />
                <Button
                  type="submit"
                  className="absolute right-2 top-2 h-12 px-8 rounded-xl bg-emerald-500 hover:bg-emerald-600 text-white font-medium shadow-lg"
                >
                  <span className="hidden sm:inline">Search</span>
                  <Search className="w-4 h-4 sm:hidden" />
                </Button>
              </div>
            </div>
          </form>

          {/* Enhanced Stats Cards */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-5xl mx-auto">
            <Card className="group hover:scale-105 transition-all duration-300 bg-white/10 backdrop-blur-md border-white/20 shadow-2xl hover:shadow-3xl">
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-emerald-500/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform border border-emerald-400/30">
                  <Leaf className="w-8 h-8 text-emerald-400" />
                </div>
                <p className="text-3xl font-bold text-white mb-1">{totalSpecies.toLocaleString()}</p>
                <p className="text-sm text-gray-300 font-medium">Species Documented</p>
              </CardContent>
            </Card>

            <Card className="group hover:scale-105 transition-all duration-300 bg-white/10 backdrop-blur-md border-white/20 shadow-2xl hover:shadow-3xl">
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-blue-500/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform border border-blue-400/30">
                  <Camera className="w-8 h-8 text-blue-400" />
                </div>
                <p className="text-3xl font-bold text-white mb-1">{totalPhotos.toLocaleString()}</p>
                <p className="text-sm text-gray-300 font-medium">Photos Captured</p>
              </CardContent>
            </Card>

            <Card className="group hover:scale-105 transition-all duration-300 bg-white/10 backdrop-blur-md border-white/20 shadow-2xl hover:shadow-3xl">
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-purple-500/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform border border-purple-400/30">
                  <Globe className="w-8 h-8 text-purple-400" />
                </div>
                <p className="text-3xl font-bold text-white mb-1">{totalCategories}</p>
                <p className="text-sm text-gray-300 font-medium">Animal Categories</p>
              </CardContent>
            </Card>

            <Card className="group hover:scale-105 transition-all duration-300 bg-white/10 backdrop-blur-md border-white/20 shadow-2xl hover:shadow-3xl">
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-orange-500/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform border border-orange-400/30">
                  <MapPin className="w-8 h-8 text-orange-400" />
                </div>
                <p className="text-3xl font-bold text-white mb-1">{totalLocations}</p>
                <p className="text-sm text-gray-300 font-medium">Global Locations</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Featured Photography Gallery */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <Badge className="bg-emerald-100 text-emerald-700 border-emerald-200 px-4 py-2 text-sm font-medium mb-6">
              <Camera className="w-4 h-4 mr-2" />
              Featured Gallery
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Latest Captures
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Stunning wildlife photography showcasing nature's most incredible moments and species from around the world.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={onViewAllPhotos}
                className="bg-gray-900 hover:bg-black text-white px-8 py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all"
              >
                <Camera className="w-5 h-5 mr-2" />
                View Full Gallery
              </Button>
              <Button
                onClick={onViewAllSpecies}
                variant="outline"
                className="border-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white px-8 py-3 rounded-xl font-medium transition-all"
              >
                Explore Species
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
          </div>

          {featuredLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="animate-pulse border-0 shadow-lg">
                  <div className="h-64 bg-gradient-to-br from-gray-200 to-gray-300 rounded-t-xl" />
                  <CardContent className="p-6">
                    <div className="h-6 bg-gray-200 rounded-lg mb-3" />
                    <div className="h-4 bg-gray-200 rounded-lg w-2/3 mb-2" />
                    <div className="h-4 bg-gray-200 rounded-lg w-1/2" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : recentPhotos.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {recentPhotos.slice(0, 8).map((photo, index) => (
                <div
                  key={photo.id}
                  className="group relative aspect-square overflow-hidden rounded-xl bg-gray-100 hover:scale-105 transition-all duration-300 cursor-pointer shadow-lg hover:shadow-2xl"
                >
                  <img
                    src={photo.url}
                    alt={photo.species_name || 'Wildlife'}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <div className="absolute bottom-0 left-0 right-0 p-4 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                    <h3 className="font-semibold text-sm mb-1">{photo.species_name || 'Wildlife'}</h3>
                    {photo.photographer && (
                      <p className="text-xs text-gray-300">by {photo.photographer}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Camera className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-700 mb-2">No Photos Yet</h3>
              <p className="text-gray-500">Be the first to share your wildlife photography!</p>
            </div>
          )}
        </div>
      </section>

      {/* Recent Photos Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-900 via-gray-800 to-black">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <Badge className="bg-white/10 backdrop-blur-sm text-white border-white/20 px-4 py-2 text-sm font-medium mb-6">
              <Camera className="w-4 h-4 mr-2" />
              Photography Collection
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Captured Moments
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              Professional wildlife photography showcasing nature's most incredible moments from photographers around the world.
            </p>
            <Button
              onClick={onViewAllPhotos}
              className="bg-white text-gray-900 hover:bg-gray-100 px-8 py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all"
            >
              <Camera className="w-5 h-5 mr-2" />
              Explore Gallery
            </Button>
          </div>

          {photosLoading ? (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {[...Array(10)].map((_, i) => (
                <div key={i} className="aspect-square bg-white/10 rounded-xl animate-pulse" />
              ))}
            </div>
          ) : recentPhotos.length > 0 ? (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {recentPhotos.slice(0, 10).map((photo, index) => (
                <div
                  key={photo.id}
                  className="group relative aspect-square overflow-hidden rounded-xl bg-gray-800 hover:scale-105 transition-all duration-300 cursor-pointer shadow-xl hover:shadow-2xl"
                >
                  <img
                    src={photo.url}
                    alt={photo.species_name || 'Wildlife'}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <div className="absolute bottom-0 left-0 right-0 p-3 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                    <h3 className="font-semibold text-sm mb-1 truncate">{photo.species_name || 'Wildlife'}</h3>
                    {photo.photographer && (
                      <p className="text-xs text-gray-300 truncate">by {photo.photographer}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Camera className="w-12 h-12 text-white/60" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">No Photos Yet</h3>
              <p className="text-gray-400">Be the first to share your wildlife photography!</p>
            </div>
          )}
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <Badge className="bg-gray-100 text-gray-700 border-gray-200 px-4 py-2 text-sm font-medium mb-6">
              <Globe className="w-4 h-4 mr-2" />
              Wildlife Categories
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Discover by Species
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Explore the incredible diversity of life through our curated wildlife categories
            </p>
          </div>

          {metadata?.categories && metadata.categories.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
              {metadata.categories.map((category) => (
                <Link
                  key={category.category}
                  to={`/wildlife?category=${encodeURIComponent(category.category)}&tab=species`}
                  className="group"
                >
                  <Card className="hover:shadow-xl transition-all duration-300 group-hover:scale-105 border border-gray-200 shadow-sm bg-white overflow-hidden hover:border-gray-300">
                    <CardContent className="p-6 text-center relative">
                      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-white opacity-0 group-hover:opacity-100 transition-opacity" />
                      <div className="relative">
                        <div className="text-4xl mb-4 transform group-hover:scale-110 transition-transform">
                          {category.category === 'Birds' && '🦅'}
                          {category.category === 'Mammals' && '🦌'}
                          {category.category === 'Reptiles' && '🦎'}
                          {category.category === 'Amphibians' && '🐸'}
                          {category.category === 'Fish' && '🐟'}
                          {category.category === 'Insects' && '🦋'}
                          {category.category === 'Marine Life' && '🐠'}
                          {category.category === 'Arachnids' && '🕷️'}
                          {category.category === 'Mollusks' && '🐚'}
                          {!['Birds', 'Mammals', 'Reptiles', 'Amphibians', 'Fish', 'Insects', 'Marine Life', 'Arachnids', 'Mollusks'].includes(category.category) && '🌿'}
                        </div>
                        <h3 className="font-semibold text-lg text-gray-900 mb-2">
                          {category.category}
                        </h3>
                        <p className="text-sm text-gray-500 font-medium">
                          {category.count} species
                        </p>
                        <div className="mt-3">
                          <ArrowRight className="w-4 h-4 text-gray-400 mx-auto opacity-0 group-hover:opacity-100 transition-opacity" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Globe className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-700 mb-2">Loading Categories</h3>
              <p className="text-gray-500">Discovering wildlife categories...</p>
            </div>
          )}
        </div>
      </section>

      {/* Location Filters */}
      <LocationFilters onLocationSelect={onLocationSelect} />

      {/* Call to Action */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-gray-900 to-black relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-96 h-96 bg-emerald-500/10 rounded-full blur-3xl" />
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl" />
        </div>

        <div className="relative max-w-4xl mx-auto text-center text-white">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
            Begin Your Journey
          </h2>

          <p className="text-xl mb-10 text-gray-300 max-w-2xl mx-auto">
            Immerse yourself in stunning wildlife photography and discover nature's most incredible stories
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              size="lg"
              onClick={onViewAllSpecies}
              className="bg-white text-gray-900 hover:bg-gray-100 px-8 py-3 rounded-xl font-semibold shadow-xl hover:shadow-2xl transition-all"
            >
              <Eye className="w-5 h-5 mr-2" />
              Explore Wildlife
            </Button>
            <Button
              size="lg"
              onClick={onViewAllPhotos}
              variant="outline"
              className="border-2 border-white/30 text-white hover:bg-white/10 hover:border-white/50 px-8 py-3 rounded-xl font-semibold transition-all backdrop-blur-sm"
            >
              <Camera className="w-5 h-5 mr-2" />
              View Gallery
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PublicHomepage;
