import React, { useMemo } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { FunFacts } from '@/components/ui/FunFacts';

import {
  ArrowLeft,
  MapPin,
  Calendar,
  Ruler,
  Weight,
  Heart,
  Clock,
  Leaf,
  Shield,
  Info,
  Camera,
  Share2
} from 'lucide-react';
import type { PublicSpecies } from '@/hooks/usePublicWildlifeData';
import { PublicPhotoGallery } from './PublicPhotoGallery';

interface PublicSpeciesDetailProps {
  species: PublicSpecies;
  onBack?: () => void;
  showFavorite?: boolean;
  isFavorite?: boolean;
  onToggleFavorite?: (speciesId: string) => void;
  onFilterByCategory?: (category: string) => void;
  onFilterByConservationStatus?: (status: string) => void;
}

const getConservationStatusColor = (status: string) => {
  switch (status) {
    case 'Least Concern': return 'bg-green-100 text-green-800 border-green-200';
    case 'Near Threatened': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'Vulnerable': return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'Endangered': return 'bg-red-100 text-red-800 border-red-200';
    case 'Critically Endangered': return 'bg-red-200 text-red-900 border-red-300';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getCategoryIcon = (category: string) => {
  switch (category?.toLowerCase()) {
    case 'birds': return '🦅';
    case 'mammals': return '🦌';
    case 'reptiles': return '🦎';
    case 'amphibians': return '🐸';
    case 'fish': return '🐟';
    case 'insects': return '🦋';
    case 'marine life': return '🐠';
    default: return '🌿';
  }
};

const formatSize = (sizeCm: number | null) => {
  if (!sizeCm) return null;
  if (sizeCm >= 100) {
    return `${(sizeCm / 100).toFixed(1)}m`;
  }
  return `${sizeCm}cm`;
};

const formatWeight = (weightG: number | null) => {
  if (!weightG) return null;
  if (weightG >= 1000) {
    return `${(weightG / 1000).toFixed(1)}kg`;
  }
  return `${weightG}g`;
};

export const PublicSpeciesDetail: React.FC<PublicSpeciesDetailProps> = ({
  species,
  onBack,
  showFavorite = false,
  isFavorite = false,
  onToggleFavorite,
  onFilterByCategory,
  onFilterByConservationStatus
}) => {
  const mainPhoto = species.photos?.[0];
  
  // Use fun facts from the dedicated table if available, otherwise fallback to JSONB fields
  const funFacts = useMemo(() => {
    // If we have fun_facts from the dedicated table, use those
    if (species.fun_facts && Array.isArray(species.fun_facts)) {
      return species.fun_facts.filter(fact => fact && fact.trim().length > 0);
    }

    // Fallback to extracting from JSONB fields (for backward compatibility)
    const facts: string[] = [];

    if (species.ai_fun_facts) {
      if (Array.isArray(species.ai_fun_facts)) {
        facts.push(...species.ai_fun_facts);
      } else if (typeof species.ai_fun_facts === 'string') {
        facts.push(species.ai_fun_facts);
      }
    }

    if (species.fun_facts_field) {
      if (Array.isArray(species.fun_facts_field)) {
        facts.push(...species.fun_facts_field);
      } else if (typeof species.fun_facts_field === 'string') {
        facts.push(species.fun_facts_field);
      }
    }

    return facts.filter(fact => fact && fact.trim().length > 0);
  }, [species.fun_facts, species.ai_fun_facts, species.fun_facts_field]);

  const handleShare = async () => {
    const url = window.location.href;
    const title = `${species.name} - Wildlife Explorer`;
    const text = species.description || `Learn about ${species.name}, a fascinating ${species.category?.toLowerCase()} species.`;

    if (navigator.share) {
      try {
        await navigator.share({ title, text, url });
      } catch (error) {
        // Fallback to copying URL
        navigator.clipboard.writeText(url);
      }
    } else {
      navigator.clipboard.writeText(url);
    }
  };

  const handleToggleFavorite = () => {
    if (onToggleFavorite) {
      onToggleFavorite(species.id);
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        {onBack && (
          <Button variant="outline" onClick={onBack} className="flex items-center gap-2">
            <ArrowLeft className="w-4 h-4" />
            Back to Gallery
          </Button>
        )}
        <div className="flex items-center gap-2">
          {showFavorite && (
            <Button
              variant={isFavorite ? "default" : "outline"}
              onClick={handleToggleFavorite}
              className="flex items-center gap-2"
            >
              <Heart className={`w-4 h-4 ${isFavorite ? 'fill-current' : ''}`} />
              {isFavorite ? 'Favorited' : 'Add to Favorites'}
            </Button>
          )}
          <Button variant="outline" onClick={handleShare} className="flex items-center gap-2">
            <Share2 className="w-4 h-4" />
            Share
          </Button>
        </div>
      </div>

      {/* Hero Section */}
      <Card className="overflow-hidden">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
          {/* Image */}
          <div className="relative h-64 lg:h-96">
            {mainPhoto ? (
              <img
                src={mainPhoto.url}
                alt={species.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = '/placeholder.svg';
                }}
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-green-100 to-green-200 flex items-center justify-center">
                <div className="text-center text-green-600">
                  <Camera className="w-16 h-16 mx-auto mb-4" />
                  <p className="text-lg font-medium">No photo available</p>
                </div>
              </div>
            )}
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent" />
          </div>

          {/* Info */}
          <CardContent className="p-6 lg:p-8">
            <div className="space-y-4">
              {/* Title and badges */}
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Badge
                    variant="secondary"
                    className="text-sm cursor-pointer hover:bg-gray-200 transition-colors"
                    onClick={() => onFilterByCategory?.(species.category || '')}
                    title={`Filter by ${species.category}`}
                  >
                    <span className="mr-1">{getCategoryIcon(species.category || '')}</span>
                    {species.category}
                  </Badge>
                  {species.conservation_status && (
                    <Badge
                      className={`cursor-pointer hover:opacity-80 transition-opacity ${getConservationStatusColor(species.conservation_status)}`}
                      onClick={() => onFilterByConservationStatus?.(species.conservation_status || '')}
                      title={`Filter by ${species.conservation_status}`}
                    >
                      <Shield className="w-3 h-3 mr-1" />
                      {species.conservation_status}
                    </Badge>
                  )}
                  {species.featured && (
                    <Badge className="bg-yellow-500 text-white">
                      ⭐ Featured
                    </Badge>
                  )}
                </div>
                
                <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">
                  {species.name}
                </h1>
                
                {species.common_name && species.common_name !== species.name && (
                  <p className="text-lg text-gray-600 mb-1">{species.common_name}</p>
                )}
                
                {species.scientific_name && (
                  <p className="text-lg italic text-gray-500">{species.scientific_name}</p>
                )}
              </div>

              {/* Quick stats */}
              <div className="grid grid-cols-2 gap-4">
                {species.size_cm && (
                  <div className="flex items-center gap-2 text-sm">
                    <Ruler className="w-4 h-4 text-gray-500" />
                    <span className="font-medium">Size:</span>
                    <span>{formatSize(species.size_cm)}</span>
                  </div>
                )}
                {species.weight_g && (
                  <div className="flex items-center gap-2 text-sm">
                    <Weight className="w-4 h-4 text-gray-500" />
                    <span className="font-medium">Weight:</span>
                    <span>{formatWeight(species.weight_g)}</span>
                  </div>
                )}
                {species.lifespan_years && (
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="w-4 h-4 text-gray-500" />
                    <span className="font-medium">Lifespan:</span>
                    <span>{species.lifespan_years} years</span>
                  </div>
                )}
                <div className="flex items-center gap-2 text-sm">
                  <Camera className="w-4 h-4 text-gray-500" />
                  <span className="font-medium">Photos:</span>
                  <span>{species.photos?.length || 0}</span>
                </div>
              </div>

              {/* Description */}
              {species.description && (
                <div>
                  <p className="text-gray-700 leading-relaxed">
                    {species.description.replace(/AI Facts: \[object Object\]/g, '')}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </div>
      </Card>

      {/* Detailed Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Habitat & Behavior */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Leaf className="w-5 h-5 text-green-600" />
              Habitat & Behavior
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {species.habitat && (
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Habitat</h4>
                <p className="text-gray-600 text-sm">{species.habitat}</p>
              </div>
            )}
            {species.diet && (
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Diet</h4>
                <p className="text-gray-600 text-sm">{species.diet}</p>
              </div>
            )}
            {species.behavior && (
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Behavior</h4>
                <p className="text-gray-600 text-sm">{species.behavior}</p>
              </div>
            )}
            {species.migration_pattern && (
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Migration</h4>
                <p className="text-gray-600 text-sm">{species.migration_pattern}</p>
              </div>
            )}
            {species.breeding_season && (
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Breeding Season</h4>
                <p className="text-gray-600 text-sm">{species.breeding_season}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Conservation & Facts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="w-5 h-5 text-blue-600" />
              Conservation & Facts
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {species.threat_level && (
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Threat Level</h4>
                <p className="text-gray-600 text-sm">{species.threat_level}</p>
              </div>
            )}
            {species.population_trend && (
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Population Trend</h4>
                <p className="text-gray-600 text-sm">{species.population_trend}</p>
              </div>
            )}
            {/* Geographic Information */}
            {(species.countries || species.states_provinces || species.primary_region || species.regions) && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  Geographic Distribution
                </h4>
                <div className="space-y-2">
                  {/* Countries */}
                  {species.countries && species.countries.length > 0 && (
                    <div>
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Countries</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {species.countries.map((country: string, index: number) => (
                          <Link
                            key={index}
                            to={`/wildlife?location=${encodeURIComponent(country)}&tab=species`}
                            className="inline-block"
                          >
                            <Badge
                              variant="default"
                              className="text-xs hover:bg-blue-600 transition-colors cursor-pointer"
                            >
                              {country}
                            </Badge>
                          </Link>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* States/Provinces */}
                  {species.states_provinces && species.states_provinces.length > 0 && (
                    <div>
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">States & Regions</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {species.states_provinces.map((region: string, index: number) => (
                          <Link
                            key={index}
                            to={`/wildlife?location=${encodeURIComponent(region)}&tab=species`}
                            className="inline-block"
                          >
                            <Badge
                              variant="secondary"
                              className="text-xs hover:bg-gray-300 transition-colors cursor-pointer"
                            >
                              {region}
                            </Badge>
                          </Link>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Primary Region */}
                  {species.primary_region && (
                    <div>
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Primary Region</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        <Link
                          to={`/wildlife?location=${encodeURIComponent(species.primary_region)}&tab=species`}
                          className="inline-block"
                        >
                          <Badge
                            variant="outline"
                            className="text-xs hover:bg-gray-100 transition-colors cursor-pointer"
                          >
                            {species.primary_region}
                          </Badge>
                        </Link>
                      </div>
                    </div>
                  )}

                  {/* Legacy regions field as fallback */}
                  {!species.countries && !species.states_provinces && !species.primary_region && species.regions && (
                    <p className="text-gray-600 text-sm">{species.regions}</p>
                  )}
                </div>
              </div>
            )}
            {funFacts.length > 0 && (
              <div>
                <FunFacts facts={funFacts} title="Fun Facts" />
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Photo Gallery */}
      {species.photos && species.photos.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Camera className="w-5 h-5 text-purple-600" />
              Photo Gallery ({species.photos.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <PublicPhotoGallery 
              photos={species.photos.map(photo => ({ ...photo, species: { 
                id: species.id, 
                name: species.name || '', 
                category: species.category || '',
                conservation_status: species.conservation_status 
              }}))} 
              viewMode="grid"
              showSpeciesInfo={false}
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PublicSpeciesDetail;
