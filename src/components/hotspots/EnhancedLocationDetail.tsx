import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  MapPin, 
  Calendar, 
  Thermometer, 
  Cloud, 
  Bird, 
  Camera, 
  Clock,
  Filter,
  Sparkles,
  ExternalLink,
  Navigation,
  Sun,
  Droplets
} from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { LoadingSpinner } from '@/components/public/LoadingSpinner';
import { generateLocationContentWithGemini } from '@/lib/geminiLocationAI';

interface LocationDetail {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  state_province?: string;
  country?: string;
  description?: string;
  habitat_types?: string[];
  visitor_tips?: string;
  best_times?: string;
  website_url?: string;
  contact_info?: string;
  photo_url?: string;
  total_species: number;
  common_species: number;
  rare_species: number;
  breeding_species: number;
  categories_present: string[];
}

interface SpeciesDetail {
  species_id: string;
  species_name: string;
  common_name?: string;
  scientific_name?: string;
  category?: string;
  conservation_status?: string;
  abundance: string;
  seasonal_presence: string[];
  breeding_status: string;
  best_months: number[];
  notes?: string;
  confidence_level: string;
  observation_count: number;
  photo_count: number;
}

interface WeatherData {
  temperature: number;
  condition: string;
  humidity: number;
  windSpeed: number;
}

const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
const seasonNames = ['spring', 'summer', 'fall', 'winter'];
const abundanceLevels = ['rare', 'uncommon', 'common', 'abundant', 'very_common'];
const breedingStatuses = ['non_breeding', 'possible', 'probable', 'confirmed'];

export default function EnhancedLocationDetail() {
  const { id } = useParams<{ id: string }>();
  const [location, setLocation] = useState<LocationDetail | null>(null);
  const [species, setSpecies] = useState<SpeciesDetail[]>([]);
  const [filteredSpecies, setFilteredSpecies] = useState<SpeciesDetail[]>([]);
  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState(true);
  const [generatingAI, setGeneratingAI] = useState(false);
  
  // Filters
  const [seasonFilter, setSeasonFilter] = useState<string>('all');
  const [abundanceFilter, setAbundanceFilter] = useState<string>('all');
  const [breedingFilter, setBreedingFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');

  useEffect(() => {
    if (id) {
      fetchLocationData();
    }
  }, [id]);

  useEffect(() => {
    applyFilters();
  }, [species, seasonFilter, abundanceFilter, breedingFilter, categoryFilter]);

  const fetchLocationData = async () => {
    try {
      setLoading(true);
      
      // Fetch location details
      const { data: locationData, error: locationError } = await supabase
        .from('hotspot_highlights')
        .select('*')
        .eq('id', id)
        .single();

      if (locationError) throw locationError;
      setLocation(locationData);

      // Fetch species for this location
      const { data: speciesData, error: speciesError } = await supabase
        .from('species_location_details')
        .select('*')
        .eq('location_id', id)
        .order('abundance', { ascending: false })
        .order('species_name');

      if (speciesError) throw speciesError;
      setSpecies(speciesData || []);

      // Fetch weather data (mock for now - you can integrate real weather API)
      if (locationData) {
        fetchWeatherData(locationData.latitude, locationData.longitude);
      }

    } catch (error) {
      console.error('Error fetching location data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchWeatherData = async (lat: number, lng: number) => {
    // Mock weather data - replace with real weather API
    setWeather({
      temperature: Math.round(Math.random() * 30 + 10), // 10-40°C
      condition: ['Sunny', 'Partly Cloudy', 'Cloudy', 'Light Rain'][Math.floor(Math.random() * 4)],
      humidity: Math.round(Math.random() * 40 + 40), // 40-80%
      windSpeed: Math.round(Math.random() * 20 + 5) // 5-25 km/h
    });
  };

  const applyFilters = () => {
    let filtered = [...species];

    if (seasonFilter !== 'all') {
      filtered = filtered.filter(s => 
        s.seasonal_presence && s.seasonal_presence.includes(seasonFilter)
      );
    }

    if (abundanceFilter !== 'all') {
      filtered = filtered.filter(s => s.abundance === abundanceFilter);
    }

    if (breedingFilter !== 'all') {
      filtered = filtered.filter(s => s.breeding_status === breedingFilter);
    }

    if (categoryFilter !== 'all') {
      filtered = filtered.filter(s => s.category === categoryFilter);
    }

    setFilteredSpecies(filtered);
  };

  const generateAIContent = async () => {
    if (!location) return;

    try {
      setGeneratingAI(true);

      // Prepare key species data for AI generation (top 10 most interesting species)
      const keySpecies = species
        .filter(s => s.abundance !== 'rare' || s.conservation_status || s.breeding_status === 'confirmed')
        .slice(0, 10)
        .map(s => ({
          name: s.species_name,
          commonName: s.common_name,
          scientificName: s.scientific_name,
          category: s.category,
          abundance: s.abundance,
          seasonalPresence: s.seasonal_presence || [],
          breedingStatus: s.breeding_status,
          bestMonths: s.best_months || [],
          conservationStatus: s.conservation_status,
          notes: s.notes
        }));

      console.log('🔍 Debug AI Generation Data:');
      console.log('Species count:', species.length);
      console.log('Key species for AI:', keySpecies);
      console.log('Location data:', {
        name: location.name,
        totalSpecies: location.total_species,
        commonSpecies: location.common_species,
        rareSpecies: location.rare_species,
        categories: location.categories_present
      });

      const aiContent = await generateLocationContentWithGemini({
        locationName: location.name,
        latitude: location.latitude,
        longitude: location.longitude,
        stateProvince: location.state_province,
        country: location.country,
        habitatTypes: location.habitat_types,
        speciesCount: location.total_species,
        categories: location.categories_present,
        commonSpecies: location.common_species,
        rareSpecies: location.rare_species,
        breedingSpecies: location.breeding_species,
        keySpecies: keySpecies
      });

      console.log('Generated AI content:', aiContent);

      // Try to update location with AI-generated content
      try {
        const { error } = await supabase
          .from('locations')
          .update({
            description: aiContent.description,
            visitor_tips: aiContent.visitorTips,
            best_times: aiContent.bestTimes
          })
          .eq('id', id);

        if (error) {
          console.error('Database update error:', error);
          // Show the content in a more user-friendly way
          const contentPreview = `
🎉 AI Content Generated Successfully!

📝 Description: ${aiContent.description.substring(0, 150)}...

💡 Visitor Tips: ${aiContent.visitorTips.substring(0, 150)}...

⏰ Best Times: ${aiContent.bestTimes.substring(0, 150)}...

⚠️ Note: Content generated but couldn't save to database. Please run the ADD_AI_CONTENT_COLUMNS.sql script first.
          `.trim();

          alert(contentPreview);
        } else {
          // Refresh location data on successful update
          await fetchLocationData();
          alert('🎉 AI content generated and saved successfully! The page will refresh to show the new content.');
        }
      } catch (dbError) {
        console.error('Database update failed:', dbError);
        // Show the content even if database update fails
        const contentPreview = `
🎉 AI Content Generated Successfully!

📝 Description: ${aiContent.description.substring(0, 150)}...

💡 Visitor Tips: ${aiContent.visitorTips.substring(0, 150)}...

⏰ Best Times: ${aiContent.bestTimes.substring(0, 150)}...

⚠️ Note: Content generated but couldn't save to database.
        `.trim();

        alert(contentPreview);
      }

    } catch (error) {
      console.error('Error generating AI content:', error);
      alert(`Failed to generate AI content: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setGeneratingAI(false);
    }
  };

  const getAbundanceColor = (abundance: string) => {
    switch (abundance) {
      case 'very_common': return 'bg-green-100 text-green-800';
      case 'abundant': return 'bg-green-100 text-green-700';
      case 'common': return 'bg-blue-100 text-blue-800';
      case 'uncommon': return 'bg-yellow-100 text-yellow-800';
      case 'rare': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getBreedingColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-purple-100 text-purple-800';
      case 'probable': return 'bg-purple-100 text-purple-700';
      case 'possible': return 'bg-purple-100 text-purple-600';
      case 'non_breeding': return 'bg-gray-100 text-gray-600';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCurrentMonth = () => new Date().getMonth() + 1;

  if (loading) return <LoadingSpinner />;
  if (!location) return <div>Location not found</div>;

  const mapUrl = `https://www.google.com/maps?q=${location.latitude},${location.longitude}&z=15`;
  const directionsUrl = `https://www.google.com/maps/dir/?api=1&destination=${location.latitude},${location.longitude}`;

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header Section */}
      <div className="relative">
        {location.photo_url && (
          <div className="h-64 bg-cover bg-center rounded-lg mb-6" 
               style={{ backgroundImage: `url(${location.photo_url})` }}>
            <div className="absolute inset-0 bg-black bg-opacity-40 rounded-lg flex items-end">
              <div className="p-6 text-white">
                <h1 className="text-4xl font-bold mb-2">{location.name}</h1>
                <p className="text-lg opacity-90">
                  📍 {location.state_province}, {location.country}
                </p>
              </div>
            </div>
          </div>
        )}
        
        {!location.photo_url && (
          <div className="mb-6">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">{location.name}</h1>
            <p className="text-xl text-gray-600">
              📍 {location.state_province}, {location.country}
            </p>
          </div>
        )}
      </div>

      {/* Quick Stats & Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4 text-center">
            <Bird className="w-8 h-8 mx-auto mb-2 text-blue-600" />
            <div className="text-2xl font-bold text-blue-600">{location.total_species}</div>
            <div className="text-sm text-gray-600">Total Species</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Camera className="w-8 h-8 mx-auto mb-2 text-green-600" />
            <div className="text-2xl font-bold text-green-600">{location.common_species}</div>
            <div className="text-sm text-gray-600">Common Species</div>
          </CardContent>
        </Card>

        {weather && (
          <Card>
            <CardContent className="p-4 text-center">
              <Thermometer className="w-8 h-8 mx-auto mb-2 text-orange-600" />
              <div className="text-2xl font-bold text-orange-600">{weather.temperature}°C</div>
              <div className="text-sm text-gray-600">{weather.condition}</div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardContent className="p-4 space-y-2">
            <Button
              onClick={generateAIContent}
              disabled={generatingAI}
              className="w-full"
              variant="outline"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              {generatingAI ? 'Generating...' : `AI Enhance (${species.length} species)`}
            </Button>
            <div className="flex gap-1">
              <Button size="sm" variant="outline" className="flex-1" asChild>
                <a href={mapUrl} target="_blank" rel="noopener noreferrer">
                  <MapPin className="w-3 h-3 mr-1" />
                  Map
                </a>
              </Button>
              <Button size="sm" variant="outline" className="flex-1" asChild>
                <a href={directionsUrl} target="_blank" rel="noopener noreferrer">
                  <Navigation className="w-3 h-3 mr-1" />
                  Directions
                </a>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="species">Species ({filteredSpecies.length})</TabsTrigger>
          <TabsTrigger value="visiting">Visiting Info</TabsTrigger>
          <TabsTrigger value="photos">Photos</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle>About This Location</CardTitle>
              </CardHeader>
              <CardContent>
                {location.description ? (
                  <div className="space-y-4">
                    {location.description.split(/\.\s+(?=[A-Z])/).map((paragraph, index) => (
                      <p key={index} className="text-gray-700 leading-relaxed">
                        {paragraph.trim()}{paragraph.trim().endsWith('.') ? '' : '.'}
                      </p>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Sparkles className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>No description available.</p>
                    <Button
                      onClick={generateAIContent}
                      disabled={generatingAI}
                      className="mt-4"
                      variant="outline"
                    >
                      <Sparkles className="w-4 h-4 mr-2" />
                      Generate with AI
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Habitat & Weather */}
            <div className="space-y-4">
              {/* Habitat Types */}
              {location.habitat_types && location.habitat_types.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Habitat Types</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {location.habitat_types.map((habitat, index) => (
                        <Badge key={index} variant="outline" className="capitalize">
                          {habitat.replace('_', ' ')}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Current Weather */}
              {weather && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <Cloud className="w-5 h-5 mr-2" />
                      Current Weather
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center">
                        <Sun className="w-4 h-4 mr-2 text-orange-500" />
                        <span>{weather.temperature}°C</span>
                      </div>
                      <div className="flex items-center">
                        <Cloud className="w-4 h-4 mr-2 text-gray-500" />
                        <span>{weather.condition}</span>
                      </div>
                      <div className="flex items-center">
                        <Droplets className="w-4 h-4 mr-2 text-blue-500" />
                        <span>{weather.humidity}% humidity</span>
                      </div>
                      <div className="flex items-center">
                        <Navigation className="w-4 h-4 mr-2 text-green-500" />
                        <span>{weather.windSpeed} km/h wind</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Species Categories */}
              {location.categories_present && location.categories_present.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Wildlife Categories</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {location.categories_present.map((category, index) => (
                        <Badge key={index} variant="secondary" className="capitalize">
                          {category}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        {/* Species Tab */}
        <TabsContent value="species" className="space-y-6">
          {/* Species Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Filter className="w-5 h-5 mr-2" />
                Filter Species
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Season</label>
                  <Select value={seasonFilter} onValueChange={setSeasonFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All seasons" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Seasons</SelectItem>
                      {seasonNames.map(season => (
                        <SelectItem key={season} value={season} className="capitalize">
                          {season}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Abundance</label>
                  <Select value={abundanceFilter} onValueChange={setAbundanceFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All abundance" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Abundance</SelectItem>
                      {abundanceLevels.map(level => (
                        <SelectItem key={level} value={level} className="capitalize">
                          {level.replace('_', ' ')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Breeding</label>
                  <Select value={breedingFilter} onValueChange={setBreedingFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All breeding" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Breeding Status</SelectItem>
                      {breedingStatuses.map(status => (
                        <SelectItem key={status} value={status} className="capitalize">
                          {status.replace('_', ' ')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Category</label>
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {location.categories_present.map(category => (
                        <SelectItem key={category} value={category} className="capitalize">
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Species List */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredSpecies.map((speciesItem) => (
              <Card key={speciesItem.species_id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-semibold text-gray-900 line-clamp-1">
                        {speciesItem.species_name}
                      </h4>
                      {speciesItem.common_name && speciesItem.common_name !== speciesItem.species_name && (
                        <p className="text-sm text-gray-600 line-clamp-1">
                          {speciesItem.common_name}
                        </p>
                      )}
                      {speciesItem.scientific_name && (
                        <p className="text-sm italic text-gray-500 line-clamp-1">
                          {speciesItem.scientific_name}
                        </p>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <Badge className={getAbundanceColor(speciesItem.abundance)}>
                        {speciesItem.abundance.replace('_', ' ')}
                      </Badge>
                      <Badge className={getBreedingColor(speciesItem.breeding_status)}>
                        {speciesItem.breeding_status.replace('_', ' ')}
                      </Badge>
                    </div>

                    {speciesItem.seasonal_presence && speciesItem.seasonal_presence.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {speciesItem.seasonal_presence.map((season) => (
                          <Badge key={season} variant="outline" className="text-xs capitalize">
                            {season}
                          </Badge>
                        ))}
                      </div>
                    )}

                    {speciesItem.best_months && speciesItem.best_months.length > 0 && (
                      <div className="text-xs text-gray-500">
                        <Clock className="w-3 h-3 inline mr-1" />
                        Best months: {speciesItem.best_months.map(m => monthNames[m-1]).join(', ')}
                      </div>
                    )}

                    {speciesItem.observation_count > 0 && (
                      <div className="text-xs text-gray-500">
                        {speciesItem.observation_count} observations
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredSpecies.length === 0 && (
            <Card>
              <CardContent className="p-8 text-center text-gray-500">
                <Bird className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No species found matching the current filters.</p>
                <Button
                  onClick={() => {
                    setSeasonFilter('all');
                    setAbundanceFilter('all');
                    setBreedingFilter('all');
                    setCategoryFilter('all');
                  }}
                  variant="outline"
                  className="mt-4"
                >
                  Clear Filters
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Visiting Info Tab */}
        <TabsContent value="visiting" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Visitor Tips</CardTitle>
              </CardHeader>
              <CardContent>
                {location.visitor_tips ? (
                  <p className="text-gray-700 leading-relaxed">{location.visitor_tips}</p>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Clock className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>No visitor tips available.</p>
                    <Button
                      onClick={generateAIContent}
                      disabled={generatingAI}
                      className="mt-4"
                      variant="outline"
                    >
                      <Sparkles className="w-4 h-4 mr-2" />
                      Generate with AI
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Best Times to Visit</CardTitle>
              </CardHeader>
              <CardContent>
                {location.best_times ? (
                  <p className="text-gray-700 leading-relaxed">{location.best_times}</p>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Calendar className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>No timing information available.</p>
                    <Button
                      onClick={generateAIContent}
                      disabled={generatingAI}
                      className="mt-4"
                      variant="outline"
                    >
                      <Sparkles className="w-4 h-4 mr-2" />
                      Generate with AI
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Contact & Links */}
          {(location.website_url || location.contact_info) && (
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {location.website_url && (
                  <div>
                    <Button variant="outline" asChild>
                      <a href={location.website_url} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Visit Website
                      </a>
                    </Button>
                  </div>
                )}
                {location.contact_info && (
                  <div>
                    <h4 className="font-medium mb-2">Contact Details</h4>
                    <p className="text-gray-700">{location.contact_info}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Photos Tab */}
        <TabsContent value="photos" className="space-y-6">
          <Card>
            <CardContent className="p-8 text-center text-gray-500">
              <Camera className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>Photo gallery coming soon!</p>
              <p className="text-sm mt-2">This will show location photos and wildlife photography.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
