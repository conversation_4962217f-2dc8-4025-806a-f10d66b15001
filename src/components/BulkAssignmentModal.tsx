import React from 'react';
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, Di<PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, CheckCircle, ImageIcon } from 'lucide-react';

interface Species {
  id: string;
  name: string;
  common_name: string | null;
  scientific_name: string | null;
  category: string | null;
  conservation_status: string | null;
  published: boolean;
}

interface BulkAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  photoCount: number;
  species: Species | null;
  isLoading?: boolean;
}

export function BulkAssignmentModal({
  isOpen,
  onClose,
  onConfirm,
  photoCount,
  species,
  isLoading = false
}: BulkAssignmentModalProps) {
  if (!species) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-orange-600" />
            Confirm Bulk Assignment
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-4">
              <ImageIcon className="w-8 h-8 text-blue-600" />
              <span className="text-2xl font-bold">{photoCount}</span>
              <span className="text-lg">photos</span>
            </div>
            
            <p className="text-sm text-muted-foreground mb-4">
              Are you sure you want to assign {photoCount} photo{photoCount !== 1 ? 's' : ''} to:
            </p>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold text-lg">
                {species.common_name || species.name}
              </h3>
              {species.scientific_name && (
                <p className="text-sm text-muted-foreground italic">
                  {species.scientific_name}
                </p>
              )}
              {species.category && (
                <Badge variant="outline" className="mt-2">
                  {species.category}
                </Badge>
              )}
            </div>
          </div>

          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="flex items-start gap-2">
              <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5" />
              <div className="text-sm text-blue-700">
                <p className="font-medium">This action will:</p>
                <ul className="mt-1 space-y-1">
                  <li>• Update all {photoCount} photo{photoCount !== 1 ? 's' : ''} with the new species assignment</li>
                  <li>• Log each change in the override system</li>
                  <li>• Update the photo counts for this species</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button 
            onClick={onConfirm} 
            disabled={isLoading}
            className="min-w-[100px]"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Assigning...
              </>
            ) : (
              <>
                <CheckCircle className="w-4 h-4 mr-2" />
                Assign {photoCount} Photo{photoCount !== 1 ? 's' : ''}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 