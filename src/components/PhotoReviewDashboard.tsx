import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { supabase } from "@/integrations/supabase/client";
import { useSpeciesWithPhotos } from "@/hooks/useSpeciesData";
import { toast } from "sonner";
import { 
  Eye, 
  CheckCircle, 
  XCircle, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>fresh<PERSON><PERSON>,
  Image as ImageIcon,
  MapPin,
  Tag
} from "lucide-react";

interface PhotoForReview {
  id: number;
  url: string;
  title: string | null;
  description: string | null;
  notes: string | null;
  location: string | null;
  species_id: string | null;
  hash: string | null;
  ai_generated_metadata?: boolean;
  needs_review?: boolean;
  published: boolean;
  created_at: string;
  species?: {
    id: string;
    name: string;
    scientific_name: string | null;
    common_name: string | null;
  };
}

interface ReviewStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  ai_generated: number;
}

export const PhotoReviewDashboard = () => {
  const [photos, setPhotos] = useState<PhotoForReview[]>([]);
  const [filteredPhotos, setFilteredPhotos] = useState<PhotoForReview[]>([]);
  const [stats, setStats] = useState<ReviewStats>({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    ai_generated: 0
  });
  const [loading, setLoading] = useState(true);
  const [selectedPhoto, setSelectedPhoto] = useState<PhotoForReview | null>(null);
  const [filters, setFilters] = useState({
    status: 'all' as 'all' | 'pending' | 'approved' | 'rejected',
    ai_generated: 'all' as 'all' | 'true' | 'false',
    search: ''
  });
  const { data: speciesData = [] } = useSpeciesWithPhotos();

  useEffect(() => {
    fetchPhotos();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [photos, filters]);

  const fetchPhotos = async () => {
    setLoading(true);
    try {
      // Get photos and species separately, then join them
      const [photosData, speciesData] = await Promise.all([
        supabase.from('photos_v2').select('*').order('created_at', { ascending: false }),
        supabase.from('species_v2').select('id, name, scientific_name, common_name')
      ]);

      if (photosData.error) throw photosData.error;
      if (speciesData.error) throw speciesData.error;

      // Create a map for quick species lookup
      const speciesMap = new Map(speciesData.data?.map(s => [s.id, s]) || []);

      // Transform data to match our interface and remove duplicates
      const allPhotos: PhotoForReview[] = (photosData.data || []).map(photo => ({
        ...photo,
        species: photo.species_id ? speciesMap.get(photo.species_id) || null : null,
        ai_generated_metadata: false, // Default to false since field doesn't exist
        needs_review: false // Default to false since field doesn't exist
      }));

      // Debug: Check for duplicates
      const urlCounts = allPhotos.reduce((acc, photo) => {
        if (photo.url) {
          acc[photo.url] = (acc[photo.url] || 0) + 1;
        }
        return acc;
      }, {} as Record<string, number>);

      const duplicateUrls = Object.entries(urlCounts).filter(([url, count]) => count > 1);
      console.log(`Found ${duplicateUrls.length} URLs with duplicates:`, duplicateUrls.slice(0, 5));

      // Remove duplicates based on URL (more reliable than URL + title)
      const uniquePhotos = allPhotos.filter((photo, index, array) => {
        if (!photo.url) return true; // Keep photos without URLs
        const duplicateIndex = array.findIndex(p => p.url === photo.url);
        return duplicateIndex === index; // Keep only the first occurrence
      });

      console.log(`Original photos: ${allPhotos.length}, After deduplication: ${uniquePhotos.length}`);

      const transformedData = uniquePhotos;

      setPhotos(transformedData);
      calculateStats(transformedData);
    } catch (error) {
      console.error('Error fetching photos:', error);
      toast.error('Failed to fetch photos');
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (photoList: PhotoForReview[]) => {
    const newStats: ReviewStats = {
      total: photoList.length,
      pending: 0, // No pending since needs_review field doesn't exist
      approved: photoList.filter(p => p.published).length,
      rejected: photoList.filter(p => !p.published).length,
      ai_generated: 0 // No AI generated since field doesn't exist
    };
    setStats(newStats);
  };

  const applyFilters = () => {
    let filtered = [...photos];

    // Status filter
    if (filters.status !== 'all') {
      switch (filters.status) {
        case 'pending':
          // No pending photos since needs_review field doesn't exist
          filtered = [];
          break;
        case 'approved':
          filtered = filtered.filter(p => p.published);
          break;
        case 'rejected':
          filtered = filtered.filter(p => !p.published);
          break;
      }
    }

    // AI generated filter - skip since field doesn't exist
    // if (filters.ai_generated !== 'all') {
    //   const aiGenerated = filters.ai_generated === 'true';
    //   filtered = filtered.filter(p => p.ai_generated_metadata === aiGenerated);
    // }

    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(p => 
        p.title?.toLowerCase().includes(searchTerm) ||
        p.description?.toLowerCase().includes(searchTerm) ||
        p.notes?.toLowerCase().includes(searchTerm) ||
        p.location?.toLowerCase().includes(searchTerm) ||
        p.species?.name.toLowerCase().includes(searchTerm) ||
        p.species?.scientific_name?.toLowerCase().includes(searchTerm)
      );
    }

    setFilteredPhotos(filtered);
  };

  const approvePhoto = async (photoId: number) => {
    try {
      const { error } = await supabase
        .from('photos_v2')
        .update({
          published: true
        })
        .eq('id', photoId);

      if (error) throw error;

      toast.success('Photo approved and published');
      fetchPhotos();
    } catch (error) {
      console.error('Error approving photo:', error);
      toast.error('Failed to approve photo');
    }
  };

  const rejectPhoto = async (photoId: number) => {
    try {
      const { error } = await supabase
        .from('photos_v2')
        .update({
          published: false
        })
        .eq('id', photoId);

      if (error) throw error;

      toast.success('Photo rejected');
      fetchPhotos();
    } catch (error) {
      console.error('Error rejecting photo:', error);
      toast.error('Failed to reject photo');
    }
  };

  const updatePhoto = async (photoId: number, updates: Partial<PhotoForReview>) => {
    try {
      const { error } = await supabase
        .from('photos_v2')
        .update(updates)
        .eq('id', photoId);

      if (error) throw error;

      toast.success('Photo updated successfully');
      fetchPhotos();
      setSelectedPhoto(null);
    } catch (error) {
      console.error('Error updating photo:', error);
      toast.error('Failed to update photo');
    }
  };

  const getStatusBadge = (photo: PhotoForReview) => {
    if (photo.published) {
      return <Badge className="bg-green-100 text-green-800">Published</Badge>;
    }
    return <Badge variant="destructive">Unpublished</Badge>;
  };

  const getConfidenceColor = (photo: PhotoForReview) => {
    // This would be based on AI confidence if available
    // For now, use a simple heuristic based on whether it's AI-generated
    if (photo.ai_generated_metadata) {
      return 'bg-blue-100 text-blue-800';
    }
    return 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Photo Review Dashboard</h1>
          <p className="text-gray-600">Review and approve AI-generated photo metadata</p>
        </div>
        <Button onClick={fetchPhotos} disabled={loading} className="flex items-center gap-2">
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <ImageIcon className="w-5 h-5 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">Total Photos</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-yellow-500" />
              <div>
                <p className="text-sm text-gray-600">Pending Review</p>
                <p className="text-2xl font-bold">{stats.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-500" />
              <div>
                <p className="text-sm text-gray-600">Approved</p>
                <p className="text-2xl font-bold">{stats.approved}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <XCircle className="w-5 h-5 text-red-500" />
              <div>
                <p className="text-sm text-gray-600">Rejected</p>
                <p className="text-2xl font-bold">{stats.rejected}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Brain className="w-5 h-5 text-purple-500" />
              <div>
                <p className="text-sm text-gray-600">AI Generated</p>
                <p className="text-2xl font-bold">{stats.ai_generated}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Label>Search</Label>
              <Input
                placeholder="Search photos..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              />
            </div>
            <div>
              <Label>Status</Label>
              <Select
                value={filters.status}
                onValueChange={(value: string) => setFilters(prev => ({ ...prev, status: value as 'all' | 'pending' | 'approved' | 'rejected' }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="pending">Pending Review</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>AI Generated</Label>
              <Select
                value={filters.ai_generated}
                onValueChange={(value: string) => setFilters(prev => ({ ...prev, ai_generated: value as 'all' | 'true' | 'false' }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="true">AI Generated</SelectItem>
                  <SelectItem value="false">Manual</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Photo Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredPhotos.map((photo) => (
          <Card key={photo.id} className="cursor-pointer hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="aspect-square mb-3 bg-gray-100 rounded-lg overflow-hidden">
                <img
                  src={photo.url}
                  alt={photo.title || 'Photo'}
                  className="w-full h-full object-cover"
                />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium truncate">{photo.title || 'Untitled'}</h3>
                  {getStatusBadge(photo)}
                </div>

                <div className="text-sm text-gray-600 space-y-1">
                  <div className="flex items-center gap-1">
                    <Tag className="w-3 h-3" />
                    <span>{photo.species?.name || 'Unknown Species'}</span>
                  </div>
                  {photo.location && (
                    <div className="flex items-center gap-1">
                      <MapPin className="w-3 h-3" />
                      <span>{photo.location}</span>
                    </div>
                  )}
                </div>

                <div className="flex gap-2 pt-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setSelectedPhoto(photo)}
                    className="flex-1"
                  >
                    <Eye className="w-3 h-3 mr-1" />
                    Edit
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => approvePhoto(photo.id)}
                    className="flex-1"
                    variant={photo.published ? "secondary" : "default"}
                  >
                    <CheckCircle className="w-3 h-3 mr-1" />
                    {photo.published ? "Published" : "Publish"}
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => rejectPhoto(photo.id)}
                    className="flex-1"
                  >
                    <XCircle className="w-3 h-3 mr-1" />
                    Unpublish
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Review Modal */}
      {selectedPhoto && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Review Photo</span>
                <Button variant="ghost" size="sm" onClick={() => setSelectedPhoto(null)}>
                  <XCircle className="w-4 h-4" />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <img
                    src={selectedPhoto.url}
                    alt={selectedPhoto.title || 'Photo'}
                    className="w-full rounded-lg"
                  />
                </div>
                <div className="space-y-4">
                  <div>
                    <Label>Title</Label>
                    <Input
                      value={selectedPhoto.title || ''}
                      onChange={(e) => setSelectedPhoto(prev => prev ? { ...prev, title: e.target.value } : null)}
                    />
                  </div>
                  
                  <div>
                    <Label>Species</Label>
                    <Select
                      value={selectedPhoto.species_id || ''}
                      onValueChange={(value) => setSelectedPhoto(prev => prev ? { ...prev, species_id: value } : null)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select species..." />
                      </SelectTrigger>
                      <SelectContent>
                        {speciesData.map((species) => (
                          <SelectItem key={species.id} value={species.id}>
                            {species.name} {species.scientific_name && `(${species.scientific_name})`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Location</Label>
                    <Input
                      value={selectedPhoto.location || ''}
                      onChange={(e) => setSelectedPhoto(prev => prev ? { ...prev, location: e.target.value } : null)}
                    />
                  </div>

                  <div>
                    <Label>Description</Label>
                    <Textarea
                      value={selectedPhoto.description || ''}
                      onChange={(e) => setSelectedPhoto(prev => prev ? { ...prev, description: e.target.value } : null)}
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label>Notes</Label>
                    <Textarea
                      value={selectedPhoto.notes || ''}
                      onChange={(e) => setSelectedPhoto(prev => prev ? { ...prev, notes: e.target.value } : null)}
                      rows={2}
                    />
                  </div>

                  <div className="flex gap-2">
                    <Button
                      onClick={() => updatePhoto(selectedPhoto.id, {
                        title: selectedPhoto.title,
                        description: selectedPhoto.description,
                        notes: selectedPhoto.notes,
                        location: selectedPhoto.location,
                        species_id: selectedPhoto.species_id
                      })}
                      className="flex-1"
                    >
                      Save Changes
                    </Button>
                    <Button
                      onClick={() => approvePhoto(selectedPhoto.id)}
                      className="flex-1"
                      variant={selectedPhoto.published ? "secondary" : "default"}
                    >
                      {selectedPhoto.published ? "Published" : "Publish"}
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={() => rejectPhoto(selectedPhoto.id)}
                      className="flex-1"
                    >
                      Unpublish
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}; 