import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { supabase } from '@/integrations/supabase/client';
import { getPhotosNeedingReviewCount } from '@/lib/photoApi';
import { 
  BarChart3,
  FileText,
  Camera,
  Users,
  Globe,
  Settings,
  Upload,
  Edit,
  Eye,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  Database,
  Zap,
  Shield,
  RefreshCw,
  Plus,
  Search,
  Filter,
  Download,
  Loader2,
  AlertCircle,
  Brain,
  Sparkles,
  Grid3X3,
  List,
  Tag,
  MapPin,
  Calendar
} from 'lucide-react';

// Import existing components
import { EnhancedSpeciesPhotoMatrix } from '@/components/EnhancedSpeciesPhotoMatrix';
import { AIDashboardEditor } from '@/components/AIDashboardEditor';
import { PhotoAssignment } from '@/components/PhotoAssignment';
import { DataIntegrityDashboard } from '@/components/DataIntegrityDashboard';
import { ContentAnalytics } from '@/components/ContentAnalytics';
import { UnassignedPhotosWidget } from '@/components/UnassignedPhotosWidget';
import { SpeciesDataQualityDashboard } from '@/components/SpeciesDataQualityDashboard';
import { SpeciesManager } from '@/components/SpeciesManager';

interface DashboardStats {
  species: {
    total: number;
    published: number;
    draft: number;
    withPhotos: number;
    recentlyUpdated: number;
  };
  photos: {
    total: number;
    published: number;
    draft: number;
    assigned: number;
    unassigned: number;
    recentlyUploaded: number;
  };
  ai: {
    pendingReview: number;
    aiGenerated: number;
    confidence: number;
    overrides: number;
  };
  system: {
    totalUsers: number;
    activeUsers: number;
    storageUsed: number;
    apiCalls: number;
  };
}

interface RecentActivity {
  id: string;
  type: 'species_created' | 'species_updated' | 'photo_uploaded' | 'photo_assigned' | 'ai_review' | 'bulk_publish';
  description: string;
  timestamp: string;
  user?: string;
}

export function UnifiedCMSDashboard() {
  const { isAdmin, adminUser, ready, loading: authLoading } = useAdminAuth();
  const [stats, setStats] = useState<DashboardStats>({
    species: { total: 0, published: 0, draft: 0, withPhotos: 0, recentlyUpdated: 0 },
    photos: { total: 0, published: 0, draft: 0, assigned: 0, unassigned: 0, recentlyUploaded: 0 },
    ai: { pendingReview: 0, aiGenerated: 0, confidence: 0, overrides: 0 },
    system: { totalUsers: 0, activeUsers: 0, storageUsed: 0, apiCalls: 0 }
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (isAdmin) {
      loadDashboardData();
    }
  }, [isAdmin]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Load comprehensive stats
      const [
        { count: totalSpecies },
        { count: publishedSpecies },
        { count: speciesWithPhotos },
        { count: totalPhotos },
        { count: publishedPhotos },
        { count: assignedPhotos },
        { count: aiPendingReview },
        { count: aiGenerated }
      ] = await Promise.all([
        supabase.from('species_v2').select('*', { count: 'exact', head: true }),
        supabase.from('species_v2').select('*', { count: 'exact', head: true }).eq('published', true),
        supabase.from('species_v2').select('*', { count: 'exact', head: true }).gt('photo_count', 0),
        supabase.from('photos_v2').select('*', { count: 'exact', head: true }),
        supabase.from('photos_v2').select('*', { count: 'exact', head: true }).eq('published', true),
        supabase.from('photos_v2').select('*', { count: 'exact', head: true }).not('species_id', 'is', null),
        supabase.from('photos_v2').select('*', { count: 'exact', head: true }).not('ai_suggested_id', 'is', null).eq('ai_reviewed', false),
        supabase.from('photos_v2').select('*', { count: 'exact', head: true }).eq('ai_generated_metadata', true)
      ]);

      // Load recent activity
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
      const [
        { count: recentSpeciesUpdates },
        { count: recentPhotoUploads }
      ] = await Promise.all([
        supabase.from('species_v2').select('*', { count: 'exact', head: true }).gte('updated_at', weekAgo),
        supabase.from('photos_v2').select('*', { count: 'exact', head: true }).gte('created_at', weekAgo)
      ]);

      // Calculate AI confidence average
      const { data: aiConfidenceData } = await supabase
        .from('photos_v2')
        .select('ai_confidence')
        .not('ai_confidence', 'is', null);

      const avgConfidence = aiConfidenceData?.length 
        ? aiConfidenceData.reduce((sum, item) => sum + (item.ai_confidence || 0), 0) / aiConfidenceData.length
        : 0;

      setStats({
        species: {
          total: totalSpecies || 0,
          published: publishedSpecies || 0,
          draft: (totalSpecies || 0) - (publishedSpecies || 0),
          withPhotos: speciesWithPhotos || 0,
          recentlyUpdated: recentSpeciesUpdates || 0,
        },
        photos: {
          total: totalPhotos || 0,
          published: publishedPhotos || 0,
          draft: (totalPhotos || 0) - (publishedPhotos || 0),
          assigned: assignedPhotos || 0,
          unassigned: (totalPhotos || 0) - (assignedPhotos || 0),
          recentlyUploaded: recentPhotoUploads || 0,
        },
        ai: {
          pendingReview: aiPendingReview || 0,
          aiGenerated: aiGenerated || 0,
          confidence: avgConfidence,
          overrides: 0 // TODO: Load from ai_override_log
        },
        system: {
          totalUsers: 1,
          activeUsers: 1,
          storageUsed: 75,
          apiCalls: 1250
        }
      });

      // Generate recent activity
      const activities: RecentActivity[] = [
        {
          id: '1',
          type: 'species_updated',
          description: `${recentSpeciesUpdates || 0} species updated this week`,
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          type: 'photo_uploaded',
          description: `${recentPhotoUploads || 0} photos uploaded this week`,
          timestamp: new Date().toISOString(),
        },
        {
          id: '3',
          type: 'ai_review',
          description: `${aiPendingReview || 0} photos pending AI review`,
          timestamp: new Date().toISOString(),
        },
        {
          id: '4',
          type: 'bulk_publish',
          description: `${publishedSpecies || 0} species currently published`,
          timestamp: new Date().toISOString(),
        }
      ];

      setRecentActivity(activities);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
          <p className="text-lg text-gray-600">Loading CMS dashboard...</p>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <CardTitle>Access Denied</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-4">
              You need administrator privileges to access the CMS dashboard.
            </p>
            <Button onClick={() => window.location.href = '/admin/login'}>
              Go to Admin Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const speciesPublishRate = stats.species.total > 0 ? (stats.species.published / stats.species.total) * 100 : 0;
  const photosPublishRate = stats.photos.total > 0 ? (stats.photos.published / stats.photos.total) * 100 : 0;
  const aiConfidenceRate = stats.ai.confidence * 100;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <Database className="w-8 h-8 text-blue-600" />
                Wildlife CMS Hub
              </h1>
              <p className="text-gray-600 mt-1">Unified Content Management & AI Review System</p>
              {adminUser && (
                <div className="mt-2 flex items-center gap-2 text-sm text-gray-500">
                  <Users className="w-4 h-4" />
                  <span>Logged in as: {adminUser.email}</span>
                  <Badge variant={ready ? "default" : "secondary"}>
                    {ready ? "Ready" : "Initializing"}
                  </Badge>
                </div>
              )}
            </div>
            <div className="flex gap-2">
              <Button onClick={() => window.open('/', '_blank')} variant="outline">
                <Globe className="w-4 h-4 mr-2" />
                View Public Site
              </Button>
              <Button onClick={loadDashboardData} variant="outline">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-8">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="matrix" className="flex items-center gap-2">
              <Grid3X3 className="w-4 h-4" />
              Content Matrix
            </TabsTrigger>
            <TabsTrigger value="species" className="flex items-center gap-2">
              <Database className="w-4 h-4" />
              Species
            </TabsTrigger>
            <TabsTrigger value="ai-review" className="flex items-center gap-2">
              <Brain className="w-4 h-4" />
              AI Review
              {stats.ai.pendingReview > 0 && (
                <Badge variant="destructive" className="ml-1 text-xs">
                  {stats.ai.pendingReview}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              Analytics
            </TabsTrigger>
            <TabsTrigger value="tools" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Tools
            </TabsTrigger>
            <TabsTrigger value="data-quality" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              Data Quality
            </TabsTrigger>
            <TabsTrigger value="system" className="flex items-center gap-2">
              <Shield className="w-4 h-4" />
              System
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <FileText className="w-5 h-5 text-blue-600" />
                    <h3 className="font-semibold">Species</h3>
                  </div>
                  <div className="text-2xl font-bold">{stats.species.total}</div>
                  <div className="text-sm text-gray-600">
                    {stats.species.published} published • {stats.species.draft} drafts
                  </div>
                  <Progress value={speciesPublishRate} className="mt-2 h-2" />
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Camera className="w-5 h-5 text-green-600" />
                    <h3 className="font-semibold">Photos</h3>
                  </div>
                  <div className="text-2xl font-bold">{stats.photos.total}</div>
                  <div className="text-sm text-gray-600">
                    {stats.photos.published} published • {stats.photos.draft} drafts
                  </div>
                  <Progress value={photosPublishRate} className="mt-2 h-2" />
                </CardContent>
              </Card>

              <Link to="/admin/photo-assignment">
                <Card className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Brain className="w-5 h-5 text-purple-600" />
                      <h3 className="font-semibold">AI Processing</h3>
                      {stats.ai.pendingReview > 0 && (
                        <Badge variant="destructive" className="text-xs">
                          {stats.ai.pendingReview} to review
                        </Badge>
                      )}
                    </div>
                    <div className="text-2xl font-bold">{stats.ai.aiGenerated}</div>
                    <div className="text-sm text-gray-600">
                      {stats.ai.pendingReview} need review • {Math.round(aiConfidenceRate)}% confidence
                    </div>
                    <Progress value={aiConfidenceRate} className="mt-2 h-2" />
                  </CardContent>
                </Card>
              </Link>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingUp className="w-5 h-5 text-orange-600" />
                    <h3 className="font-semibold">Activity</h3>
                  </div>
                  <div className="text-2xl font-bold">{stats.species.recentlyUpdated + stats.photos.recentlyUploaded}</div>
                  <div className="text-sm text-gray-600">
                    Updates this week
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions & Unassigned Photos */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Button className="h-20 flex-col gap-2" onClick={() => setActiveTab('matrix')}>
                        <Plus className="w-6 h-6" />
                        Add Species
                      </Button>
                      <Button className="h-20 flex-col gap-2" onClick={() => setActiveTab('matrix')}>
                        <Upload className="w-6 h-6" />
                        Upload Photos
                      </Button>
                      <Button className="h-20 flex-col gap-2" onClick={() => setActiveTab('ai-review')}>
                        <Brain className="w-6 h-6" />
                        Review AI Content
                        {stats.ai.pendingReview > 0 && (
                          <Badge variant="destructive" className="text-xs">
                            {stats.ai.pendingReview}
                          </Badge>
                        )}
                      </Button>
                      <Link to="/admin/photo-assignment">
                        <Button className="h-20 flex-col gap-2 w-full" variant="outline">
                          <Camera className="w-6 h-6" />
                          Photo Assignment
                          {stats.photos.unassigned > 0 && (
                            <Badge variant="secondary" className="text-xs">
                              {stats.photos.unassigned}
                            </Badge>
                          )}
                        </Button>
                      </Link>
                      <Button className="h-20 flex-col gap-2" onClick={() => setActiveTab('analytics')}>
                        <BarChart3 className="w-6 h-6" />
                        View Analytics
                      </Button>
                      <Button className="h-20 flex-col gap-2" onClick={() => setActiveTab('data-quality')}>
                        <AlertTriangle className="w-6 h-6" />
                        Data Quality
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div>
                <UnassignedPhotosWidget />
              </div>
            </div>

            {/* Recent Activity & System Status */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {recentActivity.map(activity => (
                      <div key={activity.id} className="flex items-center gap-3 p-3 border rounded-lg">
                        <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                        <div className="flex-1">
                          <p className="text-sm font-medium">{activity.description}</p>
                          <p className="text-xs text-gray-500">
                            {new Date(activity.timestamp).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Content Quality</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Species without photos</span>
                      <Badge variant={stats.species.total - stats.species.withPhotos > 0 ? "destructive" : "default"}>
                        {stats.species.total - stats.species.withPhotos}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Unassigned photos</span>
                      <Badge variant={stats.photos.unassigned > 0 ? "destructive" : "default"}>
                        {stats.photos.unassigned}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">AI pending review</span>
                      <Badge variant={stats.ai.pendingReview > 0 ? "destructive" : "default"}>
                        {stats.ai.pendingReview}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Draft content</span>
                      <Badge variant="secondary">
                        {stats.species.draft + stats.photos.draft}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Content Matrix Tab */}
          <TabsContent value="matrix">
            <EnhancedSpeciesPhotoMatrix />
          </TabsContent>

          {/* Species Management Tab */}
          <TabsContent value="species">
            <SpeciesManager />
          </TabsContent>

          {/* AI Review Tab */}
          <TabsContent value="ai-review" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="w-5 h-5" />
                    Photos Needing Review
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <AIDashboardEditor 
                    viewType="photos_needing_review" 
                    onUpdate={loadDashboardData}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Sparkles className="w-5 h-5" />
                    AI-Generated Species
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <AIDashboardEditor 
                    viewType="ai_created_species" 
                    onUpdate={loadDashboardData}
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics">
            <ContentAnalytics />
          </TabsContent>

          {/* Tools Tab */}
          <TabsContent value="tools" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Button className="h-20 flex-col gap-2" onClick={() => window.open('/admin/sync', '_blank')}>
                <Database className="w-6 h-6" />
                Data Sync Tools
              </Button>
              <Button className="h-20 flex-col gap-2" onClick={() => window.open('/admin/photo-recovery', '_blank')}>
                <Shield className="w-6 h-6" />
                Photo Recovery
              </Button>
              <Button className="h-20 flex-col gap-2" onClick={() => window.open('/admin/species-enrichment', '_blank')}>
                <Edit className="w-6 h-6" />
                Species Enrichment
              </Button>
              <Button className="h-20 flex-col gap-2" onClick={() => window.open('/admin/species-import', '_blank')}>
                <Upload className="w-6 h-6" />
                Species Import
              </Button>
              <Button className="h-20 flex-col gap-2" onClick={() => setActiveTab('system')}>
                <Settings className="w-6 h-6" />
                System Settings
              </Button>
              <Button className="h-20 flex-col gap-2" onClick={() => window.open('/ai-dashboard', '_blank')}>
                <Zap className="w-6 h-6" />
                AI Dashboard
              </Button>
            </div>
          </TabsContent>

          {/* Data Quality Tab */}
          <TabsContent value="data-quality">
            <SpeciesDataQualityDashboard />
          </TabsContent>

          {/* System Tab */}
          <TabsContent value="system">
            <DataIntegrityDashboard />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
