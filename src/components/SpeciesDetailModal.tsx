import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { Edit3, Save, X, AlertCircle, CheckCircle } from 'lucide-react';

interface Species {
  id: string;
  name: string;
  common_name: string | null;
  scientific_name: string | null;
  category: string | null;
  conservation_status: string | null;
  description?: string | null;
  published: boolean;
  total_photos?: number;
  published_photos?: number;
}

interface SpeciesDetailModalProps {
  species: Species | null;
  isOpen: boolean;
  onClose: () => void;
  onSpeciesUpdate: () => void;
}

export function SpeciesDetailModal({ 
  species, 
  isOpen, 
  onClose, 
  onSpeciesUpdate 
}: SpeciesDetailModalProps) {
  const [formData, setFormData] = useState<Partial<Species>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Initialize form data when species changes
  React.useEffect(() => {
    if (species) {
      setFormData({
        name: species.name || '',
        common_name: species.common_name || '',
        scientific_name: species.scientific_name || '',
        category: species.category || 'unknown',
        conservation_status: species.conservation_status || 'unknown',
        description: species.description || '',
        published: species.published
      });
      setHasChanges(false);
    }
  }, [species]);

  const handleInputChange = (field: keyof Species, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    if (!species || !hasChanges) return;

    setIsLoading(true);
    try {
      // Update species in database
      const { error: updateError } = await supabase
        .from('species_v2')
        .update({
          name: formData.name,
          common_name: formData.common_name,
          scientific_name: formData.scientific_name,
          category: formData.category,
          conservation_status: formData.conservation_status,
          description: formData.description,
          published: formData.published
        })
        .eq('id', species.id);

      if (updateError) throw updateError;

      // Log the override
      const { error: logError } = await supabase
        .from('ai_override_log')
        .insert({
          table_name: 'species_v2',
          record_id: species.id,
          field_name: 'metadata',
          old_value: JSON.stringify({
            name: species.name,
            common_name: species.common_name,
            scientific_name: species.scientific_name,
            category: species.category,
            conservation_status: species.conservation_status,
            description: species.description,
            published: species.published
          }),
          new_value: JSON.stringify(formData),
          override_type: 'metadata_edit',
          override_reason: 'inline species editing',
          user_id: (await supabase.auth.getUser()).data.user?.id || null
        });

      if (logError) {
        console.error('Failed to log override:', logError);
      }

      toast.success(`Species Updated: ${formData.name || species.name} has been updated successfully.`);

      onSpeciesUpdate();
      onClose();
    } catch (error) {
      console.error('Error updating species:', error);
      toast.error("Failed to update species. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      // Reset form data to original values
      if (species) {
        setFormData({
          name: species.name || '',
          common_name: species.common_name || '',
          scientific_name: species.scientific_name || '',
          category: species.category || 'unknown',
          conservation_status: species.conservation_status || 'unknown',
          description: species.description || '',
          published: species.published
        });
        setHasChanges(false);
      }
    }
    onClose();
  };

  const getConservationStatusColor = (status: string | null) => {
    if (!status) return 'secondary';
    switch (status.toLowerCase()) {
      case 'endangered':
      case 'critically endangered':
        return 'destructive';
      case 'vulnerable':
      case 'near threatened':
        return 'default';
      case 'least concern':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  if (!species) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit3 className="w-5 h-5" />
            Edit Species: {species.common_name || species.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={formData.name || ''}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Species name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="common_name">Common Name</Label>
                <Input
                  id="common_name"
                  value={formData.common_name || ''}
                  onChange={(e) => handleInputChange('common_name', e.target.value)}
                  placeholder="Common name"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="scientific_name">Scientific Name</Label>
              <Input
                id="scientific_name"
                value={formData.scientific_name || ''}
                onChange={(e) => handleInputChange('scientific_name', e.target.value)}
                placeholder="Scientific name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Species description"
                rows={3}
              />
            </div>
          </div>

          {/* Classification */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Classification</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select 
                  value={formData.category || 'unknown'} 
                  onValueChange={(value) => handleInputChange('category', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="unknown" disabled>Select a category</SelectItem>
                    <SelectItem value="mammal">Mammal</SelectItem>
                    <SelectItem value="bird">Bird</SelectItem>
                    <SelectItem value="reptile">Reptile</SelectItem>
                    <SelectItem value="amphibian">Amphibian</SelectItem>
                    <SelectItem value="fish">Fish</SelectItem>
                    <SelectItem value="invertebrate">Invertebrate</SelectItem>
                    <SelectItem value="plant">Plant</SelectItem>
                    <SelectItem value="fungus">Fungus</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="conservation_status">Conservation Status</Label>
                <Select 
                  value={formData.conservation_status || 'unknown'}
                  onValueChange={(value) => handleInputChange('conservation_status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="unknown" disabled>Select a status</SelectItem>
                    <SelectItem value="least concern">Least Concern</SelectItem>
                    <SelectItem value="near threatened">Near Threatened</SelectItem>
                    <SelectItem value="vulnerable">Vulnerable</SelectItem>
                    <SelectItem value="endangered">Endangered</SelectItem>
                    <SelectItem value="critically endangered">Critically Endangered</SelectItem>
                    <SelectItem value="extinct in the wild">Extinct in the Wild</SelectItem>
                    <SelectItem value="extinct">Extinct</SelectItem>
                    <SelectItem value="data deficient">Data Deficient</SelectItem>
                    <SelectItem value="not evaluated">Not Evaluated</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Status and Statistics */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Status & Statistics</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Publication Status</Label>
                <div className="flex items-center gap-2">
                  <Button
                    variant={formData.published ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleInputChange('published', !formData.published)}
                    className="w-full"
                  >
                    {formData.published ? (
                      <>
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Published
                      </>
                    ) : (
                      <>
                        <X className="w-4 h-4 mr-2" />
                        Draft
                      </>
                    )}
                  </Button>
                </div>
              </div>

              {species.total_photos !== undefined && (
                <div className="space-y-2">
                  <Label>Total Photos</Label>
                  <div className="text-2xl font-bold text-center">
                    {species.total_photos}
                  </div>
                </div>
              )}

              {species.published_photos !== undefined && (
                <div className="space-y-2">
                  <Label>Published Photos</Label>
                  <div className="text-2xl font-bold text-center">
                    {species.published_photos}
                  </div>
                </div>
              )}
            </div>

            {/* Current Conservation Status Display */}
            {species.conservation_status && (
              <div className="space-y-2">
                <Label>Current Conservation Status</Label>
                <Badge variant={getConservationStatusColor(species.conservation_status)}>
                  {species.conservation_status}
                </Badge>
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {hasChanges && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <AlertCircle className="w-4 h-4" />
                Unsaved changes
              </div>
            )}
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleCancel} disabled={isLoading}>
              Cancel
            </Button>
            <Button 
              onClick={handleSave} 
              disabled={isLoading || !hasChanges}
              className="min-w-[100px]"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 