/**
 * @deprecated This component is deprecated. Use MultiSelectFilter directly instead:
 * 
 * ```tsx
 * <MultiSelectFilter
 *   options={categories}
 *   selected={selectedCategories}
 *   onChange={onCategoryChange}
 *   label="Filter by Category"
 *   colorScheme="green"
 * />
 * ```
 * 
 * This wrapper is maintained for backward compatibility.
 */

import { MultiSelectFilter } from './MultiSelectFilter';

interface CategoryFilterProps {
  categories: string[];
  selectedCategories: string[];
  onCategoryChange: (categories: string[]) => void;
}

export const CategoryFilter = ({ 
  categories, 
  selectedCategories, 
  onCategoryChange 
}: CategoryFilterProps) => {
  return (
    <MultiSelectFilter
      options={categories}
      selected={selectedCategories}
      onChange={onCategoryChange}
      label="Filter by Category"
      colorScheme="green"
    />
  );
};
