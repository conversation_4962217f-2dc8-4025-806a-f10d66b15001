import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { isValidUUID } from '@/utils/validate';
import { Card, CardContent } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { AlertTriangle, ArrowLeft } from 'lucide-react';

interface UUIDRouteGuardProps {
  component: React.ComponentType;
}

const UUIDRouteGuard: React.FC<UUIDRouteGuardProps> = ({ component: Component, ...rest }) => {
  const { id } = useParams<{ id: string }>();

  if (!isValidUUID(id)) {
    console.error(`Invalid UUID detected at routing level: ${id}`);
    return (
      <div className="container mx-auto py-8">
        <Button asChild variant="outline" className="mb-6">
          <Link to="/species">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Species List
          </Link>
        </Button>
        
        <Card className="border-red-200 bg-red-50/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-red-800 mb-2">Invalid Species ID</h2>
              <p className="text-red-600 mb-4">
                The species ID provided in the URL is not a valid format.
              </p>
              <p className="text-sm text-red-500">
                Please use the species list to find the correct species.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return <Component {...rest} />;
};

export default UUIDRouteGuard; 