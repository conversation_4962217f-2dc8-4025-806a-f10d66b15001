import React from "react";
import { Link } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Camera, Book, Ruler, Weight, Lightbulb, Pencil } from "lucide-react";
import type { Tables } from "@/integrations/supabase/types";
import { extractFunFacts, parseSize, parseWeight } from "@/hooks/useSpeciesData";
import placeholder from '/placeholder.svg';

type SpeciesV2 = Tables<"species_v2">;
type Photo = Tables<"photos_v2">;

interface SpeciesWithPhotos extends SpeciesV2 {
  photos: Photo[];
  photo_count: number;
}

interface SpeciesCardV2Props {
  species: SpeciesWithPhotos;
  isAdmin?: boolean;
  onEdit?: (species: SpeciesWithPhotos) => void;
}

const getConservationStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'critically endangered':
      return 'bg-red-600 text-white hover:bg-red-700';
    case 'endangered':
      return 'bg-red-500 text-white hover:bg-red-600';
    case 'vulnerable':
      return 'bg-orange-500 text-white hover:bg-orange-600';
    case 'near threatened':
      return 'bg-yellow-500 text-black hover:bg-yellow-600';
    case 'least concern':
      return 'bg-wildlife-success text-white hover:bg-wildlife-primary';
    default:
      return 'bg-gray-500 text-white hover:bg-gray-600';
  }
};

const getCategoryIcon = (category: string) => {
  return <Book className="w-4 h-4" />;
};

// Helper function to clean habitat text - remove brackets, quotes, and array formatting
const cleanHabitatText = (habitat: string) => {
  if (!habitat) return '';
  return habitat
    .replace(/^\["|"\]$/g, '') // Remove opening [" and closing "]
    .replace(/^"|"$/g, '') // Remove standalone quotes
    .replace(/\\"/g, '"') // Unescape quotes
    .replace(/\[|\]/g, '') // Remove any remaining brackets
    .trim();
};

// Enhanced function to clean text content of formatting issues and stray characters
const cleanTextContent = (text: string): string => {
  if (!text) return '';
  
  return text
    // Remove JSON-like structures
    .replace(/\{"[^"]*":\s*"[^"]*"[^}]*\}/g, '')
    .replace(/\[object Object\]/g, '')
    .replace(/\{"state":"empty","value":null,"isStale":true\}/g, '')
    
    // Remove stray characters and formatting
    .replace(/[{}[\]]/g, '') // Remove brackets and braces
    .replace(/•/g, '') // Remove bullet points
    .replace(/\s*-\s*/g, ' ') // Replace dashes with spaces
    .replace(/\s*\*\s*/g, ' ') // Replace asterisks with spaces
    .replace(/^\d+\.\s*/gm, '') // Remove numbered list formatting
    
    // Clean up quotes and escape characters
    .replace(/\\"/g, '"') // Unescape quotes
    .replace(/^["'\s]+|["'\s]+$/g, '') // Remove leading/trailing quotes and spaces
    .replace(/\s{2,}/g, ' ') // Replace multiple spaces with single space
    
    // Remove common prefixes that shouldn't be in main text
    .replace(/^(fun fact|fact|did you know)[:\s]*/i, '')
    .replace(/🎯\s*(fun facts?[:\s]*)?/i, '')
    .replace(/^AI Facts?[:\s]*/i, '')
    
    .trim();
};

const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
  e.currentTarget.src = placeholder;
};

export const SpeciesCardV2 = ({ species, isAdmin = false, onEdit }: SpeciesCardV2Props) => {
  const mainPhoto = species.photos?.[0];
  const photoCount = species.photos?.length || species.photo_count || 0;
  const cleanedHabitat = cleanHabitatText(species.habitat || '');
  const funFacts = extractFunFacts(species);
  const funFact = funFacts.length > 0 ? funFacts[0] : null;
  const sizeCm = parseSize(species.size_cm);
  const weightG = parseWeight(species.weight_g);

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    if (onEdit) onEdit(species);
  };

  // Show all species cards, not just ones with photos
  return (
    <div className="relative group">
      <Link to={`/species/${species.id}`} className="block">
        <Card className="overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gradient-to-br from-white to-wildlife-secondary border-border cursor-pointer h-full">
          <div className="relative h-48 overflow-hidden">
            {species.photos && species.photos.length > 0 ? (
              <img
                src={species.photos[0]?.url ?? placeholder}
                alt={species.name || 'Species photo'}
                className="w-full h-48 object-cover"
                onError={handleImageError}
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-wildlife-secondary to-muted flex items-center justify-center">
                <Camera className="w-12 h-12 text-wildlife-primary" />
              </div>
            )}
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
            <div className="absolute bottom-2 left-2 flex gap-2">
              <Badge variant="secondary" className="bg-white/90 text-wildlife-primary-darker">
                {getCategoryIcon(species.category || '')}
                <span className="ml-1">{species.category}</span>
              </Badge>
              {species.conservation_status && (
                <Badge className={getConservationStatusColor(species.conservation_status)}>
                  {species.conservation_status}
                </Badge>
              )}
            </div>
          </div>
          
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-bold text-foreground">
              {species.name || 'Unknown Species'}
            </CardTitle>
            {species.scientific_name && (
              <p className="text-sm italic text-wildlife-primary-dark font-medium">
                {species.scientific_name}
              </p>
            )}
            {species.family && (
              <p className="text-xs text-wildlife-primary">
                Family: {species.family}
              </p>
            )}
          </CardHeader>
          
          <CardContent className="pt-0 flex flex-col justify-between flex-grow">
            <div>
              {species.description && !funFact && (
                <p className="text-sm text-gray-700 mb-3 line-clamp-3">
                  {cleanTextContent(species.description)}
                </p>
              )}
              
              {funFact && (
                <div className="mb-3 p-2 bg-yellow-50 rounded-md border border-yellow-100">
                  <div className="flex items-start gap-2">
                    <Lightbulb className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                    <p className="text-sm text-gray-700 italic line-clamp-3">
                      {funFact}
                    </p>
                  </div>
                </div>
              )}
              
              {/* Physical characteristics */}
              {(sizeCm || weightG) && (
                <div className="flex gap-4 text-xs text-gray-600 mb-3">
                  {sizeCm && (
                    <div className="flex items-center gap-1">
                      <Ruler className="w-3 h-3" />
                      <span>{sizeCm.value}{sizeCm.unit}</span>
                    </div>
                  )}
                  {weightG && (
                    <div className="flex items-center gap-1">
                      <Weight className="w-3 h-3" />
                      <span>{weightG.value}{weightG.unit}</span>
                    </div>
                  )}
                </div>
              )}
              
              <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                {cleanedHabitat && (
                  <div>
                    <span className="font-semibold text-wildlife-primary-dark">Habitat:</span>
                    <p className="truncate">{cleanedHabitat}</p>
                  </div>
                )}
                {species.diet && (
                  <div>
                    <span className="font-semibold text-wildlife-primary-dark">Diet:</span>
                    <p className="truncate">{cleanTextContent(species.diet)}</p>
                  </div>
                )}
              </div>
            </div>
            
            {photoCount > 0 && (
              <div className="mt-3 pt-3 border-t border-border">
                <div className="flex items-center gap-1 text-sm text-wildlife-primary">
                  <Camera className="w-4 h-4" />
                  <span>{photoCount} photo{photoCount !== 1 ? 's' : ''}</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </Link>
      {/* Admin edit overlay */}
      {isAdmin && (
        <button
          className="absolute top-2 right-2 z-10 p-1 rounded-full bg-white/80 hover:bg-wildlife-secondary border border-border shadow transition"
          onClick={handleEdit}
          title="Edit species"
        >
          <Pencil className="w-4 h-4 text-wildlife-primary-dark" />
        </button>
      )}
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default React.memo(SpeciesCardV2);