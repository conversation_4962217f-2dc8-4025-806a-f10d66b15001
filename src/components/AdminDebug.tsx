import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAdminAuth } from '@/hooks/useAdminAuth';

export function AdminDebug() {
  const { isAdmin, adminUser, loading } = useAdminAuth();
  
  const adminEmail = import.meta.env.VITE_ADMIN_EMAIL;
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const hasSupabaseKey = !!import.meta.env.VITE_SUPABASE_ANON_KEY;

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Admin Authentication Debug</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-medium mb-2">Environment Variables</h3>
          <div className="space-y-2 text-sm">
            <div>
              <strong>VITE_ADMIN_EMAIL:</strong> 
              <Badge variant={adminEmail ? "default" : "destructive"} className="ml-2">
                {adminEmail || "Not Set"}
              </Badge>
            </div>
            <div>
              <strong>VITE_SUPABASE_URL:</strong> 
              <Badge variant={supabaseUrl ? "default" : "destructive"} className="ml-2">
                {supabaseUrl ? "Set" : "Not Set"}
              </Badge>
            </div>
            <div>
              <strong>VITE_SUPABASE_ANON_KEY:</strong> 
              <Badge variant={hasSupabaseKey ? "default" : "destructive"} className="ml-2">
                {hasSupabaseKey ? "Set" : "Not Set"}
              </Badge>
            </div>
          </div>
        </div>

        <div>
          <h3 className="font-medium mb-2">Authentication Status</h3>
          <div className="space-y-2 text-sm">
            <div>
              <strong>Loading:</strong> 
              <Badge variant={loading ? "default" : "secondary"} className="ml-2">
                {loading ? "Yes" : "No"}
              </Badge>
            </div>
            <div>
              <strong>Is Admin:</strong> 
              <Badge variant={isAdmin ? "default" : "secondary"} className="ml-2">
                {isAdmin ? "Yes" : "No"}
              </Badge>
            </div>
            <div>
              <strong>Admin User:</strong> 
              <Badge variant={adminUser ? "default" : "secondary"} className="ml-2">
                {adminUser ? adminUser.email : "None"}
              </Badge>
            </div>
          </div>
        </div>

        <div>
          <h3 className="font-medium mb-2">Current URL</h3>
          <div className="text-sm bg-gray-100 p-2 rounded">
            {window.location.href}
          </div>
        </div>

        <div>
          <h3 className="font-medium mb-2">Magic Link Redirect URL</h3>
          <div className="text-sm bg-gray-100 p-2 rounded">
            {`${window.location.origin}/admin/dashboard`}
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 