import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { getServiceRoleClient } from '@/lib/supabaseServiceRole';
import { Edit, Save, X, CheckCircle, AlertCircle, Eye, EyeOff } from 'lucide-react';

interface PhotoRecord {
  id: number;
  url: string;
  title: string | null;
  description: string | null;
  location: string | null;
  species_id: string | null;
  published: boolean;
  created_at: string;
  species_name: string | null;
  scientific_name: string | null;
  common_name: string | null;
  ai_generated_metadata: boolean;
  needs_review: boolean;
}

interface SpeciesRecord {
  id: string;
  name: string;
  common_name: string | null;
  scientific_name: string | null;
  published: boolean;
  created_at: string;
  updated_at: string;
  created_by_ai: boolean;
  ai_confidence: number;
}

interface EditData {
  title?: string;
  description?: string;
  name?: string;
  common_name?: string;
  scientific_name?: string;
  species_id?: string;
  published?: boolean;
  location?: string;
}

interface AIDashboardEditorProps {
  viewType: 'photos_needing_review' | 'ai_created_species';
  onUpdate?: () => void;
}

export function AIDashboardEditor({ viewType, onUpdate }: AIDashboardEditorProps) {
  const [records, setRecords] = useState<(PhotoRecord | SpeciesRecord)[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingId, setEditingId] = useState<string | number | null>(null);
  const [editData, setEditData] = useState<EditData>({});
  const [speciesOptions, setSpeciesOptions] = useState<Array<{ id: string; name: string; common_name: string | null }>>([]);

  useEffect(() => {
    loadRecords();
    if (viewType === 'photos_needing_review') {
      loadSpeciesOptions();
    }
  }, [viewType]);

  const loadRecords = async () => {
    try {
      setLoading(true);

      // Use actual v2 tables instead of views
      let query;
      if (viewType === 'photos_needing_review') {
        // Load unpublished photos from photos_v2 (without species join due to relationship issues)
        query = supabase
          .from('photos_v2')
          .select('*')
          .eq('published', false)
          .order('created_at', { ascending: false });
      } else if (viewType === 'ai_created_species') {
        // Load AI-generated species from species_v2
        query = supabase
          .from('species_v2')
          .select('*')
          .eq('ai_generated', true)
          .order('created_at', { ascending: false });
      } else {
        throw new Error(`Unknown view type: ${viewType}`);
      }

      const { data, error } = await query;
      if (error) throw error;
      setRecords(data || []);
    } catch (error) {
      console.error('Error loading records:', error);
      toast.error("Failed to load records");
      setRecords([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  const loadSpeciesOptions = async () => {
    try {
      const { data, error } = await supabase
        .from('species_v2')
        .select('id, name, common_name')
        .eq('published', true)
        .order('name');

      if (error) throw error;
      setSpeciesOptions(data || []);
    } catch (error) {
      console.error('Error loading species options:', error);
    }
  };

  const startEditing = (record: PhotoRecord | SpeciesRecord) => {
    setEditingId(record.id);
    setEditData({
      title: 'title' in record ? record.title : '',
      description: 'description' in record ? record.description : '',
      name: 'name' in record ? record.name : '',
      common_name: 'common_name' in record ? record.common_name : '',
      scientific_name: 'scientific_name' in record ? record.scientific_name : '',
      species_id: 'species_id' in record ? record.species_id : '',
      published: record.published,
    });
  };

  const cancelEditing = () => {
    setEditingId(null);
    setEditData({});
  };

  const saveChanges = async () => {
    if (!editingId) return;

    try {
      const tableName = viewType === 'photos_needing_review' ? 'photos_v2' : 'species_v2';
      const { error } = await supabase
        .from(tableName)
        .update(editData)
        .eq('id', editingId);

      if (error) throw error;

      toast.success("Changes saved successfully");

      setEditingId(null);
      setEditData({});
      loadRecords();
      onUpdate?.();
    } catch (error) {
      console.error('Error saving changes:', error);
      toast.error("Failed to save changes");
    }
  };

  const togglePublished = async (record: PhotoRecord | SpeciesRecord) => {
    try {
      const tableName = viewType === 'photos_needing_review' ? 'photos_v2' : 'species_v2';
      // Use service role client for admin operations
      const serviceRoleClient = getServiceRoleClient();
      const { error } = await serviceRoleClient
        .from(tableName)
        .update({ published: !record.published })
        .eq('id', record.id);

      if (error) throw error;

      toast.success(`${record.published ? 'Unpublished' : 'Published'} successfully`);

      loadRecords();
      onUpdate?.();
    } catch (error) {
      console.error('Error toggling published status:', error);
      toast.error("Failed to update status");
    }
  };

  const renderPhotoRecord = (record: PhotoRecord) => {
    const isEditing = editingId === record.id;

    return (
      <Card key={record.id} className="mb-4">
        <CardContent className="p-4">
          <div className="flex items-start gap-4">
            {/* Photo thumbnail */}
            <div className="flex-shrink-0">
              <img
                src={record.url || '/placeholder.svg'}
                alt={record.title || 'Photo'}
                className="w-20 h-20 object-cover rounded-lg"
                onError={(e) => {
                  e.currentTarget.src = '/placeholder.svg';
                }}
              />
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Badge variant={record.published ? "default" : "secondary"}>
                    {record.published ? "Published" : "Draft"}
                  </Badge>
                  {record.ai_generated_metadata && (
                    <Badge variant="outline">AI Generated</Badge>
                  )}
                  {record.needs_review && (
                    <Badge variant="destructive">Needs Review</Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => togglePublished(record)}
                  >
                    {record.published ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </Button>
                  {isEditing ? (
                    <>
                      <Button size="sm" onClick={saveChanges}>
                        <Save className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={cancelEditing}>
                        <X className="w-4 h-4" />
                      </Button>
                    </>
                  ) : (
                    <Button variant="outline" size="sm" onClick={() => startEditing(record)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              </div>

              {isEditing ? (
                <div className="space-y-3">
                  <Input
                    placeholder="Title"
                    value={editData.title || ''}
                    onChange={(e) => setEditData({ ...editData, title: e.target.value })}
                  />
                  <Textarea
                    placeholder="Description"
                    value={editData.description || ''}
                    onChange={(e) => setEditData({ ...editData, description: e.target.value })}
                  />
                  <Input
                    placeholder="Location"
                    value={editData.location || ''}
                    onChange={(e) => setEditData({ ...editData, location: e.target.value })}
                  />
                  <Select
                    value={editData.species_id || ''}
                    onValueChange={(value) => setEditData({ ...editData, species_id: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select species" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">No species</SelectItem>
                      {speciesOptions.map((species) => (
                        <SelectItem key={species.id} value={species.id}>
                          {species.name} {species.common_name && `(${species.common_name})`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ) : (
                <div>
                  <h3 className="font-semibold text-lg mb-1">{record.title || 'Untitled'}</h3>
                  {record.description && (
                    <p className="text-sm text-muted-foreground mb-2">{record.description}</p>
                  )}
                  {record.location && (
                    <p className="text-sm text-muted-foreground mb-2">
                      📍 {record.location}
                    </p>
                  )}
                  {record.species_name && (
                    <p className="text-sm">
                      <span className="font-medium">Species:</span> {record.species_name}
                      {record.common_name && ` (${record.common_name})`}
                    </p>
                  )}
                  <p className="text-xs text-muted-foreground mt-2">
                    Created: {new Date(record.created_at).toLocaleDateString()}
                  </p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderSpeciesRecord = (record: SpeciesRecord) => {
    const isEditing = editingId === record.id;

    return (
      <Card key={record.id} className="mb-4">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Badge variant={record.published ? "default" : "secondary"}>
                {record.published ? "Published" : "Draft"}
              </Badge>
              {record.created_by_ai && (
                <Badge variant="outline">AI Created</Badge>
              )}
              {record.ai_confidence > 0 && (
                <Badge variant="outline">
                  {Math.round(record.ai_confidence * 100)}% confidence
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => togglePublished(record)}
              >
                {record.published ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </Button>
              {isEditing ? (
                <>
                  <Button size="sm" onClick={saveChanges}>
                    <Save className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={cancelEditing}>
                    <X className="w-4 h-4" />
                  </Button>
                </>
              ) : (
                <Button variant="outline" size="sm" onClick={() => startEditing(record)}>
                  <Edit className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>

          {isEditing ? (
            <div className="space-y-3">
              <Input
                placeholder="Species name"
                value={editData.name || ''}
                onChange={(e) => setEditData({ ...editData, name: e.target.value })}
              />
              <Input
                placeholder="Common name"
                value={editData.common_name || ''}
                onChange={(e) => setEditData({ ...editData, common_name: e.target.value })}
              />
              <Input
                placeholder="Scientific name"
                value={editData.scientific_name || ''}
                onChange={(e) => setEditData({ ...editData, scientific_name: e.target.value })}
              />
            </div>
          ) : (
            <div>
              <h3 className="font-semibold text-lg mb-1">{record.name}</h3>
              {record.common_name && (
                <p className="text-sm text-muted-foreground mb-1">
                  Common: {record.common_name}
                </p>
              )}
              {record.scientific_name && (
                <p className="text-sm text-muted-foreground mb-2">
                  Scientific: <em>{record.scientific_name}</em>
                </p>
              )}
              <p className="text-xs text-muted-foreground">
                Created: {new Date(record.created_at).toLocaleDateString()}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">
          {viewType === 'photos_needing_review' ? 'Photos Needing Review' : 'AI-Created Species'}
        </h2>
        <Button onClick={loadRecords} variant="outline">
          Refresh
        </Button>
      </div>

      {records.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <CheckCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No items to review</h3>
            <p className="text-muted-foreground">
              {viewType === 'photos_needing_review' 
                ? 'All photos have been reviewed and processed.'
                : 'No AI-created species are pending review.'
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div>
          {records.map((record) => 
            viewType === 'photos_needing_review' 
              ? renderPhotoRecord(record as PhotoRecord)
              : renderSpeciesRecord(record as SpeciesRecord)
          )}
        </div>
      )}
    </div>
  );
} 