import React, { useState, useEffect } from 'react';
import { supabase } from '../integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { <PERSON><PERSON> } from './ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Progress } from './ui/progress';
import { Alert, AlertDescription } from './ui/alert';
import { Separator } from './ui/separator';
import { 
  Database, 
  Image, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Info,
  ExternalLink,
  Download,
  Copy,
  FileCheck,
  FileX,
  Trash2,
  RefreshCw,
  Hash,
  Link,
  Users,
  Eye
} from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { toast } from 'sonner';

interface DataStats {
  totalSpecies: number;
  totalPhotos: number;
  speciesWithPhotos: number;
  photosWithSpecies: number;
  orphanedPhotos: number;
  speciesWithoutPhotos: number;
  publishedSpecies: number;
  publishedPhotos: number;
  duplicatePhotos: number;
}

interface StorageVerificationResult {
  checked: number;
  found: number;
  missing: number;
}

interface SpeciesAnalysis {
  id: string;
  name: string;
  photoCount: number;
  hasDescription: boolean;
  hasScientificName: boolean;
  hasCategory: boolean;
  hasConservationStatus: boolean;
  published: boolean;
  completeness: number;
}

interface PhotoAnalysis {
  id: number;
  title: string;
  speciesName: string | null;
  hasDescription: boolean;
  hasLocation: boolean;
  hasPhotographer: boolean;
  urlPattern: string;
  published: boolean;
  completeness: number;
}

interface StorageAnalysis {
  bucket: string;
  folder: string;
  fileCount: number;
  totalSize: number;
  urlPattern: string;
}

interface DataIssue {
  issue_type: string;
  count: number;
}

interface PhotoIssue {
  id: number;
  url: string | null;
  title: string | null;
  species_id: string | null;
  species_name: string | null;
  cleanup_status: string;
  issue_description: string;
  created_at: string;
}

interface SpeciesIssue {
  id: string;
  name: string;
  common_name: string | null;
  scientific_name: string | null;
  published: boolean;
  created_at: string;
  issue_type: string;
}

interface CleanupSummary {
  orphaned_photos: number;
  duplicate_photos: number;
  empty_species_unpublished: number;
  broken_urls: number;
  total_issues_fixed: number;
}

const DataConsistencyDashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DataStats | null>(null);
  const [speciesAnalysis, setSpeciesAnalysis] = useState<SpeciesAnalysis[]>([]);
  const [photoAnalysis, setPhotoAnalysis] = useState<PhotoAnalysis[]>([]);
  const [storageAnalysis, setStorageAnalysis] = useState<StorageAnalysis[]>([]);
  const [issues, setIssues] = useState<DataIssue[]>([]);
  const [photoIssues, setPhotoIssues] = useState<PhotoIssue[]>([]);
  const [speciesIssues, setSpeciesIssues] = useState<SpeciesIssue[]>([]);
  const [activeTab, setActiveTab] = useState('overview');
  const [verifyingStorage, setVerifyingStorage] = useState(false);
  const [storageVerificationResult, setStorageVerificationResult] = useState<StorageVerificationResult | null>(null);
  const [cleanupLoading, setCleanupLoading] = useState(false);
  const [cleanupSummary, setCleanupSummary] = useState<CleanupSummary | null>(null);
  const [showCleanupDialog, setShowCleanupDialog] = useState(false);
  const [selectedSpecies, setSelectedSpecies] = useState<string>('');
  const [availableSpecies, setAvailableSpecies] = useState<Array<{ id: string; name: string }>>([]);


  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load basic statistics
      await loadBasicStats();
      
      // Load detailed analysis
      await loadSpeciesAnalysis();
      await loadPhotoAnalysis();
      await loadStorageAnalysis();
      
      // Identify issues
      identifyIssues();
      
    } catch (error) {
      console.error('Error loading data consistency analysis:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadBasicStats = async () => {
    // Get total counts
    const { count: totalSpecies } = await supabase
      .from('species_v2')
      .select('*', { count: 'exact', head: true });

    const { count: totalPhotos } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true });

    // Get species with photos
    const { data: photosWithSpeciesData, error: photosError } = await supabase
      .from('photos_v2')
      .select('species_id')
      .not('species_id', 'is', null);

    if (photosError) throw photosError;
    const uniqueSpeciesIds = new Set((photosWithSpeciesData || []).map(p => p.species_id));
    
    // Get photos with species
    const { count: photosWithSpecies } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true })
      .not('species_id', 'is', null);

    // Get published counts
    const { count: publishedSpecies } = await supabase
      .from('species_v2')
      .select('*', { count: 'exact', head: true })
      .eq('published', true);

    const { count: publishedPhotos } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true })
      .eq('published', true);

    const { data: duplicateData, error: duplicateError } = await supabase.rpc('get_duplicate_photos');
    if (duplicateError) throw duplicateError;
    const duplicatePhotos = duplicateData ? duplicateData.reduce((acc, curr) => acc + curr.duplicate_count - 1, 0) : 0;

    setStats({
      totalSpecies: totalSpecies || 0,
      totalPhotos: totalPhotos || 0,
      speciesWithPhotos: uniqueSpeciesIds.size,
      photosWithSpecies: photosWithSpecies || 0,
      orphanedPhotos: (totalPhotos || 0) - (photosWithSpecies || 0),
      speciesWithoutPhotos: (totalSpecies || 0) - uniqueSpeciesIds.size,
      publishedSpecies: publishedSpecies || 0,
      publishedPhotos: publishedPhotos || 0,
      duplicatePhotos: duplicatePhotos,
    });
  };

  const loadSpeciesAnalysis = async () => {
    const { data: species, error } = await supabase
      .from('species_v2')
      .select(`
        id,
        name,
        description,
        scientific_name,
        category,
        conservation_status,
        published,
        photo_count
      `)
      .order('name');

    if (error) {
      console.error('Error loading species analysis:', error);
      return;
    }

    const analysis: SpeciesAnalysis[] = species?.map(species => {
      const hasDescription = !!species.description;
      const hasScientificName = !!species.scientific_name;
      const hasCategory = !!species.category;
      const hasConservationStatus = !!species.conservation_status;
      
      const completeness = [
        hasDescription,
        hasScientificName,
        hasCategory,
        hasConservationStatus,
        species.photo_count > 0
      ].filter(Boolean).length / 5 * 100;

      return {
        id: species.id,
        name: species.name,
        photoCount: species.photo_count || 0,
        hasDescription,
        hasScientificName,
        hasCategory,
        hasConservationStatus,
        published: species.published,
        completeness: Math.round(completeness)
      };
    }) || [];

    setSpeciesAnalysis(analysis);
  };

  const loadPhotoAnalysis = async () => {
    const { data: photos } = await supabase
      .from('photos_v2')
      .select(`
        id,
        title,
        description,
        location,
        photographer,
        url,
        published,
        species_id,
        species:species_id(name)
      `);

    const analysis = photos?.map(photo => {
      const hasDescription = !!photo.description;
      const hasLocation = !!photo.location;
      const hasPhotographer = !!photo.photographer;
      
      const completeness = [
        hasDescription,
        hasLocation,
        hasPhotographer,
        !!photo.species_id
      ].filter(Boolean).length / 4 * 100;

      // Extract URL pattern
      const urlPattern = photo.url ? extractUrlPattern(photo.url) : 'No URL';

      return {
        id: photo.id,
        title: photo.title || 'Untitled',
        speciesName: photo.species?.name || null,
        hasDescription,
        hasLocation,
        hasPhotographer,
        urlPattern,
        published: photo.published,
        completeness: Math.round(completeness)
      };
    }) || [];

    setPhotoAnalysis(analysis);
  };

  const loadStorageAnalysis = async () => {
    // Analyze URL patterns to understand storage structure
    const { data: photos } = await supabase
      .from('photos_v2')
      .select('url')
      .not('url', 'is', null);

    const storageMap = new Map<string, StorageAnalysis>();

    photos?.forEach(photo => {
      if (photo.url) {
        const pattern = extractUrlPattern(photo.url);
        const parts = pattern.split('/');
        const bucket = parts[0] || 'unknown';
        const folder = parts[1] || 'unknown';

        const key = `${bucket}/${folder}`;
        const existing = storageMap.get(key);
        
        if (existing) {
          existing.fileCount++;
        } else {
          storageMap.set(key, {
            bucket,
            folder,
            fileCount: 1,
            totalSize: 0, // We can't get actual file sizes without storage API
            urlPattern: pattern
          });
        }
      }
    });

    setStorageAnalysis(Array.from(storageMap.values()));
  };

  const extractUrlPattern = (url: string): string => {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      // Extract bucket and folder structure
      const publicIndex = pathParts.findIndex(part => part === 'public');
      if (publicIndex !== -1 && publicIndex + 2 < pathParts.length) {
        return `${pathParts[publicIndex + 1]}/${pathParts[publicIndex + 2]}`;
      }
      return 'unknown';
    } catch {
      return 'invalid-url';
    }
  };

  const identifyIssues = () => {
    const newIssues: string[] = [];

    if (stats) {
      if (stats.orphanedPhotos > 0) {
        newIssues.push(`${stats.orphanedPhotos} photos are not associated with any species`);
      }
      if (stats.speciesWithoutPhotos > 0) {
        newIssues.push(`${stats.speciesWithoutPhotos} species have no photos`);
      }
      if (stats.publishedSpecies === 0) {
        newIssues.push('No species are published');
      }
      if (stats.publishedPhotos === 0) {
        newIssues.push('No photos are published');
      }
    }

    // Check for URL issues
    const urlIssues = photoAnalysis.filter(p => p.urlPattern === 'invalid-url' || p.urlPattern === 'unknown');
    if (urlIssues.length > 0) {
      newIssues.push(`${urlIssues.length} photos have invalid or unknown URL patterns`);
    }

    // Check for low completeness
    const lowCompletenessSpecies = speciesAnalysis.filter(s => s.completeness < 50);
    if (lowCompletenessSpecies.length > 0) {
      newIssues.push(`${lowCompletenessSpecies.length} species have less than 50% data completeness`);
    }

    setIssues(newIssues.map(issue => ({ issue_type: issue, count: 1 })));
  };

  const verifyStorage = async () => {
    setVerifyingStorage(true);
    setStorageVerificationResult(null);

    const { data: orphanedPhotos, error } = await supabase
      .from('photos_v2')
      .select('url')
      .is('species_id', null)
      .not('url', 'is', null)
      .limit(100);

    if (error || !orphanedPhotos) {
      console.error('Error fetching orphaned photos:', error);
      setVerifyingStorage(false);
      return;
    }

    let found = 0;
    let missing = 0;

    for (const photo of orphanedPhotos) {
      if (photo.url) {
        try {
          const res = await fetch(photo.url, { method: 'HEAD' });
          if (res.ok) {
            found++;
          } else {
            missing++;
          }
        } catch (e) {
          missing++;
        }
      }
    }

    setStorageVerificationResult({
      checked: orphanedPhotos.length,
      found,
      missing,
    });
    setVerifyingStorage(false);
  };

  const exportData = () => {
    const data = {
      stats,
      speciesAnalysis,
      photoAnalysis,
      storageAnalysis,
      issues,
      exportDate: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `data-consistency-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const fetchDataIssues = async () => {
    try {
      const { data, error } = await supabase
        .from('photo_issues_summary')
        .select('*');

      if (error) throw error;
      setIssues(data || []);
    } catch (error) {
      console.error('Error fetching data issues:', error);
      toast.error("Failed to fetch data issues");
    }
  };

  const fetchPhotoIssues = async () => {
    try {
      const { data, error } = await supabase
        .from('photos_cleanup_dashboard')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;
      setPhotoIssues(data || []);
    } catch (error) {
      console.error('Error fetching photo issues:', error);
    }
  };

  const fetchSpeciesIssues = async () => {
    try {
      const { data, error } = await supabase
        .from('species_with_no_photos')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setSpeciesIssues(data || []);
    } catch (error) {
      console.error('Error fetching species issues:', error);
    }
  };

  const fetchAvailableSpecies = async () => {
    try {
      const { data, error } = await supabase
        .from('species_v2')
        .select('id, name')
        .eq('published', true)
        .order('name');

      if (error) throw error;
      setAvailableSpecies(data || []);
    } catch (error) {
      console.error('Error fetching species:', error);
    }
  };

  const loadAllData = async () => {
    setLoading(true);
    await Promise.all([
      fetchDataIssues(),
      fetchPhotoIssues(),
      fetchSpeciesIssues(),
      fetchAvailableSpecies()
    ]);
    setLoading(false);
  };

  const runCleanup = async (dryRun: boolean = false) => {
    setCleanupLoading(true);
    try {
      if (dryRun) {
        // For dry run, just show the current issues
        setCleanupSummary({
          orphaned_photos: issues.find(i => i.issue_type === 'orphaned')?.count || 0,
          duplicate_photos: (issues.find(i => i.issue_type === 'duplicate_hash')?.count || 0) + 
                           (issues.find(i => i.issue_type === 'duplicate_url')?.count || 0),
          empty_species_unpublished: speciesIssues.filter(s => s.published).length,
          broken_urls: issues.find(i => i.issue_type === 'missing_file')?.count || 0,
          total_issues_fixed: 0
        });
      } else {
        // Run actual cleanup operations
        const results = await Promise.all([
          supabase.rpc('cleanup_orphaned_photos'),
          supabase.rpc('cleanup_duplicate_photos'),
          supabase.rpc('unpublish_empty_species')
        ]);

        const [orphanedResult, duplicateResult, speciesResult] = results;
        
        setCleanupSummary({
          orphaned_photos: orphanedResult.data || 0,
          duplicate_photos: duplicateResult.data?.[0]?.deleted_count || 0,
          empty_species_unpublished: speciesResult.data || 0,
          broken_urls: issues.find(i => i.issue_type === 'missing_file')?.count || 0,
          total_issues_fixed: (orphanedResult.data || 0) + (duplicateResult.data?.[0]?.deleted_count || 0) + (speciesResult.data || 0)
        });

        // Refresh data after cleanup
        await loadAllData();
      }

      toast.success(dryRun ? "Dry run completed" : "Cleanup completed successfully");
    } catch (error) {
      console.error('Error during cleanup:', error);
      toast.error("Failed to run cleanup");
    } finally {
      setCleanupLoading(false);
    }
  };

  const reassignPhotoSpecies = async (photoId: number, newSpeciesId: string) => {
    try {
      // Get current photo to know old species
      const { data: currentPhoto, error: fetchError } = await supabase
        .from('photos_v2')
        .select('species_id')
        .eq('id', photoId)
        .single();

      if (fetchError) throw fetchError;

      const { error } = await supabase
        .from('photos_v2')
        .update({ species_id: newSpeciesId })
        .eq('id', photoId);

      if (error) throw error;

      // Update species photo counts for affected species
      const affectedSpeciesIds = new Set<string>();

      // Add old species ID if it exists
      if (currentPhoto.species_id) {
        affectedSpeciesIds.add(currentPhoto.species_id);
      }

      // Add new species ID
      affectedSpeciesIds.add(newSpeciesId);

      // Update photo counts for all affected species
      for (const speciesId of affectedSpeciesIds) {
        const { count, error: countError } = await supabase
          .from('photos_v2')
          .select('*', { count: 'exact', head: true })
          .eq('species_id', speciesId)
          .eq('published', true);

        if (!countError) {
          await supabase
            .from('species_v2')
            .update({ photo_count: count || 0 })
            .eq('id', speciesId);
        }
      }

      toast.success("Photo species reassigned successfully");

      // Refresh data
      await loadAllData();
    } catch (error) {
      console.error('Error reassigning photo species:', error);
      toast.error("Failed to reassign photo species");
    }
  };

  const deletePhoto = async (photoId: number) => {
    try {
      const { error } = await supabase
        .from('photos_v2')
        .delete()
        .eq('id', photoId);

      if (error) throw error;

      toast.success("Photo deleted successfully");

      // Refresh data
      await loadAllData();
    } catch (error) {
      console.error('Error deleting photo:', error);
      toast.error("Failed to delete photo");
    }
  };

  const getIssueIcon = (issueType: string) => {
    switch (issueType) {
      case 'orphaned': return <FileX className="h-4 w-4" />;
      case 'duplicate_hash': return <Hash className="h-4 w-4" />;
      case 'duplicate_url': return <Link className="h-4 w-4" />;
      case 'invalid_species': return <Users className="h-4 w-4" />;
      case 'missing_file': return <AlertTriangle className="h-4 w-4" />;
      default: return <Database className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ok':
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />OK</Badge>;
      case 'orphaned':
        return <Badge variant="destructive"><FileX className="h-3 w-3 mr-1" />Orphaned</Badge>;
      case 'duplicate_hash':
      case 'duplicate_url':
        return <Badge variant="secondary"><Hash className="h-3 w-3 mr-1" />Duplicate</Badge>;
      case 'invalid_species':
        return <Badge variant="outline"><Users className="h-3 w-3 mr-1" />Invalid Species</Badge>;
      case 'missing_file':
        return <Badge variant="destructive"><AlertTriangle className="h-3 w-3 mr-1" />Broken URL</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const totalIssues = issues.reduce((sum, issue) => sum + issue.count, 0);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Data Consistency Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor and fix data integrity issues in your wildlife database
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => loadAllData()} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Dialog open={showCleanupDialog} onOpenChange={setShowCleanupDialog}>
            <DialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                Run Cleanup
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Data Cleanup</DialogTitle>
                <DialogDescription>
                  This will permanently delete orphaned and duplicate photos, and unpublish species with no photos.
                  Consider running a dry run first to see what will be affected.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <Button 
                  onClick={() => runCleanup(true)} 
                  disabled={cleanupLoading}
                  variant="outline"
                  className="w-full"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Dry Run (Preview Changes)
                </Button>
                <Button 
                  onClick={() => runCleanup(false)} 
                  disabled={cleanupLoading}
                  variant="destructive"
                  className="w-full"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Run Actual Cleanup
                </Button>
              </div>
              {cleanupSummary && (
                <Alert>
                  <AlertDescription>
                    <strong>Cleanup Summary:</strong><br />
                    • Orphaned photos: {cleanupSummary.orphaned_photos}<br />
                    • Duplicate photos: {cleanupSummary.duplicate_photos}<br />
                    • Empty species unpublished: {cleanupSummary.empty_species_unpublished}<br />
                    • Broken URLs: {cleanupSummary.broken_urls}<br />
                    • Total issues fixed: {cleanupSummary.total_issues_fixed}
                  </AlertDescription>
                </Alert>
              )}
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Issues</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalIssues}</div>
            <Progress value={totalIssues > 0 ? Math.min((totalIssues / 100) * 100, 100) : 0} className="mt-2" />
          </CardContent>
        </Card>

        {issues.map((issue) => (
          <Card key={issue.issue_type}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium capitalize">
                {issue.issue_type.replace('_', ' ')}
              </CardTitle>
              {getIssueIcon(issue.issue_type)}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{issue.count}</div>
              <p className="text-xs text-muted-foreground">
                {issue.count > 0 ? 'Needs attention' : 'No issues'}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Issues */}
      <Tabs defaultValue="photos" className="space-y-4">
        <TabsList>
          <TabsTrigger value="photos">Photo Issues</TabsTrigger>
          <TabsTrigger value="species">Species Issues</TabsTrigger>
        </TabsList>

        <TabsContent value="photos" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Photo Issues</CardTitle>
              <CardDescription>
                Photos with data integrity issues that need attention
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead>Species</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Issue</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {photoIssues.filter(p => p.cleanup_status !== 'ok').map((photo) => (
                    <TableRow key={photo.id}>
                      <TableCell>{photo.id}</TableCell>
                      <TableCell>{photo.title || 'Untitled'}</TableCell>
                      <TableCell>{photo.species_name || 'None'}</TableCell>
                      <TableCell>{getStatusBadge(photo.cleanup_status)}</TableCell>
                      <TableCell className="max-w-xs truncate">{photo.issue_description}</TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          {photo.cleanup_status === 'orphaned' && (
                            <Select onValueChange={(value) => reassignPhotoSpecies(photo.id, value)}>
                              <SelectTrigger className="w-32">
                                <SelectValue placeholder="Reassign" />
                              </SelectTrigger>
                              <SelectContent>
                                {availableSpecies.map((species) => (
                                  <SelectItem key={species.id} value={species.id}>
                                    {species.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          )}
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => deletePhoto(photo.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="species" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Species Issues</CardTitle>
              <CardDescription>
                Species with no associated photos
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Common Name</TableHead>
                    <TableHead>Scientific Name</TableHead>
                    <TableHead>Published</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {speciesIssues.map((species) => (
                    <TableRow key={species.id}>
                      <TableCell>{species.name}</TableCell>
                      <TableCell>{species.common_name || '-'}</TableCell>
                      <TableCell>{species.scientific_name || '-'}</TableCell>
                      <TableCell>
                        <Badge variant={species.published ? "default" : "secondary"}>
                          {species.published ? "Published" : "Unpublished"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            // Navigate to species detail page for manual photo assignment
                            window.open(`/species/${species.id}`, '_blank');
                          }}
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DataConsistencyDashboard; 