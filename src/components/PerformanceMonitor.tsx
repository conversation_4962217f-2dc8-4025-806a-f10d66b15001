import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useMemoryMonitor, usePerformanceMonitor } from '@/utils/performance';
import { 
  Activity, 
  Zap, 
  Database, 
  Clock, 
  BarChart3,
  RefreshCw,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

interface PerformanceMetrics {
  renderTime: number;
  memoryUsage: number;
  queryCount: number;
  cacheHitRate: number;
}

export const PerformanceMonitor = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    memoryUsage: 0,
    queryCount: 0,
    cacheHitRate: 0
  });
  
  const memoryInfo = useMemoryMonitor();
  const { renderCount } = usePerformanceMonitor('PerformanceMonitor');

  useEffect(() => {
    // Update metrics periodically
    const interval = setInterval(() => {
      setMetrics(prev => ({
        ...prev,
        renderTime: performance.now() % 100, // Simulated
        memoryUsage: memoryInfo?.usedJSHeapSize ? 
          (memoryInfo.usedJSHeapSize / memoryInfo.totalJSHeapSize) * 100 : 0,
        queryCount: prev.queryCount + Math.floor(Math.random() * 3),
        cacheHitRate: 75 + Math.random() * 20 // Simulated 75-95%
      }));
    }, 2000);

    return () => clearInterval(interval);
  }, [memoryInfo]);

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          size="sm"
          variant="outline"
          onClick={() => setIsVisible(true)}
          className="bg-white shadow-lg"
        >
          <Activity className="w-4 h-4 mr-2" />
          Performance
        </Button>
      </div>
    );
  }

  const getPerformanceStatus = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return { status: 'good', color: 'text-green-600' };
    if (value <= thresholds.warning) return { status: 'warning', color: 'text-yellow-600' };
    return { status: 'poor', color: 'text-red-600' };
  };

  const renderTimeStatus = getPerformanceStatus(metrics.renderTime, { good: 16, warning: 33 });
  const memoryStatus = getPerformanceStatus(metrics.memoryUsage, { good: 50, warning: 80 });

  return (
    <div className="fixed bottom-4 right-4 z-50 w-80">
      <Card className="shadow-lg">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <Activity className="w-4 h-4" />
              Performance Monitor
            </CardTitle>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsVisible(false)}
            >
              ×
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3 text-xs">
          {/* Render Performance */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-1">
                <Zap className="w-3 h-3" />
                Render Time
              </span>
              <Badge variant={renderTimeStatus.status === 'good' ? 'default' : 'destructive'}>
                {metrics.renderTime.toFixed(1)}ms
              </Badge>
            </div>
            <Progress 
              value={Math.min(metrics.renderTime / 33 * 100, 100)} 
              className="h-1"
            />
          </div>

          {/* Memory Usage */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-1">
                <Database className="w-3 h-3" />
                Memory Usage
              </span>
              <Badge variant={memoryStatus.status === 'good' ? 'default' : 'destructive'}>
                {metrics.memoryUsage.toFixed(1)}%
              </Badge>
            </div>
            <Progress 
              value={metrics.memoryUsage} 
              className="h-1"
            />
          </div>

          {/* Query Performance */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-1">
                <BarChart3 className="w-3 h-3" />
                DB Queries
              </span>
              <Badge variant="outline">
                {metrics.queryCount}
              </Badge>
            </div>
          </div>

          {/* Cache Hit Rate */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                Cache Hit Rate
              </span>
              <Badge variant={metrics.cacheHitRate > 80 ? 'default' : 'secondary'}>
                {metrics.cacheHitRate.toFixed(1)}%
              </Badge>
            </div>
            <Progress 
              value={metrics.cacheHitRate} 
              className="h-1"
            />
          </div>

          {/* Component Renders */}
          <div className="flex items-center justify-between">
            <span className="flex items-center gap-1">
              <RefreshCw className="w-3 h-3" />
              Renders
            </span>
            <Badge variant="outline">
              {renderCount}
            </Badge>
          </div>

          {/* Memory Details */}
          {memoryInfo && (
            <div className="pt-2 border-t space-y-1 text-xs text-gray-600">
              <div className="flex justify-between">
                <span>Used Heap:</span>
                <span>{(memoryInfo.usedJSHeapSize / 1024 / 1024).toFixed(1)}MB</span>
              </div>
              <div className="flex justify-between">
                <span>Total Heap:</span>
                <span>{(memoryInfo.totalJSHeapSize / 1024 / 1024).toFixed(1)}MB</span>
              </div>
            </div>
          )}

          {/* Performance Tips */}
          <div className="pt-2 border-t">
            <div className="text-xs text-gray-600">
              {renderTimeStatus.status === 'poor' && (
                <div className="flex items-center gap-1 text-red-600">
                  <AlertTriangle className="w-3 h-3" />
                  Slow renders detected
                </div>
              )}
              {memoryStatus.status === 'poor' && (
                <div className="flex items-center gap-1 text-red-600">
                  <AlertTriangle className="w-3 h-3" />
                  High memory usage
                </div>
              )}
              {renderTimeStatus.status === 'good' && memoryStatus.status === 'good' && (
                <div className="flex items-center gap-1 text-green-600">
                  <CheckCircle className="w-3 h-3" />
                  Performance is good
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Development-only performance monitor
export const DevPerformanceMonitor = () => {
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return <PerformanceMonitor />;
};
