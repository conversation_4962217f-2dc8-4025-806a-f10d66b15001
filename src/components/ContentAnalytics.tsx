import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/integrations/supabase/client';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  Camera,
  Eye,
  Calendar,
  MapPin,
  Tag,
  Clock,
  Activity,
  Zap,
  FileText
} from 'lucide-react';

interface AnalyticsData {
  overview: {
    totalSpecies: number;
    totalPhotos: number;
    publishedSpecies: number;
    publishedPhotos: number;
    categoriesCount: number;
    avgPhotosPerSpecies: number;
  };
  trends: {
    speciesGrowth: number;
    photoGrowth: number;
    publishingRate: number;
  };
  categories: Array<{
    name: string;
    count: number;
    percentage: number;
  }>;
  topSpecies: Array<{
    id: string;
    name: string;
    photoCount: number;
    published: boolean;
  }>;
  recentActivity: Array<{
    type: 'species_created' | 'photo_uploaded' | 'species_published';
    item: string;
    timestamp: string;
  }>;
  qualityMetrics: {
    speciesWithPhotos: number;
    speciesWithDescriptions: number;
    photosWithMetadata: number;
    completenessScore: number;
  };
}

export function ContentAnalytics() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');

  useEffect(() => {
    loadAnalytics();
  }, [timeRange]);

  const loadAnalytics = async () => {
    setLoading(true);
    try {
      // Load species data
      const { data: speciesData, error: speciesError } = await supabase
        .from('species_v2')
        .select('id, name, category, published, description, created_at');

      if (speciesError) throw speciesError;

      // Load photos data
      const { data: photosData, error: photosError } = await supabase
        .from('photos_v2')
        .select('id, species_id, published, title, description, location, photographer, created_at');

      if (photosError) throw photosError;

      // Calculate analytics
      const totalSpecies = speciesData?.length || 0;
      const totalPhotos = photosData?.length || 0;
      const publishedSpecies = speciesData?.filter(s => s.published).length || 0;
      const publishedPhotos = photosData?.filter(p => p.published).length || 0;

      // Category analysis
      const categoryMap = new Map<string, number>();
      (speciesData || []).forEach(species => {
        const category = species.category || 'Other';
        categoryMap.set(category, (categoryMap.get(category) || 0) + 1);
      });

      const categories = Array.from(categoryMap.entries())
        .map(([name, count]) => ({
          name,
          count,
          percentage: totalSpecies > 0 ? (count / totalSpecies) * 100 : 0
        }))
        .sort((a, b) => b.count - a.count);

      // Top species by photo count
      const speciesPhotoCount = new Map<string, number>();
      (photosData || []).forEach(photo => {
        if (photo.species_id) {
          speciesPhotoCount.set(photo.species_id, (speciesPhotoCount.get(photo.species_id) || 0) + 1);
        }
      });

      const topSpecies = (speciesData || [])
        .map(species => ({
          id: species.id,
          name: species.name,
          photoCount: speciesPhotoCount.get(species.id) || 0,
          published: species.published
        }))
        .sort((a, b) => b.photoCount - a.photoCount)
        .slice(0, 10);

      // Quality metrics
      const speciesWithPhotos = (speciesData || []).filter(s => speciesPhotoCount.get(s.id) > 0).length;
      const speciesWithDescriptions = (speciesData || []).filter(s => s.description && s.description.trim().length > 0).length;
      const photosWithMetadata = (photosData || []).filter(p =>
        (p.title && p.title.trim().length > 0) ||
        (p.description && p.description.trim().length > 0) ||
        (p.location && p.location.trim().length > 0) ||
        (p.photographer && p.photographer.trim().length > 0)
      ).length;

      const completenessScore = totalSpecies > 0 ? 
        ((speciesWithPhotos / totalSpecies) * 0.4 + 
         (speciesWithDescriptions / totalSpecies) * 0.3 + 
         (photosWithMetadata / totalPhotos) * 0.3) * 100 : 0;

      // Recent activity (simplified - in real app would track actual activity)
      const recentActivity = [
        { type: 'species_created' as const, item: 'Red-winged Blackbird', timestamp: new Date().toISOString() },
        { type: 'photo_uploaded' as const, item: 'White-tailed Deer photo', timestamp: new Date(Date.now() - 3600000).toISOString() },
        { type: 'species_published' as const, item: 'Northern Cardinal', timestamp: new Date(Date.now() - 7200000).toISOString() },
      ];

      const analyticsData: AnalyticsData = {
        overview: {
          totalSpecies,
          totalPhotos,
          publishedSpecies,
          publishedPhotos,
          categoriesCount: categories.length,
          avgPhotosPerSpecies: totalSpecies > 0 ? totalPhotos / totalSpecies : 0
        },
        trends: {
          speciesGrowth: 12, // Placeholder - would calculate from time-based data
          photoGrowth: 25,
          publishingRate: publishedSpecies > 0 ? (publishedSpecies / totalSpecies) * 100 : 0
        },
        categories,
        topSpecies,
        recentActivity,
        qualityMetrics: {
          speciesWithPhotos,
          speciesWithDescriptions,
          photosWithMetadata,
          completenessScore
        }
      };

      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <BarChart3 className="w-8 h-8 animate-pulse mx-auto mb-4" />
            <p>Loading analytics...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!analytics) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p>Failed to load analytics data</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{analytics.overview.totalSpecies}</div>
            <div className="text-sm text-gray-600">Total Species</div>
            <div className="flex items-center justify-center mt-1">
              <TrendingUp className="w-3 h-3 text-green-500 mr-1" />
              <span className="text-xs text-green-500">+{analytics.trends.speciesGrowth}%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{analytics.overview.totalPhotos}</div>
            <div className="text-sm text-gray-600">Total Photos</div>
            <div className="flex items-center justify-center mt-1">
              <TrendingUp className="w-3 h-3 text-green-500 mr-1" />
              <span className="text-xs text-green-500">+{analytics.trends.photoGrowth}%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{analytics.overview.publishedSpecies}</div>
            <div className="text-sm text-gray-600">Published Species</div>
            <div className="text-xs text-gray-500 mt-1">
              {analytics.trends.publishingRate.toFixed(1)}% rate
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-emerald-600">{analytics.overview.publishedPhotos}</div>
            <div className="text-sm text-gray-600">Published Photos</div>
            <div className="text-xs text-gray-500 mt-1">
              {analytics.overview.totalPhotos > 0 ? ((analytics.overview.publishedPhotos / analytics.overview.totalPhotos) * 100).toFixed(1) : 0}% rate
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{analytics.overview.categoriesCount}</div>
            <div className="text-sm text-gray-600">Categories</div>
            <div className="text-xs text-gray-500 mt-1">
              {analytics.overview.avgPhotosPerSpecies.toFixed(1)} avg photos
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{analytics.qualityMetrics.completenessScore.toFixed(0)}%</div>
            <div className="text-sm text-gray-600">Completeness</div>
            <div className="text-xs text-gray-500 mt-1">
              Quality Score
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="categories" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="top-species">Top Species</TabsTrigger>
          <TabsTrigger value="quality">Quality</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Tag className="w-5 h-5" />
                Species by Category
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analytics.categories.map(category => (
                  <div key={category.name} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="font-medium">{category.name}</span>
                      <Badge variant="outline">{category.count} species</Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${category.percentage}%` }}
                        />
                      </div>
                      <span className="text-sm text-gray-500 w-12 text-right">
                        {category.percentage.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="top-species" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Camera className="w-5 h-5" />
                Species with Most Photos
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analytics.topSpecies.map((species, index) => (
                  <div key={species.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-500 w-6">#{index + 1}</span>
                      <span className="font-medium">{species.name}</span>
                      {species.published && <Badge variant="default" className="text-xs">Published</Badge>}
                    </div>
                    <Badge variant="outline">{species.photoCount} photos</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="quality" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5" />
                Content Quality Metrics
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex justify-between mb-2">
                  <span>Species with Photos</span>
                  <span>{analytics.qualityMetrics.speciesWithPhotos}/{analytics.overview.totalSpecies}</span>
                </div>
                <Progress value={(analytics.qualityMetrics.speciesWithPhotos / analytics.overview.totalSpecies) * 100} />
              </div>
              
              <div>
                <div className="flex justify-between mb-2">
                  <span>Species with Descriptions</span>
                  <span>{analytics.qualityMetrics.speciesWithDescriptions}/{analytics.overview.totalSpecies}</span>
                </div>
                <Progress value={(analytics.qualityMetrics.speciesWithDescriptions / analytics.overview.totalSpecies) * 100} />
              </div>
              
              <div>
                <div className="flex justify-between mb-2">
                  <span>Photos with Metadata</span>
                  <span>{analytics.qualityMetrics.photosWithMetadata}/{analytics.overview.totalPhotos}</span>
                </div>
                <Progress value={(analytics.qualityMetrics.photosWithMetadata / analytics.overview.totalPhotos) * 100} />
              </div>
              
              <div className="pt-4 border-t">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Overall Completeness Score</span>
                  <Badge variant={analytics.qualityMetrics.completenessScore > 70 ? "default" : "secondary"}>
                    {analytics.qualityMetrics.completenessScore.toFixed(0)}%
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="w-5 h-5" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analytics.recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <div className="flex-1">
                      <span className="font-medium">{activity.item}</span>
                      <div className="text-sm text-gray-500">
                        {activity.type.replace('_', ' ')} • {new Date(activity.timestamp).toLocaleString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
