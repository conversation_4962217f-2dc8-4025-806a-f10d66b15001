import React, { useState, useEffect } from 'react';
import { useDrop } from 'react-dnd';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  MapPin, 
  Camera, 
  Check, 
  AlertCircle,
  ExternalLink,
  Navigation
} from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

interface Location {
  id: string;
  name: string;
  state_province?: string;
  country?: string;
  latitude: number;
  longitude: number;
  description?: string;
  photo_count?: number;
}

interface LocationDropZoneProps {
  location: Location;
  onPhotoAssigned?: () => void;
  className?: string;
  showPhotos?: boolean;
}

export function LocationDropZone({
  location,
  onPhotoAssigned,
  className = '',
  showPhotos = false
}: LocationDropZoneProps) {
  const [isAssigning, setIsAssigning] = useState(false);
  const [assignedPhotos, setAssignedPhotos] = useState<any[]>([]);

  // Fetch assigned photos for this location
  useEffect(() => {
    if (showPhotos) {
      fetchAssignedPhotos();
    }
  }, [location.id, showPhotos]);

  const fetchAssignedPhotos = async () => {
    try {
      const locationQuery = `%${location.name}%`;
      const { data, error } = await supabase
        .from('photos_v2')
        .select('*')
        .ilike('location', locationQuery)
        .eq('published', true)
        .limit(3)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setAssignedPhotos(data || []);
    } catch (error) {
      console.error('Error fetching assigned photos for location:', error);
    }
  };

  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: 'PHOTO',
    drop: async (item: { photoId: string; selectedPhotoIds?: string[] }) => {
      await handlePhotoAssignment(item);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  }));

  const handlePhotoAssignment = async (item: { photoId: string; selectedPhotoIds?: string[] }) => {
    setIsAssigning(true);

    try {
      // Determine which photos to assign
      const photoIds = item.selectedPhotoIds && item.selectedPhotoIds.length > 0
        ? item.selectedPhotoIds
        : [item.photoId];

      console.log('Assigning photos to location:', { photoIds, location: location.name });

      // Update photos with location information (simplified approach)
      // For now, just update the location field - metadata can be added later
      const updateData = {
        location: `${location.name}, ${location.state_province}, ${location.country}`
      };

      const { error } = await supabase
        .from('photos_v2')
        .update(updateData)
        .in('id', photoIds);

      if (error) {
        console.error('Database update error:', error);
        throw error;
      }

      const photoCount = photoIds.length;
      toast.success(
        `${photoCount} photo${photoCount > 1 ? 's' : ''} assigned to ${location.name}!`
      );

      onPhotoAssigned?.();
      if (showPhotos) {
        fetchAssignedPhotos();
      }
    } catch (error) {
      console.error('Error assigning photo to location:', error);
      toast.error(`Failed to assign photo to location: ${error.message || 'Unknown error'}`);
    } finally {
      setIsAssigning(false);
    }
  };

  const getDropZoneStyles = () => {
    let baseStyles = "transition-all duration-200 border-2 border-dashed ";
    
    if (isAssigning) {
      return baseStyles + "border-blue-400 bg-blue-50 scale-105";
    } else if (isOver && canDrop) {
      return baseStyles + "border-green-400 bg-green-50 scale-105 shadow-lg";
    } else if (canDrop) {
      return baseStyles + "border-gray-300 hover:border-blue-300 hover:bg-blue-50";
    } else {
      return baseStyles + "border-gray-200 bg-gray-50";
    }
  };

  const mapUrl = `https://www.google.com/maps?q=${location.latitude},${location.longitude}&z=15`;
  const directionsUrl = `https://www.google.com/maps/dir/?api=1&destination=${location.latitude},${location.longitude}`;

  return (
    <div
      ref={drop}
      className={`${getDropZoneStyles()} ${className}`}
    >
      <Card className="h-full border-0 shadow-none bg-transparent">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-start justify-between text-sm">
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4 text-blue-600 flex-shrink-0" />
              <span className="font-medium truncate">{location.name}</span>
            </div>
            {isAssigning && (
              <div className="flex items-center gap-1 text-blue-600">
                <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
              </div>
            )}
          </CardTitle>
        </CardHeader>
        
        <CardContent className="pt-0 space-y-3">
          {/* Location Info */}
          <div className="space-y-1">
            <p className="text-xs text-gray-600">
              📍 {location.state_province}, {location.country}
            </p>
            {location.description && (
              <p className="text-xs text-gray-500 line-clamp-2">
                {location.description}
              </p>
            )}
          </div>

          {/* Photo Count */}
          <div className="flex items-center gap-2">
            <Camera className="w-3 h-3 text-gray-400" />
            <span className="text-xs text-gray-600">
              {assignedPhotos.length} photo{assignedPhotos.length !== 1 ? 's' : ''}
            </span>
          </div>

          {/* Assigned Photos Preview */}
          {showPhotos && assignedPhotos.length > 0 && (
            <div className="flex gap-1 mb-2">
              {assignedPhotos.slice(0, 3).map((photo) => (
                <img
                  key={photo.id}
                  src={photo.url || ''}
                  alt="Assigned photo"
                  className="w-8 h-8 object-cover rounded border"
                />
              ))}
              {assignedPhotos.length > 3 && (
                <div className="w-8 h-8 bg-gray-100 rounded border flex items-center justify-center">
                  <span className="text-xs text-gray-500">+{assignedPhotos.length - 3}</span>
                </div>
              )}
            </div>
          )}

          {/* Drop Zone Indicator */}
          <div className="text-center py-2">
            {isAssigning ? (
              <div className="flex items-center justify-center gap-2 text-blue-600">
                <Check className="w-4 h-4" />
                <span className="text-xs font-medium">Assigning...</span>
              </div>
            ) : isOver && canDrop ? (
              <div className="flex items-center justify-center gap-2 text-green-600">
                <Check className="w-4 h-4" />
                <span className="text-xs font-medium">Drop to assign</span>
              </div>
            ) : canDrop ? (
              <div className="text-xs text-gray-500">
                Drag photos here
              </div>
            ) : (
              <div className="text-xs text-gray-400">
                Ready for photos
              </div>
            )}
          </div>

          {/* View Photos Button */}
          {showPhotos && assignedPhotos.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              className="w-full h-7 text-xs mb-2"
              onClick={() => {
                // TODO: Open photo viewer modal
                console.log('View photos for location:', location.name, assignedPhotos);
              }}
            >
              <Camera className="w-3 h-3 mr-1" />
              View Photos ({assignedPhotos.length})
            </Button>
          )}

          {/* Quick Actions */}
          <div className="flex gap-1">
            <Button
              variant="outline"
              size="sm"
              className="flex-1 h-7 text-xs"
              onClick={() => window.open(mapUrl, '_blank')}
            >
              <ExternalLink className="w-3 h-3 mr-1" />
              Map
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex-1 h-7 text-xs"
              onClick={() => window.open(directionsUrl, '_blank')}
            >
              <Navigation className="w-3 h-3 mr-1" />
              Directions
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
