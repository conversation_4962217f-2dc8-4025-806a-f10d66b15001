import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  RefreshCw, 
  Database,
  FileText,
  Link,
  BarChart3
} from "lucide-react";

interface DataIntegrityReport {
  timestamp: string;
  totalSpecies: number;
  totalPhotos: number;
  publishedSpecies: number;
  publishedPhotos: number;
  speciesWithPhotos: number;
  speciesWithoutPhotos: number;
  photosWithSpecies: number;
  orphanedPhotos: number;
  duplicatePhotos: number;
  airtablePhotos: number;
  uploadedPhotos: number;
  issues: string[];
  recommendations: string[];
}

export const DataVerification = () => {
  const [report, setReport] = useState<DataIntegrityReport | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastCheck, setLastCheck] = useState<string | null>(null);

  useEffect(() => {
    // Load last check time from localStorage
    const saved = localStorage.getItem('lastDataVerification');
    if (saved) {
      setLastCheck(saved);
    }
  }, []);

  const runDataVerification = async () => {
    setIsLoading(true);
    try {
      console.log('🔍 Starting comprehensive data verification...');
      
      // Get all data counts
      const [
        speciesResult,
        photosResult,
        publishedSpeciesResult,
        publishedPhotosResult,
        speciesWithPhotosResult,
        photosWithSpeciesResult,
        orphanedPhotosResult,
        airtablePhotosResult
      ] = await Promise.all([
        supabase.from('species_v2').select('*', { count: 'exact', head: true }),
        supabase.from('photos_v2').select('*', { count: 'exact', head: true }),
        supabase.from('species_v2').select('*', { count: 'exact', head: true }).eq('published', true),
        supabase.from('photos_v2').select('*', { count: 'exact', head: true }).eq('published', true),
        supabase.from('species_v2').select('id').not('id', 'in', '(SELECT DISTINCT species_id FROM photos_v2 WHERE species_id IS NOT NULL)'),
        supabase.from('photos_v2').select('id, species_id').not('species_id', 'is', null),
        supabase.from('photos_v2').select('id, species_id').not('species_id', 'is', null).not('species_id', 'in', '(SELECT id FROM species_v2)'),
        supabase.from('photos_v2').select('*', { count: 'exact', head: true }).not('airtable_id', 'is', null)
      ]);

      // Check for duplicate photos by hash
      const { data: duplicateHashes } = await supabase
        .from('photos_v2')
        .select('hash')
        .not('hash', 'is', null);

      const hashCounts = duplicateHashes?.reduce((acc, photo) => {
        if (photo.hash) {
          acc[photo.hash] = (acc[photo.hash] || 0) + 1;
        }
        return acc;
      }, {} as Record<string, number>) || {};

      const duplicateCount = Object.values(hashCounts).filter(count => count > 1).length;

      // Get species with photos count
      const { data: speciesPhotoCounts } = await supabase
        .from('species_v2')
        .select(`
          id,
          name,
          photo_count,
          photos_v2(count)
        `);

      const issues: string[] = [];
      const recommendations: string[] = [];

      // Analyze data integrity
      const totalSpecies = speciesResult.count || 0;
      const totalPhotos = photosResult.count || 0;
      const publishedSpecies = publishedSpeciesResult.count || 0;
      const publishedPhotos = publishedPhotosResult.count || 0;
      const speciesWithoutPhotos = speciesWithPhotosResult.data?.length || 0;
      const orphanedPhotos = orphanedPhotosResult.data?.length || 0;
      const airtablePhotos = airtablePhotosResult.count || 0;
      const uploadedPhotos = totalPhotos - airtablePhotos;

      // Check for issues
      if (orphanedPhotos > 0) {
        issues.push(`${orphanedPhotos} photos reference non-existent species`);
        recommendations.push('Run data cleanup to remove orphaned photos');
      }

      if (duplicateCount > 0) {
        issues.push(`${duplicateCount} duplicate photo groups found`);
        recommendations.push('Use the deduplication tool to remove duplicate photos');
      }

      if (speciesWithoutPhotos > totalSpecies * 0.3) {
        issues.push(`${speciesWithoutPhotos} species have no photos (${Math.round(speciesWithoutPhotos/totalSpecies*100)}%)`);
        recommendations.push('Consider importing more photos or unpublishing species without photos');
      }

      // Check photo count accuracy
      const photoCountMismatches = speciesPhotoCounts?.filter(species => {
        const actualCount = species.photos_v2?.[0]?.count || 0;
        return species.photo_count !== actualCount;
      }).length || 0;

      if (photoCountMismatches > 0) {
        issues.push(`${photoCountMismatches} species have incorrect photo counts`);
        recommendations.push('Run photo count update to fix species photo counts');
      }

      if (totalPhotos === 0) {
        issues.push('No photos found in database');
        recommendations.push('Import photos from Airtable or upload new photos');
      }

      if (totalSpecies === 0) {
        issues.push('No species found in database');
        recommendations.push('Import species data from Airtable');
      }

      const newReport: DataIntegrityReport = {
        timestamp: new Date().toISOString(),
        totalSpecies,
        totalPhotos,
        publishedSpecies,
        publishedPhotos,
        speciesWithPhotos: totalSpecies - speciesWithoutPhotos,
        speciesWithoutPhotos,
        photosWithSpecies: totalPhotos - orphanedPhotos,
        orphanedPhotos,
        duplicatePhotos: duplicateCount,
        airtablePhotos,
        uploadedPhotos,
        issues,
        recommendations
      };

      setReport(newReport);
      
      const checkTime = new Date().toISOString();
      setLastCheck(checkTime);
      localStorage.setItem('lastDataVerification', checkTime);
      
      console.log('✅ Data verification completed:', newReport);

    } catch (error) {
      console.error('❌ Data verification failed:', error);
      setReport({
        timestamp: new Date().toISOString(),
        totalSpecies: 0,
        totalPhotos: 0,
        publishedSpecies: 0,
        publishedPhotos: 0,
        speciesWithPhotos: 0,
        speciesWithoutPhotos: 0,
        photosWithSpecies: 0,
        orphanedPhotos: 0,
        duplicatePhotos: 0,
        airtablePhotos: 0,
        uploadedPhotos: 0,
        issues: [`Verification failed: ${error.message}`],
        recommendations: ['Check database connection and try again']
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getHealthStatus = () => {
    if (!report) return 'unknown';
    if (report.issues.length === 0) return 'healthy';
    if (report.issues.length <= 2) return 'warning';
    return 'critical';
  };

  const getHealthIcon = () => {
    const status = getHealthStatus();
    switch (status) {
      case 'healthy': return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'critical': return <XCircle className="w-5 h-5 text-red-500" />;
      default: return <Database className="w-5 h-5 text-gray-500" />;
    }
  };

  const getHealthColor = () => {
    const status = getHealthStatus();
    switch (status) {
      case 'healthy': return 'border-green-200 bg-green-50';
      case 'warning': return 'border-yellow-200 bg-yellow-50';
      case 'critical': return 'border-red-200 bg-red-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BarChart3 className="w-6 h-6" />
            Data Integrity Verification
          </h2>
          <p className="text-gray-600">
            Comprehensive analysis of your wildlife database
          </p>
          {lastCheck && (
            <p className="text-sm text-gray-500 mt-1">
              Last checked: {new Date(lastCheck).toLocaleString()}
            </p>
          )}
        </div>
        
        <Button 
          onClick={runDataVerification}
          disabled={isLoading}
          size="lg"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          {isLoading ? 'Verifying...' : 'Run Verification'}
        </Button>
      </div>

      {report && (
        <>
          {/* Health Status Overview */}
          <Card className={getHealthColor()}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {getHealthIcon()}
                Database Health Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{report.totalSpecies}</div>
                  <div className="text-sm text-gray-600">Total Species</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{report.totalPhotos}</div>
                  <div className="text-sm text-gray-600">Total Photos</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{report.speciesWithPhotos}</div>
                  <div className="text-sm text-gray-600">Species with Photos</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{report.issues.length}</div>
                  <div className="text-sm text-gray-600">Issues Found</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Detailed Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="w-5 h-5" />
                  Content Statistics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Published Species:</span>
                  <Badge variant="default">{report.publishedSpecies}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Published Photos:</span>
                  <Badge variant="default">{report.publishedPhotos}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Species without Photos:</span>
                  <Badge variant={report.speciesWithoutPhotos > 0 ? "destructive" : "default"}>
                    {report.speciesWithoutPhotos}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Orphaned Photos:</span>
                  <Badge variant={report.orphanedPhotos > 0 ? "destructive" : "default"}>
                    {report.orphanedPhotos}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Link className="w-5 h-5" />
                  Data Sources
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>From Airtable:</span>
                  <Badge variant="outline">{report.airtablePhotos}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Uploaded Directly:</span>
                  <Badge variant="outline">{report.uploadedPhotos}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Duplicate Photos:</span>
                  <Badge variant={report.duplicatePhotos > 0 ? "destructive" : "default"}>
                    {report.duplicatePhotos}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Data Coverage:</span>
                  <Badge variant="secondary">
                    {report.totalSpecies > 0 ? Math.round((report.speciesWithPhotos / report.totalSpecies) * 100) : 0}%
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Issues and Recommendations */}
          {report.issues.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-700">
                  <XCircle className="w-5 h-5" />
                  Issues Found ({report.issues.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {report.issues.map((issue, index) => (
                    <Alert key={index} className="border-red-200 bg-red-50">
                      <AlertTriangle className="h-4 w-4 text-red-600" />
                      <AlertDescription className="text-red-800">
                        {issue}
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {report.recommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-blue-700">
                  <FileText className="w-5 h-5" />
                  Recommendations ({report.recommendations.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {report.recommendations.map((recommendation, index) => (
                    <Alert key={index} className="border-blue-200 bg-blue-50">
                      <AlertDescription className="text-blue-800">
                        • {recommendation}
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Report Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Verification Report Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-gray-600 space-y-1">
                <div>Report generated: {new Date(report.timestamp).toLocaleString()}</div>
                <div>Database health: <Badge variant={getHealthStatus() === 'healthy' ? 'default' : 'destructive'}>{getHealthStatus()}</Badge></div>
                <div>Total records analyzed: {report.totalSpecies + report.totalPhotos}</div>
                <div>Data integrity score: {report.issues.length === 0 ? '100%' : `${Math.max(0, 100 - (report.issues.length * 10))}%`}</div>
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {!report && !isLoading && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Database className="w-16 h-16 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-700 mb-2">
              No verification report available
            </h3>
            <p className="text-gray-500 mb-4">
              Run a data verification to analyze your database integrity
            </p>
            <Button onClick={runDataVerification}>
              <BarChart3 className="w-4 h-4 mr-2" />
              Start Verification
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};