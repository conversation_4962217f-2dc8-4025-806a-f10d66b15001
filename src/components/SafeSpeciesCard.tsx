import React from 'react';
import { SpeciesCardV2 } from './SpeciesCardV2';
import { Card, CardContent } from '@/components/ui/card';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import type { Tables } from '@/integrations/supabase/types';

type SpeciesV2 = Tables<"species_v2">;
type Photo = Tables<"photos_v2">;

interface SpeciesWithPhotos extends SpeciesV2 {
  photos: Photo[];
  photo_count: number;
}

interface SafeSpeciesCardProps {
  species: SpeciesWithPhotos;
  isAdmin?: boolean;
  onEdit?: (species: SpeciesWithPhotos) => void;
}

interface SafeSpeciesCardState {
  hasError: boolean;
  error?: Error;
}

class SafeSpeciesCardErrorBoundary extends React.Component<
  SafeSpeciesCardProps,
  SafeSpeciesCardState
> {
  constructor(props: SafeSpeciesCardProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): SafeSpeciesCardState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('SpeciesCard Error:', error, errorInfo);
    console.error('Species data:', this.props.species);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      return (
        <Card className="h-full border-red-200 bg-red-50">
          <CardContent className="p-4 flex flex-col items-center justify-center h-full min-h-[300px]">
            <AlertTriangle className="w-8 h-8 text-red-500 mb-2" />
            <h3 className="text-sm font-medium text-red-800 mb-2">
              Error loading species card
            </h3>
            <p className="text-xs text-red-600 text-center mb-3">
              Species: {this.props.species?.name || 'Unknown'}
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={this.handleRetry}
              className="text-red-700 border-red-300 hover:bg-red-100"
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              Retry
            </Button>
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-3 text-xs">
                <summary className="cursor-pointer text-red-700">
                  Error Details
                </summary>
                <pre className="mt-1 p-2 bg-red-100 rounded text-red-800 overflow-auto max-w-full">
                  {this.state.error.message}
                </pre>
              </details>
            )}
          </CardContent>
        </Card>
      );
    }

    return <SpeciesCardV2 {...this.props} />;
  }
}

// Validation function to check if species data is valid
const validateSpeciesData = (species: any): species is SpeciesWithPhotos => {
  if (!species || typeof species !== 'object') {
    return false;
  }

  // Check required fields
  if (!species.id || typeof species.id !== 'string') {
    return false;
  }

  // Ensure photos is an array
  if (species.photos && !Array.isArray(species.photos)) {
    return false;
  }

  // Ensure photo_count is a number
  if (species.photo_count !== undefined && typeof species.photo_count !== 'number') {
    return false;
  }

  return true;
};

// Main safe wrapper component
export const SafeSpeciesCard: React.FC<SafeSpeciesCardProps> = (props) => {
  // Validate species data before rendering
  if (!validateSpeciesData(props.species)) {
    return (
      <Card className="h-full border-yellow-200 bg-yellow-50">
        <CardContent className="p-4 flex flex-col items-center justify-center h-full min-h-[300px]">
          <AlertTriangle className="w-8 h-8 text-yellow-500 mb-2" />
          <h3 className="text-sm font-medium text-yellow-800 mb-2">
            Invalid species data
          </h3>
          <p className="text-xs text-yellow-600 text-center">
            The species data is malformed or missing required fields.
          </p>
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-3 text-xs">
              <summary className="cursor-pointer text-yellow-700">
                Data Details
              </summary>
              <pre className="mt-1 p-2 bg-yellow-100 rounded text-yellow-800 overflow-auto max-w-full">
                {JSON.stringify(props.species, null, 2)}
              </pre>
            </details>
          )}
        </CardContent>
      </Card>
    );
  }

  // Sanitize the species data to prevent rendering issues
  const sanitizedSpecies: SpeciesWithPhotos = {
    ...props.species,
    name: props.species.name || 'Unknown Species',
    photos: Array.isArray(props.species.photos) ? props.species.photos : [],
    photo_count: typeof props.species.photo_count === 'number' ? props.species.photo_count : 0,
    // Ensure other fields are safe
    scientific_name: props.species.scientific_name || undefined,
    category: props.species.category || undefined,
    conservation_status: props.species.conservation_status || undefined,
    description: props.species.description || undefined,
    habitat: props.species.habitat || undefined,
    diet: props.species.diet || undefined,
    family: props.species.family || undefined,
    size_cm: props.species.size_cm || undefined,
    weight_g: props.species.weight_g || undefined,
  };

  return (
    <SafeSpeciesCardErrorBoundary
      {...props}
      species={sanitizedSpecies}
    />
  );
};

export default SafeSpeciesCard;
