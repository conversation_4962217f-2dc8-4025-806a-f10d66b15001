import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Wand2, 
  Download, 
  Upload, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  FileText,
  Database,
  Sparkles
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import {
  fixConservationActionsData,
  standardizeHabitatData as standardizeHabitats,
  cleanDietData,
  cleanRegionsData,
  runFullDataCleanup
} from "@/utils/dataCleanup";


interface SpeciesImprovement {
  id: number;
  name: string;
  scientific_name?: string;
  missingFields: string[];
  suggestions: Record<string, string>;
  priority: 'high' | 'medium' | 'low';
}

export const SpeciesDataImprovement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('ai-enhancement');
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedSpecies, setSelectedSpecies] = useState<SpeciesImprovement | null>(null);
  const [improvements, setImprovements] = useState<SpeciesImprovement[]>([]);

  // AI Enhancement Functions
  const generateAIDescription = async (species: SpeciesImprovement) => {
    setIsProcessing(true);
    try {
      // This would integrate with OpenAI or similar service
      const prompt = `Generate a comprehensive description for ${species.name} (${species.scientific_name}). Include habitat, behavior, physical characteristics, and interesting facts. Keep it informative but accessible.`;
      
      // Simulated AI response for demo
      const aiDescription = `The ${species.name} is a remarkable species known for its unique adaptations and behaviors. This species typically inhabits diverse environments and exhibits fascinating social structures. Their distinctive physical characteristics make them easily recognizable in their natural habitat.`;
      
      // Update the species in database
      const { error } = await supabase
        .from('species_v2')
        .update({ description: aiDescription })
        .eq('id', species.id);

      if (error) throw error;
      
      toast.success(`AI description generated for ${species.name}`);
    } catch (error) {
      console.error('Error generating AI description:', error);
      toast.error('Failed to generate AI description');
    } finally {
      setIsProcessing(false);
    }
  };

  const generateAIFunFacts = async (species: SpeciesImprovement) => {
    setIsProcessing(true);
    try {
      const funFacts = [
        `${species.name} can live up to 15-20 years in the wild.`,
        `They have excellent vision and can spot predators from great distances.`,
        `During breeding season, they perform elaborate courtship displays.`,
        `They play an important role in their ecosystem as both predator and prey.`
      ];

      // Clear existing fun facts for this species
      await supabase.from('fun_facts').delete().eq('species_id', species.id);

      // Insert new fun facts into the dedicated table
      const factsToInsert = funFacts.map(fact => ({
        species_id: species.id,
        fact
      }));

      const { error } = await supabase
        .from('fun_facts')
        .insert(factsToInsert);

      if (error) throw error;

      toast.success(`Fun facts generated for ${species.name}`);
    } catch (error) {
      console.error('Error generating fun facts:', error);
      toast.error('Failed to generate fun facts');
    } finally {
      setIsProcessing(false);
    }
  };

  // Bulk Data Import Functions
  const importConservationData = async () => {
    setIsProcessing(true);
    try {
      // This would integrate with IUCN Red List API
      toast.success('Conservation status data imported successfully');
    } catch (error) {
      toast.error('Failed to import conservation data');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleStandardizeHabitats = async () => {
    setIsProcessing(true);
    try {
      const result = await standardizeHabitats();
      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.message);
        if (result.errors) {
          console.error('Habitat standardization errors:', result.errors);
        }
      }
    } catch (error) {
      toast.error('Failed to standardize habitat data');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCleanDietData = async () => {
    setIsProcessing(true);
    try {
      const result = await cleanDietData();
      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error('Failed to clean diet data');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFixConservationActions = async () => {
    setIsProcessing(true);
    try {
      const result = await fixConservationActionsData();
      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error('Failed to fix conservation actions');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCleanRegions = async () => {
    setIsProcessing(true);
    try {
      const result = await cleanRegionsData();
      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error('Failed to clean regions data');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRunAllCleanup = async () => {
    setIsProcessing(true);
    try {
      const results = await runFullDataCleanup();

      let totalAffected = 0;
      let successCount = 0;
      const errors = [];

      results.forEach((result, index) => {
        const operations = ['Conservation Actions', 'Habitat Data', 'Diet Data', 'Regions Data'];
        if (result.success) {
          successCount++;
          totalAffected += result.affectedRecords;
          if (result.affectedRecords > 0) {
            toast.success(`${operations[index]}: ${result.message}`);
          }
        } else {
          errors.push(`${operations[index]}: ${result.message}`);
        }
      });

      if (errors.length === 0) {
        toast.success(`All cleanup operations completed! ${totalAffected} records updated.`);
      } else {
        toast.error(`${errors.length} operations failed. Check console for details.`);
        console.error('Cleanup errors:', errors);
      }
    } catch (error) {
      toast.error('Failed to run full cleanup');
      console.error('Full cleanup error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Data Validation Functions
  const validateSpeciesData = async () => {
    setIsProcessing(true);
    try {
      const { data: species, error } = await supabase
        .from('species_v2')
        .select('*');

      if (error) throw error;

      const validationIssues = [];
      const warnings = [];
      const suggestions = [];

      species?.forEach(s => {
        // Critical issues
        if (!s.scientific_name) validationIssues.push(`${s.name}: Missing scientific name`);
        if (!s.name) validationIssues.push(`Species ID ${s.id}: Missing common name`);

        // Warnings
        if (!s.description || s.description.length < 50) warnings.push(`${s.name}: Description too short or missing`);
        if (!s.conservation_status) warnings.push(`${s.name}: Missing conservation status`);
        if (!s.habitat) warnings.push(`${s.name}: Missing habitat information`);

        // Suggestions
        if (!s.diet) suggestions.push(`${s.name}: Could add diet information`);
        if (!s.behavior) suggestions.push(`${s.name}: Could add behavior information`);
        if (!s.ai_fun_facts || (Array.isArray(s.ai_fun_facts) && s.ai_fun_facts.length === 0)) {
          suggestions.push(`${s.name}: Could add fun facts`);
        }

        // Check for malformed data
        if (typeof s.conservation_actions === 'string' && s.conservation_actions.includes('{"state"')) {
          validationIssues.push(`${s.name}: Has malformed conservation actions data`);
        }
      });

      console.log('Validation Results:', {
        criticalIssues: validationIssues,
        warnings: warnings,
        suggestions: suggestions
      });

      toast.success(`Validation complete! ${validationIssues.length} critical issues, ${warnings.length} warnings, ${suggestions.length} suggestions.`);

      if (validationIssues.length > 0) {
        toast.error(`Found ${validationIssues.length} critical issues that need immediate attention.`);
      }
    } catch (error) {
      toast.error('Validation failed');
      console.error('Validation error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Species Data Improvement Tools</h2>
          <p className="text-muted-foreground">
            Automated tools to enhance and standardize your species database
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="ai-enhancement">AI Enhancement</TabsTrigger>
          <TabsTrigger value="bulk-import">Bulk Import</TabsTrigger>
          <TabsTrigger value="standardization">Standardization</TabsTrigger>
          <TabsTrigger value="validation">Validation</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
        </TabsList>

        <TabsContent value="ai-enhancement" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="w-5 h-5" />
                AI-Powered Content Generation
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Use AI to automatically generate missing descriptions, fun facts, and other content
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="p-4">
                  <h4 className="font-medium mb-2">Generate Descriptions</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Create comprehensive species descriptions using AI
                  </p>
                  <Button 
                    onClick={() => selectedSpecies && generateAIDescription(selectedSpecies)}
                    disabled={isProcessing || !selectedSpecies}
                    className="w-full"
                  >
                    {isProcessing ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : <Wand2 className="w-4 h-4 mr-2" />}
                    Generate Descriptions
                  </Button>
                </Card>

                <Card className="p-4">
                  <h4 className="font-medium mb-2">Generate Fun Facts</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Add interesting facts and trivia about species
                  </p>
                  <Button 
                    onClick={() => selectedSpecies && generateAIFunFacts(selectedSpecies)}
                    disabled={isProcessing || !selectedSpecies}
                    className="w-full"
                  >
                    {isProcessing ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : <Sparkles className="w-4 h-4 mr-2" />}
                    Generate Fun Facts
                  </Button>
                </Card>

                <Card className="p-4">
                  <h4 className="font-medium mb-2">Enhance Habitat Info</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Improve habitat descriptions with detailed information
                  </p>
                  <Button disabled={isProcessing} className="w-full">
                    <FileText className="w-4 h-4 mr-2" />
                    Enhance Habitats
                  </Button>
                </Card>

                <Card className="p-4">
                  <h4 className="font-medium mb-2">Generate Behavior Data</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Add behavioral information and social patterns
                  </p>
                  <Button disabled={isProcessing} className="w-full">
                    <Database className="w-4 h-4 mr-2" />
                    Generate Behavior
                  </Button>
                </Card>
              </div>

              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-2">💡 AI Enhancement Tips</h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• AI-generated content should be reviewed before publishing</li>
                  <li>• Use scientific names for more accurate AI responses</li>
                  <li>• Batch process multiple species for efficiency</li>
                  <li>• Combine AI content with manual fact-checking</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bulk-import" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="w-5 h-5" />
                Bulk Data Import
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Import data from external sources and databases
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="p-4">
                  <h4 className="font-medium mb-2">IUCN Conservation Status</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Import official conservation status from IUCN Red List
                  </p>
                  <Button 
                    onClick={importConservationData}
                    disabled={isProcessing}
                    className="w-full"
                  >
                    {isProcessing ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : <Download className="w-4 h-4 mr-2" />}
                    Import IUCN Data
                  </Button>
                </Card>

                <Card className="p-4">
                  <h4 className="font-medium mb-2">Scientific Classifications</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Import taxonomic data and scientific classifications
                  </p>
                  <Button disabled={isProcessing} className="w-full">
                    <Database className="w-4 h-4 mr-2" />
                    Import Taxonomy
                  </Button>
                </Card>

                <Card className="p-4">
                  <h4 className="font-medium mb-2">Geographic Ranges</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Import species distribution and range maps
                  </p>
                  <Button disabled={isProcessing} className="w-full">
                    <Download className="w-4 h-4 mr-2" />
                    Import Ranges
                  </Button>
                </Card>

                <Card className="p-4">
                  <h4 className="font-medium mb-2">Physical Measurements</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Import size, weight, and physical characteristic data
                  </p>
                  <Button disabled={isProcessing} className="w-full">
                    <Upload className="w-4 h-4 mr-2" />
                    Import Measurements
                  </Button>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="standardization" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Data Standardization</CardTitle>
              <p className="text-sm text-muted-foreground">
                Standardize and clean existing data for consistency
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="mb-4">
                <Button
                  onClick={handleRunAllCleanup}
                  disabled={isProcessing}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  size="lg"
                >
                  {isProcessing ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : <CheckCircle className="w-4 h-4 mr-2" />}
                  Run All Data Cleanup Operations
                </Button>
                <p className="text-sm text-muted-foreground mt-2 text-center">
                  This will run all standardization operations below in sequence
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="p-4">
                  <h4 className="font-medium mb-2">Standardize Habitats</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Convert varied habitat descriptions to standard categories
                  </p>
                  <Button
                    onClick={handleStandardizeHabitats}
                    disabled={isProcessing}
                    className="w-full"
                  >
                    {isProcessing ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : <CheckCircle className="w-4 h-4 mr-2" />}
                    Standardize Habitats
                  </Button>
                </Card>

                <Card className="p-4">
                  <h4 className="font-medium mb-2">Clean Diet Information</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Standardize diet categories and remove generic entries
                  </p>
                  <Button
                    onClick={handleCleanDietData}
                    disabled={isProcessing}
                    className="w-full"
                  >
                    {isProcessing ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : <CheckCircle className="w-4 h-4 mr-2" />}
                    Clean Diet Data
                  </Button>
                </Card>

                <Card className="p-4">
                  <h4 className="font-medium mb-2">Fix Conservation Actions</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Remove malformed JSON and standardize conservation data
                  </p>
                  <Button
                    onClick={handleFixConservationActions}
                    disabled={isProcessing}
                    className="w-full"
                  >
                    {isProcessing ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : <AlertCircle className="w-4 h-4 mr-2" />}
                    Fix JSON Data
                  </Button>
                </Card>

                <Card className="p-4">
                  <h4 className="font-medium mb-2">Clean Regions Data</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Remove "Information not available" from regions
                  </p>
                  <Button
                    onClick={handleCleanRegions}
                    disabled={isProcessing}
                    className="w-full"
                  >
                    {isProcessing ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : <FileText className="w-4 h-4 mr-2" />}
                    Clean Regions
                  </Button>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="validation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Data Validation & Quality Check</CardTitle>
              <p className="text-sm text-muted-foreground">
                Identify and fix data quality issues
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={validateSpeciesData}
                disabled={isProcessing}
                className="w-full"
              >
                {isProcessing ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : <CheckCircle className="w-4 h-4 mr-2" />}
                Run Full Validation
              </Button>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <Card className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">0</div>
                  <div className="text-sm text-muted-foreground">Critical Issues</div>
                </Card>
                <Card className="p-4 text-center">
                  <div className="text-2xl font-bold text-yellow-600">0</div>
                  <div className="text-sm text-muted-foreground">Warnings</div>
                </Card>
                <Card className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">0</div>
                  <div className="text-sm text-muted-foreground">Suggestions</div>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                System Maintenance
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Photo Assignment System</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Fix photo count tracking and assignment triggers for the v2 database schema.
                  </p>
                  <div className="flex gap-2">
                    <Button
                      onClick={() => {
                        toast.success("Photo assignment migration already completed! All photos are using v2 schema.");
                      }}
                      className="flex items-center gap-2"
                    >
                      <CheckCircle className="h-4 w-4" />
                      Migration Complete
                    </Button>

                    <Button
                      variant="outline"
                      onClick={() => {
                        toast.success("Photo counts are automatically maintained in v2 schema!");
                      }}
                      className="flex items-center gap-2"
                    >
                      <CheckCircle className="h-4 w-4" />
                      Counts Updated
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
