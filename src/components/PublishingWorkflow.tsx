import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { getServiceRoleClient } from '@/lib/supabaseServiceRole';
import { 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Eye, 
  EyeOff,
  Upload,
  Download,
  RefreshCw,
  BarChart3,
  FileText,
  Camera,
  Users,
  Globe,
  Loader2
} from 'lucide-react';

interface ContentStats {
  species: {
    total: number;
    published: number;
    draft: number;
    withPhotos: number;
    withoutPhotos: number;
  };
  photos: {
    total: number;
    published: number;
    draft: number;
    assigned: number;
    unassigned: number;
  };
}

interface ContentItem {
  id: string;
  name: string;
  type: 'species' | 'photo';
  published: boolean;
  created_at: string;
  updated_at: string;
  photo_count?: number;
  species_name?: string;
}

export const PublishingWorkflow = () => {
  const [stats, setStats] = useState<ContentStats>({
    species: { total: 0, published: 0, draft: 0, withPhotos: 0, withoutPhotos: 0 },
    photos: { total: 0, published: 0, draft: 0, assigned: 0, unassigned: 0 }
  });
  const [recentContent, setRecentContent] = useState<ContentItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [bulkOperationLoading, setBulkOperationLoading] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // Use count queries for better performance
      const [
        { count: totalSpecies },
        { count: publishedSpecies },
        { count: speciesWithPhotos },
        { count: totalPhotos },
        { count: publishedPhotos },
        { count: assignedPhotos }
      ] = await Promise.all([
        supabase.from('species_v2').select('*', { count: 'exact', head: true }),
        supabase.from('species_v2').select('*', { count: 'exact', head: true }).eq('published', true),
        supabase.from('species_v2').select('*', { count: 'exact', head: true }).gt('photo_count', 0),
        supabase.from('photos').select('*', { count: 'exact', head: true }),
        supabase.from('photos').select('*', { count: 'exact', head: true }).eq('published', true),
        supabase.from('photos').select('*', { count: 'exact', head: true }).not('species_id', 'is', null)
      ]);

      // Calculate stats from count queries
      const speciesStats = {
        total: totalSpecies || 0,
        published: publishedSpecies || 0,
        draft: (totalSpecies || 0) - (publishedSpecies || 0),
        withPhotos: speciesWithPhotos || 0,
        withoutPhotos: (totalSpecies || 0) - (speciesWithPhotos || 0),
      };

      const photosStats = {
        total: totalPhotos || 0,
        published: publishedPhotos || 0,
        draft: (totalPhotos || 0) - (publishedPhotos || 0),
        assigned: assignedPhotos || 0,
        unassigned: (totalPhotos || 0) - (assignedPhotos || 0),
      };

      setStats({ species: speciesStats, photos: photosStats });

      // Prepare recent content
      const recentSpecies: ContentItem[] = (speciesData || [])
        .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
        .slice(0, 10)
        .map(s => ({
          id: s.id,
          name: s.name,
          type: 'species' as const,
          published: s.published,
          created_at: s.created_at,
          updated_at: s.updated_at,
          photo_count: s.photo_count
        }));

      const recentPhotos: ContentItem[] = (photosData || [])
        .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
        .slice(0, 10)
        .map(p => ({
          id: p.id.toString(),
          name: p.title || 'Untitled Photo',
          type: 'photo' as const,
          published: p.published,
          created_at: p.created_at,
          updated_at: p.updated_at,
          species_name: p.species?.name
        }));

      // Combine and sort by update time
      const allRecent = [...recentSpecies, ...recentPhotos]
        .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
        .slice(0, 20);

      setRecentContent(allRecent);
    } catch (error) {
      console.error('Error loading publishing data:', error);
      toast.error('Failed to load publishing data');
    } finally {
      setLoading(false);
    }
  };

  const handleBulkPublish = async (type: 'species' | 'photos', publish: boolean) => {
    setBulkOperationLoading(true);
    try {
      const table = type === 'species' ? 'species_v2' : 'photos_v2';
      // Use service role client for admin operations
      const serviceRoleClient = getServiceRoleClient();
      const { error } = await serviceRoleClient
        .from(table)
        .update({ published: publish })
        .eq('published', !publish); // Only update items with opposite status

      if (error) throw error;

      toast.success(`All ${type} ${publish ? 'published' : 'unpublished'} successfully`);
      loadData(); // Reload data to update stats
    } catch (error) {
      console.error(`Error bulk ${publish ? 'publishing' : 'unpublishing'} ${type}:`, error);
      toast.error(`Failed to ${publish ? 'publish' : 'unpublish'} ${type}`);
    } finally {
      setBulkOperationLoading(false);
    }
  };

  const toggleItemStatus = async (item: ContentItem) => {
    try {
      const table = item.type === 'species' ? 'species_v2' : 'photos_v2';
      const { error } = await supabase
        .from(table)
        .update({ published: !item.published })
        .eq('id', item.id);

      if (error) throw error;

      toast.success(`${item.name} ${!item.published ? 'published' : 'unpublished'}`);
      loadData(); // Reload data to update stats
    } catch (error) {
      console.error('Error toggling item status:', error);
      toast.error('Failed to update status');
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          Loading publishing data...
        </CardContent>
      </Card>
    );
  }

  const speciesPublishRate = stats.species.total > 0 ? (stats.species.published / stats.species.total) * 100 : 0;
  const photosPublishRate = stats.photos.total > 0 ? (stats.photos.published / stats.photos.total) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Publishing Workflow</h2>
          <p className="text-gray-600">Manage content publication status and workflow</p>
        </div>
        <Button onClick={loadData} variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <FileText className="w-5 h-5 text-blue-600" />
              <h3 className="font-semibold">Species</h3>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Published</span>
                <span className="font-medium">{stats.species.published}/{stats.species.total}</span>
              </div>
              <Progress value={speciesPublishRate} className="h-2" />
              <div className="text-xs text-gray-500">
                {speciesPublishRate.toFixed(1)}% published
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Camera className="w-5 h-5 text-green-600" />
              <h3 className="font-semibold">Photos</h3>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Published</span>
                <span className="font-medium">{stats.photos.published}/{stats.photos.total}</span>
              </div>
              <Progress value={photosPublishRate} className="h-2" />
              <div className="text-xs text-gray-500">
                {photosPublishRate.toFixed(1)}% published
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Globe className="w-5 h-5 text-purple-600" />
              <h3 className="font-semibold">Coverage</h3>
            </div>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Species with photos</span>
                <span className="font-medium">{stats.species.withPhotos}</span>
              </div>
              <div className="flex justify-between">
                <span>Assigned photos</span>
                <span className="font-medium">{stats.photos.assigned}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <BarChart3 className="w-5 h-5 text-orange-600" />
              <h3 className="font-semibold">Quality</h3>
            </div>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Draft species</span>
                <span className="font-medium">{stats.species.draft}</span>
              </div>
              <div className="flex justify-between">
                <span>Unassigned photos</span>
                <span className="font-medium">{stats.photos.unassigned}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bulk Operations */}
      <Card>
        <CardHeader>
          <CardTitle>Bulk Operations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h4 className="font-semibold">Species Operations</h4>
              <div className="flex gap-2">
                <Button 
                  onClick={() => handleBulkPublish('species', true)}
                  disabled={bulkOperationLoading}
                  size="sm"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Publish All Species
                </Button>
                <Button 
                  onClick={() => handleBulkPublish('species', false)}
                  disabled={bulkOperationLoading}
                  variant="outline"
                  size="sm"
                >
                  <EyeOff className="w-4 h-4 mr-2" />
                  Unpublish All
                </Button>
              </div>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-semibold">Photo Operations</h4>
              <div className="flex gap-2">
                <Button 
                  onClick={() => handleBulkPublish('photos', true)}
                  disabled={bulkOperationLoading}
                  size="sm"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Publish All Photos
                </Button>
                <Button 
                  onClick={() => handleBulkPublish('photos', false)}
                  disabled={bulkOperationLoading}
                  variant="outline"
                  size="sm"
                >
                  <EyeOff className="w-4 h-4 mr-2" />
                  Unpublish All
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Content */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Content Updates</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {recentContent.map(item => (
              <div key={`${item.type}-${item.id}`} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {item.type === 'species' ? (
                    <FileText className="w-4 h-4 text-blue-600" />
                  ) : (
                    <Camera className="w-4 h-4 text-green-600" />
                  )}
                  <div>
                    <h4 className="font-medium">{item.name}</h4>
                    <div className="text-sm text-gray-600">
                      {item.type === 'species' && item.photo_count && (
                        <span>{item.photo_count} photos • </span>
                      )}
                      {item.type === 'photo' && item.species_name && (
                        <span>{item.species_name} • </span>
                      )}
                      Updated {new Date(item.updated_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Badge variant={item.published ? "default" : "secondary"}>
                    {item.published ? "Published" : "Draft"}
                  </Badge>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => toggleItemStatus(item)}
                  >
                    {item.published ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
