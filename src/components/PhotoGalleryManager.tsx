import React from 'react';
import { PhotoManager } from './PhotoManager';

/**
 * @deprecated Use PhotoManager with mode="gallery" instead
 * 
 * PhotoGalleryManager - Legacy component for photo gallery management operations
 * 
 * This component has been deprecated in favor of the unified PhotoManager component.
 * Please use PhotoManager with mode="gallery" for new implementations.
 * 
 * @example
 * ```tsx
 * // Instead of:
 * <PhotoGalleryManager />
 * 
 * // Use:
 * <PhotoManager mode="gallery" title="Photo Gallery" />
 * ```
 */
export function PhotoGalleryManager() {
  console.warn(
    'PhotoGalleryManager component is deprecated. Use PhotoManager with mode="gallery" instead.'
  );
  
  return (
    <PhotoManager 
      mode="gallery" 
      title="Photo Gallery"
    />
  );
}