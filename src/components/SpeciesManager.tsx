
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { getServiceRoleClient } from '@/lib/supabaseServiceRole';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { Loader2, Save, Plus, X, Edit, Trash2 } from "lucide-react";
import { DataIntegrityService } from '@/utils/dataIntegrity';

interface Species {
  id: string;
  name: string;
  common_name?: string;
  scientific_name?: string;
  category?: string;
  conservation_status?: string;
  description?: string;
  habitat?: string;
  diet?: string;
  behavior?: string;
  family?: string;
  size_cm?: number;
  weight_g?: number;
  lifespan_years?: number;
  migration_pattern?: string;
  breeding_season?: string;
  threat_level?: string;
  population_trend?: string;
  featured?: boolean;
  published?: boolean;
  tags?: string[];
}

export const SpeciesManager = () => {
  const [species, setSpecies] = useState<Species[]>([]);
  const [editingSpecies, setEditingSpecies] = useState<Species | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  useEffect(() => {
    loadSpecies();
  }, []);

  const loadSpecies = async () => {
    setIsLoading(true);
    try {
      // Load only essential fields for performance
      const { data, error } = await supabase
        .from('species_v2')
        .select(`
          id,
          name,
          common_name,
          scientific_name,
          category,
          conservation_status,
          published,
          featured,
          photo_count
        `)
        .order('name')
        .limit(1000); // Increased limit to load more species

      if (error) throw error;
      setSpecies(data || []);
    } catch (error) {
      console.error('Error loading species:', error);
      toast.error('Failed to load species');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateNew = () => {
    const newSpecies: Species = {
      id: '',
      name: '',
      common_name: '',
      scientific_name: '',
      category: 'Birds',
      conservation_status: 'Least Concern',
      description: '',
      habitat: '',
      diet: '',
      behavior: '',
      family: '',
      featured: false,
      published: true,
      tags: []
    };
    setEditingSpecies(newSpecies);
    setIsCreating(true);
  };

  const handleEdit = (species: Species) => {
    console.log('Edit button clicked for species:', species.name, species);
    setEditingSpecies({ ...species });
    setIsCreating(false);
    toast.info(`Opening editor for ${species.name} - Scroll down to see the edit form`);

    // Scroll to edit form after a brief delay to ensure it renders
    setTimeout(() => {
      const editForm = document.querySelector('[data-edit-form]');
      if (editForm) {
        editForm.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  };

  const handleSave = async () => {
    if (!editingSpecies || !editingSpecies.name.trim()) {
      toast.error('Species name is required');
      return;
    }

    // Validate species data
    const validation = DataIntegrityService.validateSpecies({
      name: editingSpecies.name,
      scientific_name: editingSpecies.scientific_name,
      category: editingSpecies.category,
      conservation_status: editingSpecies.conservation_status,
      published: editingSpecies.published
    });

    if (!validation.isValid) {
      toast.error(`Validation failed: ${validation.errors.join(', ')}`);
      return;
    }

    if (validation.warnings.length > 0) {
      validation.warnings.forEach(warning => toast.warning(warning));
    }

    setIsSaving(true);
    try {
      if (isCreating) {
        // Use the safe creation method from DataIntegrityService
        const result = await DataIntegrityService.createSpecies({
          name: editingSpecies.name,
          scientific_name: editingSpecies.scientific_name,
          category: editingSpecies.category,
          conservation_status: editingSpecies.conservation_status,
          published: editingSpecies.published
        });

        if (!result.success) {
          toast.error(`Failed to create species: ${result.errors?.join(', ')}`);
          return;
        }

        toast.success('Species created successfully!');
      } else {
        // Use service role client for admin operations
        const serviceRoleClient = getServiceRoleClient();
        const { error } = await serviceRoleClient
          .from('species_v2')
          .update({
            ...editingSpecies,
            updated_at: new Date().toISOString()
          })
          .eq('id', editingSpecies.id);

        if (error) throw error;
        toast.success('Species updated successfully!');
      }

      setEditingSpecies(null);
      setIsCreating(false);
      loadSpecies();
    } catch (error) {
      console.error('Error saving species:', error);
      toast.error('Failed to save species');
    } finally {
      setIsSaving(false);
    }
  };

  const handleInputChange = (field: keyof Species, value: any) => {
    if (!editingSpecies) return;
    setEditingSpecies(prev => prev ? { ...prev, [field]: value } : null);
  };

  const addTag = (tag: string) => {
    if (!editingSpecies || !tag.trim()) return;
    const tags = editingSpecies.tags || [];
    if (!tags.includes(tag.trim())) {
      handleInputChange('tags', [...tags, tag.trim()]);
    }
  };

  const removeTag = (tagToRemove: string) => {
    if (!editingSpecies) return;
    handleInputChange('tags', editingSpecies.tags?.filter(tag => tag !== tagToRemove) || []);
  };

  const handleDelete = async (species: Species) => {
    if (!species.id) return;

    const confirmed = window.confirm(
      `Are you sure you want to delete "${species.name}"?\n\n` +
      `This will:\n` +
      `• Remove the species from the database\n` +
      `• Unassign all photos from this species\n` +
      `• This action cannot be undone`
    );

    if (!confirmed) return;

    setDeletingId(species.id);
    try {
      // First, check how many photos are assigned to this species
      const { count: photoCount, error: countError } = await supabase
        .from('photos_v2')
        .select('*', { count: 'exact', head: true })
        .eq('species_id', species.id);

      if (countError) {
        console.error('Error counting photos:', countError);
        toast.error('Failed to check photo assignments');
        return;
      }

      // Unassign all photos from this species
      if (photoCount && photoCount > 0) {
        const { error: unassignError } = await supabase
          .from('photos_v2')
          .update({
            species_id: null,
            updated_at: new Date().toISOString()
          })
          .eq('species_id', species.id);

        if (unassignError) {
          console.error('Error unassigning photos:', unassignError);
          toast.error('Failed to unassign photos from species');
          return;
        }

        toast.success(`Unassigned ${photoCount} photos from species`);
      }

      // Delete the species
      const { error: deleteError } = await supabase
        .from('species_v2')
        .delete()
        .eq('id', species.id);

      if (deleteError) {
        console.error('Error deleting species:', deleteError);
        toast.error('Failed to delete species');
        return;
      }

      toast.success(`Successfully deleted "${species.name}" and unassigned ${photoCount || 0} photos`);

      // Refresh the species list
      loadSpecies();

      // Close editor if we're editing the deleted species
      if (editingSpecies?.id === species.id) {
        setEditingSpecies(null);
        setIsCreating(false);
      }

    } catch (error) {
      console.error('Error in delete operation:', error);
      toast.error('Failed to delete species');
    } finally {
      setDeletingId(null);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          Loading species...
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Species List */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Species Management</CardTitle>
          <Button onClick={handleCreateNew}>
            <Plus className="w-4 h-4 mr-2" />
            Create New Species
          </Button>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {species.map((spec) => (
              <div key={spec.id} className="border rounded-lg p-4 space-y-2">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="font-semibold">{spec.name}</h3>
                    {spec.common_name && (
                      <p className="text-sm text-gray-600">{spec.common_name}</p>
                    )}
                    {spec.scientific_name && (
                      <p className="text-sm italic text-gray-500">{spec.scientific_name}</p>
                    )}
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(spec)}
                      title="Edit species"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(spec)}
                      disabled={deletingId === spec.id}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      title="Delete species"
                    >
                      {deletingId === spec.id ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <Trash2 className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Badge variant={spec.published ? "default" : "secondary"}>
                    {spec.published ? "Published" : "Draft"}
                  </Badge>
                  {spec.featured && <Badge variant="outline">Featured</Badge>}
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-gray-600">
                    Category: {spec.category || 'Not set'}
                  </p>
                  <p className="text-sm text-gray-600">
                    Photos: {spec.photo_count || 0}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Species Editor */}
      {editingSpecies && (
        <Card data-edit-form>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>
              {isCreating ? 'Create New Species' : `Edit ${editingSpecies.name}`}
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setEditingSpecies(null)}
            >
              <X className="w-4 h-4" />
            </Button>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={editingSpecies.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Species name"
                />
              </div>
              <div>
                <Label htmlFor="common_name">Common Name</Label>
                <Input
                  id="common_name"
                  value={editingSpecies.common_name || ''}
                  onChange={(e) => handleInputChange('common_name', e.target.value)}
                  placeholder="Common name"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="scientific_name">Scientific Name</Label>
                <Input
                  id="scientific_name"
                  value={editingSpecies.scientific_name || ''}
                  onChange={(e) => handleInputChange('scientific_name', e.target.value)}
                  placeholder="Scientific name"
                />
              </div>
              <div>
                <Label htmlFor="category">Category</Label>
                <Select
                  value={editingSpecies.category || ''}
                  onValueChange={(value) => handleInputChange('category', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Birds">Birds</SelectItem>
                    <SelectItem value="Mammals">Mammals</SelectItem>
                    <SelectItem value="Reptiles">Reptiles</SelectItem>
                    <SelectItem value="Amphibians">Amphibians</SelectItem>
                    <SelectItem value="Fish">Fish</SelectItem>
                    <SelectItem value="Insects">Insects</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Description */}
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={editingSpecies.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Species description"
                rows={4}
              />
            </div>

            {/* Physical Characteristics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="size_cm">Size (cm)</Label>
                <Input
                  id="size_cm"
                  type="number"
                  value={editingSpecies.size_cm || ''}
                  onChange={(e) => handleInputChange('size_cm', e.target.value ? Number(e.target.value) : null)}
                  placeholder="Size in cm"
                />
              </div>
              <div>
                <Label htmlFor="weight_g">Weight (g)</Label>
                <Input
                  id="weight_g"
                  type="number"
                  value={editingSpecies.weight_g || ''}
                  onChange={(e) => handleInputChange('weight_g', e.target.value ? Number(e.target.value) : null)}
                  placeholder="Weight in grams"
                />
              </div>
              <div>
                <Label htmlFor="lifespan_years">Lifespan (years)</Label>
                <Input
                  id="lifespan_years"
                  type="number"
                  value={editingSpecies.lifespan_years || ''}
                  onChange={(e) => handleInputChange('lifespan_years', e.target.value ? Number(e.target.value) : null)}
                  placeholder="Lifespan in years"
                />
              </div>
            </div>

            {/* Habitat and Behavior */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="habitat">Habitat</Label>
                <Textarea
                  id="habitat"
                  value={editingSpecies.habitat || ''}
                  onChange={(e) => handleInputChange('habitat', e.target.value)}
                  placeholder="Natural habitat"
                  rows={3}
                />
              </div>
              <div>
                <Label htmlFor="diet">Diet</Label>
                <Textarea
                  id="diet"
                  value={editingSpecies.diet || ''}
                  onChange={(e) => handleInputChange('diet', e.target.value)}
                  placeholder="Diet and feeding habits"
                  rows={3}
                />
              </div>
            </div>

            {/* Conservation */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="conservation_status">Conservation Status</Label>
                <Select
                  value={editingSpecies.conservation_status || ''}
                  onValueChange={(value) => handleInputChange('conservation_status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Least Concern">Least Concern</SelectItem>
                    <SelectItem value="Near Threatened">Near Threatened</SelectItem>
                    <SelectItem value="Vulnerable">Vulnerable</SelectItem>
                    <SelectItem value="Endangered">Endangered</SelectItem>
                    <SelectItem value="Critically Endangered">Critically Endangered</SelectItem>
                    <SelectItem value="Extinct in the Wild">Extinct in the Wild</SelectItem>
                    <SelectItem value="Extinct">Extinct</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="population_trend">Population Trend</Label>
                <Select
                  value={editingSpecies.population_trend || ''}
                  onValueChange={(value) => handleInputChange('population_trend', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select trend" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Increasing">Increasing</SelectItem>
                    <SelectItem value="Stable">Stable</SelectItem>
                    <SelectItem value="Decreasing">Decreasing</SelectItem>
                    <SelectItem value="Unknown">Unknown</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Toggles */}
            <div className="flex gap-6">
              <div className="flex items-center space-x-2">
                <Switch
                  id="published"
                  checked={editingSpecies.published || false}
                  onCheckedChange={(checked) => handleInputChange('published', checked)}
                />
                <Label htmlFor="published">Published</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="featured"
                  checked={editingSpecies.featured || false}
                  onCheckedChange={(checked) => handleInputChange('featured', checked)}
                />
                <Label htmlFor="featured">Featured</Label>
              </div>
            </div>

            {/* Save Button */}
            <div className="flex justify-end">
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    {isCreating ? 'Create Species' : 'Save Changes'}
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
