import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  ImageIcon, 
  Tag, 
  AlertCircle, 
  CheckCircle, 
  XCircle, 
  Eye, 
  EyeOff,
  Database,
  TrendingUp,
  TrendingDown,
  Activity,
  BarChart3,
  PieChart,
  Users,
  Calendar,
  MapPin,
  Camera,
  Brain,
  Sparkles
} from 'lucide-react';

interface MetricCard {
  title: string;
  value: number | string;
  subtitle?: string;
  icon: React.ReactNode;
  color: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'gray';
  trend?: {
    value: number;
    direction: 'up' | 'down' | 'neutral';
    label: string;
  };
  progress?: number;
  badge?: {
    text: string;
    variant: 'default' | 'secondary' | 'destructive' | 'outline';
  };
}

interface ChartData {
  label: string;
  value: number;
  color: string;
}

interface DashboardMetricsProps {
  title?: string;
  description?: string;
  metrics: MetricCard[];
  charts?: {
    type: 'pie' | 'bar' | 'line';
    title: string;
    data: ChartData[];
  }[];
  className?: string;
  showProgress?: boolean;
  compact?: boolean;
}

/**
 * DashboardMetrics - Shared component for displaying analytics and metrics
 * 
 * Consolidates common dashboard analytics display logic used across
 * DataConsistencyDashboard and PhotoReviewDashboard components.
 * 
 * @example
 * ```tsx
 * <DashboardMetrics
 *   title="Photo Analytics"
 *   metrics={[
 *     {
 *       title: "Total Photos",
 *       value: 1250,
 *       icon: <ImageIcon className="w-5 h-5" />,
 *       color: "blue"
 *     }
 *   ]}
 * />
 * ```
 */
export function DashboardMetrics({
  title,
  description,
  metrics,
  charts = [],
  className = '',
  showProgress = false,
  compact = false
}: DashboardMetricsProps) {
  const getColorClasses = (color: MetricCard['color']) => {
    switch (color) {
      case 'blue':
        return {
          bg: 'bg-blue-50',
          text: 'text-blue-600',
          border: 'border-blue-200'
        };
      case 'green':
        return {
          bg: 'bg-green-50',
          text: 'text-green-600',
          border: 'border-green-200'
        };
      case 'yellow':
        return {
          bg: 'bg-yellow-50',
          text: 'text-yellow-600',
          border: 'border-yellow-200'
        };
      case 'red':
        return {
          bg: 'bg-red-50',
          text: 'text-red-600',
          border: 'border-red-200'
        };
      case 'purple':
        return {
          bg: 'bg-purple-50',
          text: 'text-purple-600',
          border: 'border-purple-200'
        };
      case 'gray':
        return {
          bg: 'bg-gray-50',
          text: 'text-gray-600',
          border: 'border-gray-200'
        };
    }
  };

  const getTrendIcon = (direction: 'up' | 'down' | 'neutral') => {
    switch (direction) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-600" />;
      case 'neutral':
        return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  const renderChart = (chart: DashboardMetricsProps['charts'][0]) => {
    if (chart.type === 'pie') {
      const total = chart.data.reduce((sum, item) => sum + item.value, 0);
      return (
        <div className="space-y-3">
          {chart.data.map((item, index) => {
            const percentage = total > 0 ? (item.value / total) * 100 : 0;
            return (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="text-sm font-medium">{item.label}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">{item.value}</span>
                  <span className="text-xs text-gray-500">({percentage.toFixed(1)}%)</span>
                </div>
              </div>
            );
          })}
        </div>
      );
    }

    if (chart.type === 'bar') {
      const maxValue = Math.max(...chart.data.map(item => item.value));
      return (
        <div className="space-y-3">
          {chart.data.map((item, index) => {
            const percentage = maxValue > 0 ? (item.value / maxValue) * 100 : 0;
            return (
              <div key={index} className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">{item.label}</span>
                  <span className="text-gray-600">{item.value}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="h-2 rounded-full transition-all duration-300"
                    style={{ 
                      width: `${percentage}%`,
                      backgroundColor: item.color
                    }}
                  />
                </div>
              </div>
            );
          })}
        </div>
      );
    }

    return null;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      {(title || description) && (
        <div className="space-y-2">
          {title && (
            <h2 className="text-2xl font-bold flex items-center gap-2">
              <BarChart3 className="w-6 h-6" />
              {title}
            </h2>
          )}
          {description && (
            <p className="text-gray-600">{description}</p>
          )}
        </div>
      )}

      {/* Metrics Grid */}
      <div className={`grid gap-4 ${
        compact 
          ? 'grid-cols-2 md:grid-cols-4 lg:grid-cols-6' 
          : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
      }`}>
        {metrics.map((metric, index) => {
          const colors = getColorClasses(metric.color);
          return (
            <Card key={index} className={`${colors.bg} ${colors.border} hover:shadow-md transition-shadow`}>
              <CardContent className={`p-4 ${compact ? 'p-3' : 'p-6'}`}>
                <div className="flex items-center justify-between">
                  <div className={`p-2 rounded-lg ${colors.bg}`}>
                    <div className={colors.text}>
                      {metric.icon}
                    </div>
                  </div>
                  {metric.trend && (
                    <div className="flex items-center gap-1">
                      {getTrendIcon(metric.trend.direction)}
                      <span className={`text-xs font-medium ${
                        metric.trend.direction === 'up' ? 'text-green-600' :
                        metric.trend.direction === 'down' ? 'text-red-600' :
                        'text-gray-600'
                      }`}>
                        {metric.trend.value}%
                      </span>
                    </div>
                  )}
                </div>
                
                <div className="mt-3">
                  <div className="flex items-center gap-2">
                    <p className={`text-2xl font-bold ${colors.text}`}>
                      {typeof metric.value === 'number' 
                        ? metric.value.toLocaleString() 
                        : metric.value
                      }
                    </p>
                    {metric.badge && (
                      <Badge variant={metric.badge.variant} className="text-xs">
                        {metric.badge.text}
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm font-medium text-gray-700 mt-1">
                    {metric.title}
                  </p>
                  {metric.subtitle && (
                    <p className="text-xs text-gray-500 mt-1">
                      {metric.subtitle}
                    </p>
                  )}
                </div>

                {showProgress && metric.progress !== undefined && (
                  <div className="mt-3">
                    <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                      <span>Progress</span>
                      <span>{metric.progress}%</span>
                    </div>
                    <Progress value={metric.progress} className="h-2" />
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Charts */}
      {charts.length > 0 && (
        <div className="grid gap-6 md:grid-cols-2">
          {charts.map((chart, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {chart.type === 'pie' && <PieChart className="w-5 h-5" />}
                  {chart.type === 'bar' && <BarChart3 className="w-5 h-5" />}
                  {chart.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {renderChart(chart)}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
} 