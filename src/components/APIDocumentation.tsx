import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Copy, ExternalLink, Code, Database, Search, Image, Tag } from "lucide-react";
import { toast } from "sonner";

const API_BASE = "https://raqxptgrugnmxdunbuty.supabase.co/functions/v1/api/v1";

interface EndpointExample {
  method: string;
  path: string;
  description: string;
  parameters?: string[];
  example: string;
  response: string;
}

const endpointExamples: Record<string, EndpointExample[]> = {
  species: [
    {
      method: "GET",
      path: "/species",
      description: "Get all species with pagination and filtering",
      parameters: ["page", "limit", "category", "conservation_status", "featured", "published"],
      example: `${API_BASE}/species?page=1&limit=20&category=Birds&conservation_status=Endangered`,
      response: `{
  "success": true,
  "data": [
    {
      "id": "american-goldfinch",
      "name": "American Goldfinch",
      "common_name": "American Goldfinch",
      "scientific_name": "Spinus tristis",
      "category": "Birds",
      "conservation_status": "Least Concern",
      "description": "...",
      "photo_count": 5,
      "published": true
    }
  ],
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8
  }
}`
    },
    {
      method: "GET",
      path: "/species/{id}",
      description: "Get a specific species with photos",
      parameters: [],
      example: `${API_BASE}/species/american-goldfinch`,
      response: `{
  "success": true,
  "data": {
    "id": "american-goldfinch",
    "name": "American Goldfinch",
    "photos": [
      {
        "id": 1,
        "url": "https://...",
        "title": "Male in breeding plumage",
        "photographer": "John Doe"
      }
    ]
  }
}`
    },
    {
      method: "POST",
      path: "/species",
      description: "Create a new species (admin only)",
      parameters: ["name (required)", "category", "description", "habitat", "diet"],
      example: `curl -X POST ${API_BASE}/species \\
  -H "Content-Type: application/json" \\
  -d '{
    "name": "New Species",
    "category": "Birds",
    "description": "A new species description"
  }'`,
      response: `{
  "success": true,
  "data": {
    "id": "new-species-id",
    "name": "New Species",
    "created_at": "2024-01-01T00:00:00Z"
  }
}`
    }
  ],
  photos: [
    {
      method: "GET",
      path: "/photos",
      description: "Get all photos with filtering",
      parameters: ["page", "limit", "species_id", "photographer", "location", "published"],
      example: `${API_BASE}/photos?species_id=american-goldfinch&limit=10`,
      response: `{
  "success": true,
  "data": [
    {
      "id": 1,
      "url": "https://...",
      "title": "Photo title",
      "photographer": "John Doe",
      "species": {
        "id": "american-goldfinch",
        "name": "American Goldfinch"
      }
    }
  ]
}`
    },
    {
      method: "GET",
      path: "/photos/{id}",
      description: "Get a specific photo with species details",
      parameters: [],
      example: `${API_BASE}/photos/1`,
      response: `{
  "success": true,
  "data": {
    "id": 1,
    "url": "https://...",
    "title": "Photo title",
    "species": {
      "id": "american-goldfinch",
      "name": "American Goldfinch",
      "description": "..."
    }
  }
}`
    }
  ],
  search: [
    {
      method: "GET",
      path: "/search",
      description: "Search across species and photos",
      parameters: ["q (required)", "type", "page", "limit", "category", "conservation_status"],
      example: `${API_BASE}/search?q=goldfinch&type=all&limit=20`,
      response: `{
  "success": true,
  "data": {
    "species": [...],
    "photos": [...],
    "meta": {
      "query": "goldfinch",
      "type": "all",
      "totalSpecies": 5,
      "totalPhotos": 12
    }
  }
}`
    }
  ],
  metadata: [
    {
      method: "GET",
      path: "/categories",
      description: "Get all available categories with counts",
      parameters: [],
      example: `${API_BASE}/categories`,
      response: `{
  "success": true,
  "data": [
    {
      "category": "Birds",
      "count": 150
    },
    {
      "category": "Mammals",
      "count": 75
    }
  ]
}`
    },
    {
      method: "GET",
      path: "/conservation-statuses",
      description: "Get all conservation statuses with counts",
      parameters: [],
      example: `${API_BASE}/conservation-statuses`,
      response: `{
  "success": true,
  "data": [
    {
      "status": "Least Concern",
      "count": 200
    },
    {
      "status": "Endangered",
      "count": 25
    }
  ]
}`
    }
  ]
};

export const APIDocumentation = () => {
  const [copiedEndpoint, setCopiedEndpoint] = useState<string | null>(null);

  const copyToClipboard = async (text: string, endpoint: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedEndpoint(endpoint);
      toast.success('Copied to clipboard');
      setTimeout(() => setCopiedEndpoint(null), 2000);
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'bg-green-100 text-green-800';
      case 'POST': return 'bg-blue-100 text-blue-800';
      case 'PUT': return 'bg-yellow-100 text-yellow-800';
      case 'DELETE': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Wildlife API Documentation</h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Use these endpoints to access wildlife species and photo data. All endpoints return JSON responses
          with a consistent format including success status and metadata.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Code className="w-5 h-5" />
            Base URL
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <code className="text-sm font-mono">{API_BASE}</code>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(API_BASE, 'base')}
            >
              <Copy className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="species" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="species" className="flex items-center gap-2">
            <Database className="w-4 h-4" />
            Species
          </TabsTrigger>
          <TabsTrigger value="photos" className="flex items-center gap-2">
            <Image className="w-4 h-4" />
            Photos
          </TabsTrigger>
          <TabsTrigger value="search" className="flex items-center gap-2">
            <Search className="w-4 h-4" />
            Search
          </TabsTrigger>
          <TabsTrigger value="metadata" className="flex items-center gap-2">
            <Tag className="w-4 h-4" />
            Metadata
          </TabsTrigger>
        </TabsList>

        {Object.entries(endpointExamples).map(([category, endpoints]) => (
          <TabsContent key={category} value={category} className="space-y-4">
            {endpoints.map((endpoint, index) => (
              <Card key={index}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Badge className={getMethodColor(endpoint.method)}>
                        {endpoint.method}
                      </Badge>
                      <code className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                        {endpoint.path}
                      </code>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(endpoint.example, `${category}-${index}`)}
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                  <p className="text-gray-600 mt-2">{endpoint.description}</p>
                  {endpoint.parameters && endpoint.parameters.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm font-medium text-gray-700 mb-1">Parameters:</p>
                      <div className="flex flex-wrap gap-1">
                        {endpoint.parameters.map((param, paramIndex) => (
                          <Badge key={paramIndex} variant="outline" className="text-xs">
                            {param}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Example Request:</h4>
                    <div className="bg-gray-900 text-green-400 p-3 rounded-lg overflow-x-auto">
                      <pre className="text-sm">{endpoint.example}</pre>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Example Response:</h4>
                    <div className="bg-gray-50 p-3 rounded-lg overflow-x-auto">
                      <pre className="text-sm text-gray-800">{endpoint.response}</pre>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        ))}
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Response Format</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Success Response:</h4>
              <div className="bg-gray-50 p-3 rounded-lg">
                <pre className="text-sm">
{`{
  "success": true,
  "data": { /* actual data */ },
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8
  }
}`}
                </pre>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">Error Response:</h4>
              <div className="bg-gray-50 p-3 rounded-lg">
                <pre className="text-sm">
{`{
  "success": false,
  "error": {
    "message": "Error description",
    "code": "ERROR_CODE",
    "details": { /* additional error details */ }
  }
}`}
                </pre>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Usage Examples</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">JavaScript/TypeScript:</h4>
              <div className="bg-gray-900 text-green-400 p-3 rounded-lg">
                <pre className="text-sm">
{`// Get all birds
const response = await fetch('${API_BASE}/species?category=Birds');
const data = await response.json();

// Search for goldfinch
const searchResponse = await fetch('${API_BASE}/search?q=goldfinch');
const searchData = await searchResponse.json();

// Get photos for a specific species
const photosResponse = await fetch('${API_BASE}/photos?species_id=american-goldfinch');
const photosData = await photosResponse.json();`}
                </pre>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">cURL:</h4>
              <div className="bg-gray-900 text-green-400 p-3 rounded-lg">
                <pre className="text-sm">
{`# Get featured species
curl "${API_BASE}/species?featured=true"

# Search for endangered species
curl "${API_BASE}/search?q=endangered&type=species"

# Get categories
curl "${API_BASE}/categories"`}
                </pre>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}; 