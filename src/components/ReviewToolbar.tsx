import React from 'react';
import { PhotoReviewItem } from '@/hooks/usePhotoReviewData';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';

interface ReviewToolbarProps {
  data?: PhotoReviewItem[];
  isLoading?: boolean;
  onRefetch?: () => void;
  selectedCount?: number;
  onSelectAll?: () => void;
  allSelected?: boolean;
}

export const ReviewToolbar: React.FC<ReviewToolbarProps> = ({ data = [], isLoading, onRefetch, selectedCount = 0, onSelectAll, allSelected }) => {
  // Placeholder filter state
  const [species, setSpecies] = React.useState('');
  const [location, setLocation] = React.useState('');
  const [confidence, setConfidence] = React.useState('');

  return (
    <div className="flex flex-wrap items-center gap-4 mb-6">
      <Checkbox checked={allSelected} onCheckedChange={onSelectAll} />
      <span className="text-sm">Select All</span>
      <div className="font-semibold text-lg">Photos to review: {isLoading ? '…' : data.length}</div>
      <div className="text-gray-500">Selected: {selectedCount}</div>
      <select className="border rounded px-2 py-1" value={species} onChange={e => setSpecies(e.target.value)}>
        <option value="">All Species</option>
        {/* TODO: Populate with real species */}
        <option value="sp1">Species 1</option>
      </select>
      <select className="border rounded px-2 py-1" value={location} onChange={e => setLocation(e.target.value)}>
        <option value="">All Locations</option>
        {/* TODO: Populate with real locations */}
        <option value="loc1">Location 1</option>
      </select>
      <select className="border rounded px-2 py-1" value={confidence} onChange={e => setConfidence(e.target.value)}>
        <option value="">All Confidence</option>
        <option value=">0.9">90%+</option>
        <option value=">0.8">80%+</option>
      </select>
      <Button variant="outline" size="sm" onClick={onRefetch}>Refresh</Button>
      {/* TODO: Add real filter logic and selected count */}
    </div>
  );
}; 