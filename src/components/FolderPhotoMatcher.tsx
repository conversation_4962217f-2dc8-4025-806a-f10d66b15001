import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { FolderOpen } from 'lucide-react';

export function FolderPhotoMatcher() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FolderOpen className="w-5 h-5 text-green-600" />
          Folder Photo Matcher
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600">
          Tools for matching photos with folder structures. This component is currently under development.
        </p>
      </CardContent>
    </Card>
  );
}
