import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  Edit, 
  Save, 
  X, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Upload,
  Eye,
  EyeOff
} from 'lucide-react';
import { EnrichedSpecies } from '@/utils/aiSpeciesGenerator';

interface SpeciesImportTableProps {
  species: EnrichedSpecies[];
  onSpeciesUpdate: (index: number, updatedSpecies: EnrichedSpecies) => void;
  onSpeciesDelete: (index: number) => void;
  onBulkPublish: (published: boolean) => void;
  onInsertToSupabase: () => void;
  isInserting: boolean;
  dryRunMode: boolean;
}

type EditMode = 'none' | 'editing';

export function SpeciesImportTable({
  species,
  onSpeciesUpdate,
  onSpeciesDelete,
  onBulkPublish,
  onInsertToSupabase,
  isInserting,
  dryRunMode
}: SpeciesImportTableProps) {
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editMode, setEditMode] = useState<EditMode>('none');
  const [bulkPublished, setBulkPublished] = useState(false);

  const handleEdit = (index: number) => {
    setEditingIndex(index);
    setEditMode('editing');
  };

  const handleSave = (index: number, updatedSpecies: EnrichedSpecies) => {
    onSpeciesUpdate(index, updatedSpecies);
    setEditingIndex(null);
    setEditMode('none');
  };

  const handleCancel = () => {
    setEditingIndex(null);
    setEditMode('none');
  };

  const handleBulkPublishToggle = (published: boolean) => {
    setBulkPublished(published);
    onBulkPublish(published);
  };

  const getStatusIcon = (species: EnrichedSpecies) => {
    if (species.ai_generated) {
      return <CheckCircle className="w-4 h-4 text-green-600" />;
    }
    return <Edit className="w-4 h-4 text-blue-600" />;
  };

  const getStatusBadge = (species: EnrichedSpecies) => {
    if (species.ai_generated) {
      return <Badge variant="secondary" className="text-xs">AI Generated</Badge>;
    }
    return <Badge variant="outline" className="text-xs">Edited</Badge>;
  };

  if (species.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <AlertCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold">No Species to Import</h3>
          <p className="text-muted-foreground">Add some species names and generate metadata first.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Review & Edit Species ({species.length})</span>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Switch
                  id="bulk-publish"
                  checked={bulkPublished}
                  onCheckedChange={handleBulkPublishToggle}
                />
                <Label htmlFor="bulk-publish">Bulk Publish</Label>
              </div>
              <Button
                onClick={onInsertToSupabase}
                disabled={isInserting}
                className={dryRunMode ? 'bg-yellow-600 hover:bg-yellow-700' : ''}
              >
                {isInserting ? (
                  <Clock className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Upload className="w-4 h-4 mr-2" />
                )}
                {dryRunMode ? 'Dry Run Test' : 'Insert to Supabase'}
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">Status</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Scientific Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Conservation Status</TableHead>
                  <TableHead>Published</TableHead>
                  <TableHead className="w-20">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {species.map((speciesItem, index) => (
                  <TableRow key={index} className={speciesItem.ai_generated ? 'bg-yellow-50' : ''}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(speciesItem)}
                        {getStatusBadge(speciesItem)}
                      </div>
                    </TableCell>
                    <TableCell>
                      {editingIndex === index ? (
                        <Input
                          value={speciesItem.name}
                          onChange={(e) => {
                            const updated = { ...speciesItem, name: e.target.value };
                            onSpeciesUpdate(index, updated);
                          }}
                          className="w-full"
                        />
                      ) : (
                        <div className="font-medium">{speciesItem.name}</div>
                      )}
                    </TableCell>
                    <TableCell>
                      {editingIndex === index ? (
                        <Input
                          value={speciesItem.scientific_name}
                          onChange={(e) => {
                            const updated = { ...speciesItem, scientific_name: e.target.value };
                            onSpeciesUpdate(index, updated);
                          }}
                          className="w-full"
                        />
                      ) : (
                        <div className="text-sm text-muted-foreground">{speciesItem.scientific_name}</div>
                      )}
                    </TableCell>
                    <TableCell>
                      {editingIndex === index ? (
                        <Input
                          value={speciesItem.category}
                          onChange={(e) => {
                            const updated = { ...speciesItem, category: e.target.value };
                            onSpeciesUpdate(index, updated);
                          }}
                          className="w-full"
                        />
                      ) : (
                        <Badge variant="outline">{speciesItem.category}</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {editingIndex === index ? (
                        <Input
                          value={speciesItem.conservation_status}
                          onChange={(e) => {
                            const updated = { ...speciesItem, conservation_status: e.target.value };
                            onSpeciesUpdate(index, updated);
                          }}
                          className="w-full"
                        />
                      ) : (
                        <Badge 
                          variant={
                            speciesItem.conservation_status === 'Endangered' || 
                            speciesItem.conservation_status === 'Critically Endangered' 
                              ? 'destructive' 
                              : 'secondary'
                          }
                        >
                          {speciesItem.conservation_status}
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      {editingIndex === index ? (
                        <Switch
                          checked={speciesItem.published}
                          onCheckedChange={(checked) => {
                            const updated = { ...speciesItem, published: checked };
                            onSpeciesUpdate(index, updated);
                          }}
                        />
                      ) : (
                        <div className="flex items-center gap-2">
                          {speciesItem.published ? (
                            <Eye className="w-4 h-4 text-green-600" />
                          ) : (
                            <EyeOff className="w-4 h-4 text-muted-foreground" />
                          )}
                          <span className="text-sm">
                            {speciesItem.published ? 'Published' : 'Draft'}
                          </span>
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {editingIndex === index ? (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleSave(index, speciesItem)}
                            >
                              <Save className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={handleCancel}
                            >
                              <X className="w-3 h-3" />
                            </Button>
                          </>
                        ) : (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(index)}
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onSpeciesDelete(index)}
                            >
                              <X className="w-3 h-3" />
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Detailed View for Editing */}
      {editingIndex !== null && species[editingIndex] && (
        <Card>
          <CardHeader>
            <CardTitle>Edit Species Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={species[editingIndex].description}
                    onChange={(e) => {
                      const updated = { ...species[editingIndex], description: e.target.value };
                      onSpeciesUpdate(editingIndex, updated);
                    }}
                    rows={3}
                  />
                </div>
                <div>
                  <Label htmlFor="habitat">Habitat</Label>
                  <Textarea
                    id="habitat"
                    value={species[editingIndex].habitat}
                    onChange={(e) => {
                      const updated = { ...species[editingIndex], habitat: e.target.value };
                      onSpeciesUpdate(editingIndex, updated);
                    }}
                    rows={2}
                  />
                </div>
                <div>
                  <Label htmlFor="diet">Diet</Label>
                  <Input
                    id="diet"
                    value={species[editingIndex].diet}
                    onChange={(e) => {
                      const updated = { ...species[editingIndex], diet: e.target.value };
                      onSpeciesUpdate(editingIndex, updated);
                    }}
                  />
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="locations">Related Locations</Label>
                  <Textarea
                    id="locations"
                    value={species[editingIndex].related_locations.join(', ')}
                    onChange={(e) => {
                      const locations = e.target.value.split(',').map(loc => loc.trim()).filter(loc => loc);
                      const updated = { ...species[editingIndex], related_locations: locations };
                      onSpeciesUpdate(editingIndex, updated);
                    }}
                    placeholder="Enter locations separated by commas"
                    rows={3}
                  />
                </div>
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Switch
                      id="ai-generated"
                      checked={species[editingIndex].ai_generated}
                      onCheckedChange={(checked) => {
                        const updated = { ...species[editingIndex], ai_generated: checked };
                        onSpeciesUpdate(editingIndex, updated);
                      }}
                    />
                    <Label htmlFor="ai-generated">AI Generated</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      id="published"
                      checked={species[editingIndex].published}
                      onCheckedChange={(checked) => {
                        const updated = { ...species[editingIndex], published: checked };
                        onSpeciesUpdate(editingIndex, updated);
                      }}
                    />
                    <Label htmlFor="published">Published</Label>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 