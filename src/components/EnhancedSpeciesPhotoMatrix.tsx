import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { UnifiedSpeciesEditor } from '@/components/UnifiedSpeciesEditor';
import { EnhancedPhotoUpload } from '@/components/EnhancedPhotoUpload';
import { PhotoEditModal } from '@/components/PhotoEditModal';
import { ContentAnalytics } from '@/components/ContentAnalytics';
import { useDebouncedSearch, usePerformanceMonitor, useOptimizedFilter } from '@/hooks/useVirtualization';
import { 
  Search, 
  RefreshCw,
  ChevronDown,
  ChevronRight,
  Download,
  ImageIcon,
  AlertCircle,
  CheckCircle,
  Info,
  Camera,
  Plus,
  Bot,
  Upload,
  X,
  Pencil,
  Filter,
  Grid3X3,
  List,
  Settings,
  Trash2,
  Edit,
  Save,
  MoreHorizontal,
  Brain,
  Sparkles,
  Clock,
  Users,
  BarChart3,
  CheckSquare,
  Square,
  Eye,
  EyeOff,
  MapPin,
  Calendar,
  Tag,
  FileText
} from 'lucide-react';
import { 
  getSpeciesWithAllPhotoData,
  filterSpeciesPhotoMatrix,
  getUniqueCategories,
  exportToCSV,
  downloadCSV,
  type SpeciesPhotoMatrixData,
  type SpeciesWithPhotos,
  type Photo
} from '@/utils/speciesPhotoMatrix';
import { generateAISpeciesMetadata, insertSpeciesToSupabase } from '@/utils/aiSpeciesGenerator';
import { getServiceRoleClient } from '@/lib/supabaseServiceRole';
import { WildlifeAIAgent } from '@/lib/aiAgent';
import { supabase } from '@/integrations/supabase/client';
import { AuditLogger, VersionControl, withPerformanceTracking } from '@/utils/auditLogger';
import { validateSpeciesPhotoMatrixData, sanitizeSpeciesPhotoMatrixData } from '@/utils/dataValidation';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { TouchBackend } from 'react-dnd-touch-backend';
import { isMobile } from 'react-device-detect';
import { cn } from '@/lib/utils';

// Types and interfaces
interface FilterState {
  category: string;
  publishedStatus: string;
  photoStatus: string;
  location: string;
}

interface ViewState {
  mode: 'grid' | 'list';
  showUnassigned: boolean;
  selectedSpecies: Set<string>;
  selectedPhotos: Set<string>;
}

interface CMSStats {
  totalSpecies: number;
  publishedSpecies: number;
  draftSpecies: number;
  totalPhotos: number;
  publishedPhotos: number;
  unassignedPhotos: number;
  aiPendingReview: number;
}

// Category icons mapping
const categoryIcons: Record<string, string> = {
  'Birds': '🦅',
  'Mammals': '🦌',
  'Reptiles': '🦎',
  'Amphibians': '🐸',
  'Fish': '🐟',
  'Insects': '🦋',
  'Marine': '🐋',
  'Other': '🔍'
};

const ItemTypes = {
  PHOTO: 'photo',
};

// Enhanced Photo Tile Component
const EnhancedPhotoTile = ({ 
  photo, 
  speciesId, 
  isSelected, 
  onSelect, 
  onEdit,
  showDetails = false 
}: { 
  photo: Photo; 
  speciesId: string;
  isSelected: boolean;
  onSelect: (photoId: string, selected: boolean) => void;
  onEdit: (photo: Photo) => void;
  showDetails?: boolean;
}) => {
  const [isImageError, setIsImageError] = useState(false);
  const [{ isDragging }, drag] = useDrag(() => ({
    type: ItemTypes.PHOTO,
    item: { photoId: photo.id, originalSpeciesId: speciesId },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  }));

  const fileName = photo.url ? photo.url.split('/').pop() : 'Untitled';

  return (
    <div
      ref={drag}
      className={cn(
        "relative group border rounded-lg bg-white shadow-sm transition-all duration-200 ease-in-out cursor-grab",
        isDragging ? "opacity-50 scale-95 shadow-lg" : "opacity-100 scale-100 hover:shadow-md",
        isSelected ? "ring-2 ring-blue-500" : ""
      )}
    >
      {/* Selection checkbox */}
      <div className="absolute top-2 left-2 z-10">
        <Checkbox
          checked={isSelected}
          onCheckedChange={(checked) => onSelect(photo.id.toString(), !!checked)}
          className="bg-white/80 backdrop-blur-sm"
        />
      </div>

      {/* Photo content */}
      <div className="aspect-square w-full">
        {isImageError || !photo.url ? (
          <div className="w-full h-full flex flex-col items-center justify-center bg-gray-100 text-gray-400 rounded-t-lg">
            <AlertCircle className="w-8 h-8 mb-1" />
            <span className="text-xs text-center px-1">Image not available</span>
          </div>
        ) : (
          <img
            src={photo.url}
            alt={fileName || 'photo'}
            className="w-full h-full object-cover rounded-t-lg"
            onError={() => setIsImageError(true)}
          />
        )}
      </div>

      {/* Photo info */}
      <div className="p-2 text-xs">
        <p className="font-medium truncate" title={fileName || undefined}>{fileName}</p>
        <p className="text-gray-500 truncate">
          {photo.title || 'No title'}
        </p>
        {showDetails && (
          <div className="mt-1 space-y-1">
            {photo.location && (
              <div className="flex items-center gap-1 text-gray-400">
                <MapPin className="w-3 h-3" />
                <span className="truncate">{photo.location}</span>
              </div>
            )}
            {photo.date_taken && (
              <div className="flex items-center gap-1 text-gray-400">
                <Calendar className="w-3 h-3" />
                <span>{new Date(photo.date_taken).toLocaleDateString()}</span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Status badges */}
      <div className="absolute top-2 right-2 flex flex-col gap-1">
        {photo.published && (
          <Badge className="text-xs px-1.5 py-0.5" variant="default">
            Published
          </Badge>
        )}
        {photo.ai_generated && (
          <Badge className="text-xs px-1.5 py-0.5" variant="secondary">
            <Brain className="w-3 h-3 mr-1" />
            AI
          </Badge>
        )}
      </div>

      {/* Action buttons on hover */}
      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center gap-2">
        <Button
          size="sm"
          variant="secondary"
          onClick={(e) => {
            e.stopPropagation();
            onEdit(photo);
          }}
        >
          <Edit className="w-4 h-4" />
        </Button>
        <Button
          size="sm"
          variant="secondary"
          onClick={(e) => {
            e.stopPropagation();
            window.open(photo.url, '_blank');
          }}
        >
          <Eye className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};

// Enhanced Species Section Component
const EnhancedSpeciesSection = ({ 
  speciesWithPhotos,
  onDropPhoto,
  isExpanded,
  onToggleExpand,
  onEdit,
  onExport,
  onUploadPhotos,
  onTogglePublish,
  isSelected,
  onSelect,
  selectedPhotos,
  onSelectPhoto,
  onEditPhoto,
  isAdmin,
  showPhotoDetails
}: { 
  speciesWithPhotos: SpeciesWithPhotos;
  onDropPhoto: (photoId: string, originalSpeciesId: string, newSpeciesId: string) => void;
  isExpanded: boolean;
  onToggleExpand: () => void;
  onEdit: (species: SpeciesWithPhotos) => void;
  onExport: (species: SpeciesWithPhotos) => void;
  onUploadPhotos: (speciesId: string) => void;
  onTogglePublish: (speciesId: string, published: boolean) => void;
  isSelected: boolean;
  onSelect: (speciesId: string, selected: boolean) => void;
  selectedPhotos: Set<string>;
  onSelectPhoto: (photoId: string, selected: boolean) => void;
  onEditPhoto: (photo: Photo) => void;
  isAdmin: boolean;
  showPhotoDetails: boolean;
}) => {
  const [{ isOver }, drop] = useDrop(() => ({
    accept: ItemTypes.PHOTO,
    drop: (item: { photoId: string; originalSpeciesId: string }) => {
      if (item.originalSpeciesId !== speciesWithPhotos.species.id) {
        onDropPhoto(item.photoId, item.originalSpeciesId, speciesWithPhotos.species.id)
      }
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  }));
  
  const { species, photos } = speciesWithPhotos;
  const categoryIcon = categoryIcons[species.category || 'Other'] || '🔍';
  const publishedPhotos = photos.filter(p => p.published).length;

  return (
    <details open={isExpanded} onToggle={onToggleExpand} className="border rounded-lg mb-2 bg-white">
      <summary 
        ref={drop}
        className={cn(
          "p-4 hover:bg-muted/50 cursor-pointer transition-colors flex items-center justify-between",
          isOver ? "bg-blue-100" : "",
          isSelected ? "bg-blue-50 border-blue-300" : ""
        )}
      >
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Checkbox
              checked={isSelected}
              onCheckedChange={(checked) => onSelect(species.id, !!checked)}
              onClick={(e) => e.stopPropagation()}
            />
            <span>{isExpanded ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}</span>
          </div>
          <div className="flex items-center gap-3">
            <span className="text-2xl">{categoryIcon}</span>
            <div>
              <h3 className="font-semibold text-lg">{species.name}</h3>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span className="italic">{species.scientific_name}</span>
                {species.common_name && (<><span>•</span><span>{species.common_name}</span></>)}
                {species.category && (<><span>•</span><span>{species.category}</span></>)}
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <div className="text-right">
            <div className="flex items-center gap-2">
              <Badge 
                variant={species.published ? "default" : "secondary"}
                className="cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  onTogglePublish(species.id, !species.published);
                }}
              >
                {species.published ? "Published" : "Draft"}
              </Badge>
              <Badge variant="outline">{photos.length} photos</Badge>
              <Badge variant="outline" className="text-green-600">
                {publishedPhotos} published
              </Badge>
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              {species.conservation_status && (<span>Status: {species.conservation_status}</span>)}
            </div>
          </div>
          
          {/* Action buttons */}
          <div className="flex items-center gap-1">
            <Button 
              onClick={(e) => { e.stopPropagation(); onUploadPhotos(species.id); }} 
              variant="outline" 
              size="sm"
              title="Upload photos"
            >
              <Upload className="w-4 h-4" />
            </Button>
            <Button 
              onClick={(e) => { e.stopPropagation(); onExport(speciesWithPhotos); }} 
              variant="outline" 
              size="sm"
              title="Export data"
            >
              <Download className="w-4 h-4" />
            </Button>
            {isAdmin && (
              <Button 
                onClick={(e) => { e.stopPropagation(); onEdit(speciesWithPhotos); }} 
                variant="outline" 
                size="sm"
                title="Edit species"
              >
                <Pencil className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </summary>
      
      {/* Photos grid */}
      <div className="border-t bg-muted/30 p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Photos ({photos.length})</span>
            {photos.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  const allSelected = photos.every(p => selectedPhotos.has(p.id.toString()));
                  photos.forEach(p => onSelectPhoto(p.id.toString(), !allSelected));
                }}
              >
                {photos.every(p => selectedPhotos.has(p.id.toString())) ? (
                  <>
                    <CheckSquare className="w-4 h-4 mr-1" />
                    Deselect All
                  </>
                ) : (
                  <>
                    <Square className="w-4 h-4 mr-1" />
                    Select All
                  </>
                )}
              </Button>
            )}
          </div>
          <Button
            onClick={() => onUploadPhotos(species.id)}
            variant="outline"
            size="sm"
          >
            <Plus className="w-4 h-4 mr-1" />
            Add Photos
          </Button>
        </div>
        
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-3">
          {photos.map(photo => (
            <EnhancedPhotoTile 
              key={photo.id} 
              photo={photo} 
              speciesId={species.id}
              isSelected={selectedPhotos.has(photo.id.toString())}
              onSelect={onSelectPhoto}
              onEdit={onEditPhoto}
              showDetails={showPhotoDetails}
            />
          ))}
        </div>
        
        {photos.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <ImageIcon className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>No photos assigned to this species</p>
            <Button
              onClick={() => onUploadPhotos(species.id)}
              variant="outline"
              size="sm"
              className="mt-2"
            >
              <Plus className="w-4 h-4 mr-1" />
              Upload First Photo
            </Button>
          </div>
        )}
      </div>
    </details>
  );
};

// Main Enhanced Species Photo Matrix Component
export function EnhancedSpeciesPhotoMatrix() {
  const [data, setData] = useState<SpeciesPhotoMatrixData | null>(null);
  const [filteredData, setFilteredData] = useState<SpeciesPhotoMatrixData | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedSpecies, setExpandedSpecies] = useState<Set<string>>(new Set());

  // Performance monitoring
  const { renderCount } = usePerformanceMonitor('EnhancedSpeciesPhotoMatrix');

  // Debounced search for better performance
  const debouncedSearchTerm = useDebouncedSearch(searchTerm, 300);

  // Filter and view states
  const [filters, setFilters] = useState<FilterState>({
    category: 'all',
    publishedStatus: 'all',
    photoStatus: 'all',
    location: ''
  });

  const [viewState, setViewState] = useState<ViewState>({
    mode: 'grid',
    showUnassigned: true,
    selectedSpecies: new Set(),
    selectedPhotos: new Set()
  });

  // Modal states
  const [selectedSpecies, setSelectedSpecies] = useState<SpeciesWithPhotos | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [uploadSpeciesId, setUploadSpeciesId] = useState<string>('');
  const [speciesEditorOpen, setSpeciesEditorOpen] = useState(false);
  const [editingSpecies, setEditingSpecies] = useState<SpeciesWithPhotos | null>(null);
  const [editorMode, setEditorMode] = useState<'create' | 'edit'>('create');
  const [aiProcessingModalOpen, setAiProcessingModalOpen] = useState(false);
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null);
  const [photoEditModalOpen, setPhotoEditModalOpen] = useState(false);
  const [analyticsModalOpen, setAnalyticsModalOpen] = useState(false);

  // Stats
  const [stats, setStats] = useState<CMSStats>({
    totalSpecies: 0,
    publishedSpecies: 0,
    draftSpecies: 0,
    totalPhotos: 0,
    publishedPhotos: 0,
    unassignedPhotos: 0,
    aiPendingReview: 0
  });

  const { isAdmin } = useAdminAuth();

  // Load data with performance tracking
  const loadData = useCallback(withPerformanceTracking('loadSpeciesData', async () => {
    try {
      setLoading(true);
      const rawData = await getSpeciesWithAllPhotoData();

      // Validate and sanitize data structure
      let speciesPhotoData;
      if (validateSpeciesPhotoMatrixData(rawData)) {
        speciesPhotoData = rawData;
      } else {
        console.warn('Data validation failed, sanitizing data:', rawData);
        speciesPhotoData = sanitizeSpeciesPhotoMatrixData(rawData);
      }

      setData(speciesPhotoData);
      setFilteredData(speciesPhotoData);

      // Calculate stats with safe array access
      const newStats: CMSStats = {
        totalSpecies: speciesPhotoData.species?.length || 0,
        publishedSpecies: speciesPhotoData.species?.filter(s => s.species?.published).length || 0,
        draftSpecies: speciesPhotoData.species?.filter(s => !s.species?.published).length || 0,
        totalPhotos: speciesPhotoData.totalPhotos || 0,
        publishedPhotos: speciesPhotoData.publishedPhotos || 0,
        unassignedPhotos: speciesPhotoData.unassigned_photos?.length || 0,
        aiPendingReview: 0 // TODO: Calculate from AI review queue
      };
      setStats(newStats);

    } catch (error) {
      console.error('Error loading species photo data:', error);
      toast.error("Failed to load species and photo data");

      // Set empty data structure on error
      const emptyData = {
        species: [],
        unassigned_photos: [],
        totalPhotos: 0,
        publishedPhotos: 0,
        unpublishedPhotos: 0
      };
      setData(emptyData);
      setFilteredData(emptyData);
    } finally {
      setLoading(false);
    }
  }), []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // Apply filters with performance optimization
  useEffect(() => {
    if (data) {
      let filtered = filterSpeciesPhotoMatrix(data, debouncedSearchTerm, {
        photoPublished: filters.photoStatus === 'all' ? null : filters.photoStatus === 'published',
        speciesPublished: filters.publishedStatus === 'all' ? null : filters.publishedStatus === 'published',
        category: filters.category === 'all' ? undefined : filters.category
      });

      // Additional location filtering
      if (filters.location) {
        filtered = {
          ...filtered,
          species: filtered.species.map(s => ({
            ...s,
            photos: s.photos.filter(p =>
              p.location?.toLowerCase().includes(filters.location.toLowerCase())
            )
          })).filter(s => s.photos.length > 0 || !filters.location)
        };
      }

      setFilteredData(filtered);
    }
  }, [data, debouncedSearchTerm, filters]);

  // Drag and drop handler
  const handleDropPhoto = async (photoId: string, originalSpeciesId: string, newSpeciesId: string) => {
    if (originalSpeciesId === newSpeciesId) return;

    // Optimistic update
    setData(prevData => {
      if (!prevData) return null;

      let photoToMove: Photo | undefined;
      const newData: SpeciesPhotoMatrixData = JSON.parse(JSON.stringify(prevData));

      if (originalSpeciesId === 'unassigned') {
        const photoIndex = newData.unassigned_photos.findIndex(p => p.id === photoId);
        if (photoIndex > -1) [photoToMove] = newData.unassigned_photos.splice(photoIndex, 1);
      } else {
        const sourceSpecies = newData.species.find(s => s.species.id === originalSpeciesId);
        if (sourceSpecies) {
          const photoIndex = sourceSpecies.photos.findIndex(p => p.id === photoId);
          if (photoIndex > -1) {
            [photoToMove] = sourceSpecies.photos.splice(photoIndex, 1);
            sourceSpecies.species.photo_count = sourceSpecies.photos.length;
          }
        }
      }

      if (!photoToMove) return prevData;

      if (newSpeciesId === 'unassigned') {
        photoToMove.species_id = null;
        newData.unassigned_photos.push(photoToMove);
      } else {
        const targetSpecies = newData.species.find(s => s.species.id === newSpeciesId);
        if (targetSpecies) {
          photoToMove.species_id = newSpeciesId;
          targetSpecies.photos.push(photoToMove);
          targetSpecies.species.photo_count = targetSpecies.photos.length;
        }
      }
      return newData;
    });

    // Update database
    try {
      const newSpeciesIdValue = newSpeciesId === 'unassigned' ? null : newSpeciesId;
      const { error } = await supabase
        .from('photos_v2')
        .update({ species_id: newSpeciesIdValue })
        .eq('id', photoId);

      if (error) throw error;

      // Update species photo counts for affected species
      const affectedSpeciesIds = new Set<string>();

      // Add old species ID if it exists
      if (originalSpeciesId && originalSpeciesId !== 'unassigned') {
        affectedSpeciesIds.add(originalSpeciesId);
      }

      // Add new species ID if assigning to one
      if (newSpeciesIdValue) {
        affectedSpeciesIds.add(newSpeciesIdValue);
      }

      // Update photo counts for all affected species
      for (const speciesId of affectedSpeciesIds) {
        const { count, error: countError } = await supabase
          .from('photos_v2')
          .select('*', { count: 'exact', head: true })
          .eq('species_id', speciesId)
          .eq('published', true);

        if (!countError) {
          // Use service role client for admin operations
          const serviceRoleClient = getServiceRoleClient();
          await serviceRoleClient
            .from('species_v2')
            .update({ photo_count: count || 0 })
            .eq('id', speciesId);
        }
      }

      // Log the photo assignment
      await AuditLogger.getInstance().logPhotoAssignment(
        photoId,
        originalSpeciesId === 'unassigned' ? null : originalSpeciesId,
        newSpeciesIdValue
      );

      toast.success("Successfully moved photo");
    } catch (error) {
      console.error('Error updating photo:', error);
      toast.error("Could not reassign photo");
      loadData(); // Reload on error
    }
  };

  // Species management handlers
  const handleEditSpecies = (speciesData: SpeciesWithPhotos) => {
    setEditingSpecies(speciesData);
    setEditorMode('edit');
    setSpeciesEditorOpen(true);
  };

  const handleSpeciesUpdate = () => {
    loadData();
    setModalOpen(false);
  };

  const handleTogglePublish = async (speciesId: string, published: boolean) => {
    try {
      // Use service role client for admin operations
      const serviceRoleClient = getServiceRoleClient();
      const { error } = await serviceRoleClient
        .from('species_v2')
        .update({ published })
        .eq('id', speciesId);

      if (error) throw error;

      // Log the publish/unpublish action
      await AuditLogger.getInstance().logSpeciesPublish(speciesId, published);

      toast.success(`Species ${published ? 'published' : 'unpublished'}`);
      loadData();
    } catch (error) {
      console.error('Error updating species:', error);
      toast.error('Failed to update species status');
    }
  };

  // Photo management handlers
  const handleUploadPhotos = (speciesId: string) => {
    setUploadSpeciesId(speciesId);
    setUploadModalOpen(true);
  };

  const handleUploadComplete = () => {
    setUploadModalOpen(false);
    loadData();
  };

  const handleSelectSpecies = (speciesId: string, selected: boolean) => {
    setViewState(prev => {
      const newSelected = new Set(prev.selectedSpecies);
      if (selected) {
        newSelected.add(speciesId);
      } else {
        newSelected.delete(speciesId);
      }
      return { ...prev, selectedSpecies: newSelected };
    });
  };

  const handleSelectPhoto = (photoId: string, selected: boolean) => {
    setViewState(prev => {
      const newSelected = new Set(prev.selectedPhotos);
      if (selected) {
        newSelected.add(photoId);
      } else {
        newSelected.delete(photoId);
      }
      return { ...prev, selectedPhotos: newSelected };
    });
  };

  const handleEditPhoto = (photo: Photo) => {
    setSelectedPhoto(photo);
    setPhotoEditModalOpen(true);
  };

  const handlePhotoEditSuccess = () => {
    setPhotoEditModalOpen(false);
    setSelectedPhoto(null);
    loadData();
  };

  // Bulk operations
  const handleBulkPublish = async (publish: boolean) => {
    const selectedIds = Array.from(viewState.selectedSpecies);
    if (selectedIds.length === 0) {
      toast.error('No species selected');
      return;
    }

    try {
      // Use service role client for admin operations
      const serviceRoleClient = getServiceRoleClient();
      const { error } = await serviceRoleClient
        .from('species_v2')
        .update({ published: publish })
        .in('id', selectedIds);

      if (error) throw error;
      toast.success(`${selectedIds.length} species ${publish ? 'published' : 'unpublished'}`);
      setViewState(prev => ({ ...prev, selectedSpecies: new Set() }));
      loadData();
    } catch (error) {
      console.error('Error bulk updating species:', error);
      toast.error('Failed to update species');
    }
  };

  const handleBulkPhotoPublish = async (publish: boolean) => {
    const selectedIds = Array.from(viewState.selectedPhotos).map(id => parseInt(id));
    if (selectedIds.length === 0) {
      toast.error('No photos selected');
      return;
    }

    try {
      // Use service role client for admin operations
      const serviceRoleClient = getServiceRoleClient();
      const { error } = await serviceRoleClient
        .from('photos_v2')
        .update({ published: publish })
        .in('id', selectedIds);

      if (error) throw error;
      toast.success(`${selectedIds.length} photos ${publish ? 'published' : 'unpublished'}`);
      setViewState(prev => ({ ...prev, selectedPhotos: new Set() }));
      loadData();
    } catch (error) {
      console.error('Error bulk updating photos:', error);
      toast.error('Failed to update photos');
    }
  };

  const handleBulkPhotoDelete = async () => {
    const selectedIds = Array.from(viewState.selectedPhotos).map(id => parseInt(id));
    if (selectedIds.length === 0) {
      toast.error('No photos selected');
      return;
    }

    if (!confirm(`Are you sure you want to delete ${selectedIds.length} photos? This action cannot be undone.`)) {
      return;
    }

    try {
      const { error } = await supabase
        .from('photos_v2')
        .delete()
        .in('id', selectedIds);

      if (error) throw error;
      toast.success(`${selectedIds.length} photos deleted`);
      setViewState(prev => ({ ...prev, selectedPhotos: new Set() }));
      loadData();
    } catch (error) {
      console.error('Error deleting photos:', error);
      toast.error('Failed to delete photos');
    }
  };

  // Utility functions
  const toggleSpeciesExpansion = (speciesId: string) => {
    setExpandedSpecies(prev => {
      const newSet = new Set(prev);
      if (newSet.has(speciesId)) newSet.delete(speciesId);
      else newSet.add(speciesId);
      return newSet;
    });
  };

  const expandAll = () => filteredData && setExpandedSpecies(new Set(filteredData.species.map(s => s.species.id)));
  const collapseAll = () => setExpandedSpecies(new Set());

  const handleExportAll = () => {
    if (!data || !Array.isArray(data.species)) {
      toast.error("No data available to export");
      return;
    }
    try {
      const csv = exportToCSV(data);
      downloadCSV(csv, 'all_species_photos.csv');
      toast.success("Exported All Data");
    } catch (error) {
      console.error('Export error:', error);
      toast.error("Failed to export data");
    }
  };

  const handleExportSpecies = (speciesWithPhotos: SpeciesWithPhotos) => {
    if (!data || !Array.isArray(data.species)) {
      toast.error("No data available to export");
      return;
    }
    try {
      const csv = exportToCSV(data, speciesWithPhotos.species.id);
      downloadCSV(csv, `${speciesWithPhotos.species.name}_photos.csv`);
      toast.success(`Exported ${speciesWithPhotos.species.name}`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error("Failed to export species data");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4" />
          <p>Loading Species & Photo Matrix...</p>
        </div>
      </div>
    );
  }

  const backend = isMobile ? TouchBackend : HTML5Backend;
  const categories = data && data.species ? getUniqueCategories(data.species.map(s => s.species)) : [];

  return (
    <DndProvider backend={backend}>
      <div className="space-y-6">
        {/* Header with stats and quick actions */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-2xl">Species & Photo CMS</CardTitle>
                <p className="text-muted-foreground mt-1">
                  Comprehensive content management for wildlife species and photos
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button onClick={() => {
                  setEditingSpecies(null);
                  setEditorMode('create');
                  setSpeciesEditorOpen(true);
                }} className="gap-2">
                  <Plus className="w-4 h-4" />
                  Create Species
                </Button>
                <Button onClick={() => setAiProcessingModalOpen(true)} variant="outline" className="gap-2">
                  <Brain className="w-4 h-4" />
                  AI Tools
                </Button>
                <Button onClick={() => setAnalyticsModalOpen(true)} variant="outline" className="gap-2">
                  <BarChart3 className="w-4 h-4" />
                  Analytics
                </Button>
              </div>
            </div>

            {/* Stats dashboard */}
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4 mt-6">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{stats.totalSpecies}</div>
                <div className="text-sm text-blue-600">Total Species</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{stats.publishedSpecies}</div>
                <div className="text-sm text-green-600">Published</div>
              </div>
              <div className="text-center p-3 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">{stats.draftSpecies}</div>
                <div className="text-sm text-yellow-600">Drafts</div>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">{stats.totalPhotos}</div>
                <div className="text-sm text-purple-600">Total Photos</div>
              </div>
              <div className="text-center p-3 bg-emerald-50 rounded-lg">
                <div className="text-2xl font-bold text-emerald-600">{stats.publishedPhotos}</div>
                <div className="text-sm text-emerald-600">Published Photos</div>
              </div>
              <div className="text-center p-3 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">{stats.unassignedPhotos}</div>
                <div className="text-sm text-orange-600">Unassigned</div>
              </div>
              <div className="text-center p-3 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">{stats.aiPendingReview}</div>
                <div className="text-sm text-red-600">AI Review</div>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Filters and controls */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
              {/* Search and filters */}
              <div className="flex flex-wrap gap-3 items-center">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search species, photos, locations..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-64"
                  />
                </div>

                <Select value={filters.category} onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>
                        {categoryIcons[category] || '🔍'} {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={filters.publishedStatus} onValueChange={(value) => setFilters(prev => ({ ...prev, publishedStatus: value }))}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={filters.photoStatus} onValueChange={(value) => setFilters(prev => ({ ...prev, photoStatus: value }))}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Photos" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Photos</SelectItem>
                    <SelectItem value="published">Published Photos</SelectItem>
                    <SelectItem value="unpublished">Unpublished Photos</SelectItem>
                  </SelectContent>
                </Select>

                <Input
                  placeholder="Filter by location..."
                  value={filters.location}
                  onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                  className="w-48"
                />
              </div>

              {/* View controls */}
              <div className="flex items-center gap-2">
                <Button onClick={loadData} variant="outline" size="sm" disabled={loading}>
                  <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                </Button>
                <Separator orientation="vertical" className="h-6" />
                <Button onClick={expandAll} variant="outline" size="sm">
                  Expand All
                </Button>
                <Button onClick={collapseAll} variant="outline" size="sm">
                  Collapse All
                </Button>
                <Button onClick={handleExportAll} variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-1" />
                  Export
                </Button>
              </div>
            </div>

            {/* Bulk actions bar */}
            {(viewState.selectedSpecies.size > 0 || viewState.selectedPhotos.size > 0) && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <span className="font-medium">
                      {viewState.selectedSpecies.size > 0 && `${viewState.selectedSpecies.size} species selected`}
                      {viewState.selectedSpecies.size > 0 && viewState.selectedPhotos.size > 0 && ', '}
                      {viewState.selectedPhotos.size > 0 && `${viewState.selectedPhotos.size} photos selected`}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    {viewState.selectedSpecies.size > 0 && (
                      <>
                        <Button onClick={() => handleBulkPublish(true)} size="sm" variant="default">
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Publish Species
                        </Button>
                        <Button onClick={() => handleBulkPublish(false)} size="sm" variant="outline">
                          <EyeOff className="w-4 h-4 mr-1" />
                          Unpublish Species
                        </Button>
                      </>
                    )}
                    {viewState.selectedPhotos.size > 0 && (
                      <>
                        <Button onClick={() => handleBulkPhotoPublish(true)} size="sm" variant="default">
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Publish Photos
                        </Button>
                        <Button onClick={() => handleBulkPhotoPublish(false)} size="sm" variant="outline">
                          <EyeOff className="w-4 h-4 mr-1" />
                          Unpublish Photos
                        </Button>
                        <Button onClick={handleBulkPhotoDelete} size="sm" variant="destructive">
                          <Trash2 className="w-4 h-4 mr-1" />
                          Delete Photos
                        </Button>
                      </>
                    )}
                    <Button
                      onClick={() => setViewState(prev => ({ ...prev, selectedSpecies: new Set(), selectedPhotos: new Set() }))}
                      size="sm"
                      variant="ghost"
                    >
                      <X className="w-4 h-4 mr-1" />
                      Clear Selection
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Main content area */}
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              {(filteredData?.species && Array.isArray(filteredData.species) ? filteredData.species : []).map(s => (
                <EnhancedSpeciesSection
                  key={s.species.id}
                  speciesWithPhotos={s}
                  onDropPhoto={handleDropPhoto}
                  isExpanded={expandedSpecies.has(s.species.id)}
                  onToggleExpand={() => toggleSpeciesExpansion(s.species.id)}
                  onEdit={handleEditSpecies}
                  onExport={handleExportSpecies}
                  onUploadPhotos={handleUploadPhotos}
                  onTogglePublish={handleTogglePublish}
                  isSelected={viewState.selectedSpecies.has(s.species.id)}
                  onSelect={handleSelectSpecies}
                  selectedPhotos={viewState.selectedPhotos}
                  onSelectPhoto={handleSelectPhoto}
                  onEditPhoto={handleEditPhoto}
                  isAdmin={!!isAdmin}
                  showPhotoDetails={true}
                />
              ))}

              {/* Unassigned photos section */}
              {viewState.showUnassigned && filteredData?.unassigned_photos && filteredData.unassigned_photos.length > 0 && (
                <div className="mt-8 p-4 rounded-lg bg-gray-50 border-2 border-dashed border-gray-300">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                      <AlertCircle className="w-5 h-5 text-orange-500" />
                      Unassigned Photos ({filteredData.unassigned_photos.length})
                    </h3>
                    <Button
                      onClick={() => setAiProcessingModalOpen(true)}
                      variant="outline"
                      size="sm"
                      className="gap-2"
                    >
                      <Brain className="w-4 h-4" />
                      AI Assign
                    </Button>
                  </div>
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-3">
                    {filteredData.unassigned_photos.map(photo => (
                      <EnhancedPhotoTile
                        key={photo.id}
                        photo={photo}
                        speciesId="unassigned"
                        isSelected={viewState.selectedPhotos.has(photo.id.toString())}
                        onSelect={handleSelectPhoto}
                        onEdit={handleEditPhoto}
                        showDetails={true}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Empty state */}
              {(!filteredData?.species || !Array.isArray(filteredData.species) || filteredData.species.length === 0) && (
                <div className="text-center py-12">
                  <ImageIcon className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No species found</h3>
                  <p className="text-gray-500 mb-4">
                    {searchTerm || Object.values(filters).some(f => f !== 'all' && f !== '')
                      ? 'Try adjusting your search or filters'
                      : 'Get started by creating your first species'
                    }
                  </p>
                  <Button onClick={() => {
                    setEditingSpecies(null);
                    setEditorMode('create');
                    setSpeciesEditorOpen(true);
                  }} className="gap-2">
                    <Plus className="w-4 h-4" />
                    Create First Species
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Modals */}

      {/* Photo Upload Modal */}
      <Dialog open={uploadModalOpen} onOpenChange={setUploadModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Upload Photos</DialogTitle>
          </DialogHeader>
          <EnhancedPhotoUpload
            preselectedSpeciesId={uploadSpeciesId}
            onUploadComplete={handleUploadComplete}
            onClose={() => setUploadModalOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* AI Processing Modal */}
      <Dialog open={aiProcessingModalOpen} onOpenChange={setAiProcessingModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Brain className="w-5 h-5" />
              AI Tools & Processing
            </DialogTitle>
          </DialogHeader>
          <Tabs defaultValue="upload" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="upload">Smart Upload</TabsTrigger>
              <TabsTrigger value="assign">Auto Assign</TabsTrigger>
              <TabsTrigger value="generate">Generate Species</TabsTrigger>
            </TabsList>

            <TabsContent value="upload" className="space-y-4">
              <EnhancedPhotoUpload
                onUploadComplete={(photoId) => {
                  console.log('AI upload complete:', photoId);
                  loadData();
                }}
                onClose={() => setAiProcessingModalOpen(false)}
              />
            </TabsContent>

            <TabsContent value="assign" className="space-y-4">
              <div className="space-y-6">
                <div className="text-center">
                  <Brain className="w-12 h-12 mx-auto mb-4 text-blue-500" />
                  <h3 className="text-lg font-medium mb-2">AI Photo Assignment</h3>
                  <p className="text-gray-500 mb-4">
                    Automatically assign unassigned photos to species using AI image recognition
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Quick Assignment</h4>
                    <p className="text-sm text-gray-500 mb-3">
                      Process all unassigned photos with high-confidence matches
                    </p>
                    <Button className="w-full gap-2">
                      <Sparkles className="w-4 h-4" />
                      Auto-Assign ({stats.unassignedPhotos} photos)
                    </Button>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Review Mode</h4>
                    <p className="text-sm text-gray-500 mb-3">
                      Generate suggestions for manual review and approval
                    </p>
                    <Button variant="outline" className="w-full gap-2">
                      <Eye className="w-4 h-4" />
                      Generate Suggestions
                    </Button>
                  </div>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium text-blue-800 mb-2">AI Assignment Settings</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center justify-between">
                      <span>Confidence Threshold:</span>
                      <Badge variant="outline">85%</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Auto-publish assigned photos:</span>
                      <Switch defaultChecked={false} />
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Create audit log:</span>
                      <Switch defaultChecked={true} />
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="generate" className="space-y-4">
              <div className="space-y-6">
                <div className="text-center">
                  <Bot className="w-12 h-12 mx-auto mb-4 text-green-500" />
                  <h3 className="text-lg font-medium mb-2">AI Species Generation</h3>
                  <p className="text-gray-500 mb-4">
                    Generate comprehensive species data using AI and scientific databases
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">From Photo Analysis</h4>
                    <p className="text-sm text-gray-500 mb-3">
                      Analyze unassigned photos to identify new species
                    </p>
                    <Button className="w-full gap-2">
                      <Camera className="w-4 h-4" />
                      Analyze Photos
                    </Button>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Manual Generation</h4>
                    <p className="text-sm text-gray-500 mb-3">
                      Create species from name or description
                    </p>
                    <Button
                      variant="outline"
                      className="w-full gap-2"
                      onClick={() => {
                        setAiProcessingModalOpen(false);
                        setEditingSpecies(null);
                        setEditorMode('create');
                        setSpeciesEditorOpen(true);
                      }}
                    >
                      <Plus className="w-4 h-4" />
                      Create Species
                    </Button>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Bulk Enhancement</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <Button variant="outline" size="sm" className="gap-2">
                      <FileText className="w-4 h-4" />
                      Enhance Descriptions
                    </Button>
                    <Button variant="outline" size="sm" className="gap-2">
                      <MapPin className="w-4 h-4" />
                      Update Habitats
                    </Button>
                    <Button variant="outline" size="sm" className="gap-2">
                      <BarChart3 className="w-4 h-4" />
                      Sync Conservation Status
                    </Button>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* Unified Species Editor */}
      <UnifiedSpeciesEditor
        species={editingSpecies}
        isOpen={speciesEditorOpen}
        onClose={() => {
          setSpeciesEditorOpen(false);
          setEditingSpecies(null);
        }}
        onSave={(savedSpecies) => {
          loadData();
          setSpeciesEditorOpen(false);
          setEditingSpecies(null);
        }}
        mode={editorMode}
        enableAI={true}
      />

      {/* Photo Edit Modal */}
      <PhotoEditModal
        photo={selectedPhoto}
        open={photoEditModalOpen}
        onOpenChange={setPhotoEditModalOpen}
        onSuccess={handlePhotoEditSuccess}
      />

      {/* Analytics Modal */}
      <Dialog open={analyticsModalOpen} onOpenChange={setAnalyticsModalOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Content Analytics & Insights
            </DialogTitle>
          </DialogHeader>
          <ContentAnalytics />
        </DialogContent>
      </Dialog>
    </DndProvider>
  );
}
