import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Shield, User, Eye, EyeOff } from 'lucide-react';
import { useAdminAuth } from '@/hooks/useAdminAuth';

export function AdminToggle() {
  const { isAdmin, adminUser, login, logout } = useAdminAuth();

  return (
    <Card className="fixed bottom-4 right-4 w-80 z-50 shadow-lg border-2">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Shield className="w-4 h-4" />
          Admin Status (Dev Mode)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Current Status:</span>
          <Badge variant={isAdmin ? "default" : "secondary"} className="flex items-center gap-1">
            {isAdmin ? <Shield className="w-3 h-3" /> : <User className="w-3 h-3" />}
            {isAdmin ? "Admin" : "Public User"}
          </Badge>
        </div>
        
        {isAdmin && adminUser && (
          <div className="text-xs text-gray-600">
            Logged in as: {adminUser.email}
          </div>
        )}
        
        <div className="flex gap-2">
          {isAdmin ? (
            <Button 
              onClick={logout} 
              variant="outline" 
              size="sm" 
              className="flex-1 text-xs"
            >
              <EyeOff className="w-3 h-3 mr-1" />
              View as Public
            </Button>
          ) : (
            <Button 
              onClick={login} 
              size="sm" 
              className="flex-1 text-xs"
            >
              <Eye className="w-3 h-3 mr-1" />
              View as Admin
            </Button>
          )}
        </div>
        
        <div className="text-xs text-gray-500 border-t pt-2">
          <strong>Admin Features:</strong> CMS Hub, Data Import, System Monitor
        </div>
      </CardContent>
    </Card>
  );
}
