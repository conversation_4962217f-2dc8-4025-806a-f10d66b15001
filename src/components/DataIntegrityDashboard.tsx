import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import { DataIntegrityService } from '@/utils/dataIntegrity';
import { 
  CheckCircle, 
  AlertTriangle, 
  RefreshCw, 
  Database, 
  Shield, 
  Zap,
  FileText,
  Camera,
  AlertCircle,
  Loader2,
  Settings
} from 'lucide-react';

interface IntegrityStatus {
  orphanedPhotos: string[];
  photoCountUpdates: { updated: number; errors: number };
  issues: string[];
  lastChecked?: Date;
}

export const DataIntegrityDashboard = () => {
  const [status, setStatus] = useState<IntegrityStatus>({
    orphanedPhotos: [],
    photoCountUpdates: { updated: 0, errors: 0 },
    issues: []
  });
  const [loading, setLoading] = useState(false);
  const [fixing, setFixing] = useState(false);

  useEffect(() => {
    performIntegrityCheck();
  }, []);

  const performIntegrityCheck = async () => {
    setLoading(true);
    try {
      const result = await DataIntegrityService.performIntegrityCheck();
      setStatus({
        ...result,
        lastChecked: new Date()
      });
      
      if (result.issues.length === 0) {
        toast.success('Data integrity check completed - no issues found');
      } else {
        toast.warning(`Found ${result.issues.length} data integrity issues`);
      }
    } catch (error) {
      console.error('Error performing integrity check:', error);
      toast.error('Failed to perform integrity check');
    } finally {
      setLoading(false);
    }
  };

  const fixOrphanedPhotos = async () => {
    if (status.orphanedPhotos.length === 0) {
      toast.info('No orphaned photos to fix');
      return;
    }

    setFixing(true);
    try {
      const success = await DataIntegrityService.fixOrphanedPhotos(status.orphanedPhotos);
      if (success) {
        toast.success(`Fixed ${status.orphanedPhotos.length} orphaned photos`);
        performIntegrityCheck(); // Refresh status
      } else {
        toast.error('Failed to fix orphaned photos');
      }
    } catch (error) {
      console.error('Error fixing orphaned photos:', error);
      toast.error('Failed to fix orphaned photos');
    } finally {
      setFixing(false);
    }
  };

  const recalculatePhotoCounts = async () => {
    setFixing(true);
    try {
      const result = await DataIntegrityService.recalculateAllPhotoCounts();
      if (result.errors === 0) {
        toast.success(`Updated photo counts for ${result.updated} species`);
      } else {
        toast.warning(`Updated ${result.updated} species, ${result.errors} errors`);
      }
      performIntegrityCheck(); // Refresh status
    } catch (error) {
      console.error('Error recalculating photo counts:', error);
      toast.error('Failed to recalculate photo counts');
    } finally {
      setFixing(false);
    }
  };

  const getHealthScore = (): number => {
    const totalIssues = status.orphanedPhotos.length + status.photoCountUpdates.errors + status.issues.length;
    if (totalIssues === 0) return 100;
    if (totalIssues <= 5) return 80;
    if (totalIssues <= 10) return 60;
    if (totalIssues <= 20) return 40;
    return 20;
  };

  const healthScore = getHealthScore();
  const isHealthy = healthScore >= 80;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Data Integrity Dashboard</h2>
          <p className="text-gray-600">Monitor and maintain database consistency</p>
        </div>
        <Button onClick={performIntegrityCheck} disabled={loading} variant="outline">
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Check Integrity
        </Button>
      </div>

      {/* Health Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Database Health Score
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-lg font-semibold">Overall Health</span>
              <Badge variant={isHealthy ? "default" : "destructive"}>
                {isHealthy ? <CheckCircle className="w-3 h-3 mr-1" /> : <AlertTriangle className="w-3 h-3 mr-1" />}
                {healthScore}%
              </Badge>
            </div>
            <Progress value={healthScore} className="h-3" />
            <div className="text-sm text-gray-600">
              {status.lastChecked && (
                <span>Last checked: {status.lastChecked.toLocaleString()}</span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Issues Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Camera className="w-5 h-5 text-orange-600" />
              <h3 className="font-semibold">Orphaned Photos</h3>
            </div>
            <div className="text-2xl font-bold">{status.orphanedPhotos.length}</div>
            <div className="text-sm text-gray-600">
              Photos without valid species
            </div>
            {status.orphanedPhotos.length > 0 && (
              <Button 
                size="sm" 
                className="mt-2 w-full" 
                onClick={fixOrphanedPhotos}
                disabled={fixing}
              >
                {fixing ? <Loader2 className="w-3 h-3 mr-1 animate-spin" /> : <Zap className="w-3 h-3 mr-1" />}
                Fix Orphaned Photos
              </Button>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <FileText className="w-5 h-5 text-blue-600" />
              <h3 className="font-semibold">Photo Count Errors</h3>
            </div>
            <div className="text-2xl font-bold">{status.photoCountUpdates.errors}</div>
            <div className="text-sm text-gray-600">
              Species with incorrect counts
            </div>
            <Button 
              size="sm" 
              className="mt-2 w-full" 
              onClick={recalculatePhotoCounts}
              disabled={fixing}
            >
              {fixing ? <Loader2 className="w-3 h-3 mr-1 animate-spin" /> : <RefreshCw className="w-3 h-3 mr-1" />}
              Recalculate Counts
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="w-5 h-5 text-red-600" />
              <h3 className="font-semibold">Total Issues</h3>
            </div>
            <div className="text-2xl font-bold">{status.issues.length}</div>
            <div className="text-sm text-gray-600">
              Data integrity problems
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Issues List */}
      {status.issues.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5" />
              Data Integrity Issues
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {status.issues.map((issue, index) => (
                <Alert key={index}>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{issue}</AlertDescription>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Maintenance Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Maintenance Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold">Automated Fixes</h4>
              <div className="space-y-2">
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={fixOrphanedPhotos}
                  disabled={fixing || status.orphanedPhotos.length === 0}
                >
                  <Zap className="w-4 h-4 mr-2" />
                  Fix Orphaned Photos ({status.orphanedPhotos.length})
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={recalculatePhotoCounts}
                  disabled={fixing}
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Recalculate All Photo Counts
                </Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-semibold">Manual Review</h4>
              <div className="space-y-2">
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={performIntegrityCheck}
                  disabled={loading}
                >
                  <Database className="w-4 h-4 mr-2" />
                  Full Integrity Check
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  disabled
                >
                  <FileText className="w-4 h-4 mr-2" />
                  Export Integrity Report
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Status Information */}
      <Card>
        <CardHeader>
          <CardTitle>System Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold mb-2">Database Tables</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• species_v2 (primary species table)</li>
                <li>• photos (photo metadata and assignments)</li>
                <li>• Foreign key: photos.species_id → species_v2.id</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Integrity Checks</h4>
              <ul className="space-y-1 text-gray-600">
                <li>• Orphaned photo detection</li>
                <li>• Photo count synchronization</li>
                <li>• Foreign key validation</li>
                <li>• Data format validation</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
