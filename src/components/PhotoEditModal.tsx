import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { useSpeciesList } from '@/hooks/useSpeciesData';
import { Photo } from '@/lib/api';
import { 
  Save, 
  X, 
  <PERSON>ader2, 
  Eye,
  EyeOff,
  MapPin,
  Calendar,
  Camera,
  Tag,
  User,
  FileText
} from 'lucide-react';



interface PhotoEditModalProps {
  photo: Photo | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

interface PhotoFormData {
  title: string;
  description: string;
  photographer: string;
  location: string;
  species_id: string;
  published: boolean;
  tags: string[];
  camera_settings: string;
  weather_conditions: string;
  time_of_day: string;
  notes: string;
  date_taken: string;
}

export function PhotoEditModal({ photo, open, onOpenChange, onSuccess }: PhotoEditModalProps) {
  const [formData, setFormData] = useState<PhotoFormData>({
    title: '',
    description: '',
    photographer: '',
    location: '',
    species_id: '',
    published: false,
    tags: [],
    camera_settings: '',
    weather_conditions: '',
    time_of_day: '',
    notes: '',
    date_taken: ''
  });
  const [isUpdating, setIsUpdating] = useState(false);
  const [newTag, setNewTag] = useState('');
  
  const { data: speciesData = [] } = useSpeciesList({
    published: true,
    limit: 1000 // Get all species for the dropdown
  });

  useEffect(() => {
    if (photo) {
      setFormData({
        title: photo.title || '',
        description: photo.description || '',
        photographer: photo.photographer || '',
        location: photo.location || '',
        species_id: photo.species_id || '',
        published: photo.published,
        tags: photo.tags || [],
        camera_settings: photo.camera_settings || '',
        weather_conditions: photo.weather_conditions || '',
        time_of_day: photo.time_of_day || '',
        notes: photo.notes || '',
        date_taken: photo.date_taken ? photo.date_taken.split('T')[0] : ''
      });
    }
  }, [photo]);

  const handleInputChange = (field: keyof PhotoFormData, value: string | boolean | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSubmit = async () => {
    if (!photo) return;

    setIsUpdating(true);

    try {
      const updateData = {
        title: formData.title.trim() || null,
        description: formData.description.trim() || null,
        photographer: formData.photographer.trim() || null,
        location: formData.location.trim() || null,
        species_id: formData.species_id || null,
        published: formData.published,
        tags: formData.tags.length > 0 ? formData.tags : null,
        camera_settings: formData.camera_settings.trim() || null,
        weather_conditions: formData.weather_conditions.trim() || null,
        time_of_day: formData.time_of_day.trim() || null,
        notes: formData.notes.trim() || null,
        date_taken: formData.date_taken || null,
        updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('photos_v2')
        .update(updateData)
        .eq('id', photo.id);

      if (error) throw error;

      toast.success('Photo updated successfully!');
      onSuccess();
      onOpenChange(false);
    } catch (error) {
      console.error('Error updating photo:', error);
      toast.error('Failed to update photo. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  if (!photo) return null;

  const selectedSpecies = speciesData?.find(s => s.id === formData.species_id);

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Camera className="w-5 h-5" />
            Edit Photo
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Photo preview */}
          <div className="space-y-4">
            <div className="aspect-square w-full bg-gray-100 rounded-lg overflow-hidden">
              <img
                src={photo.url}
                alt={photo.title || 'Photo'}
                className="w-full h-full object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = '/placeholder.svg';
                }}
              />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Badge variant={formData.published ? "default" : "secondary"}>
                  {formData.published ? "Published" : "Draft"}
                </Badge>
                {selectedSpecies && (
                  <Badge variant="outline">
                    {selectedSpecies.name}
                  </Badge>
                )}
              </div>
              
              <div className="text-sm text-gray-500">
                <p>Photo ID: {photo.id}</p>
                <p>Created: {new Date(photo.created_at).toLocaleDateString()}</p>
                <p>URL: <a href={photo.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">View original</a></p>
              </div>
            </div>
          </div>

          {/* Edit form */}
          <div className="space-y-4">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic">Basic</TabsTrigger>
                <TabsTrigger value="metadata">Metadata</TabsTrigger>
                <TabsTrigger value="technical">Technical</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Photo title..."
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Photo description..."
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="species">Species</Label>
                  <Select value={formData.species_id} onValueChange={(value) => handleInputChange('species_id', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select species" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Unassigned</SelectItem>
                      {(speciesData || []).map(species => (
                        <SelectItem key={species.id} value={species.id}>
                          {species.name} ({species.scientific_name})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="published"
                    checked={formData.published}
                    onCheckedChange={(checked) => handleInputChange('published', checked)}
                  />
                  <Label htmlFor="published">Published</Label>
                </div>
              </TabsContent>

              <TabsContent value="metadata" className="space-y-4">
                <div>
                  <Label htmlFor="photographer">Photographer</Label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      id="photographer"
                      value={formData.photographer}
                      onChange={(e) => handleInputChange('photographer', e.target.value)}
                      placeholder="Photographer name..."
                      className="pl-10"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="location">Location</Label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      id="location"
                      value={formData.location}
                      onChange={(e) => handleInputChange('location', e.target.value)}
                      placeholder="Photo location..."
                      className="pl-10"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="date_taken">Date Taken</Label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      id="date_taken"
                      type="date"
                      value={formData.date_taken}
                      onChange={(e) => handleInputChange('date_taken', e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div>
                  <Label>Tags</Label>
                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <Input
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        placeholder="Add tag..."
                        onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                      />
                      <Button onClick={handleAddTag} variant="outline" size="sm">
                        <Tag className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {formData.tags.map(tag => (
                        <Badge key={tag} variant="secondary" className="cursor-pointer" onClick={() => handleRemoveTag(tag)}>
                          {tag} <X className="w-3 h-3 ml-1" />
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="technical" className="space-y-4">
                <div>
                  <Label htmlFor="camera_settings">Camera Settings</Label>
                  <Input
                    id="camera_settings"
                    value={formData.camera_settings}
                    onChange={(e) => handleInputChange('camera_settings', e.target.value)}
                    placeholder="e.g., f/2.8, 1/500s, ISO 400"
                  />
                </div>

                <div>
                  <Label htmlFor="weather_conditions">Weather Conditions</Label>
                  <Input
                    id="weather_conditions"
                    value={formData.weather_conditions}
                    onChange={(e) => handleInputChange('weather_conditions', e.target.value)}
                    placeholder="e.g., Sunny, Overcast, Rainy"
                  />
                </div>

                <div>
                  <Label htmlFor="time_of_day">Time of Day</Label>
                  <Input
                    id="time_of_day"
                    value={formData.time_of_day}
                    onChange={(e) => handleInputChange('time_of_day', e.target.value)}
                    placeholder="e.g., Morning, Afternoon, Evening"
                  />
                </div>

                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    placeholder="Additional notes about the photo..."
                    rows={3}
                  />
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>

        <DialogFooter>
          <Button onClick={handleClose} variant="outline">
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isUpdating}
            className="gap-2"
          >
            {isUpdating ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Updating...
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                Save Changes
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
