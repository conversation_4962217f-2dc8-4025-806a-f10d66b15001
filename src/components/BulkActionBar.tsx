import React, { useState } from 'react';
import { PhotoReviewItem } from '@/hooks/usePhotoReviewData';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';

interface BulkActionBarProps {
  selectedPhotos: PhotoReviewItem[];
  onAccept: () => void;
  onReject: () => void;
  loading?: boolean;
}

export const BulkActionBar: React.FC<BulkActionBarProps> = ({ selectedPhotos, onAccept, onReject, loading }) => {
  const [confirmType, setConfirmType] = useState<'accept' | 'reject' | null>(null);
  const canAccept = selectedPhotos.length > 0 && selectedPhotos.every(p => p.ai_suggested_id);

  return (
    <div className="fixed bottom-0 left-0 w-full bg-white border-t shadow-lg z-50 py-4 px-6 flex items-center gap-4 justify-center">
      <span className="font-semibold">Selected: {selectedPhotos.length}</span>
      <Button 
        variant="default" 
        disabled={!canAccept || loading}
        onClick={() => setConfirmType('accept')}
      >
        ✅ Accept AI Suggestions
      </Button>
      <Button 
        variant="destructive" 
        disabled={selectedPhotos.length === 0 || loading}
        onClick={() => setConfirmType('reject')}
      >
        ❌ Reject
      </Button>
      {/* Confirm Dialog */}
      <Dialog open={!!confirmType} onOpenChange={open => !open && setConfirmType(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {confirmType === 'accept' ? `Accept ${selectedPhotos.length} AI suggestion(s)?` : `Reject ${selectedPhotos.length} photo(s)?`}
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            {confirmType === 'accept' ? (
              <span>This will assign the AI-suggested species and mark as reviewed.</span>
            ) : (
              <span>This will clear the suggestion, mark as not reviewed, and log an override.</span>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmType(null)} disabled={loading}>Cancel</Button>
            <Button 
              variant={confirmType === 'accept' ? 'default' : 'destructive'}
              onClick={() => {
                setConfirmType(null);
                if (confirmType === 'accept') onAccept();
                else onReject();
              }}
              disabled={loading || (confirmType === 'accept' && !canAccept)}
            >
              Confirm
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}; 