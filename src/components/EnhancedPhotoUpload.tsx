import React, { useState, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { supabase } from '@/integrations/supabase/client';
import { getServiceRoleClient } from '@/lib/supabaseServiceRole';
import { useSpeciesList } from '@/hooks/useSpeciesData';
import { toast } from 'sonner';
import {
  Upload,
  X,
  Brain,
  CheckCircle,
  AlertCircle,
  Eye,
  EyeOff,
  Image as ImageIcon,
  Sparkles,
  Clock,
  Camera,
  Search,
  Check,
  ChevronsUpDown
} from 'lucide-react';

interface PhotoUploadData {
  title: string;
  description: string;
  photographer: string;
  location: string;
  species_id: string | null;
  camera_settings: string;
  weather_conditions: string;
  time_of_day: string;
  tags: string[];
}

interface UploadProgress {
  progress: number;
  status: 'uploading' | 'processing' | 'ai-generating' | 'complete' | 'error';
  message: string;
}

interface EnhancedPhotoUploadProps {
  onUploadComplete?: (photoIds: number[]) => void;
  onClose?: () => void;
  preselectedSpeciesId?: string;
}

export const EnhancedPhotoUpload: React.FC<EnhancedPhotoUploadProps> = ({
  onUploadComplete,
  onClose,
  preselectedSpeciesId
}) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, UploadProgress>>({});
  const [isUploading, setIsUploading] = useState(false);
  const [useAI, setUseAI] = useState(true);
  const [metadata, setMetadata] = useState<PhotoUploadData>({
    title: '',
    description: '',
    photographer: '',
    location: '',
    species_id: preselectedSpeciesId || null,
    camera_settings: '',
    weather_conditions: 'not-specified',
    time_of_day: 'not-specified',
    tags: []
  });
  const [newTag, setNewTag] = useState('');
  const [speciesSearchOpen, setSpeciesSearchOpen] = useState(false);
  const [speciesSearchValue, setSpeciesSearchValue] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { data: speciesData = [], isLoading: speciesLoading, error: speciesError } = useSpeciesList({
    published: false, // Include unpublished species for admin use
    limit: 1000 // Load many more species for the dropdown
  });



  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const validFiles: File[] = [];

    files.forEach(file => {
      if (validateFile(file)) {
        validFiles.push(file);
      }
    });

    setSelectedFiles(prev => [...prev, ...validFiles]);
  };

  const validateFile = (file: File): boolean => {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

    if (!allowedTypes.includes(file.type)) {
      toast.error(`${file.name}: Invalid file type. Please upload JPG, PNG, or WebP images only.`);
      return false;
    }

    if (file.size > maxSize) {
      toast.error(`${file.name}: File size too large. Maximum size is 5MB.`);
      return false;
    }

    return true;
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const addTag = () => {
    if (newTag.trim() && !metadata.tags.includes(newTag.trim())) {
      setMetadata(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setMetadata(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const generateAICaption = async (file: File, speciesId: string | null): Promise<Partial<PhotoUploadData>> => {
    try {
      const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
      if (!apiKey) {
        throw new Error('Gemini API key not configured');
      }

      // Get species information if provided
      let speciesInfo = '';
      if (speciesId) {
        const species = speciesData.find(s => s.id === speciesId);
        if (species) {
          speciesInfo = `This photo is of a ${species.name} (${species.scientific_name}). ${species.description || ''}`;
        }
      }

      // Convert image to base64 for Gemini Vision API
      const base64Image = await fileToBase64(file);
      
      const prompt = `Analyze this wildlife photo and generate appropriate metadata. ${speciesInfo}

Please provide:
1. A descriptive title (max 100 characters)
2. A detailed description of what you see in the photo (max 500 characters)
3. Suggested tags (comma-separated, max 10 tags)
4. Time of day if visible (morning/afternoon/evening/night)
5. Weather conditions if visible (sunny/cloudy/rainy/etc)

Format your response as JSON:
{
  "title": "...",
  "description": "...", 
  "tags": ["tag1", "tag2", "tag3"],
  "time_of_day": "...",
  "weather_conditions": "..."
}`;

      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contents: [{
            parts: [
              { text: prompt },
              { 
                inline_data: {
                  mime_type: file.type,
                  data: base64Image.split(',')[1] // Remove data:image/jpeg;base64, prefix
                }
              }
            ]
          }]
        })
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.statusText}`);
      }

      const result = await response.json();
      const aiText = result.candidates?.[0]?.content?.parts?.[0]?.text;
      
      if (!aiText) {
        throw new Error('No response from AI');
      }

      // Parse JSON response
      const jsonMatch = aiText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('Could not parse AI response');
      }

      const aiData = JSON.parse(jsonMatch[0]);
      
      return {
        title: aiData.title || '',
        description: aiData.description || '',
        tags: Array.isArray(aiData.tags) ? aiData.tags : [],
        time_of_day: aiData.time_of_day || '',
        weather_conditions: aiData.weather_conditions || ''
      };

    } catch (error) {
      console.error('AI caption generation error:', error);
      toast.error('AI caption generation failed, using manual input');
      return {};
    }
  };

  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  };

  const uploadPhoto = async (file: File, photoMetadata: PhotoUploadData): Promise<{ success: boolean; photoId?: number; error?: string }> => {
    try {
      // Use singleton service role client for uploads
      // This bypasses RLS policies for photo uploads
      const serviceRoleClient = getServiceRoleClient();

      // Generate unique filename
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 15);
      const extension = file.name.split('.').pop();
      const filename = `${timestamp}_${randomString}.${extension}`;

      // Upload to Supabase Storage using service role client
      const { data: uploadData, error: uploadError } = await serviceRoleClient.storage
        .from('species')
        .upload(filename, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        throw new Error(`Upload failed: ${uploadError.message}`);
      }

      // Get public URL
      const { data: urlData } = serviceRoleClient.storage
        .from('species')
        .getPublicUrl(filename);

      // Save to photos_v2 table
      const photoRecord = {
        url: urlData.publicUrl,
        title: photoMetadata.title,
        description: photoMetadata.description,
        photographer: photoMetadata.photographer,
        location: photoMetadata.location,
        species_id: photoMetadata.species_id,
        camera_settings: photoMetadata.camera_settings,
        weather_conditions: photoMetadata.weather_conditions === 'not-specified' ? '' : photoMetadata.weather_conditions,
        time_of_day: photoMetadata.time_of_day === 'not-specified' ? '' : photoMetadata.time_of_day,
        tags: photoMetadata.tags || [],
        published: true,
        notes: `Enhanced upload: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`,
        ai_generated_metadata: useAI,
        needs_review: false,
        ai_reviewed: true
      };

      const { data: dbData, error: dbError } = await serviceRoleClient
        .from('photos_v2')
        .insert(photoRecord)
        .select()
        .single();

      if (dbError) {
        // Clean up uploaded file
        await serviceRoleClient.storage.from('species').remove([filename]);
        throw new Error(`Database error: ${dbError.message}`);
      }

      return { success: true, photoId: dbData.id };

    } catch (error) {
      console.error('Upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown upload error'
      };
    }
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) {
      toast.error('Please select at least one photo');
      return;
    }

    if (!useAI && !metadata.title.trim()) {
      toast.error('Please provide a title or enable AI caption generation');
      return;
    }

    setIsUploading(true);
    const uploadedIds: number[] = [];

    try {
      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        const fileKey = `${file.name}-${i}`;
        
        // Update progress
        setUploadProgress(prev => ({
          ...prev,
          [fileKey]: { progress: 10, status: 'uploading', message: 'Starting upload...' }
        }));

        let fileMetadata = { ...metadata };

        // Generate AI captions if enabled
        if (useAI) {
          setUploadProgress(prev => ({
            ...prev,
            [fileKey]: { progress: 30, status: 'ai-generating', message: 'Generating AI captions...' }
          }));

          const aiData = await generateAICaption(file, metadata.species_id);
          
          // Merge AI data with user data (AI generates title if not provided)
          fileMetadata = {
            ...fileMetadata,
            title: aiData.title || metadata.title || `Photo ${i + 1}`,
            description: aiData.description || metadata.description || '',
            tags: aiData.tags && aiData.tags.length > 0 ? aiData.tags : metadata.tags,
            time_of_day: aiData.time_of_day || ((metadata.time_of_day && metadata.time_of_day !== 'not-specified') ? metadata.time_of_day : ''),
            weather_conditions: aiData.weather_conditions || ((metadata.weather_conditions && metadata.weather_conditions !== 'not-specified') ? metadata.weather_conditions : '')
          };
        }

        // Add file number to title if multiple files
        if (selectedFiles.length > 1 && !fileMetadata.title.includes(`(${i + 1}/`)) {
          fileMetadata.title = `${fileMetadata.title} (${i + 1}/${selectedFiles.length})`;
        }

        setUploadProgress(prev => ({
          ...prev,
          [fileKey]: { progress: 60, status: 'uploading', message: 'Uploading to storage...' }
        }));

        const result = await uploadPhoto(file, fileMetadata);

        if (result.success && result.photoId) {
          uploadedIds.push(result.photoId);
          setUploadProgress(prev => ({
            ...prev,
            [fileKey]: { progress: 100, status: 'complete', message: 'Upload complete!' }
          }));
          toast.success(`Successfully uploaded ${file.name}`);
        } else {
          setUploadProgress(prev => ({
            ...prev,
            [fileKey]: { progress: 0, status: 'error', message: result.error || 'Upload failed' }
          }));
          toast.error(`Failed to upload ${file.name}: ${result.error}`);
        }
      }

      if (uploadedIds.length > 0) {
        toast.success(`Successfully uploaded ${uploadedIds.length} photo(s)`);
        
        // Reset form
        setSelectedFiles([]);
        setMetadata({
          title: '',
          description: '',
          photographer: '',
          location: '',
          species_id: preselectedSpeciesId || null,
          camera_settings: '',
          weather_conditions: 'not-specified',
          time_of_day: 'not-specified',
          tags: []
        });
        setUploadProgress({});

        // Notify parent component
        onUploadComplete?.(uploadedIds);
      }

    } catch (error) {
      console.error('Upload process error:', error);
      toast.error('Upload process failed');
    } finally {
      setIsUploading(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    return (bytes / 1024 / 1024).toFixed(2) + ' MB';
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Camera className="w-5 h-5" />
          Enhanced Photo Upload
        </CardTitle>
        {onClose && (
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        )}
      </CardHeader>
      <CardContent className="space-y-6">
        {/* AI Toggle */}
        <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded-lg">
          <Checkbox
            id="use-ai"
            checked={useAI}
            onCheckedChange={setUseAI}
          />
          <Label htmlFor="use-ai" className="flex items-center gap-2">
            <Brain className="w-4 h-4" />
            Use AI to generate captions and metadata
          </Label>
        </div>

        {/* File Selection */}
        <div>
          <Label>Select Photos</Label>
          <div 
            className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer"
            onClick={() => fileInputRef.current?.click()}
          >
            <ImageIcon className="w-12 h-12 mx-auto mb-4 text-gray-400" />
            <p className="text-lg font-medium text-gray-700 mb-2">
              Click to select photos or drag and drop
            </p>
            <p className="text-sm text-gray-500">
              JPG, PNG, WebP up to 5MB each
            </p>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/jpeg,image/jpg,image/png,image/webp"
              onChange={handleFileSelect}
              className="hidden"
            />
          </div>
        </div>

        {/* Selected Files */}
        {selectedFiles.length > 0 && (
          <div>
            <Label>Selected Files ({selectedFiles.length})</Label>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {selectedFiles.map((file, index) => {
                const fileKey = `${file.name}-${index}`;
                const progress = uploadProgress[fileKey];
                
                return (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <div className="flex items-center gap-2">
                      <ImageIcon className="w-4 h-4" />
                      <span className="text-sm font-medium">{file.name}</span>
                      <span className="text-xs text-gray-500">({formatFileSize(file.size)})</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {progress && (
                        <div className="flex items-center gap-2">
                          {progress.status === 'complete' && <CheckCircle className="w-4 h-4 text-green-500" />}
                          {progress.status === 'error' && <AlertCircle className="w-4 h-4 text-red-500" />}
                          {progress.status === 'ai-generating' && <Brain className="w-4 h-4 text-blue-500 animate-pulse" />}
                          {progress.status === 'uploading' && <Upload className="w-4 h-4 text-blue-500" />}
                          <span className="text-xs">{progress.progress}%</span>
                        </div>
                      )}
                      {!isUploading && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(index)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Species Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label>Species Assignment</Label>
            <div className="relative">
              <Input
                placeholder="Search species..."
                value={speciesSearchValue}
                onChange={(e) => setSpeciesSearchValue(e.target.value)}
                onFocus={() => setSpeciesSearchOpen(true)}
                className="w-full"
                disabled={speciesLoading}
              />
              {speciesSearchOpen && (
                <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-64 overflow-auto">
                  <div
                    className="p-2 hover:bg-gray-100 cursor-pointer border-b"
                    onClick={() => {
                      setMetadata(prev => ({ ...prev, species_id: null }));
                      setSpeciesSearchOpen(false);
                      setSpeciesSearchValue('');
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-gray-400 rounded-full" />
                      <span>Unassigned (assign later)</span>
                      {!metadata.species_id && <Check className="ml-auto h-4 w-4" />}
                    </div>
                  </div>
                  {speciesData && speciesData.length > 0 ? (
                    speciesData
                      .filter(species =>
                        !speciesSearchValue ||
                        species.name.toLowerCase().includes(speciesSearchValue.toLowerCase()) ||
                        (species.scientific_name && species.scientific_name.toLowerCase().includes(speciesSearchValue.toLowerCase()))
                      )
                      .map((species) => (
                        <div
                          key={species.id}
                          className="p-2 hover:bg-gray-100 cursor-pointer"
                          onClick={() => {
                            setMetadata(prev => ({ ...prev, species_id: species.id }));
                            setSpeciesSearchOpen(false);
                            setSpeciesSearchValue('');
                          }}
                        >
                          <div className="flex items-center gap-2">
                            <div className={`w-2 h-2 rounded-full ${species.published ? 'bg-green-500' : 'bg-yellow-500'}`} />
                            <span>{species.name} ({species.scientific_name || 'No scientific name'})</span>
                            {metadata.species_id === species.id && <Check className="ml-auto h-4 w-4" />}
                          </div>
                        </div>
                      ))
                  ) : !speciesLoading && speciesSearchValue && (
                    <div className="p-2 text-gray-500">
                      No species found
                    </div>
                  )}
                </div>
              )}
              {/* Click outside to close */}
              {speciesSearchOpen && (
                <div
                  className="fixed inset-0 z-40"
                  onClick={() => setSpeciesSearchOpen(false)}
                />
              )}
            </div>
            {metadata.species_id && (
              <div className="mt-2 p-2 bg-gray-50 rounded-md">
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    speciesData?.find(s => s.id === metadata.species_id)?.published ? 'bg-green-500' : 'bg-yellow-500'
                  }`} />
                  <span className="text-sm font-medium">
                    {speciesData?.find(s => s.id === metadata.species_id)?.name || 'Unknown species'}
                  </span>
                </div>
              </div>
            )}
            <p className="text-xs text-gray-500 mt-1">
              {metadata.species_id ? 'AI will generate captions based on this species' : 'Photos will be uploaded as unassigned for later assignment'}
            </p>
          </div>

          <div>
            <Label htmlFor="photographer">Photographer</Label>
            <Input
              id="photographer"
              value={metadata.photographer}
              onChange={(e) => setMetadata(prev => ({ ...prev, photographer: e.target.value }))}
              placeholder="Photographer name"
            />
          </div>
        </div>

        {/* Basic Metadata */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              value={metadata.title}
              onChange={(e) => setMetadata(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Photo title"
              required
            />
          </div>

          <div>
            <Label htmlFor="location">Location</Label>
            <Input
              id="location"
              value={metadata.location}
              onChange={(e) => setMetadata(prev => ({ ...prev, location: e.target.value }))}
              placeholder="Where was this photo taken?"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={metadata.description}
            onChange={(e) => setMetadata(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Describe what you see in the photo..."
            rows={3}
          />
        </div>

        {/* Technical Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="time_of_day">Time of Day</Label>
            <Select
              value={metadata.time_of_day}
              onValueChange={(value) => setMetadata(prev => ({ ...prev, time_of_day: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select time" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="not-specified">Not specified</SelectItem>
                <SelectItem value="dawn">Dawn</SelectItem>
                <SelectItem value="morning">Morning</SelectItem>
                <SelectItem value="midday">Midday</SelectItem>
                <SelectItem value="afternoon">Afternoon</SelectItem>
                <SelectItem value="evening">Evening</SelectItem>
                <SelectItem value="dusk">Dusk</SelectItem>
                <SelectItem value="night">Night</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="weather_conditions">Weather</Label>
            <Select
              value={metadata.weather_conditions}
              onValueChange={(value) => setMetadata(prev => ({ ...prev, weather_conditions: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select weather" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="not-specified">Not specified</SelectItem>
                <SelectItem value="sunny">Sunny</SelectItem>
                <SelectItem value="partly cloudy">Partly Cloudy</SelectItem>
                <SelectItem value="cloudy">Cloudy</SelectItem>
                <SelectItem value="overcast">Overcast</SelectItem>
                <SelectItem value="rainy">Rainy</SelectItem>
                <SelectItem value="stormy">Stormy</SelectItem>
                <SelectItem value="foggy">Foggy</SelectItem>
                <SelectItem value="snowy">Snowy</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="camera_settings">Camera Settings</Label>
            <Input
              id="camera_settings"
              value={metadata.camera_settings}
              onChange={(e) => setMetadata(prev => ({ ...prev, camera_settings: e.target.value }))}
              placeholder="e.g., f/2.8, 1/500s, ISO 400"
            />
          </div>
        </div>

        {/* Tags */}
        <div>
          <Label>Tags</Label>
          <div className="flex flex-wrap gap-2 mb-2">
            {metadata.tags.map((tag, index) => (
              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                {tag}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-transparent"
                  onClick={() => removeTag(tag)}
                >
                  <X className="w-3 h-3" />
                </Button>
              </Badge>
            ))}
          </div>
          <div className="flex gap-2">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Add a tag..."
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
            />
            <Button type="button" onClick={addTag} variant="outline">
              Add
            </Button>
          </div>
        </div>

        {/* Upload Progress */}
        {isUploading && (
          <div className="space-y-2">
            <Label>Upload Progress</Label>
            {Object.entries(uploadProgress).map(([fileKey, progress]) => (
              <div key={fileKey} className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>{fileKey.split('-')[0]}</span>
                  <span className="flex items-center gap-1">
                    {progress.status === 'ai-generating' && <Brain className="w-3 h-3 text-blue-500" />}
                    {progress.status === 'uploading' && <Upload className="w-3 h-3 text-blue-500" />}
                    {progress.status === 'complete' && <CheckCircle className="w-3 h-3 text-green-500" />}
                    {progress.status === 'error' && <AlertCircle className="w-3 h-3 text-red-500" />}
                    {progress.message}
                  </span>
                </div>
                <Progress value={progress.progress} className="h-2" />
              </div>
            ))}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            {useAI && (
              <div className="flex items-center gap-1">
                <Sparkles className="w-4 h-4 text-blue-500" />
                AI captions enabled
              </div>
            )}
          </div>
          <div className="flex gap-2">
            {onClose && (
              <Button variant="outline" onClick={onClose} disabled={isUploading}>
                Cancel
              </Button>
            )}
            <Button
              onClick={handleUpload}
              disabled={selectedFiles.length === 0 || !metadata.title.trim() || isUploading}
              className="flex items-center gap-2"
            >
              {isUploading ? (
                <>
                  <Clock className="w-4 h-4 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4" />
                  Upload {selectedFiles.length} Photo{selectedFiles.length !== 1 ? 's' : ''}
                </>
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
