import React from 'react';
import { useDrag } from 'react-dnd';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { ImageIcon, Tag, Check, AlertTriangle } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Photo } from '@/lib/api';

interface Species {
  id: string;
  name: string;
  common_name: string | null;
  category: string | null;
}

interface DraggablePhotoProps {
  photo: Photo;
  species: Species[];
  onPhotoUpdate?: () => void;
  isSelected?: boolean;
  onSelectionChange?: (photoId: number, selected: boolean) => void;
  isMultiSelectMode?: boolean;
  selectedPhotoIds?: number[];
  onDragStart?: (photoId: number) => void;
  onDragEnd?: () => void;
}

export function DraggablePhoto({ 
  photo, 
  species,
  onPhotoUpdate,
  isSelected = false,
  onSelectionChange,
  isMultiSelectMode = false,
  selectedPhotoIds = [],
  onDragStart,
  onDragEnd
}: DraggablePhotoProps) {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'PHOTO',
    item: () => {
      onDragStart?.(photo.id);
      return {
        photoId: photo.id,
        currentSpeciesId: photo.species_id,
        photoTitle: photo.title || 'Untitled Photo',
        selectedPhotoIds: isMultiSelectMode && selectedPhotoIds.length > 0 ? selectedPhotoIds : undefined
      };
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    end: () => {
      onDragEnd?.();
    },
  }));

  const getSpeciesDisplayName = () => {
    if (photo.species) {
      return photo.species.common_name || photo.species.name;
    }
    if (photo.species_id) {
      const foundSpecies = species.find(s => s.id === photo.species_id);
      if (foundSpecies) {
        return foundSpecies.common_name || foundSpecies.name;
      }
    }
    return 'Unassigned';
  };

  const handleSelectionChange = (checked: boolean) => {
    onSelectionChange?.(photo.id, checked);
  };

  return (
    <TooltipProvider>
      <div
        ref={drag}
        className={`relative p-2 border rounded-lg shadow-sm bg-white ${isDragging ? 'opacity-50' : 'opacity-100'} cursor-grab`}
      >
        {photo.needs_recovery && (
          <Tooltip>
            <TooltipTrigger className="absolute top-1 right-1 z-10">
              <AlertTriangle className="w-5 h-5 text-yellow-500" />
            </TooltipTrigger>
            <TooltipContent>
              <p>This photo may have a broken URL and needs recovery.</p>
            </TooltipContent>
          </Tooltip>
        )}
        <div className="relative">
          <img
            src={photo.url || '/placeholder.svg'}
            alt={photo.title || 'Wildlife photo'}
            className="w-full h-24 object-cover rounded-md"
            onError={(e) => {
              console.error("Image failed to load", photo.url);
              (e.target as HTMLImageElement).src = '/placeholder.svg';
            }}
          />
          {!photo.url && (
            <span className="text-xs text-red-500 absolute top-1 left-1 bg-white p-1 rounded">Missing URL</span>
          )}
          
          {/* Status Badge */}
          <div className="absolute top-1 right-1 flex gap-1 items-center">
            <Badge variant={photo.published ? "default" : "destructive"} className="text-xs px-1.5 py-0.5">
              {photo.published ? "Published" : "Unavailable"}
            </Badge>
            {/* Admin relink/recover button placeholder */}
            {!photo.published && (
              <span className="text-xs text-red-600 cursor-pointer ml-1" title="Admin: Relink or Recover">🛠️</span>
            )}
          </div>
        </div>

        {/* Photo Info */}
        <div className="space-y-1">
          <h4 className="font-medium text-xs truncate" title={photo.title || 'Untitled Photo'}>
            {photo.title || 'Untitled Photo'}
          </h4>
          
          {/* Species Assignment */}
          <div className="flex items-center gap-1" title={`Species: ${getSpeciesDisplayName()}`}>
            <Tag className="w-3 h-3 text-muted-foreground" />
            <span className="text-xs text-muted-foreground truncate">
              {getSpeciesDisplayName()}
            </span>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
} 