/**
 * @deprecated This component is deprecated. Use MultiSelectFilter directly instead:
 * 
 * ```tsx
 * <MultiSelectFilter
 *   options={statuses}
 *   selected={selectedStatuses}
 *   onChange={onStatusChange}
 *   label="Filter by Conservation Status"
 * />
 * ```
 * 
 * This wrapper is maintained for backward compatibility.
 */

import { MultiSelectFilter } from './MultiSelectFilter';

interface ConservationStatusFilterProps {
  statuses: string[];
  selectedStatuses: string[];
  onStatusChange: (statuses: string[]) => void;
}

export const ConservationStatusFilter = ({ 
  statuses, 
  selectedStatuses, 
  onStatusChange 
}: ConservationStatusFilterProps) => {
  return (
    <MultiSelectFilter
      options={statuses}
      selected={selectedStatuses}
      onChange={onStatusChange}
      label="Filter by Conservation Status"
      colorScheme="default" // Uses automatic conservation status color coding
    />
  );
};
