import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { DataIntegrityService } from '@/utils/dataIntegrity';
import { useDebouncedCallback, useMemoizedFilter } from '@/utils/performance';
import { 
  Upload, 
  Edit, 
  Trash2, 
  Eye, 
  Search, 
  Filter,
  Grid3X3,
  List,
  Plus,
  Save,
  X,
  Camera,
  MapPin,
  User,
  Calendar,
  Tag,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface Photo {
  id: number;
  title?: string;
  url: string;
  description?: string;
  photographer?: string;
  location?: string;
  species_id?: string;
  published: boolean;
  tags?: string[];
  camera_settings?: string;
  weather_conditions?: string;
  time_of_day?: string;
  notes?: string;
  created_at: string;
  species?: {
    id: string;
    name: string;
  };
}

interface Species {
  id: string;
  name: string;
  scientific_name?: string;
}

interface EnhancedPhotoManagerProps {
  speciesId?: string; // If provided, shows photos for specific species
  onPhotoUpdate?: () => void;
}

export const EnhancedPhotoManager = ({ speciesId, onPhotoUpdate }: EnhancedPhotoManagerProps) => {
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [species, setSpecies] = useState<Species[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'published' | 'unpublished'>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedPhotos, setSelectedPhotos] = useState<number[]>([]);
  const [editingPhoto, setEditingPhoto] = useState<Photo | null>(null);
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  const ITEMS_PER_PAGE = 20; // Pagination for better performance

  // Debounced search to avoid excessive API calls
  const debouncedSearch = useDebouncedCallback((term: string) => {
    // Only reload data if search term changes significantly
    if (term.length === 0 || term.length >= 3) {
      loadData(true);
    }
  }, 500);

  useEffect(() => {
    loadData();
  }, [speciesId]); // Only depend on speciesId, not other state

  useEffect(() => {
    debouncedSearch(searchTerm);
  }, [searchTerm, debouncedSearch]);

  const loadData = async (resetPage = true) => {
    setLoading(true);
    try {
      const currentPage = resetPage ? 1 : page;
      const offset = (currentPage - 1) * ITEMS_PER_PAGE;

      // Load photos with pagination and only essential fields
      let photosQuery = supabase
        .from('photos_v2')
        .select(`
          id,
          title,
          url,
          published,
          species_id,
          created_at,
          photographer,
          location,
          species:species_v2(id, name)
        `, { count: 'exact' })
        .order('created_at', { ascending: false })
        .range(offset, offset + ITEMS_PER_PAGE - 1);

      if (speciesId) {
        photosQuery = photosQuery.eq('species_id', speciesId);
      }

      const { data: photosData, error: photosError, count } = await photosQuery;
      if (photosError) throw photosError;

      // Load species only once (cache them)
      if (species.length === 0) {
        const { data: speciesData, error: speciesError } = await supabase
          .from('species_v2')
          .select('id, name, scientific_name')
          .eq('published', true)
          .order('name')
          .limit(500); // Increased limit to load more species
        if (speciesError) throw speciesError;
        setSpecies(speciesData || []);
      }

      if (resetPage) {
        setPhotos(photosData || []);
        setPage(1);
      } else {
        setPhotos(prev => [...prev, ...(photosData || [])]);
      }

      setTotalCount(count || 0);
      setHasMore((photosData?.length || 0) === ITEMS_PER_PAGE);
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load photos');
    } finally {
      setLoading(false);
    }
  };

  // Use memoized filtering for better performance
  const filteredPhotos = useMemoizedFilter(
    photos,
    (photo) => {
      const matchesSearch = !searchTerm ||
        photo.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        photo.photographer?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        photo.location?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        photo.species?.name?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = filterStatus === 'all' ||
        (filterStatus === 'published' && photo.published) ||
        (filterStatus === 'unpublished' && !photo.published);

      return matchesSearch && matchesStatus;
    },
    [searchTerm, filterStatus]
  );

  const handlePhotoUpdate = async (photoId: number, updates: Partial<Photo>) => {
    try {
      // Validate photo data if URL is being updated
      if (updates.url) {
        const validation = DataIntegrityService.validatePhoto({
          url: updates.url,
          title: updates.title,
          species_id: updates.species_id,
          published: updates.published
        });

        if (!validation.isValid) {
          toast.error(`Validation failed: ${validation.errors.join(', ')}`);
          return;
        }

        if (validation.warnings.length > 0) {
          validation.warnings.forEach(warning => toast.warning(warning));
        }
      }

      // Validate species exists if assigning to species
      if (updates.species_id) {
        const speciesExists = await DataIntegrityService.validateSpeciesExists(updates.species_id);
        if (!speciesExists) {
          toast.error('Selected species does not exist');
          return;
        }
      }

      const { error } = await supabase
        .from('photos')
        .update(updates)
        .eq('id', photoId);

      if (error) throw error;

      // Update photo count if species assignment changed
      const currentPhoto = photos.find(p => p.id === photoId);
      if (currentPhoto && (updates.species_id !== undefined || updates.published !== undefined)) {
        // Update old species count if photo was reassigned
        if (currentPhoto.species_id && updates.species_id !== currentPhoto.species_id) {
          await DataIntegrityService.updateSpeciesPhotoCount(currentPhoto.species_id);
        }
        // Update new species count
        if (updates.species_id) {
          await DataIntegrityService.updateSpeciesPhotoCount(updates.species_id);
        }
      }

      setPhotos(prev => prev.map(photo =>
        photo.id === photoId ? { ...photo, ...updates } : photo
      ));

      toast.success('Photo updated successfully');
      onPhotoUpdate?.();
    } catch (error) {
      console.error('Error updating photo:', error);
      toast.error('Failed to update photo');
    }
  };

  const handleBulkPublish = async (publish: boolean) => {
    if (selectedPhotos.length === 0) {
      toast.error('No photos selected');
      return;
    }

    try {
      const { error } = await supabase
        .from('photos')
        .update({ published: publish })
        .in('id', selectedPhotos);

      if (error) throw error;

      setPhotos(prev => prev.map(photo => 
        selectedPhotos.includes(photo.id) ? { ...photo, published: publish } : photo
      ));

      setSelectedPhotos([]);
      toast.success(`${selectedPhotos.length} photos ${publish ? 'published' : 'unpublished'}`);
      onPhotoUpdate?.();
    } catch (error) {
      console.error('Error updating photos:', error);
      toast.error('Failed to update photos');
    }
  };

  const handleDeletePhoto = async (photoId: number) => {
    if (!confirm('Are you sure you want to delete this photo?')) return;

    try {
      const { error } = await supabase
        .from('photos_v2')
        .delete()
        .eq('id', photoId);

      if (error) throw error;

      setPhotos(prev => prev.filter(photo => photo.id !== photoId));
      toast.success('Photo deleted successfully');
      onPhotoUpdate?.();
    } catch (error) {
      console.error('Error deleting photo:', error);
      toast.error('Failed to delete photo');
    }
  };

  const togglePhotoSelection = (photoId: number) => {
    setSelectedPhotos(prev => 
      prev.includes(photoId) 
        ? prev.filter(id => id !== photoId)
        : [...prev, photoId]
    );
  };

  const selectAllPhotos = () => {
    setSelectedPhotos(filteredPhotos.map(photo => photo.id));
  };

  const clearSelection = () => {
    setSelectedPhotos([]);
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          Loading photos...
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Photo Management</h2>
          <p className="text-gray-600">
            {filteredPhotos.length} of {photos.length} photos
            {speciesId && ' for this species'}
          </p>
        </div>
        
        <div className="flex gap-2">
          <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
            <DialogTrigger asChild>
              <Button>
                <Upload className="w-4 h-4 mr-2" />
                Upload Photos
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Upload New Photos</DialogTitle>
              </DialogHeader>
              {/* PhotoUpload component would go here */}
              <div className="p-4 text-center text-gray-500">
                Photo upload interface would be integrated here
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters and Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4 items-center">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search photos by title, description, photographer, location, or species..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="flex gap-2">
              <Select value={filterStatus} onValueChange={(value: any) => setFilterStatus(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Photos</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                  <SelectItem value="unpublished">Unpublished</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid3X3 className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedPhotos.length > 0 && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg flex items-center justify-between">
              <span className="text-sm font-medium">
                {selectedPhotos.length} photos selected
              </span>
              <div className="flex gap-2">
                <Button size="sm" onClick={() => handleBulkPublish(true)}>
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Publish
                </Button>
                <Button size="sm" variant="outline" onClick={() => handleBulkPublish(false)}>
                  <AlertCircle className="w-4 h-4 mr-1" />
                  Unpublish
                </Button>
                <Button size="sm" variant="outline" onClick={clearSelection}>
                  Clear
                </Button>
              </div>
            </div>
          )}

          <div className="mt-2 flex gap-2">
            <Button size="sm" variant="outline" onClick={selectAllPhotos}>
              Select All
            </Button>
            <Button size="sm" variant="outline" onClick={clearSelection}>
              Clear Selection
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Photos Display */}
      {filteredPhotos.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Camera className="w-12 h-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-semibold text-gray-700 mb-2">No photos found</h3>
            <p className="text-gray-600 mb-4">
              {photos.length === 0
                ? 'Upload some photos to get started'
                : 'Try adjusting your search or filters'
              }
            </p>
            <Button onClick={() => setShowUploadDialog(true)}>
              <Upload className="w-4 h-4 mr-2" />
              Upload Photos
            </Button>
          </CardContent>
        </Card>
      ) : viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filteredPhotos.map(photo => (
            <Card key={photo.id} className="overflow-hidden">
              <div className="aspect-square relative">
                <img
                  src={photo.url}
                  alt={photo.title || 'Photo'}
                  className="w-full h-full object-cover"
                />

                {/* Selection overlay */}
                <div className="absolute top-2 left-2">
                  <input
                    type="checkbox"
                    checked={selectedPhotos.includes(photo.id)}
                    onChange={() => togglePhotoSelection(photo.id)}
                    className="w-4 h-4"
                  />
                </div>

                {/* Status badge */}
                <div className="absolute top-2 right-2">
                  <Badge variant={photo.published ? "default" : "secondary"}>
                    {photo.published ? "Published" : "Draft"}
                  </Badge>
                </div>

                {/* Action buttons */}
                <div className="absolute bottom-2 right-2 flex gap-1">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => setEditingPhoto(photo)}
                  >
                    <Edit className="w-3 h-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => handlePhotoUpdate(photo.id, { published: !photo.published })}
                  >
                    {photo.published ? <AlertCircle className="w-3 h-3" /> : <CheckCircle className="w-3 h-3" />}
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleDeletePhoto(photo.id)}
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>

              <CardContent className="p-3">
                <h3 className="font-semibold text-sm truncate">
                  {photo.title || 'Untitled'}
                </h3>
                {photo.species && (
                  <p className="text-xs text-gray-600 truncate">
                    {photo.species.name}
                  </p>
                )}
                <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
                  {photo.photographer && (
                    <div className="flex items-center gap-1">
                      <User className="w-3 h-3" />
                      <span className="truncate">{photo.photographer}</span>
                    </div>
                  )}
                  {photo.location && (
                    <div className="flex items-center gap-1">
                      <MapPin className="w-3 h-3" />
                      <span className="truncate">{photo.location}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="p-0">
            <div className="space-y-1">
              {filteredPhotos.map(photo => (
                <div key={photo.id} className="flex items-center gap-4 p-4 border-b last:border-b-0 hover:bg-gray-50">
                  <input
                    type="checkbox"
                    checked={selectedPhotos.includes(photo.id)}
                    onChange={() => togglePhotoSelection(photo.id)}
                    className="w-4 h-4"
                  />

                  <div className="w-16 h-16 relative">
                    <img
                      src={photo.url}
                      alt={photo.title || 'Photo'}
                      className="w-full h-full object-cover rounded"
                    />
                  </div>

                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold truncate">
                      {photo.title || 'Untitled'}
                    </h3>
                    {photo.species && (
                      <p className="text-sm text-gray-600 truncate">
                        {photo.species.name}
                      </p>
                    )}
                    <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                      {photo.photographer && (
                        <div className="flex items-center gap-1">
                          <User className="w-3 h-3" />
                          <span>{photo.photographer}</span>
                        </div>
                      )}
                      {photo.location && (
                        <div className="flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          <span>{photo.location}</span>
                        </div>
                      )}
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        <span>{new Date(photo.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Badge variant={photo.published ? "default" : "secondary"}>
                      {photo.published ? "Published" : "Draft"}
                    </Badge>

                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setEditingPhoto(photo)}
                      >
                        <Edit className="w-3 h-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handlePhotoUpdate(photo.id, { published: !photo.published })}
                      >
                        {photo.published ? <AlertCircle className="w-3 h-3" /> : <CheckCircle className="w-3 h-3" />}
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleDeletePhoto(photo.id)}
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Photo Edit Dialog */}
      {editingPhoto && (
        <PhotoEditDialog
          photo={editingPhoto}
          species={species}
          onSave={(updates) => {
            handlePhotoUpdate(editingPhoto.id, updates);
            setEditingPhoto(null);
          }}
          onClose={() => setEditingPhoto(null)}
        />
      )}
    </div>
  );
};

// Photo Edit Dialog Component
interface PhotoEditDialogProps {
  photo: Photo;
  species: Species[];
  onSave: (updates: Partial<Photo>) => void;
  onClose: () => void;
}

const PhotoEditDialog = ({ photo, species, onSave, onClose }: PhotoEditDialogProps) => {
  const [form, setForm] = useState({
    title: photo.title || '',
    description: photo.description || '',
    photographer: photo.photographer || '',
    location: photo.location || '',
    species_id: photo.species_id || '',
    published: photo.published,
    camera_settings: photo.camera_settings || '',
    weather_conditions: photo.weather_conditions || '',
    time_of_day: photo.time_of_day || '',
    notes: photo.notes || '',
    tags: photo.tags || [],
  });

  const [newTag, setNewTag] = useState('');

  const handleChange = (field: string, value: any) => {
    setForm(prev => ({ ...prev, [field]: value }));
  };

  const addTag = () => {
    if (newTag.trim() && !form.tags.includes(newTag.trim())) {
      setForm(prev => ({ ...prev, tags: [...prev.tags, newTag.trim()] }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setForm(prev => ({ ...prev, tags: prev.tags.filter(tag => tag !== tagToRemove) }));
  };

  const handleSave = () => {
    onSave(form);
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Photo #{photo.id}</DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Photo Preview */}
          <div>
            <img
              src={photo.url}
              alt={photo.title || 'Photo'}
              className="w-full h-64 object-cover rounded-lg"
            />
          </div>

          {/* Edit Form */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={form.title}
                onChange={(e) => handleChange('title', e.target.value)}
                placeholder="Photo title"
              />
            </div>

            <div>
              <Label htmlFor="species">Species</Label>
              <Select value={form.species_id} onValueChange={(value) => handleChange('species_id', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select species" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No species</SelectItem>
                  {species.map(s => (
                    <SelectItem key={s.id} value={s.id}>
                      {s.name} {s.scientific_name && `(${s.scientific_name})`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label htmlFor="photographer">Photographer</Label>
                <Input
                  id="photographer"
                  value={form.photographer}
                  onChange={(e) => handleChange('photographer', e.target.value)}
                  placeholder="Photographer name"
                />
              </div>
              <div>
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  value={form.location}
                  onChange={(e) => handleChange('location', e.target.value)}
                  placeholder="Photo location"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={form.description}
                onChange={(e) => handleChange('description', e.target.value)}
                placeholder="Photo description"
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="published"
                checked={form.published}
                onCheckedChange={(checked) => handleChange('published', checked)}
              />
              <Label htmlFor="published">Published</Label>
            </div>

            {/* Tags */}
            <div>
              <Label>Tags</Label>
              <div className="flex gap-2 mb-2 flex-wrap">
                {form.tags.map(tag => (
                  <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                    {tag}
                    <X className="w-3 h-3 cursor-pointer" onClick={() => removeTag(tag)} />
                  </Badge>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Add tag"
                  onKeyPress={(e) => e.key === 'Enter' && addTag()}
                />
                <Button size="sm" onClick={addTag}>
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2 mt-6">
          <Button variant="outline" onClick={onClose}>
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button onClick={handleSave}>
            <Save className="w-4 h-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
