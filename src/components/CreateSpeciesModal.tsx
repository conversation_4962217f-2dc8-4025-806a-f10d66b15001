import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { generateAISpeciesMetadata } from '@/utils/aiSpeciesGenerator';
import {
  Plus,
  Brain,
  Save,
  X,
  Loader2,
  Sparkles,
  CheckCircle,
  AlertCircle,
  Info,
  Globe
} from 'lucide-react';

interface CreateSpeciesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

interface SpeciesFormData {
  name: string;
  scientific_name: string;
  common_name: string;
  category: string;
  conservation_status: string;
  description: string;
  habitat: string;
  diet: string;
  behavior: string;
  family: string;
  size_cm: string;
  weight_g: string;
  lifespan_years: string;
  migration_pattern: string;
  breeding_season: string;
  threat_level: string;
  population_trend: string;
  regions: string;
  countries: string[];
  states_provinces: string[];
  primary_region: string;
  geographic_scope: string;
  published: boolean;
  featured: boolean;
}

const initialFormData: SpeciesFormData = {
  name: '',
  scientific_name: '',
  common_name: '',
  category: '',
  conservation_status: '',
  description: '',
  habitat: '',
  diet: '',
  behavior: '',
  family: '',
  size_cm: '',
  weight_g: '',
  lifespan_years: '',
  migration_pattern: '',
  breeding_season: '',
  threat_level: '',
  population_trend: '',
  regions: '',
  countries: [],
  states_provinces: [],
  primary_region: '',
  geographic_scope: 'regional',
  published: false,
  featured: false
};

const categories = [
  'Birds', 'Mammals', 'Reptiles', 'Amphibians', 'Fish', 'Insects', 'Marine', 'Other'
];

const conservationStatuses = [
  'Least Concern', 'Near Threatened', 'Vulnerable', 'Endangered', 
  'Critically Endangered', 'Extinct in the Wild', 'Extinct', 'Data Deficient'
];

const threatLevels = ['Low', 'Medium', 'High', 'Critical'];
const populationTrends = ['Increasing', 'Stable', 'Decreasing', 'Unknown'];

export function CreateSpeciesModal({ open, onOpenChange, onSuccess }: CreateSpeciesModalProps) {
  const [formData, setFormData] = useState<SpeciesFormData>(initialFormData);
  const [isCreating, setIsCreating] = useState(false);
  const [aiGenerating, setAiGenerating] = useState(false);
  const [aiProgress, setAiProgress] = useState(0);
  const [creationMode, setCreationMode] = useState<'manual' | 'ai'>('manual');
  const [aiPrompt, setAiPrompt] = useState('');

  const handleInputChange = (field: keyof SpeciesFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleAIGeneration = async () => {
    if (!aiPrompt.trim()) {
      toast.error('Please enter a species name or description');
      return;
    }

    setAiGenerating(true);
    setAiProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setAiProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const aiData = await generateAISpeciesMetadata([aiPrompt]);
      
      clearInterval(progressInterval);
      setAiProgress(100);

      if (aiData && aiData.length > 0) {
        const speciesData = aiData[0];
        setFormData(prev => ({
          ...prev,
          name: speciesData.name || prev.name,
          scientific_name: speciesData.scientific_name || prev.scientific_name,
          common_name: speciesData.name || prev.common_name,
          category: speciesData.category || prev.category,
          conservation_status: speciesData.conservation_status || prev.conservation_status,
          description: speciesData.description || prev.description,
          habitat: speciesData.habitat || prev.habitat,
          diet: speciesData.diet || prev.diet,
          behavior: speciesData.behavior || prev.behavior,
          family: speciesData.family || prev.family,
          size_cm: speciesData.size_cm?.toString() || prev.size_cm,
          weight_g: speciesData.weight_g?.toString() || prev.weight_g,
          lifespan_years: speciesData.lifespan_years?.toString() || prev.lifespan_years,
          migration_pattern: speciesData.migration_pattern || prev.migration_pattern,
          breeding_season: speciesData.breeding_season || prev.breeding_season,
          threat_level: speciesData.threat_level || prev.threat_level,
          population_trend: speciesData.population_trend || prev.population_trend,
          regions: speciesData.related_locations?.join(', ') || prev.regions,
          countries: speciesData.countries || [],
          states_provinces: speciesData.states_provinces || [],
          primary_region: speciesData.primary_region || '',
          geographic_scope: speciesData.geographic_scope || 'regional',
          published: false // Always start as draft
        }));

        toast.success('AI data generated successfully! Review and edit as needed.');
        setCreationMode('manual'); // Switch to manual mode for review
      }
    } catch (error) {
      console.error('Error generating AI data:', error);
      toast.error('Failed to generate AI data. Please try again.');
    } finally {
      setAiGenerating(false);
      setAiProgress(0);
    }
  };

  const handleSubmit = async () => {
    if (!formData.name.trim() || !formData.scientific_name.trim()) {
      toast.error('Name and scientific name are required');
      return;
    }

    setIsCreating(true);

    try {
      const speciesData = {
        name: formData.name.trim(),
        scientific_name: formData.scientific_name.trim(),
        common_name: formData.common_name.trim() || null,
        category: formData.category || null,
        conservation_status: formData.conservation_status || null,
        description: formData.description.trim() || null,
        habitat: formData.habitat.trim() || null,
        diet: formData.diet.trim() || null,
        behavior: formData.behavior.trim() || null,
        family: formData.family.trim() || null,
        size_cm: formData.size_cm ? parseInt(formData.size_cm) : null,
        weight_g: formData.weight_g ? parseInt(formData.weight_g) : null,
        lifespan_years: formData.lifespan_years ? parseInt(formData.lifespan_years) : null,
        migration_pattern: formData.migration_pattern.trim() || null,
        breeding_season: formData.breeding_season.trim() || null,
        threat_level: formData.threat_level || null,
        population_trend: formData.population_trend || null,
        regions: formData.regions.trim() || null,
        countries: formData.countries.length > 0 ? formData.countries : null,
        states_provinces: formData.states_provinces.length > 0 ? formData.states_provinces : null,
        primary_region: formData.primary_region.trim() || null,
        geographic_scope: formData.geographic_scope || 'regional',
        published: formData.published,
        featured: formData.featured,
        photo_count: 0
      };

      const { data, error } = await supabase
        .from('species_v2')
        .insert([speciesData])
        .select()
        .single();

      if (error) throw error;

      toast.success(`Species "${formData.name}" created successfully!`);
      setFormData(initialFormData);
      onSuccess();
      onOpenChange(false);
    } catch (error) {
      console.error('Error creating species:', error);
      toast.error('Failed to create species. Please try again.');
    } finally {
      setIsCreating(false);
    }
  };

  const handleClose = () => {
    setFormData(initialFormData);
    setCreationMode('manual');
    setAiPrompt('');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="w-5 h-5" />
            Create New Species
          </DialogTitle>
        </DialogHeader>

        {creationMode === 'manual' ? (
          <Tabs defaultValue="basic" className="w-full">
            <div className="flex items-center justify-between mb-4">
              <TabsList className="grid w-full grid-cols-4 max-w-md">
                <TabsTrigger value="basic">Basic</TabsTrigger>
                <TabsTrigger value="details">Details</TabsTrigger>
                <TabsTrigger value="biology">Biology</TabsTrigger>
                <TabsTrigger value="status">Status</TabsTrigger>
              </TabsList>
              <Button
                onClick={() => setCreationMode('ai')}
                variant="outline"
                className="gap-2"
              >
                <Brain className="w-4 h-4" />
                Use AI
              </Button>
            </div>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="e.g., White-tailed Deer"
                  />
                </div>
                <div>
                  <Label htmlFor="scientific_name">Scientific Name *</Label>
                  <Input
                    id="scientific_name"
                    value={formData.scientific_name}
                    onChange={(e) => handleInputChange('scientific_name', e.target.value)}
                    placeholder="e.g., Odocoileus virginianus"
                  />
                </div>
                <div>
                  <Label htmlFor="common_name">Common Name</Label>
                  <Input
                    id="common_name"
                    value={formData.common_name}
                    onChange={(e) => handleInputChange('common_name', e.target.value)}
                    placeholder="e.g., Whitetail"
                  />
                </div>
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category} value={category}>{category}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Brief description of the species..."
                  rows={3}
                />
              </div>
            </TabsContent>

            <TabsContent value="details" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="habitat">Habitat</Label>
                  <Textarea
                    id="habitat"
                    value={formData.habitat}
                    onChange={(e) => handleInputChange('habitat', e.target.value)}
                    placeholder="Natural habitat and environment..."
                    rows={2}
                  />
                </div>
                <div>
                  <Label htmlFor="diet">Diet</Label>
                  <Textarea
                    id="diet"
                    value={formData.diet}
                    onChange={(e) => handleInputChange('diet', e.target.value)}
                    placeholder="Feeding habits and diet..."
                    rows={2}
                  />
                </div>
                <div>
                  <Label htmlFor="behavior">Behavior</Label>
                  <Textarea
                    id="behavior"
                    value={formData.behavior}
                    onChange={(e) => handleInputChange('behavior', e.target.value)}
                    placeholder="Behavioral patterns..."
                    rows={2}
                  />
                </div>
                <div>
                  <Label>Geographic Distribution</Label>
                  <div className="space-y-3 p-3 border rounded-md">
                    {/* Countries */}
                    {formData.countries && formData.countries.length > 0 && (
                      <div>
                        <span className="text-xs text-muted-foreground">Countries:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {formData.countries.map((country, index) => (
                            <Badge key={index} variant="default" className="text-xs">
                              <Globe className="h-3 w-3 mr-1" />
                              {country}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* States/Provinces */}
                    {formData.states_provinces && formData.states_provinces.length > 0 && (
                      <div>
                        <span className="text-xs text-muted-foreground">Regions:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {formData.states_provinces.map((region, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {region}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Primary Region */}
                    {formData.primary_region && (
                      <div>
                        <span className="text-xs text-muted-foreground">Primary:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {formData.primary_region}
                          </Badge>
                        </div>
                      </div>
                    )}

                    {/* Legacy regions field for manual editing */}
                    <div>
                      <Label htmlFor="regions" className="text-xs">Legacy Regions (for manual editing)</Label>
                      <Textarea
                        id="regions"
                        value={formData.regions}
                        onChange={(e) => handleInputChange('regions', e.target.value)}
                        placeholder="Geographic distribution..."
                        rows={2}
                        className="text-xs"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="biology" className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="family">Family</Label>
                  <Input
                    id="family"
                    value={formData.family}
                    onChange={(e) => handleInputChange('family', e.target.value)}
                    placeholder="e.g., Cervidae"
                  />
                </div>
                <div>
                  <Label htmlFor="size_cm">Size (cm)</Label>
                  <Input
                    id="size_cm"
                    type="number"
                    value={formData.size_cm}
                    onChange={(e) => handleInputChange('size_cm', e.target.value)}
                    placeholder="Length in cm"
                  />
                </div>
                <div>
                  <Label htmlFor="weight_g">Weight (g)</Label>
                  <Input
                    id="weight_g"
                    type="number"
                    value={formData.weight_g}
                    onChange={(e) => handleInputChange('weight_g', e.target.value)}
                    placeholder="Weight in grams"
                  />
                </div>
                <div>
                  <Label htmlFor="lifespan_years">Lifespan (years)</Label>
                  <Input
                    id="lifespan_years"
                    type="number"
                    value={formData.lifespan_years}
                    onChange={(e) => handleInputChange('lifespan_years', e.target.value)}
                    placeholder="Average lifespan"
                  />
                </div>
                <div>
                  <Label htmlFor="migration_pattern">Migration Pattern</Label>
                  <Input
                    id="migration_pattern"
                    value={formData.migration_pattern}
                    onChange={(e) => handleInputChange('migration_pattern', e.target.value)}
                    placeholder="e.g., Seasonal"
                  />
                </div>
                <div>
                  <Label htmlFor="breeding_season">Breeding Season</Label>
                  <Input
                    id="breeding_season"
                    value={formData.breeding_season}
                    onChange={(e) => handleInputChange('breeding_season', e.target.value)}
                    placeholder="e.g., Fall"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="status" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="conservation_status">Conservation Status</Label>
                  <Select value={formData.conservation_status} onValueChange={(value) => handleInputChange('conservation_status', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      {conservationStatuses.map(status => (
                        <SelectItem key={status} value={status}>{status}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="threat_level">Threat Level</Label>
                  <Select value={formData.threat_level} onValueChange={(value) => handleInputChange('threat_level', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select threat level" />
                    </SelectTrigger>
                    <SelectContent>
                      {threatLevels.map(level => (
                        <SelectItem key={level} value={level}>{level}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="population_trend">Population Trend</Label>
                  <Select value={formData.population_trend} onValueChange={(value) => handleInputChange('population_trend', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select trend" />
                    </SelectTrigger>
                    <SelectContent>
                      {populationTrends.map(trend => (
                        <SelectItem key={trend} value={trend}>{trend}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="published"
                    checked={formData.published}
                    onCheckedChange={(checked) => handleInputChange('published', checked)}
                  />
                  <Label htmlFor="published">Publish immediately</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="featured"
                    checked={formData.featured}
                    onCheckedChange={(checked) => handleInputChange('featured', checked)}
                  />
                  <Label htmlFor="featured">Featured species</Label>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        ) : (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="w-5 h-5" />
                  AI Species Generation
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="ai_prompt">Species Name or Description</Label>
                  <Input
                    id="ai_prompt"
                    value={aiPrompt}
                    onChange={(e) => setAiPrompt(e.target.value)}
                    placeholder="e.g., 'Red-winged Blackbird' or 'Small songbird with red shoulder patches'"
                    disabled={aiGenerating}
                  />
                </div>
                
                {aiGenerating && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span className="text-sm">Generating species data...</span>
                    </div>
                    <Progress value={aiProgress} className="w-full" />
                  </div>
                )}
                
                <div className="flex gap-2">
                  <Button
                    onClick={handleAIGeneration}
                    disabled={aiGenerating || !aiPrompt.trim()}
                    className="gap-2"
                  >
                    <Sparkles className="w-4 h-4" />
                    Generate with AI
                  </Button>
                  <Button
                    onClick={() => setCreationMode('manual')}
                    variant="outline"
                  >
                    Manual Entry
                  </Button>
                </div>
                
                <div className="bg-blue-50 p-3 rounded-lg">
                  <div className="flex items-start gap-2">
                    <Info className="w-4 h-4 text-blue-600 mt-0.5" />
                    <div className="text-sm text-blue-800">
                      <p className="font-medium">AI will generate:</p>
                      <ul className="list-disc list-inside mt-1 space-y-1">
                        <li>Scientific classification and naming</li>
                        <li>Physical characteristics and measurements</li>
                        <li>Habitat and behavioral information</li>
                        <li>Conservation status and population data</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <DialogFooter>
          <Button onClick={handleClose} variant="outline">
            Cancel
          </Button>
          {creationMode === 'manual' && (
            <Button
              onClick={handleSubmit}
              disabled={isCreating || !formData.name.trim() || !formData.scientific_name.trim()}
              className="gap-2"
            >
              {isCreating ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  Create Species
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
