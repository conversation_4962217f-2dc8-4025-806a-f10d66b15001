import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { toast } from 'sonner';
import { Check, AlertTriangle, RefreshCw } from 'lucide-react';

interface Species {
  id: string;
  name: string;
  common_name: string;
  photo_count: number;
}

interface DuplicateGroup {
  name: string;
  species: Species[];
  count: number;
}

export function SpeciesDeduplicator() {
  const [duplicateGroups, setDuplicateGroups] = useState<DuplicateGroup[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState<string | null>(null);

  const findDuplicateSpecies = async () => {
    setIsLoading(true);
    try {
      // Temporarily disabled - RPC function not available
      // const { data, error } = await supabase.rpc('get_duplicate_species');
      
      console.warn("Skipping get_duplicate_species RPC; endpoint not available.");
      
      // Show development message
      toast.info("Duplicate species detection is under development", {
        description: "This feature will be available once the backend function is implemented."
      });
      
      setDuplicateGroups([]);
      return;

    } catch (error) {
      console.error('Error finding duplicates:', error);
      toast.error(`Failed to find duplicates: ${(error as Error).message}`);
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleMerge = async (group: DuplicateGroup, keepId: string) => {
    setIsProcessing(group.name);
    try {
      const toRemoveIds = group.species.filter(s => s.id !== keepId).map(s => s.id);
      
      toast.info(`Merging ${toRemoveIds.length} duplicates into ${keepId}...`);

      const { error: updatePhotosError } = await supabase
        .from('photos_v2')
        .update({ species_id: keepId })
        .in('species_id', toRemoveIds);

      if (updatePhotosError) throw updatePhotosError;

      const { error: deleteSpeciesError } = await supabase
        .from('species_v2')
        .delete()
        .in('id', toRemoveIds);

      if (deleteSpeciesError) throw deleteSpeciesError;

      const { count, error: countError } = await supabase
        .from('photos_v2')
        .select('*', { count: 'exact', head: true })
        .eq('species_id', keepId);
      
      if (countError) throw countError;

      await supabase.from('species_v2').update({ photo_count: count || 0 }).eq('id', keepId);

      toast.success(`Successfully merged duplicates for "${group.name}".`);
      
      await findDuplicateSpecies();

    } catch (error) {
      console.error('Merge failed:', error);
      toast.error(`Merge failed: ${(error as Error).message}`);
    } finally {
      setIsProcessing(null);
    }
  };

  useEffect(() => {
    findDuplicateSpecies();
  }, []);

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Duplicate Species Finder</CardTitle>
          <Button onClick={findDuplicateSpecies} disabled={isLoading} size="sm" variant="outline">
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Scan for Duplicates
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading && <p>Scanning for duplicate species...</p>}
        {!isLoading && duplicateGroups.length === 0 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>All Clear!</AlertTitle>
            <AlertDescription>
              No species with duplicate scientific names were found.
            </AlertDescription>
          </Alert>
        )}
        <div className="space-y-6">
          {duplicateGroups.map((group) => (
            <div key={group.name} className="border p-4 rounded-lg">
              <h3 className="font-bold text-lg mb-2">
                Group: {group.name} <span className="text-sm text-muted-foreground">({group.count} duplicates)</span>
              </h3>
              <div className="space-y-2">
                {group.species.sort((a,b) => (b.photo_count || 0) - (a.photo_count || 0)).map(s => (
                  <div key={s.id} className="flex items-center justify-between p-2 rounded-md bg-secondary">
                    <div>
                      <p className="font-mono text-sm">{s.id}</p>
                      <p>{s.common_name}</p>
                      <p className="text-xs text-muted-foreground">Photos: {s.photo_count}</p>
                    </div>
                    <Button 
                      onClick={() => handleMerge(group, s.id)}
                      disabled={isProcessing === group.name}
                      variant="default"
                      size="sm"
                    >
                      <Check className="mr-2 h-4 w-4" />
                      Keep this One
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
} 