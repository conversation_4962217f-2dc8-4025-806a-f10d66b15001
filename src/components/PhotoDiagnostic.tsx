import React from "react";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  Database, 
  Image, 
  CheckCircle, 
  AlertTriangle,
  RefreshCw,
  ExternalLink,
  Link as LinkIcon,
  Unlink
} from "lucide-react";
import { Link } from "react-router-dom";
import { toast } from "sonner";

interface Species {
  id: string;
  name: string;
  common_name?: string;
  published: boolean;
  photo_count?: number;
  photos?: Photo[];
}

interface Photo {
  id: number;
  url?: string;
  title?: string;
  published: boolean;
  species?: Partial<Species>;
}

interface PhotoDiagnostic {
  totalPhotos: number;
  publishedPhotos: number;
  photosWithValidSpecies: number;
  photosWithoutSpecies: number;
  orphanedPhotos: number;
  speciesWithPhotos: number;
  speciesWithoutPhotos: number;
  samplePhotos: Photo[];
  sampleSpecies: Species[];
  urlIssues: string[];
}

const StatCard = ({ title, value, icon: Icon }: { title: string, value: number | string, icon: React.ElementType }) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      <Icon className="h-4 w-4 text-muted-foreground" />
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
    </CardContent>
  </Card>
);

export const PhotoDiagnostic = () => {
  const [diagnostic, setDiagnostic] = useState<PhotoDiagnostic | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [testingUrls, setTestingUrls] = useState(false);

  const runDiagnostic = async () => {
    setIsLoading(true);
    try {
      console.log('🔍 Running photo diagnostic...');

      // Get photo statistics
      const { count: totalPhotos, error: totalPhotosError } = await supabase
        .from('photos_v2')
        .select('*', { count: 'exact', head: true });
      if (totalPhotosError) throw totalPhotosError;

      const { count: publishedPhotos, error: publishedPhotosError } = await supabase
        .from('photos_v2')
        .select('*', { count: 'exact', head: true })
        .eq('published', true);
      if (publishedPhotosError) throw publishedPhotosError;
      
      const { count: photosWithSpeciesId, error: photosWithSpeciesIdError } = await supabase
        .from('photos_v2')
        .select('*', { count: 'exact', head: true })
        .not('species_id', 'is', null);
      if(photosWithSpeciesIdError) throw photosWithSpeciesIdError;

      const { data: orphanedPhotos, error: orphanedPhotosError } = await supabase
        .from('photos_v2')
        .select('id, species_id')
        .not('species_id', 'is', null)
        .not('species_id', 'in', `(SELECT id FROM species)`);
      if (orphanedPhotosError) throw orphanedPhotosError;

      const orphanedPhotosCount = orphanedPhotos?.length || 0;
      const photosWithValidSpecies = (photosWithSpeciesId || 0) - orphanedPhotosCount;
      const photosWithoutSpecies = (totalPhotos || 0) - (photosWithSpeciesId || 0);

      // Get species statistics
      const { count: totalSpecies, error: totalSpeciesError } = await supabase
        .from('species_v2')
        .select('*', { count: 'exact', head: true });
      if(totalSpeciesError) throw totalSpeciesError;

      const { count: speciesWithPhotos, error: speciesWithPhotosError } = await supabase
        .from('species_v2')
        .select('*', { count: 'exact', head: true })
        .gt('photo_count', 0);
      if(speciesWithPhotosError) throw speciesWithPhotosError;

      const speciesWithoutPhotos = (totalSpecies || 0) - (speciesWithPhotos || 0);

      // Get sample data for inspection
      const { data: samplePhotos, error: samplePhotosError } = await supabase
        .from('photos_v2')
        .select(`
          id, url, title, published,
          species:species_id (id, name, common_name)
        `)
        .order('created_at', { ascending: false })
        .limit(10);
      if (samplePhotosError) throw samplePhotosError;

      const { data: sampleSpecies, error: sampleSpeciesError } = await supabase
        .from('species_v2')
        .select(`
          *,
          photos_v2(id, url, title, published)
        `)
        .order('name')
        .limit(10);
      if(sampleSpeciesError) throw sampleSpeciesError;

      // Test URL accessibility
      const urlIssues: string[] = [];
      if (samplePhotos && samplePhotos.length > 0) {
        setTestingUrls(true);
        for (const photo of samplePhotos.slice(0, 5)) {
          if(!photo.url) continue;
          try {
            const response = await fetch(photo.url, { method: 'HEAD' });
            if (!response.ok) {
              urlIssues.push(`${photo.url} - Status: ${response.status}`);
            }
          } catch (error) {
            urlIssues.push(`${photo.url} - Error: (Network Error)`);
          }
        }
        setTestingUrls(false);
      }

      const result: PhotoDiagnostic = {
        totalPhotos: totalPhotos || 0,
        publishedPhotos: publishedPhotos || 0,
        photosWithValidSpecies,
        photosWithoutSpecies,
        orphanedPhotos: orphanedPhotosCount,
        speciesWithPhotos: speciesWithPhotos || 0,
        speciesWithoutPhotos,
        samplePhotos: samplePhotos || [],
        sampleSpecies: sampleSpecies || [],
        urlIssues
      };

      setDiagnostic(result);
      console.log('📊 Diagnostic complete:', result);

    } catch (error) {
      console.error('❌ Diagnostic failed:', error);
      toast.error(`Diagnostic failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const isHealthy = diagnostic && diagnostic.orphanedPhotos === 0 && diagnostic.urlIssues.length === 0;

  const fixPhotoUrls = async () => {
    setIsLoading(true);
    toast.info("Attempting to fix photo URLs...");
    try {
      const { data: photosToFix, error: fetchError } = await supabase
        .from('photos_v2')
        .select('id, url')
        .like('url', '%/storage/v1/object/public/species%');

      if (fetchError) throw fetchError;

      if (!photosToFix || photosToFix.length === 0) {
        toast.info('No photos found with potentially incorrect storage URLs to fix.');
        setIsLoading(false);
        return;
      }

      let fixedCount = 0;
      const { data: publicUrlData } = supabase.storage.from('species_v2').getPublicUrl('');
      const supabaseUrl = publicUrlData.publicUrl.replace('/species/','');
      const updates = [];

      for (const photo of photosToFix) {
        const match = photo.url.match(/\/species\/(.+)$/);
        if (match && match[1]) {
          const path = match[1];
          const newUrl = `${supabaseUrl}/object/public/species/${path}`;
          if (photo.url !== newUrl) {
            updates.push({ id: photo.id, url: newUrl });
          }
        }
      }

      if (updates.length > 0) {
        const { error: updateError } = await supabase.from('photos_v2').upsert(updates);
        if (updateError) throw updateError;
        fixedCount = updates.length;
      }
      
      toast.success(`Successfully fixed ${fixedCount} photo URLs.`);
      runDiagnostic(); // Re-run diagnostics to see updated state
    } catch (error) {
      console.error('Error fixing photo URLs:', error);
      toast.error(`Failed to fix photo URLs: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    runDiagnostic();
  }, []);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Image className="w-5 h-5" />
            Photo Diagnostic Tool
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={runDiagnostic} disabled={isLoading}>
              <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button variant="outline" size="sm" onClick={fixPhotoUrls} disabled={isLoading || (diagnostic && diagnostic.urlIssues.length === 0)}>
              <ExternalLink className="w-4 h-4 mr-2" />
              Fix URLs
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {!diagnostic && isLoading && <p>Running diagnostics...</p>}
          {diagnostic && (
            <>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <StatCard title="Total Photos" value={diagnostic.totalPhotos} icon={Database} />
                <StatCard title="Published Photos" value={diagnostic.publishedPhotos} icon={CheckCircle} />
                <StatCard title="Photos w/ Valid Species" value={diagnostic.photosWithValidSpecies} icon={LinkIcon} />
                <StatCard title="Photos w/o Species" value={diagnostic.photosWithoutSpecies} icon={Unlink} />
              </div>

              {diagnostic.orphanedPhotos > 0 && (
                <Alert variant="destructive" className="mb-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Found {diagnostic.orphanedPhotos} orphaned photos (photos referencing non-existent species). 
                    These should be fixed or removed.
                  </AlertDescription>
                </Alert>
              )}

              {diagnostic.urlIssues.length > 0 && (
                <Alert variant="destructive" className="mb-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div>
                      <strong>URL Issues Found ({diagnostic.urlIssues.length}):</strong>
                      <ul className="list-disc list-inside mt-2 text-xs">
                        {diagnostic.urlIssues.map((issue, index) => (
                          <li key={index}>{issue}</li>
                        ))}
                      </ul>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {isHealthy && !isLoading && (
                <Alert variant="default" className="bg-green-50 border-green-200">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-700">
                    System looks healthy! All photos have valid species assignments and accessible URLs.
                  </AlertDescription>
                </Alert>
              )}

              <div className="mt-6">
                <h3 className="text-lg font-semibold mb-2">Sample Photos (Latest 10)</h3>
                <div className="space-y-4">
                  {diagnostic.samplePhotos.map((photo) => (
                    <Card key={photo.id} className="p-4 flex items-start gap-4">
                      <img src={photo.url || ''} alt={photo.title || 'Photo'} className="w-24 h-24 object-cover rounded-md bg-gray-100" onError={(e) => e.currentTarget.src = 'https://via.placeholder.com/150?text=Error'}/>
                      <div className="flex-grow">
                        <div className="flex justify-between">
                          <p className="font-bold">{photo.title || 'Untitled'}</p>
                          <Badge variant={photo.published ? 'default' : 'secondary'}>{photo.published ? 'Published' : 'Draft'}</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">ID: {photo.id}</p>
                        <p className="text-sm">
                          Species: {photo.species ? (
                            <Link to={`/species/${photo.species.id}`} className="text-blue-600 hover:underline">{photo.species.name}</Link>
                          ) : (
                            <span className="text-red-500">None</span>
                          )}
                        </p>
                        <p className="text-xs text-gray-500 break-all">URL: {photo.url || 'N/A'}</p>
                        
                      </div>
                    </Card>
                  ))}
                </div>
              </div>

              <div className="mt-6">
                <h3 className="text-lg font-semibold mb-2">Sample Species with Photo Counts (First 10)</h3>
                 <div className="space-y-4">
                  {diagnostic.sampleSpecies.map((species) => (
                    <Card key={species.id} className="p-4">
                      <div className="flex justify-between">
                        <div>
                          <p className="font-bold">{species.name}</p>
                          <p className="text-sm text-muted-foreground">{species.common_name}</p>
                        </div>
                        <Badge variant={species.published ? 'default' : 'secondary'}>{species.published ? 'Published' : 'Draft'}</Badge>
                      </div>
                      <p className="text-sm mt-2">DB Photo Count: {species.photo_count || 0}</p>
                      <p className="text-sm">Actual Photos: {species.photos?.length || 0}</p>
                    </Card>
                  ))}
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};