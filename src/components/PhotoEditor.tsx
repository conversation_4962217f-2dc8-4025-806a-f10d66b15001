
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Loader2, Save, X, Plus } from "lucide-react";

interface Photo {
  id: number;
  title?: string;
  url: string;
  description?: string;
  photographer?: string;
  location?: string;
  species_id?: string;
  published: boolean;
  tags?: string[];
  camera_settings?: string;
  weather_conditions?: string;
  time_of_day?: string;
  notes?: string;
}

interface Species {
  id: string;
  name: string;
  common_name?: string;
  scientific_name?: string;
  category?: string;
}

interface PhotoEditorProps {
  photo: Photo;
  onUpdate: (photo: Photo) => void;
  onClose: () => void;
}

export const PhotoEditor = ({ photo, onUpdate, onClose }: PhotoEditorProps) => {
  const [editedPhoto, setEditedPhoto] = useState<Photo>(photo);
  const [species, setSpecies] = useState<Species[]>([]);
  const [newTag, setNewTag] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [isLoadingSpecies, setIsLoadingSpecies] = useState(true);

  useEffect(() => {
    loadSpecies();
  }, []);

  const loadSpecies = async () => {
    setIsLoadingSpecies(true);
    try {
      const { data, error } = await supabase
        .from('species_v2')
        .select('id, name, common_name, scientific_name, category')
        .order('name');

      if (error) throw error;
      setSpecies(data || []);
    } catch (error) {
      console.error('Error loading species:', error);
      toast.error('Failed to load species list');
    } finally {
      setIsLoadingSpecies(false);
    }
  };

  const handleInputChange = (field: keyof Photo, value: any) => {
    setEditedPhoto(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addTag = () => {
    if (newTag.trim() && !editedPhoto.tags?.includes(newTag.trim())) {
      handleInputChange('tags', [...(editedPhoto.tags || []), newTag.trim()]);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    handleInputChange('tags', editedPhoto.tags?.filter(tag => tag !== tagToRemove) || []);
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const { error } = await supabase
        .from('photos_v2')
        .update({
          title: editedPhoto.title,
          description: editedPhoto.description,
          photographer: editedPhoto.photographer,
          location: editedPhoto.location,
          species_id: editedPhoto.species_id === 'no-species' ? null : editedPhoto.species_id,
          published: editedPhoto.published,
          tags: editedPhoto.tags,
          camera_settings: editedPhoto.camera_settings,
          weather_conditions: editedPhoto.weather_conditions,
          time_of_day: editedPhoto.time_of_day,
          notes: editedPhoto.notes
        })
        .eq('id', editedPhoto.id);

      if (error) throw error;

      toast.success('Photo updated successfully!');
      onUpdate(editedPhoto);
    } catch (error) {
      console.error('Error updating photo:', error);
      toast.error('Failed to update photo');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Edit Photo #{editedPhoto.id}</CardTitle>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="w-4 h-4" />
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Photo Preview */}
        <div className="flex flex-col md:flex-row gap-6">
          <div className="md:w-1/3">
            <img 
              src={editedPhoto.url} 
              alt={editedPhoto.title || 'Photo'} 
              className="w-full h-48 object-cover rounded-lg"
            />
          </div>
          
          <div className="md:w-2/3 space-y-4">
            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={editedPhoto.title || ''}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Photo title"
                />
              </div>
              
              <div>
                <Label htmlFor="photographer">Photographer</Label>
                <Input
                  id="photographer"
                  value={editedPhoto.photographer || ''}
                  onChange={(e) => handleInputChange('photographer', e.target.value)}
                  placeholder="Photographer name"
                />
              </div>
            </div>

            {/* Description */}
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={editedPhoto.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Photo description"
                rows={3}
              />
            </div>

            {/* Species Assignment */}
            <div>
              <Label htmlFor="species">Species</Label>
              {isLoadingSpecies ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Loading species...
                </div>
              ) : (
                <Select
                  value={editedPhoto.species_id || 'no-species'}
                  onValueChange={(value) => handleInputChange('species_id', value === 'no-species' ? null : value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a species" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="no-species">No species assigned</SelectItem>
                    {species.map((spec) => (
                      <SelectItem key={spec.id} value={spec.id}>
                        {spec.name} {spec.common_name && `(${spec.common_name})`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>
        </div>

        {/* Additional Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="location">Location</Label>
            <Input
              id="location"
              value={editedPhoto.location || ''}
              onChange={(e) => handleInputChange('location', e.target.value)}
              placeholder="Photo location"
            />
          </div>
          
          <div>
            <Label htmlFor="time_of_day">Time of Day</Label>
            <Input
              id="time_of_day"
              value={editedPhoto.time_of_day || ''}
              onChange={(e) => handleInputChange('time_of_day', e.target.value)}
              placeholder="e.g., Morning, Sunset"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="camera_settings">Camera Settings</Label>
            <Input
              id="camera_settings"
              value={editedPhoto.camera_settings || ''}
              onChange={(e) => handleInputChange('camera_settings', e.target.value)}
              placeholder="e.g., f/2.8, 1/500s, ISO 400"
            />
          </div>
          
          <div>
            <Label htmlFor="weather_conditions">Weather Conditions</Label>
            <Input
              id="weather_conditions"
              value={editedPhoto.weather_conditions || ''}
              onChange={(e) => handleInputChange('weather_conditions', e.target.value)}
              placeholder="e.g., Sunny, Overcast"
            />
          </div>
        </div>

        {/* Tags */}
        <div>
          <Label>Tags</Label>
          <div className="flex flex-wrap gap-2 mb-2">
            {editedPhoto.tags?.map((tag, index) => (
              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                {tag}
                <button
                  onClick={() => removeTag(tag)}
                  className="ml-1 hover:text-red-500"
                >
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            ))}
          </div>
          <div className="flex gap-2">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Add a tag"
              onKeyPress={(e) => e.key === 'Enter' && addTag()}
            />
            <Button onClick={addTag} size="sm">
              <Plus className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Notes */}
        <div>
          <Label htmlFor="notes">Notes</Label>
          <Textarea
            id="notes"
            value={editedPhoto.notes || ''}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="Additional notes"
            rows={2}
          />
        </div>

        {/* Published Toggle */}
        <div className="flex items-center space-x-2">
          <Switch
            id="published"
            checked={editedPhoto.published}
            onCheckedChange={(checked) => handleInputChange('published', checked)}
          />
          <Label htmlFor="published">Published</Label>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
