import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { PhotoEditor } from "./PhotoEditor";
import { SpeciesManager } from "./SpeciesManager";
import { SpeciesDataQualityDashboard } from "./SpeciesDataQualityDashboard";
import { toast } from "sonner";
import { Loader2, Image, Database, Search, Filter, BarChart3 } from "lucide-react";

interface Photo {
  id: number;
  title?: string;
  url: string;
  description?: string;
  photographer?: string;
  location?: string;
  species_id?: string;
  published: boolean;
  tags?: string[];
  camera_settings?: string;
  weather_conditions?: string;
  time_of_day?: string;
  notes?: string;
}

interface Species {
  id: string;
  name: string;
  common_name?: string;
  scientific_name?: string;
  category?: string;
}

export const ContentManagementSystem = () => {
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [species, setSpecies] = useState<Species[]>([]);
  const [editingPhoto, setEditingPhoto] = useState<Photo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [speciesFilter, setSpeciesFilter] = useState<string>("all");

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      // Load photos
      const { data: photosData, error: photosError } = await supabase
        .from('photos_v2')
        .select('*')
        .order('created_at', { ascending: false });

      if (photosError) throw photosError;

      // Load species for filtering
      const { data: speciesData, error: speciesError } = await supabase
        .from('species_v2')
        .select('id, name, common_name, scientific_name, category')
        .order('name');

      if (speciesError) throw speciesError;

      setPhotos(photosData || []);
      setSpecies(speciesData || []);
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePhotoUpdate = (updatedPhoto: Photo) => {
    setPhotos(prev => prev.map(photo => 
      photo.id === updatedPhoto.id ? updatedPhoto : photo
    ));
    setEditingPhoto(null);
  };

  const filteredPhotos = photos.filter(photo => {
    const matchesSearch = !searchTerm || 
      photo.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      photo.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      photo.photographer?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      photo.location?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === "all" ||
      (statusFilter === "published" && photo.published) ||
      (statusFilter === "unpublished" && !photo.published) ||
      (statusFilter === "needs_species" && !photo.species_id) ||
      (statusFilter === "has_species" && photo.species_id);

    const matchesSpecies = speciesFilter === "all" ||
      photo.species_id === speciesFilter;

    return matchesSearch && matchesStatus && matchesSpecies;
  });

  const getSpeciesName = (speciesId?: string) => {
    if (!speciesId) return "No species assigned";
    const spec = species.find(s => s.id === speciesId);
    return spec ? `${spec.name}${spec.common_name ? ` (${spec.common_name})` : ''}` : "Unknown species";
  };

  const getStatusCounts = () => {
    return {
      total: photos.length,
      published: photos.filter(p => p.published).length,
      unpublished: photos.filter(p => !p.published).length,
      needsSpecies: photos.filter(p => !p.species_id).length,
      hasSpecies: photos.filter(p => p.species_id).length
    };
  };

  const counts = getStatusCounts();

  if (editingPhoto) {
    return (
      <PhotoEditor
        photo={editingPhoto}
        onUpdate={handlePhotoUpdate}
        onClose={() => setEditingPhoto(null)}
      />
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Content Management System</h1>
        <p className="text-gray-600">Manage your wildlife photos and species data</p>
      </div>

      <Tabs defaultValue="photos" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="photos" className="flex items-center gap-2">
            <Image className="w-4 h-4" />
            Photo Management
          </TabsTrigger>
          <TabsTrigger value="species" className="flex items-center gap-2">
            <Database className="w-4 h-4" />
            Species Management
          </TabsTrigger>
          <TabsTrigger value="quality" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Data Quality
          </TabsTrigger>
        </TabsList>

        <TabsContent value="photos" className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">{counts.total}</div>
                <div className="text-sm text-gray-600">Total Photos</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{counts.published}</div>
                <div className="text-sm text-gray-600">Published</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-yellow-600">{counts.unpublished}</div>
                <div className="text-sm text-gray-600">Unpublished</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-red-600">{counts.needsSpecies}</div>
                <div className="text-sm text-gray-600">Needs Species</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">{counts.hasSpecies}</div>
                <div className="text-sm text-gray-600">Has Species</div>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search photos by title, description, photographer, or location..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Photos</SelectItem>
                      <SelectItem value="published">Published Only</SelectItem>
                      <SelectItem value="unpublished">Unpublished Only</SelectItem>
                      <SelectItem value="needs_species">Needs Species</SelectItem>
                      <SelectItem value="has_species">Has Species</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={speciesFilter} onValueChange={setSpeciesFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Filter by species" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Species</SelectItem>
                      {species.map((spec) => (
                        <SelectItem key={spec.id} value={spec.id}>
                          {spec.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Photos Grid */}
          {isLoading ? (
            <Card>
              <CardContent className="flex items-center justify-center py-8">
                <Loader2 className="w-6 h-6 animate-spin mr-2" />
                Loading photos...
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {filteredPhotos.map((photo) => (
                <Card key={photo.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="aspect-square overflow-hidden">
                    <img
                      src={photo.url}
                      alt={photo.title || `Photo ${photo.id}`}
                      className="w-full h-full object-cover cursor-pointer hover:scale-105 transition-transform"
                      onClick={() => setEditingPhoto(photo)}
                    />
                  </div>
                  <CardContent className="p-4 space-y-2">
                    <div className="flex items-start justify-between">
                      <h3 className="font-medium text-sm truncate">
                        {photo.title || `Photo #${photo.id}`}
                      </h3>
                      <Badge variant={photo.published ? "default" : "secondary"} className="text-xs">
                        {photo.published ? "Published" : "Draft"}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-600 truncate">
                      {getSpeciesName(photo.species_id)}
                    </p>
                    {photo.photographer && (
                      <p className="text-xs text-gray-500 truncate">
                        By {photo.photographer}
                      </p>
                    )}
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setEditingPhoto(photo)}
                      className="w-full text-xs"
                    >
                      Edit Photo
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {filteredPhotos.length === 0 && !isLoading && (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-gray-500">No photos match your current filters.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="species">
          <SpeciesManager />
        </TabsContent>

        <TabsContent value="quality">
          <SpeciesDataQualityDashboard />
        </TabsContent>
      </Tabs>
    </div>
  );
};
