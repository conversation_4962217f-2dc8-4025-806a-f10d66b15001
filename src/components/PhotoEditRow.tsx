import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { reassignPhotoSpecies } from '@/lib/photoApi';
import { supabase } from '@/integrations/supabase/client';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import type { Photo, Species } from '@/utils/speciesPhotoMatrix';

interface PhotoEditRowProps {
  photo: Photo;
  speciesList: { id: string; name: string; }[];
  onPhotoUpdate: (updatedPhoto: Photo) => void;
  onSpeciesCreated: (newSpecies: Species) => void;
}

export const PhotoEditRow: React.FC<PhotoEditRowProps> = ({ photo, speciesList, onPhotoUpdate, onSpeciesCreated }) => {
  const { isAdmin } = useAdminAuth();
  const [editState, setEditState] = useState<Photo>({ ...photo });
  const [saving, setSaving] = useState(false);
  const [speciesDropdownOpen, setSpeciesDropdownOpen] = useState(false);
  const [showNewSpeciesModal, setShowNewSpeciesModal] = useState(false);
  const [speciesId, setSpeciesId] = useState(photo.species_id || '');

  // Debounced save for inline fields
  const handleFieldChange = (field: string, value: any) => {
    setEditState(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async (field: string) => {
    setSaving(true);
    try {
      const { error } = await supabase
        .from('photos')
        .update({ [field]: editState[field], updated_at: new Date().toISOString() })
        .eq('id', photo.id);
      if (error) throw error;
      onPhotoUpdate({ ...photo, [field]: editState[field] });
      toast.success(`${field} updated successfully.`);
    } catch (err) {
      toast.error(`Failed to update ${field}.`);
    } finally {
      setSaving(false);
    }
  };

  const handleSpeciesChange = async (newSpeciesId: string) => {
    setSaving(true);
    try {
      await reassignPhotoSpecies({
        photoId: photo.id,
        newSpeciesId,
        oldSpeciesId: photo.species_id,
        reason: 'manual reassignment from matrix',
      });
      setSpeciesId(newSpeciesId);
      onPhotoUpdate({ ...photo, species_id: newSpeciesId });
      toast.success('Species updated successfully.');
    } catch (err) {
      toast.error('Failed to reassign species.');
    } finally {
      setSaving(false);
    }
  };

  // TODO: Modal for creating new species and auto-selecting

  return (
    <tr>
      <td>{photo.id}</td>
      <td>
        <Input
          value={editState.location || ''}
          onChange={e => handleFieldChange('location', e.target.value)}
          onBlur={() => handleSave('location')}
          placeholder="—"
          disabled={!isAdmin || saving}
        />
      </td>
      <td>
        <Input
          type="date"
          value={editState.date_taken ? editState.date_taken.slice(0, 10) : ''}
          onChange={e => handleFieldChange('date_taken', e.target.value)}
          onBlur={() => handleSave('date_taken')}
          placeholder="—"
          disabled={!isAdmin || saving}
        />
      </td>
      <td>
        <Input
          value={editState.camera_make || ''}
          onChange={e => handleFieldChange('camera_make', e.target.value)}
          onBlur={() => handleSave('camera_make')}
          placeholder="—"
          disabled={!isAdmin || saving}
        />
      </td>
      <td>
        <Input
          value={editState.camera_model || ''}
          onChange={e => handleFieldChange('camera_model', e.target.value)}
          onBlur={() => handleSave('camera_model')}
          placeholder="—"
          disabled={!isAdmin || saving}
        />
      </td>
      <td>
        <Input
          value={editState.lens || ''}
          onChange={e => handleFieldChange('lens', e.target.value)}
          onBlur={() => handleSave('lens')}
          placeholder="—"
          disabled={!isAdmin || saving}
        />
      </td>
      <td>
        <Input
          value={editState.focal_length || ''}
          onChange={e => handleFieldChange('focal_length', e.target.value)}
          onBlur={() => handleSave('focal_length')}
          placeholder="—"
          disabled={!isAdmin || saving}
        />
      </td>
      <td>
        <Input
          value={editState.aperture || ''}
          onChange={e => handleFieldChange('aperture', e.target.value)}
          onBlur={() => handleSave('aperture')}
          placeholder="—"
          disabled={!isAdmin || saving}
        />
      </td>
      <td>
        <Input
          value={editState.shutter_speed || ''}
          onChange={e => handleFieldChange('shutter_speed', e.target.value)}
          onBlur={() => handleSave('shutter_speed')}
          placeholder="—"
          disabled={!isAdmin || saving}
        />
      </td>
      <td>
        <Input
          value={editState.iso || ''}
          onChange={e => handleFieldChange('iso', e.target.value)}
          onBlur={() => handleSave('iso')}
          placeholder="—"
          disabled={!isAdmin || saving}
        />
      </td>
      <td>
        <Input
          value={editState.exposure_mode || ''}
          onChange={e => handleFieldChange('exposure_mode', e.target.value)}
          onBlur={() => handleSave('exposure_mode')}
          placeholder="—"
          disabled={!isAdmin || saving}
        />
      </td>
      <td>
        <Input
          value={editState.white_balance || ''}
          onChange={e => handleFieldChange('white_balance', e.target.value)}
          onBlur={() => handleSave('white_balance')}
          placeholder="—"
          disabled={!isAdmin || saving}
        />
      </td>
      <td>
        <Input
          value={editState.metering_mode || ''}
          onChange={e => handleFieldChange('metering_mode', e.target.value)}
          onBlur={() => handleSave('metering_mode')}
          placeholder="—"
          disabled={!isAdmin || saving}
        />
      </td>
      <td>
        <Switch
          checked={!!editState.flash_used}
          onCheckedChange={val => { handleFieldChange('flash_used', val); handleSave('flash_used'); }}
          disabled={!isAdmin || saving}
        />
      </td>
      <td>
        <Input
          value={editState.color_profile || ''}
          onChange={e => handleFieldChange('color_profile', e.target.value)}
          onBlur={() => handleSave('color_profile')}
          placeholder="—"
          disabled={!isAdmin || saving}
        />
      </td>
      <td>
        <Input
          value={editState.original_filename || ''}
          onChange={e => handleFieldChange('original_filename', e.target.value)}
          onBlur={() => handleSave('original_filename')}
          placeholder="—"
          disabled={!isAdmin || saving}
        />
      </td>
      <td>
        <Switch
          checked={!!editState.published}
          onCheckedChange={val => { handleFieldChange('published', val); handleSave('published'); }}
          disabled={!isAdmin || saving}
        />
      </td>
      <td>
        <Badge variant="outline">{photo.ai_assigned_species_id || '—'}</Badge>
      </td>
      <td>
        <Select value={speciesId} onValueChange={val => handleSpeciesChange(val)} disabled={!isAdmin || saving}>
          <SelectTrigger><SelectValue placeholder="Reassign species" /></SelectTrigger>
          <SelectContent>
            {speciesList.map(s => (
              <SelectItem key={s.id} value={s.id}>{s.name}</SelectItem>
            ))}
            <SelectItem value="__create_new__">➕ Create New</SelectItem>
          </SelectContent>
        </Select>
        {/* TODO: Modal for new species creation */}
      </td>
    </tr>
  );
}; 