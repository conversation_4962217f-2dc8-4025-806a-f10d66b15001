import React, { useState, useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { generateAISpeciesMetadata } from '@/utils/aiSpeciesGenerator';
import { getAISuggestions, testSinglePhotoAI, checkHawkSpecies, testSpecificPhoto, testWoodnymphPhoto, getCacheStats, clearAllCachedSuggestions, rejectAISuggestions } from '@/lib/photoApi';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { LocationDropZone } from './LocationDropZone';
import { useLocations } from '@/hooks/useLocations';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import '@/scripts/createTestLocation';
import {
  Search,
  ImageIcon,
  ArrowRight,
  CheckCircle,
  RefreshCw,
  Loader2,
  AlertCircle,
  Camera,
  Tag,
  Filter,
  Plus,
  Brain,
  Sparkles,
  ZoomIn,
  ExternalLink,
  MapPin,
  Globe,
  Eye,
  X,
  Wand2,
  Check,
  Database,
  Trash2,
  History
} from 'lucide-react';

interface Photo {
  id: string;
  url: string | null;
  title: string | null;
  species_id: string | null;
  published: boolean;
  created_at: string;
}

interface Species {
  id: string;
  name: string;
  common_name: string | null;
  scientific_name: string | null;
  category: string | null;
  conservation_status: string | null;
  published: boolean;
  photo_count?: number;
  description?: string;
  regions?: string;
}

interface SpeciesThumbnail {
  species_id: string;
  thumbnail_url: string | null;
}

// Draggable Photo Component
function DraggablePhoto({
  photo,
  onClick,
  isSelected = false,
  isMultiSelectMode = false,
  onToggleSelect
}: {
  photo: Photo;
  onClick: () => void;
  isSelected?: boolean;
  isMultiSelectMode?: boolean;
  onToggleSelect?: () => void;
}) {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'PHOTO',
    item: { photoId: photo.id },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  const handleClick = (e: React.MouseEvent) => {
    if (isMultiSelectMode && onToggleSelect) {
      e.preventDefault();
      onToggleSelect();
    } else {
      onClick();
    }
  };

  return (
    <div
      ref={drag}
      className={`relative group cursor-pointer border rounded-lg overflow-hidden hover:shadow-lg transition-all ${
        isDragging ? 'opacity-50' : 'opacity-100'
      } ${isSelected ? 'ring-2 ring-blue-500 border-blue-500' : ''}`}
      onClick={handleClick}
    >
      {photo.url ? (
        <div className="relative">
          <img
            src={photo.url}
            alt={photo.title || 'Unassigned photo'}
            className="w-full h-24 object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center">
            <ArrowRight className="h-4 w-4 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
          </div>
          {isDragging && (
            <div className="absolute inset-0 bg-blue-500 bg-opacity-30 flex items-center justify-center">
              <div className="text-white text-xs font-medium">Dragging...</div>
            </div>
          )}
          {/* Multi-select checkbox */}
          {isMultiSelectMode && (
            <div className="absolute top-2 left-2">
              <div className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-all ${
                isSelected
                  ? 'bg-blue-500 border-blue-500'
                  : 'bg-white border-gray-300 group-hover:border-blue-400'
              }`}>
                {isSelected && <Check className="w-3 h-3 text-white" />}
              </div>
            </div>
          )}
          {/* Selection indicator */}
          {isSelected && !isMultiSelectMode && (
            <div className="absolute top-2 right-2">
              <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                <Check className="w-3 h-3 text-white" />
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="w-full h-24 bg-gray-200 flex items-center justify-center">
          <ImageIcon className="h-6 w-6 text-gray-400" />
        </div>
      )}
      <div className="p-2">
        <p className="text-xs text-muted-foreground truncate">
          {photo.title || `Photo ${photo.id.slice(0, 8)}`}
        </p>
      </div>
    </div>
  );
}

// Species Drop Target Component
function SpeciesDropTarget({
  species,
  photoCount,
  onPhotoAssigned,
  onViewPhotos,
  publishSpeciesOnAssign = true,
  selectedPhotos = new Set(),
  onBulkAssign
}: {
  species: Species;
  photoCount: number;
  onPhotoAssigned: () => void;
  onViewPhotos: () => void;
  publishSpeciesOnAssign?: boolean;
  selectedPhotos?: Set<string>;
  onBulkAssign?: (speciesId: string) => void;
}) {
  const [isAssigning, setIsAssigning] = useState(false);
  const [assignedPhotos, setAssignedPhotos] = useState<Photo[]>([]);

  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: 'PHOTO',
    drop: async (item: { photoId: string }) => {
      // If there are selected photos, assign all of them
      if (selectedPhotos.size > 0 && onBulkAssign) {
        await onBulkAssign(species.id);
      } else {
        await handlePhotoAssignment(item.photoId);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  }));

  // Fetch assigned photos for this species
  useEffect(() => {
    fetchAssignedPhotos();
  }, [species.id]);

  const fetchAssignedPhotos = async () => {
    try {
      const { data, error } = await supabase
        .from('photos_v2')
        .select('*')
        .eq('species_id', species.id)
        .eq('published', true)
        .limit(3)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setAssignedPhotos(data || []);
    } catch (error) {
      console.error('Error fetching assigned photos:', error);
    }
  };

  const handlePhotoAssignment = async (photoId: string) => {
    setIsAssigning(true);
    try {
      // Update photo assignment
      const { error: photoError } = await supabase
        .from('photos_v2')
        .update({
          species_id: species.id,
          published: publishSpeciesOnAssign
        })
        .eq('id', photoId);

      if (photoError) throw photoError;

      // If publishSpeciesOnAssign is enabled, also publish the species
      if (publishSpeciesOnAssign) {
        const { error: speciesError } = await supabase
          .from('species_v2')
          .update({ published: true })
          .eq('id', species.id);

        if (speciesError) {
          console.error('Error publishing species:', speciesError);
          // Don't fail the whole operation if species publish fails
        }
      }

      toast.success(`Photo assigned to ${species.name}!`);
      onPhotoAssigned();
      fetchAssignedPhotos();
    } catch (error) {
      console.error('Error assigning photo to species:', error);
      toast.error('Failed to assign photo to species');
    } finally {
      setIsAssigning(false);
    }
  };

  const getDropZoneStyles = () => {
    let baseStyles = "border-2 border-dashed transition-all duration-200 ";

    if (isAssigning) {
      return baseStyles + "border-blue-400 bg-blue-50";
    } else if (isOver && canDrop) {
      return baseStyles + "border-green-400 bg-green-50";
    } else if (canDrop) {
      const multiSelectStyles = selectedPhotos.size > 0 ? "border-blue-400 bg-blue-25 " : "";
      return baseStyles + multiSelectStyles + "border-gray-300 hover:border-blue-300 hover:bg-blue-50";
    } else {
      return baseStyles + "border-gray-200";
    }
  };

  return (
    <div
      ref={drop}
      className={`${getDropZoneStyles()} rounded-lg p-3 bg-white`}
    >
      <div className="flex items-start justify-between mb-2">
        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-sm truncate">{species.name}</h4>
          {species.common_name && (
            <p className="text-xs text-gray-500 truncate">{species.common_name}</p>
          )}
          <div className="flex items-center gap-2 mt-1">
            <Badge variant="outline" className="text-xs">
              {species.category}
            </Badge>
            <span className="text-xs text-gray-500">
              {assignedPhotos.length} photo{assignedPhotos.length !== 1 ? 's' : ''}
            </span>
          </div>
          {selectedPhotos.size > 0 && (
            <p className="text-xs text-blue-600 font-medium mt-1">
              Drop to assign {selectedPhotos.size} photo{selectedPhotos.size > 1 ? 's' : ''}
            </p>
          )}
        </div>
        {isAssigning && (
          <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
        )}
      </div>

      {/* Assigned Photos Preview */}
      {assignedPhotos.length > 0 && (
        <div className="flex gap-1 mb-2">
          {assignedPhotos.slice(0, 3).map((photo) => (
            <img
              key={photo.id}
              src={photo.url || ''}
              alt="Assigned photo"
              className="w-8 h-8 object-cover rounded border"
            />
          ))}
          {assignedPhotos.length > 3 && (
            <div className="w-8 h-8 bg-gray-100 rounded border flex items-center justify-center">
              <span className="text-xs text-gray-500">+{assignedPhotos.length - 3}</span>
            </div>
          )}
        </div>
      )}

      {/* Drop Zone Indicator */}
      <div className="text-center py-2">
        {isAssigning ? (
          <span className="text-xs text-blue-600">Assigning...</span>
        ) : isOver && canDrop ? (
          <span className="text-xs text-green-600">Drop to assign</span>
        ) : (
          <span className="text-xs text-gray-500">Drop photos here</span>
        )}
      </div>

      {/* View Photos Button */}
      {assignedPhotos.length > 0 && (
        <Button
          variant="outline"
          size="sm"
          className="w-full h-7 text-xs"
          onClick={onViewPhotos}
        >
          <Eye className="w-3 h-3 mr-1" />
          View Photos ({assignedPhotos.length})
        </Button>
      )}
    </div>
  );
}

export function EnhancedPhotoAssignment() {
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [species, setSpecies] = useState<Species[]>([]);
  const [speciesThumbnails, setSpeciesThumbnails] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [regionFilter, setRegionFilter] = useState<string>('all');
  const [showAssignDialog, setShowAssignDialog] = useState(false);
  const [assigning, setAssigning] = useState(false);
  const [creatingSpecies, setCreatingSpecies] = useState(false);
  const [newSpeciesName, setNewSpeciesName] = useState('');
  const [showSpeciesPreview, setShowSpeciesPreview] = useState(false);
  const [aiGeneratedData, setAiGeneratedData] = useState<any>(null);
  const [publishImmediately, setPublishImmediately] = useState(true);
  const [publishSpeciesOnAssign, setPublishSpeciesOnAssign] = useState(true);
  const [showFullSizeImage, setShowFullSizeImage] = useState(false);
  const [selectedSpeciesForPreview, setSelectedSpeciesForPreview] = useState<Species | null>(null);
  const [showSpeciesDetail, setShowSpeciesDetail] = useState(false);
  const [enlargedThumbnail, setEnlargedThumbnail] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'species' | 'locations'>('species');
  const [selectedPhotos, setSelectedPhotos] = useState<Set<string>>(new Set());
  const [isMultiSelectMode, setIsMultiSelectMode] = useState(false);

  // Multi-select helper functions
  const togglePhotoSelection = (photoId: string) => {
    setSelectedPhotos(prev => {
      const newSet = new Set(prev);
      if (newSet.has(photoId)) {
        newSet.delete(photoId);
      } else {
        newSet.add(photoId);
      }
      return newSet;
    });
  };

  const selectAllPhotos = () => {
    setSelectedPhotos(new Set(photos.map(p => p.id)));
  };

  const clearSelection = () => {
    setSelectedPhotos(new Set());
  };

  const toggleMultiSelectMode = () => {
    setIsMultiSelectMode(!isMultiSelectMode);
    if (isMultiSelectMode) {
      clearSelection();
    }
  };

  // Bulk assignment functions
  const handleBulkAssignToSpecies = async (speciesId: string) => {
    if (selectedPhotos.size === 0) return;

    setAssigning(true);
    try {
      const photoIds = Array.from(selectedPhotos);

      // Update all selected photos
      const { error: photoError } = await supabase
        .from('photos_v2')
        .update({
          species_id: speciesId,
          published: publishSpeciesOnAssign
        })
        .in('id', photoIds);

      if (photoError) throw photoError;

      // If publishSpeciesOnAssign is enabled, also publish the species
      if (publishSpeciesOnAssign) {
        const { error: speciesError } = await supabase
          .from('species_v2')
          .update({ published: true })
          .eq('id', speciesId);

        if (speciesError) {
          console.error('Error publishing species:', speciesError);
          // Don't fail the whole operation if species publish fails
        }
      }

      const assignedSpecies = species.find(s => s.id === speciesId);
      toast.success(`${photoIds.length} photos assigned to ${assignedSpecies?.name}!`);

      clearSelection();
      setShowAssignDialog(false);
      loadData();
    } catch (error) {
      console.error('Error bulk assigning photos:', error);
      toast.error('Failed to assign photos');
    } finally {
      setAssigning(false);
    }
  };
  const [locationSearchTerm, setLocationSearchTerm] = useState('');

  // AI Review states
  const [isAIReviewing, setIsAIReviewing] = useState(false);
  const [aiReviewResults, setAiReviewResults] = useState<any[]>([]);
  const [showAIResults, setShowAIResults] = useState(false);
  const [previewPhoto, setPreviewPhoto] = useState<any>(null);

  // Fetch locations data
  const { data: locations = [], isLoading: locationsLoading, refetch: refetchLocations } = useLocations({
    published: true,
    limit: 50
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      // Load unassigned photos
      const { data: photosData, error: photosError } = await supabase
        .from('photos_v2')
        .select('*')
        .is('species_id', null)
        .eq('published', true)
        .order('created_at', { ascending: false });

      if (photosError) throw photosError;

      // Load all species (including unpublished for assignment)
      const { data: speciesData, error: speciesError } = await supabase
        .from('species_v2')
        .select(`
          id, name, common_name, scientific_name, category, conservation_status,
          published, description, regions
        `)
        .order('name');

      if (speciesError) throw speciesError;

      console.log(`📊 Loaded ${speciesData?.length || 0} total species from database`);

      // Load species thumbnails
      const { data: thumbnailData, error: thumbnailError } = await supabase
        .from('photos_v2')
        .select('species_id, url')
        .not('species_id', 'is', null)
        .eq('published', true)
        .order('created_at', { ascending: false });

      if (thumbnailError) throw thumbnailError;

      // Create thumbnail mapping (first photo for each species)
      const thumbnailMap: Record<string, string> = {};
      thumbnailData?.forEach(photo => {
        if (photo.species_id && photo.url && !thumbnailMap[photo.species_id]) {
          thumbnailMap[photo.species_id] = photo.url;
        }
      });

      setPhotos(photosData || []);
      setSpecies(speciesData || []);
      setSpeciesThumbnails(thumbnailMap);
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  // Enhanced filtering with geographic data
  const filteredSpecies = useMemo(() => {
    const filtered = species.filter(s => {
      const matchesSearch = !searchTerm ||
        s.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        s.scientific_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        s.common_name?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCategory = categoryFilter === 'all' || s.category === categoryFilter;

      const matchesRegion = regionFilter === 'all' ||
        s.regions?.includes(regionFilter);

      return matchesSearch && matchesCategory && matchesRegion;
    });

    console.log(`🔍 Species filtering: ${species.length} total → ${filtered.length} filtered (search: "${searchTerm}", category: "${categoryFilter}", region: "${regionFilter}")`);
    return filtered;
  }, [species, searchTerm, categoryFilter, regionFilter]);

  // Get unique regions for filter
  const availableRegions = useMemo(() => {
    const regions = new Set<string>();
    species.forEach(s => {
      if (s.regions) {
        // Split regions by common delimiters and add each region
        s.regions.split(/[,;]/).forEach(region => {
          const trimmed = region.trim();
          if (trimmed) regions.add(trimmed);
        });
      }
    });
    return Array.from(regions).sort();
  }, [species]);

  const handleGenerateSpeciesPreview = async () => {
    if (!newSpeciesName.trim()) {
      toast.error('Please enter a species name');
      return;
    }

    setCreatingSpecies(true);
    try {
      console.log('📋 generateAISpeciesMetadata input:', [newSpeciesName.trim()], selectedPhoto?.url);
      const result = await generateAISpeciesMetadata([newSpeciesName.trim()], selectedPhoto?.url);
      console.log('📋 generateAISpeciesMetadata result:', result);
      
      if (result && result.length > 0) {
        console.log('✅ Using AI metadata:', result[0]);
        setAiGeneratedData(result[0]);
        setShowSpeciesPreview(true);
      } else {
        toast.error('Failed to generate species data');
      }
    } catch (error) {
      console.error('Error generating species preview:', error);
      toast.error('Failed to generate species preview');
    } finally {
      setCreatingSpecies(false);
    }
  };

  const handleCreateAndAssignSpecies = async () => {
    if (!aiGeneratedData || !selectedPhoto) return;

    setAssigning(true);
    try {
      // Helper function to ensure array fields are properly formatted
      const ensureArray = (value: any): any[] => {
        if (!value) return [];
        if (Array.isArray(value)) return value;
        if (typeof value === 'string') {
          // Try to parse as JSON array first
          try {
            const parsed = JSON.parse(value);
            if (Array.isArray(parsed)) return parsed;
          } catch (e) {
            // If not JSON, split by common delimiters
            return value.split(/[,;]/).map(item => item.trim()).filter(item => item.length > 0);
          }
        }
        return [value];
      };

      // Create the species with enhanced geographic data
      const speciesData = {
        // Basic fields
        name: aiGeneratedData.name,
        common_name: aiGeneratedData.common_name,
        scientific_name: aiGeneratedData.scientific_name,
        family: aiGeneratedData.family,
        description: aiGeneratedData.description,
        habitat: aiGeneratedData.habitat,
        diet: aiGeneratedData.diet,
        behavior: aiGeneratedData.behavior,
        conservation_status: aiGeneratedData.conservation_status,
        category: aiGeneratedData.category,

        // Size and physical characteristics
        size_cm: aiGeneratedData.size_cm?.toString(),
        weight_g: aiGeneratedData.weight_g?.toString(),
        size_description: aiGeneratedData.size_description,
        lifespan_years: typeof aiGeneratedData.lifespan_years === 'number' ? aiGeneratedData.lifespan_years : parseInt(aiGeneratedData.lifespan_years) || null,

        // Biological information
        migration_pattern: aiGeneratedData.migration_pattern,
        breeding_season: aiGeneratedData.breeding_season,
        threat_level: aiGeneratedData.threat_level,
        population_trend: aiGeneratedData.population_trend,
        conservation_actions: aiGeneratedData.conservation_actions,

        // Taxonomy
        taxonomy_order: aiGeneratedData.taxonomy_order,
        taxonomy_subfamily: aiGeneratedData.taxonomy_subfamily,
        taxonomy_genus: aiGeneratedData.taxonomy_genus,
        common_group: aiGeneratedData.common_group,
        related_groups: ensureArray(aiGeneratedData.related_groups),

        // External IDs
        itis_tsn: aiGeneratedData.itis_tsn,
        gbif_id: aiGeneratedData.gbif_id,
        ebird_code: aiGeneratedData.ebird_code,
        inat_id: aiGeneratedData.inat_id,
        avibase_id: aiGeneratedData.avibase_id,

        // Geographic and metadata - ensure arrays are properly formatted
        tags: ensureArray(aiGeneratedData.tags),
        regions: aiGeneratedData.regions,
        notes: aiGeneratedData.notes,

        // Fun facts - use correct field names and ensure proper format
        ai_fun_facts: ensureArray(aiGeneratedData.fun_facts || aiGeneratedData.ai_fun_facts),
        fun_facts_field: ensureArray(aiGeneratedData.fun_facts || aiGeneratedData.ai_fun_facts),

        // AI metadata
        published: publishImmediately,
        ai_generated: true,
        ai_confidence: 0.8,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('🔍 Species data to save:', speciesData);

      const { data: newSpecies, error: speciesError } = await supabase
        .from('species_v2')
        .insert([speciesData])
        .select()
        .single();

      if (speciesError) throw speciesError;

      // Assign photo to the new species
      const { error: photoError } = await supabase
        .from('photos_v2')
        .update({ 
          species_id: newSpecies.id,
          published: publishSpeciesOnAssign 
        })
        .eq('id', selectedPhoto.id);

      if (photoError) throw photoError;

      toast.success(`Species "${aiGeneratedData.name}" created and photo assigned!`);
      
      // Reset state and reload data
      setShowSpeciesPreview(false);
      setShowAssignDialog(false);
      setSelectedPhoto(null);
      setNewSpeciesName('');
      setAiGeneratedData(null);
      loadData();
    } catch (error) {
      console.error('Error creating species:', error);
      toast.error('Failed to create species and assign photo');
    } finally {
      setAssigning(false);
    }
  };

  const handleAssignToExistingSpecies = async (speciesId: string) => {
    if (!selectedPhoto) return;

    setAssigning(true);
    try {
      // Update photo assignment
      const { error: photoError } = await supabase
        .from('photos_v2')
        .update({
          species_id: speciesId,
          published: publishSpeciesOnAssign
        })
        .eq('id', selectedPhoto.id);

      if (photoError) throw photoError;

      // If publishSpeciesOnAssign is enabled, also publish the species
      if (publishSpeciesOnAssign) {
        const { error: speciesError } = await supabase
          .from('species_v2')
          .update({ published: true })
          .eq('id', speciesId);

        if (speciesError) {
          console.error('Error publishing species:', speciesError);
          // Don't fail the whole operation if species publish fails
        }
      }

      const assignedSpecies = species.find(s => s.id === speciesId);
      toast.success(`Photo assigned to ${assignedSpecies?.name}!`);

      setShowAssignDialog(false);
      setSelectedPhoto(null);
      loadData();
    } catch (error) {
      console.error('Error assigning photo:', error);
      toast.error('Failed to assign photo');
    } finally {
      setAssigning(false);
    }
  };

  const openSpeciesDetail = (species: Species) => {
    setSelectedSpeciesForPreview(species);
    setShowSpeciesDetail(true);
  };

  // Test AI on single photo
  const testSinglePhoto = async () => {
    if (photos.length === 0) {
      toast.error('No photos available to test');
      return;
    }

    const firstPhoto = photos[0];
    toast.info(`Testing AI analysis on: ${firstPhoto.title}`);

    try {
      await testSinglePhotoAI(firstPhoto.id);
      toast.success('Check console for detailed AI analysis results');
    } catch (error) {
      console.error('Test failed:', error);
      toast.error('AI test failed. Check console for details.');
    }
  };

  // Test AI on the Glossy Black Thrush photo
  const testThrushPhoto = async () => {
    toast.info('Testing AI on Glossy Black Thrush photo...');

    try {
      await testSpecificPhoto('glossy-black-thrush');
      toast.success('Check console for Glossy Black Thrush AI analysis results');
    } catch (error) {
      console.error('Test failed:', error);
      toast.error('AI test failed. Check console for details.');
    }
  };

  // Test AI on the Crowned Woodnymph photo
  const testWoodnymph = async () => {
    toast.info('Testing AI on Crowned Woodnymph photo...');

    try {
      await testWoodnymphPhoto();
      toast.success('Check console for Crowned Woodnymph AI analysis results');
    } catch (error) {
      console.error('Test failed:', error);
      toast.error('AI test failed. Check console for details.');
    }
  };

  // Show cache statistics
  const showCacheStats = () => {
    const stats = getCacheStats();
    console.log(`📊 AI CACHE STATISTICS:`);
    console.log(`   Total cached photos: ${stats.totalCached}`);
    console.log(`   Cached photo IDs:`, stats.cacheEntries);
    toast.info(`Cache: ${stats.totalCached} photos cached. Check console for details.`);
  };

  // Clear all cached suggestions
  const clearCache = () => {
    clearAllCachedSuggestions();
    toast.success('All cached AI suggestions cleared');
  };

  // Reject suggestions for current photo
  const rejectCurrentSuggestions = () => {
    if (selectedPhoto) {
      rejectAISuggestions(selectedPhoto.id);
      toast.success('AI suggestions rejected. Photo can be re-analyzed.');
      // Refresh suggestions
      setAiSuggestions([]);
    } else {
      toast.error('No photo selected');
    }
  };

  // Load stored AI review results
  const loadStoredAIReviews = async () => {
    try {
      console.log('📋 Loading stored AI review results...');

      const { data: photosWithSuggestions, error } = await supabase
        .from('photos_v2')
        .select(`
          id,
          url,
          title,
          filename,
          ai_suggested_id,
          ai_confidence,
          ai_reviewed,
          species_v2:ai_suggested_id (
            id,
            common_name,
            name,
            scientific_name
          )
        `)
        .not('ai_suggested_id', 'is', null)
        .eq('ai_reviewed', false)
        .order('created_at', { ascending: false })
        .limit(200);

      if (error) throw error;

      const results = photosWithSuggestions?.map(photo => ({
        photoId: photo.id,
        photoUrl: photo.url,
        photoTitle: photo.title || photo.filename,
        filename: photo.filename,
        suggestion: {
          speciesId: photo.ai_suggested_id,
          speciesName: photo.species_v2?.common_name || photo.species_v2?.name || 'Unknown Species',
          scientificName: photo.species_v2?.scientific_name,
          confidenceScore: (photo.ai_confidence || 0) * 100
        },
        success: true
      })) || [];

      console.log(`📋 Found ${results.length} stored AI review results`);
      setAiReviewResults(results);
      setShowAIResults(true);

      if (results.length === 0) {
        toast.info('No pending AI reviews found. Run a new AI review to generate suggestions.');
      } else {
        toast.success(`Loaded ${results.length} pending AI reviews`);
      }

    } catch (error) {
      console.error('Error loading stored AI reviews:', error);
      toast.error('Failed to load stored AI reviews');
    }
  };

  // AI Review Functions
  const handleAIReview = async () => {
    if (photos.length === 0) {
      toast.error('No unassigned photos to review');
      return;
    }

    setIsAIReviewing(true);
    setAiReviewResults([]);

    try {
      const results = [];
      const batchSize = 3; // Smaller batches for better processing
      const photosToProcess = photos; // Process all photos

      console.log(`🔍 Starting AI review of ${photosToProcess.length} photos...`);

      for (let i = 0; i < photosToProcess.length; i += batchSize) {
        const batch = photosToProcess.slice(i, i + batchSize);
        const progress = Math.round(((i + batch.length) / photosToProcess.length) * 100);

        toast.loading(`Processing photos ${i + 1}-${Math.min(i + batchSize, photosToProcess.length)} of ${photosToProcess.length} (${progress}%)...`, {
          id: 'ai-review-progress'
        });

        const batchPromises = batch.map(async (photo) => {
          try {
            // Check if photo already has AI suggestion to avoid re-processing
            const { data: existingPhoto } = await supabase
              .from('photos_v2')
              .select('ai_suggested_id, ai_reviewed')
              .eq('id', photo.id)
              .single();

            if (existingPhoto?.ai_suggested_id && !existingPhoto.ai_reviewed) {
              console.log(`⏭️ Photo ${photo.id} already has pending AI suggestion, skipping...`);
              return null;
            }

            console.log(`🔍 Analyzing photo ${photo.id}...`);
            const suggestions = await getAISuggestions(photo.id);

            if (suggestions.length > 0) {
              const topSuggestion = suggestions[0];

              // Only proceed if confidence is reasonable (increased threshold for better quality)
              if (topSuggestion.confidenceScore >= 50) {
                // Update photo with AI suggestion and mark for review
                const { error } = await supabase
                  .from('photos_v2')
                  .update({
                    ai_suggested_id: topSuggestion.speciesId,
                    ai_confidence: topSuggestion.confidenceScore / 100,
                    ai_reviewed: false
                  })
                  .eq('id', photo.id);

                if (error) throw error;

                console.log(`✅ AI suggestion for photo ${photo.id}: ${topSuggestion.speciesName} (${topSuggestion.confidenceScore}%)`);

                return {
                  photoId: photo.id,
                  photoUrl: photo.url,
                  photoTitle: photo.title,
                  filename: photo.filename,
                  suggestion: topSuggestion,
                  success: true
                };
              } else {
                console.log(`⚠️ Low confidence (${topSuggestion.confidenceScore}%) for photo ${photo.id}, skipping...`);
                return null;
              }
            } else {
              console.log(`❌ No suggestions found for photo ${photo.id}`);
              return null;
            }
          } catch (error) {
            console.error(`❌ Error processing photo ${photo.id}:`, error);
            return {
              photoId: photo.id,
              photoUrl: photo.url,
              photoTitle: photo.title,
              error: error.message,
              success: false
            };
          }
        });

        const batchResults = await Promise.all(batchPromises);
        const validResults = batchResults.filter(result => result !== null);
        results.push(...validResults);

        // Longer delay between batches to avoid rate limiting
        if (i + batchSize < photosToProcess.length) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }

      toast.dismiss('ai-review-progress');

      console.log('🔍 AI Review Results:', results);
      setAiReviewResults(results);
      setShowAIResults(true);

      const successCount = results.filter(r => r.success).length;
      const errorCount = results.filter(r => !r.success).length;

      if (successCount > 0) {
        toast.success(`AI review complete! ${successCount} photos identified${errorCount > 0 ? `, ${errorCount} errors` : ''}.`);
      } else {
        toast.warning('AI review complete, but no confident identifications found. Try adjusting photo quality or lighting.');
      }

    } catch (error) {
      console.error('Error during AI review:', error);
      toast.error('AI review failed. Please try again.');
    } finally {
      setIsAIReviewing(false);
    }
  };

  const handleAcceptAISuggestion = async (result: any) => {
    try {
      // Update photo assignment
      const { error: photoError } = await supabase
        .from('photos_v2')
        .update({
          species_id: result.suggestion.speciesId,
          ai_reviewed: true,
          published: publishSpeciesOnAssign
          // Note: needs_review field may not exist in current schema
        })
        .eq('id', result.photoId);

      if (photoError) throw photoError;

      // If publishSpeciesOnAssign is enabled, also publish the species
      if (publishSpeciesOnAssign) {
        const { error: speciesError } = await supabase
          .from('species_v2')
          .update({ published: true })
          .eq('id', result.suggestion.speciesId);

        if (speciesError) {
          console.error('Error publishing species:', speciesError);
          // Don't fail the whole operation if species publish fails
        }
      }

      toast.success(`Photo assigned to ${result.suggestion.speciesName}!`);

      // Remove from results but don't reload data to maintain scroll position
      setAiReviewResults(prev => prev.filter(r => r.photoId !== result.photoId));

      // Update the photos count without full reload
      setPhotos(prev => prev.filter(p => p.id !== result.photoId));
    } catch (error) {
      console.error('Error accepting AI suggestion:', error);
      toast.error('Failed to assign photo');
    }
  };

  const handleRejectAISuggestion = async (result: any) => {
    try {
      const { error } = await supabase
        .from('photos_v2')
        .update({
          ai_reviewed: true,
          ai_suggested_id: null,
          ai_confidence: null
          // Note: needs_review field may not exist in current schema
        })
        .eq('id', result.photoId);

      if (error) throw error;

      // Clear cached suggestions so photo can be re-analyzed
      rejectAISuggestions(result.photoId);

      toast.success('AI suggestion rejected. Photo can be re-analyzed.');

      // Remove from results
      setAiReviewResults(prev => prev.filter(r => r.photoId !== result.photoId));
    } catch (error) {
      console.error('Error rejecting AI suggestion:', error);
      toast.error('Failed to reject suggestion');
    }
  };

  const handleAcceptAllAISuggestions = async () => {
    const successfulResults = aiReviewResults.filter(r => r.success);

    if (successfulResults.length === 0) {
      toast.error('No successful suggestions to accept');
      return;
    }

    try {
      for (const result of successfulResults) {
        await handleAcceptAISuggestion(result);
      }

      setShowAIResults(false);
      toast.success(`All ${successfulResults.length} suggestions accepted!`);
    } catch (error) {
      console.error('Error accepting all suggestions:', error);
      toast.error('Failed to accept all suggestions');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading photos and species...</span>
      </div>
    );
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="h-screen flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b bg-white">
          <div>
            <h2 className="text-2xl font-bold">Photo Assignment Center</h2>
            <p className="text-muted-foreground">
              Drag photos to species and locations • View and manage assignments
            </p>
          </div>
          <div className="flex items-center gap-4">


            <Button
              onClick={showCacheStats}
              className="flex items-center gap-2"
              variant="outline"
              size="sm"
            >
              <Database className="h-4 w-4" />
              Cache Stats
            </Button>

            <Button
              onClick={clearCache}
              className="flex items-center gap-2"
              variant="outline"
              size="sm"
            >
              <Trash2 className="h-4 w-4" />
              Clear Cache
            </Button>
            <Button
              onClick={handleAIReview}
              disabled={isAIReviewing || photos.length === 0}
              className="flex items-center gap-2"
              variant="outline"
            >
              {isAIReviewing ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Wand2 className="h-4 w-4" />
              )}
              AI Review ({photos.length})
            </Button>

            <Button
              onClick={loadStoredAIReviews}
              className="flex items-center gap-2"
              variant="outline"
            >
              <History className="h-4 w-4" />
              View Stored Reviews
            </Button>

            <Button
              onClick={() => {
                console.log('🧪 Testing AI Results dialog...');
                setAiReviewResults([
                  {
                    photoId: 'test-1',
                    photoUrl: 'https://example.com/test.jpg',
                    photoTitle: 'Test Photo',
                    suggestion: {
                      speciesId: 'test-species',
                      speciesName: 'Test Bird',
                      confidenceScore: 85
                    },
                    success: true
                  }
                ]);
                setShowAIResults(true);
              }}
              variant="secondary"
              size="sm"
            >
              Test Dialog
            </Button>
            <Badge variant="outline" className="text-lg px-3 py-1">
              {photos.length} unassigned photos
            </Badge>
          </div>
        </div>

        {/* Main Layout */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Panel - Unassigned Photos */}
          <div className="w-1/3 border-r bg-gray-50 flex flex-col">
            <div className="p-4 border-b bg-white">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold flex items-center gap-2">
                  <Camera className="w-4 h-4" />
                  Unassigned Photos ({photos.length})
                </h3>
                <div className="flex items-center gap-2">
                  <Button
                    onClick={toggleMultiSelectMode}
                    variant={isMultiSelectMode ? "default" : "outline"}
                    size="sm"
                    className="flex items-center gap-1"
                  >
                    <CheckCircle className="w-3 h-3" />
                    {isMultiSelectMode ? 'Exit' : 'Select'}
                  </Button>
                </div>
              </div>

              {/* Multi-select controls */}
              {isMultiSelectMode && (
                <div className="mt-3 flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    <span className="text-muted-foreground">
                      {selectedPhotos.size} of {photos.length} selected
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      onClick={selectAllPhotos}
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2 text-xs"
                    >
                      Select All
                    </Button>
                    <Button
                      onClick={clearSelection}
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2 text-xs"
                      disabled={selectedPhotos.size === 0}
                    >
                      Clear
                    </Button>
                  </div>
                </div>
              )}
            </div>
            <div className="flex-1 overflow-y-auto p-4">
              {photos.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                  <ImageIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No unassigned photos</p>
                  <p className="text-sm">All photos have been assigned!</p>
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-3">
                  {photos.map((photo) => (
                    <DraggablePhoto
                      key={photo.id}
                      photo={photo}
                      isSelected={selectedPhotos.has(photo.id)}
                      isMultiSelectMode={isMultiSelectMode}
                      onToggleSelect={() => togglePhotoSelection(photo.id)}
                      onClick={() => {
                        if (!isMultiSelectMode) {
                          setSelectedPhoto(photo);
                          setShowAssignDialog(true);
                        }
                      }}
                    />
                  ))}
                </div>
              )}

              {/* Bulk assignment button */}
              {selectedPhotos.size > 0 && (
                <div className="mt-4">
                  <Button
                    onClick={() => {
                      // Use the first selected photo for the dialog
                      const firstSelectedId = Array.from(selectedPhotos)[0];
                      const firstPhoto = photos.find(p => p.id === firstSelectedId);
                      if (firstPhoto) {
                        setSelectedPhoto(firstPhoto);
                        setShowAssignDialog(true);
                      }
                    }}
                    className="w-full flex items-center gap-2"
                  >
                    <Tag className="w-4 h-4" />
                    Assign {selectedPhotos.size} Selected Photo{selectedPhotos.size > 1 ? 's' : ''}
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Right Panel - Assignment Targets */}
          <div className="flex-1 flex flex-col">
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'species' | 'locations')} className="flex-1 flex flex-col">
              <div className="p-4 border-b bg-white">
                <TabsList className="grid w-full grid-cols-2 max-w-md">
                  <TabsTrigger value="species" className="flex items-center gap-2">
                    <Brain className="w-4 h-4" />
                    Species ({species.length})
                  </TabsTrigger>
                  <TabsTrigger value="locations" className="flex items-center gap-2">
                    <MapPin className="w-4 h-4" />
                    Locations ({locations.length})
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="species" className="flex-1 flex flex-col m-0">
                <div className="p-4 border-b bg-white">
                  <div className="space-y-3">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        placeholder="Search species..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    <div className="flex gap-2">
                      <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                        <SelectTrigger className="flex-1">
                          <SelectValue placeholder="Category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Categories</SelectItem>
                          <SelectItem value="Birds">Birds</SelectItem>
                          <SelectItem value="Mammals">Mammals</SelectItem>
                          <SelectItem value="Reptiles">Reptiles</SelectItem>
                          <SelectItem value="Amphibians">Amphibians</SelectItem>
                          <SelectItem value="Fish">Fish</SelectItem>
                          <SelectItem value="Insects">Insects</SelectItem>
                        </SelectContent>
                      </Select>
                      <Select value={regionFilter} onValueChange={setRegionFilter}>
                        <SelectTrigger className="flex-1">
                          <SelectValue placeholder="Region" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Regions</SelectItem>
                          {availableRegions.map((region) => (
                            <SelectItem key={region} value={region}>
                              {region}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
                <div className="flex-1 overflow-y-auto p-4">
                  {filteredSpecies.length > 0 ? (
                    <>
                      <div className="space-y-2">
                        {filteredSpecies.map((speciesItem) => (
                          <SpeciesDropTarget
                            key={speciesItem.id}
                            species={speciesItem}
                            photoCount={speciesThumbnails[speciesItem.id] ? 1 : 0}
                            onPhotoAssigned={loadData}
                            onViewPhotos={() => openSpeciesDetail(speciesItem)}
                            publishSpeciesOnAssign={publishSpeciesOnAssign}
                            selectedPhotos={selectedPhotos}
                            onBulkAssign={handleBulkAssignToSpecies}
                          />
                        ))}
                      </div>
                      {filteredSpecies.length > 10 && (
                        <div className="text-center text-sm text-muted-foreground mt-4 p-2 bg-blue-50 rounded-lg">
                          📜 Scroll to see all {filteredSpecies.length} species
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>No species found</p>
                      <p className="text-sm">Try adjusting your search or filters</p>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="locations" className="flex-1 flex flex-col m-0">
                <div className="p-4 border-b bg-white">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search locations..."
                      value={locationSearchTerm}
                      onChange={(e) => setLocationSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex-1 overflow-y-auto p-4">
                  {locationsLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      <span>Loading locations...</span>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {locations
                        .filter(location =>
                          !locationSearchTerm ||
                          location.name.toLowerCase().includes(locationSearchTerm.toLowerCase()) ||
                          location.state_province?.toLowerCase().includes(locationSearchTerm.toLowerCase()) ||
                          location.country?.toLowerCase().includes(locationSearchTerm.toLowerCase())
                        )
                        .map((location) => (
                          <LocationDropZone
                            key={location.id}
                            location={location}
                            onPhotoAssigned={() => {
                              loadData();
                              refetchLocations();
                            }}
                            className="h-auto"
                            showPhotos={true}
                          />
                        ))}
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Enhanced Assignment Dialog */}
      <Dialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5" />
              {selectedPhotos.size > 0
                ? `Assign ${selectedPhotos.size} Photo${selectedPhotos.size > 1 ? 's' : ''} to Species`
                : 'Assign Photo to Species'
              }
            </DialogTitle>
          </DialogHeader>

          {selectedPhoto && (
            <div className="space-y-6">
              {/* Photo Display - Single or Multiple */}
              {selectedPhotos.size > 0 ? (
                <div className="space-y-4">
                  <div className="text-sm text-muted-foreground">
                    Selected {selectedPhotos.size} photo{selectedPhotos.size > 1 ? 's' : ''} for assignment:
                  </div>
                  <div className="grid grid-cols-4 gap-3 max-h-48 overflow-y-auto">
                    {Array.from(selectedPhotos).map(photoId => {
                      const photo = photos.find(p => p.id === photoId);
                      if (!photo) return null;
                      return (
                        <div key={photo.id} className="relative group">
                          <img
                            src={photo.url || ''}
                            alt={photo.title || 'Selected photo'}
                            className="w-full h-20 object-cover rounded-lg cursor-pointer"
                            onClick={() => setShowFullSizeImage(true)}
                          />
                          <div className="absolute bottom-1 left-1 bg-black bg-opacity-70 text-white text-xs px-1 py-0.5 rounded text-[10px]">
                            {photo.title || `${photo.id.slice(0, 6)}`}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ) : (
                <div className="relative">
                  <div className="relative group bg-gray-100 rounded-lg overflow-hidden">
                    <img
                      src={selectedPhoto.url || ''}
                      alt="Photo to assign"
                      className="w-full h-64 object-contain rounded-lg cursor-pointer"
                      onClick={() => setShowFullSizeImage(true)}
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center rounded-lg">
                      <ZoomIn className="h-8 w-8 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                    </div>
                    {/* Filename overlay */}
                    <div className="absolute bottom-2 left-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                      {selectedPhoto.title || `Photo ${selectedPhoto.id.slice(0, 8)}`}
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">
                    Uploaded: {new Date(selectedPhoto.created_at).toLocaleDateString()}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => setShowFullSizeImage(true)}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Full Size
                  </Button>
                </div>
              )}

              {/* Enhanced Search and Filters */}
                <div className="space-y-4">
                <div className="flex gap-4">
                  <div className="flex-1">
                    <Input
                      placeholder="Search species by name, scientific name..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="Birds">Birds</SelectItem>
                      <SelectItem value="Mammals">Mammals</SelectItem>
                      <SelectItem value="Reptiles">Reptiles</SelectItem>
                      <SelectItem value="Amphibians">Amphibians</SelectItem>
                      <SelectItem value="Fish">Fish</SelectItem>
                      <SelectItem value="Invertebrates">Invertebrates</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={regionFilter} onValueChange={setRegionFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="All Regions" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Regions</SelectItem>
                      {availableRegions.map(region => (
                        <SelectItem key={region} value={region}>{region}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Enhanced Species List with Thumbnails */}
              <div className="space-y-4">
                <h3 className="font-semibold">Select Existing Species ({filteredSpecies.length})</h3>
                <div className="max-h-96 overflow-y-auto space-y-2">
                  {filteredSpecies.map((species) => (
                    <div
                      key={species.id}
                      className="flex items-center gap-4 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer group"
                      onClick={() => {
                        if (selectedPhotos.size > 0) {
                          handleBulkAssignToSpecies(species.id);
                        } else {
                          handleAssignToExistingSpecies(species.id);
                        }
                      }}
                    >
                      {/* Species Thumbnail */}
                      <div className="relative">
                        {speciesThumbnails[species.id] ? (
                          <div className="relative">
                            <img
                              src={speciesThumbnails[species.id]}
                              alt={species.name}
                              className="w-16 h-16 object-cover rounded-lg cursor-pointer"
                              onClick={(e) => {
                                e.stopPropagation();
                                setEnlargedThumbnail(speciesThumbnails[species.id]);
                              }}
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center rounded-lg">
                              <ZoomIn className="h-4 w-4 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                            </div>
                          </div>
                        ) : (
                          <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                            <ImageIcon className="h-6 w-6 text-gray-400" />
                          </div>
                        )}
                      </div>

                      {/* Species Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium truncate">{species.name}</h4>
                          {!species.published && (
                            <Badge variant="secondary" className="text-xs">Draft</Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground italic truncate">
                          {species.scientific_name}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          {species.category && (
                            <Badge variant="outline" className="text-xs">{species.category}</Badge>
                          )}
                          {species.regions && (
                            <Badge variant="outline" className="text-xs flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              {species.regions.split(',')[0].trim()}
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            openSpeciesDetail(species);
                          }}
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                        <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* AI Species Creation */}
              <div className="border-t pt-4">
                <div className="flex items-center gap-2 mb-4">
                  <AlertCircle className="h-5 w-5 text-blue-500" />
                  <span className="font-semibold">Can't find the species?</span>
                </div>
                <p className="text-sm text-muted-foreground mb-4">
                  Type the common name and AI will create the species with all required details.
                </p>
                <div className="flex gap-2">
                  <Input
                    placeholder="e.g., Red Fox, Bald Eagle, etc."
                    value={newSpeciesName}
                    onChange={(e) => setNewSpeciesName(e.target.value)}
                    className="flex-1"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter' && !creatingSpecies) {
                        handleGenerateSpeciesPreview();
                      }
                    }}
                  />
                  <Button
                    onClick={handleGenerateSpeciesPreview}
                    disabled={creatingSpecies || !newSpeciesName.trim()}
                    className="flex items-center gap-2"
                  >
                    {creatingSpecies ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Brain className="h-4 w-4" />
                    )}
                    Generate with AI
                  </Button>
                </div>
              </div>

              {/* Assignment Options */}
              <div className="border-t pt-4 space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="publish-species"
                    checked={publishSpeciesOnAssign}
                    onCheckedChange={setPublishSpeciesOnAssign}
                  />
                  <label htmlFor="publish-species" className="text-sm">
                    Publish species immediately when assigning photo
                  </label>
                </div>
              </div>
            </div>
          )}
        )}
        </DialogContent>
      </Dialog>

      {/* AI Species Preview Dialog */}
      <Dialog open={showSpeciesPreview} onOpenChange={setShowSpeciesPreview}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-blue-500" />
              Review AI-Generated Species Data
            </DialogTitle>
          </DialogHeader>

          {aiGeneratedData && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Common Name</label>
                  <Input value={aiGeneratedData.name} readOnly className="mt-1" />
                </div>
                <div>
                  <label className="text-sm font-medium">Scientific Name</label>
                  <Input value={aiGeneratedData.scientific_name} readOnly className="mt-1" />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Category</label>
                  <Input value={aiGeneratedData.category} readOnly className="mt-1" />
                </div>
                <div>
                  <label className="text-sm font-medium">Conservation Status</label>
                  <Input value={aiGeneratedData.conservation_status} readOnly className="mt-1" />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">Description</label>
                <textarea
                  value={aiGeneratedData.description}
                  readOnly
                  className="w-full mt-1 p-2 border rounded-md h-20 resize-none"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Habitat</label>
                  <textarea
                    value={aiGeneratedData.habitat}
                    readOnly
                    className="w-full mt-1 p-2 border rounded-md h-16 resize-none text-sm"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Diet</label>
                  <textarea
                    value={aiGeneratedData.diet}
                    readOnly
                    className="w-full mt-1 p-2 border rounded-md h-16 resize-none text-sm"
                  />
                </div>
              </div>

              {/* Geographic Information */}
              <div>
                <label className="text-sm font-medium">Geographic Regions</label>
                <div className="mt-2 space-y-2">
                  {/* Countries */}
                  {aiGeneratedData.countries && aiGeneratedData.countries.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      <span className="text-xs text-muted-foreground">Countries:</span>
                      {aiGeneratedData.countries.map((country: string, index: number) => (
                        <Badge key={index} variant="default" className="text-xs">
                          <Globe className="h-3 w-3 mr-1" />
                          {country}
                        </Badge>
                      ))}
                    </div>
                  )}

                  {/* States/Provinces */}
                  {aiGeneratedData.states_provinces && aiGeneratedData.states_provinces.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      <span className="text-xs text-muted-foreground">Regions:</span>
                      {aiGeneratedData.states_provinces.map((region: string, index: number) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {region}
                        </Badge>
                      ))}
                    </div>
                  )}

                  {/* Primary Region */}
                  {aiGeneratedData.primary_region && (
                    <div className="flex flex-wrap gap-1">
                      <span className="text-xs text-muted-foreground">Primary:</span>
                      <Badge variant="outline" className="text-xs">
                        {aiGeneratedData.primary_region}
                      </Badge>
                    </div>
                  )}
                </div>
              </div>

              {/* Fun Facts */}
              {(aiGeneratedData.fun_facts || aiGeneratedData.ai_fun_facts) && (
                <div>
                  <label className="text-sm font-medium">Fun Facts</label>
                  <div className="mt-2 space-y-2">
                    {(aiGeneratedData.fun_facts || aiGeneratedData.ai_fun_facts || []).map((fact: string, index: number) => (
                      <div key={index} className="flex items-start gap-2 text-sm">
                        <span className="text-blue-500 font-bold">•</span>
                        <span>{fact}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="publish-immediately"
                  checked={publishImmediately}
                  onCheckedChange={setPublishImmediately}
                />
                <label htmlFor="publish-immediately" className="text-sm">
                  Publish species immediately (make it visible to public)
                </label>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSpeciesPreview(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleCreateAndAssignSpecies}
              disabled={assigning}
              className="flex items-center gap-2"
            >
              {assigning ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <CheckCircle className="h-4 w-4" />
              )}
              Create Species & Assign Photo
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Full Size Image Dialog */}
      <Dialog open={showFullSizeImage} onOpenChange={setShowFullSizeImage}>
        <DialogContent className="max-w-5xl max-h-[95vh] p-2">
          <DialogHeader className="pb-2">
            <DialogTitle>Full Size Image</DialogTitle>
          </DialogHeader>
          {selectedPhoto && (
            <div className="flex items-center justify-center">
              <img
                src={selectedPhoto.url || ''}
                alt="Full size photo"
                className="max-w-full max-h-[80vh] object-contain rounded-lg"
              />
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Enlarged Thumbnail Dialog */}
      <Dialog open={!!enlargedThumbnail} onOpenChange={() => setEnlargedThumbnail(null)}>
        <DialogContent className="max-w-3xl max-h-[80vh] p-2">
          <DialogHeader className="pb-2">
            <DialogTitle>Species Reference Photo</DialogTitle>
          </DialogHeader>
          {enlargedThumbnail && (
            <div className="flex items-center justify-center">
              <img
                src={enlargedThumbnail}
                alt="Species reference"
                className="max-w-full max-h-[70vh] object-contain rounded-lg"
              />
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Species Detail Dialog */}
      <Dialog open={showSpeciesDetail} onOpenChange={setShowSpeciesDetail}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <ExternalLink className="h-5 w-5" />
              Species Details
            </DialogTitle>
          </DialogHeader>

          {selectedSpeciesForPreview && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Common Name</label>
                  <p className="font-medium">{selectedSpeciesForPreview.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Scientific Name</label>
                  <p className="italic">{selectedSpeciesForPreview.scientific_name}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Category</label>
                  <Badge variant="outline">{selectedSpeciesForPreview.category}</Badge>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Conservation Status</label>
                  <Badge variant="outline">{selectedSpeciesForPreview.conservation_status}</Badge>
                </div>
              </div>

              {selectedSpeciesForPreview.description && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Description</label>
                  <p className="text-sm mt-1">{selectedSpeciesForPreview.description}</p>
                </div>
              )}

              {/* Geographic Information */}
              {(selectedSpeciesForPreview.countries || selectedSpeciesForPreview.states_provinces || selectedSpeciesForPreview.primary_region) && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Geographic Distribution</label>
                  <div className="mt-2 space-y-2">
                    {/* Countries */}
                    {selectedSpeciesForPreview.countries && selectedSpeciesForPreview.countries.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        <span className="text-xs text-muted-foreground">Countries:</span>
                        {selectedSpeciesForPreview.countries.map((country: string, index: number) => (
                          <Link
                            key={index}
                            to={`/wildlife?location=${encodeURIComponent(country)}&tab=species`}
                            className="inline-block"
                          >
                            <Badge
                              variant="default"
                              className="text-xs hover:bg-blue-600 transition-colors cursor-pointer"
                            >
                              <Globe className="h-3 w-3 mr-1" />
                              {country}
                            </Badge>
                          </Link>
                        ))}
                      </div>
                    )}

                    {/* States/Provinces */}
                    {selectedSpeciesForPreview.states_provinces && selectedSpeciesForPreview.states_provinces.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        <span className="text-xs text-muted-foreground">Regions:</span>
                        {selectedSpeciesForPreview.states_provinces.map((region: string, index: number) => (
                          <Link
                            key={index}
                            to={`/wildlife?location=${encodeURIComponent(region)}&tab=species`}
                            className="inline-block"
                          >
                            <Badge
                              variant="secondary"
                              className="text-xs hover:bg-gray-300 transition-colors cursor-pointer"
                            >
                              {region}
                            </Badge>
                          </Link>
                        ))}
                      </div>
                    )}

                    {/* Primary Region */}
                    {selectedSpeciesForPreview.primary_region && (
                      <div className="flex flex-wrap gap-1">
                        <span className="text-xs text-muted-foreground">Primary:</span>
                        <Link
                          to={`/wildlife?location=${encodeURIComponent(selectedSpeciesForPreview.primary_region)}&tab=species`}
                          className="inline-block"
                        >
                          <Badge
                            variant="outline"
                            className="text-xs hover:bg-gray-100 transition-colors cursor-pointer"
                          >
                            {selectedSpeciesForPreview.primary_region}
                          </Badge>
                        </Link>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Species Thumbnail */}
              {speciesThumbnails[selectedSpeciesForPreview.id] && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Reference Photo</label>
                  <div className="mt-2">
                    <img
                      src={speciesThumbnails[selectedSpeciesForPreview.id]}
                      alt={selectedSpeciesForPreview.name}
                      className="w-full max-w-sm h-48 object-cover rounded-lg cursor-pointer"
                      onClick={() => setEnlargedThumbnail(speciesThumbnails[selectedSpeciesForPreview.id])}
                    />
                  </div>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSpeciesDetail(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* AI Review Results Dialog */}
      <Dialog open={showAIResults} onOpenChange={setShowAIResults}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Wand2 className="h-5 w-5 text-blue-500" />
              AI Review Results
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {aiReviewResults.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No AI suggestions available</p>
              </div>
            ) : (
              <>
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    {aiReviewResults.filter(r => r.success).length} photos identified
                  </p>
                  <Button
                    onClick={handleAcceptAllAISuggestions}
                    disabled={aiReviewResults.filter(r => r.success).length === 0}
                    className="flex items-center gap-2"
                  >
                    <Sparkles className="h-4 w-4" />
                    Accept All
                  </Button>
                </div>

                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {aiReviewResults.map((result, index) => (
                    <div key={index} className="flex items-center gap-4 p-4 border rounded-lg">
                      <img
                        src={result.photoUrl}
                        alt={result.photoTitle || 'Photo'}
                        className="w-16 h-16 object-cover rounded-lg cursor-pointer hover:opacity-80 transition-opacity"
                        onClick={() => setPreviewPhoto(result)}
                      />

                      <div className="flex-1">
                        <p className="font-medium">{result.photoTitle || 'Untitled Photo'}</p>
                        {result.success ? (
                          <div className="text-sm text-muted-foreground">
                            <p>Suggested: <span className="font-medium">{result.suggestion.speciesName}</span></p>
                            <p>Confidence: <span className="font-medium">{result.suggestion.confidenceScore.toFixed(1)}%</span></p>
                          </div>
                        ) : (
                          <p className="text-sm text-red-600">Error: {result.error}</p>
                        )}
                      </div>

                      {result.success && (
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => handleAcceptAISuggestion(result)}
                            className="flex items-center gap-1"
                          >
                            <Check className="h-3 w-3" />
                            Accept
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleRejectAISuggestion(result)}
                            className="flex items-center gap-1"
                          >
                            <X className="h-3 w-3" />
                            Reject
                          </Button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAIResults(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Photo Preview Modal */}
      <Dialog open={!!previewPhoto} onOpenChange={() => setPreviewPhoto(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Camera className="h-5 w-5" />
              {previewPhoto?.photoTitle || 'Photo Preview'}
            </DialogTitle>
          </DialogHeader>

          {previewPhoto && (
            <div className="space-y-4">
              <div className="flex justify-center">
                <img
                  src={previewPhoto.photoUrl}
                  alt={previewPhoto.photoTitle || 'Photo'}
                  className="max-w-full max-h-[60vh] object-contain rounded-lg"
                />
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="font-medium text-muted-foreground">Photo Details</p>
                  <p><strong>Title:</strong> {previewPhoto.photoTitle || 'Untitled'}</p>
                  <p><strong>Filename:</strong> {previewPhoto.filename || 'Unknown'}</p>
                </div>

                {previewPhoto.success && previewPhoto.suggestion && (
                  <div>
                    <p className="font-medium text-muted-foreground">AI Suggestion</p>
                    <p><strong>Species:</strong> {previewPhoto.suggestion.speciesName}</p>
                    <p><strong>Scientific:</strong> {previewPhoto.suggestion.scientificName || 'N/A'}</p>
                    <p><strong>Confidence:</strong> {previewPhoto.suggestion.confidenceScore.toFixed(1)}%</p>
                  </div>
                )}
              </div>

              {previewPhoto.success && (
                <div className="flex justify-center gap-3">
                  <Button
                    onClick={() => {
                      handleAcceptAISuggestion(previewPhoto);
                      setPreviewPhoto(null);
                    }}
                    className="flex items-center gap-2"
                  >
                    <Check className="h-4 w-4" />
                    Accept Suggestion
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      handleRejectAISuggestion(previewPhoto);
                      setPreviewPhoto(null);
                    }}
                    className="flex items-center gap-2"
                  >
                    <X className="h-4 w-4" />
                    Reject Suggestion
                  </Button>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setPreviewPhoto(null)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DndProvider>
  );
}
