import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { Trash2, RefreshCw } from "lucide-react";

interface Photo {
  id: number;
  url: string;
  title?: string;
  species_id: string;
  hash?: string;
}

interface Species {
  id: string;
  name: string;
  scientific_name?: string;
}

export const PhotoDeduplicator = () => {
  const [duplicatePhotos, setDuplicatePhotos] = useState<Photo[][]>([]);
  const [duplicateSpecies, setDuplicateSpecies] = useState<Species[][]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRemoving, setIsRemoving] = useState(false);

  useEffect(() => {
    fetchDuplicates();
  }, []);

  const fetchDuplicates = async () => {
    setIsLoading(true);
    try {
      // Fetch photos with duplicate hashes
      const { data: photos, error: photoError } = await supabase
        .from('photos_v2')
        .select('id, url, title, species_id, hash')
        .not('hash', 'is', null);

      if (photoError) throw photoError;

      // Group photos by hash
      const photosByHash: Record<string, Photo[]> = {};
      photos?.forEach(photo => {
        if (photo.hash) {
          if (!photosByHash[photo.hash]) {
            photosByHash[photo.hash] = [];
          }
          photosByHash[photo.hash].push(photo);
        }
      });

      // Filter to only include groups with duplicates
      const duplicateGroups = Object.values(photosByHash).filter(group => group.length > 1);
      setDuplicatePhotos(duplicateGroups);

      // Fetch species with duplicate scientific names
      const { data: species, error: speciesError } = await supabase
        .from('species_v2')
        .select('id, name, scientific_name')
        .not('scientific_name', 'is', null);

      if (speciesError) throw speciesError;

      // Group species by scientific name
      const speciesByName: Record<string, Species[]> = {};
      species?.forEach(s => {
        if (s.scientific_name) {
          const normalizedName = s.scientific_name.toLowerCase().trim();
          if (!speciesByName[normalizedName]) {
            speciesByName[normalizedName] = [];
          }
          speciesByName[normalizedName].push(s);
        }
      });

      // Filter to only include groups with duplicates
      const duplicateSpeciesGroups = Object.values(speciesByName).filter(group => group.length > 1);
      setDuplicateSpecies(duplicateSpeciesGroups);

    } catch (error) {
      console.error('Error fetching duplicates:', error);
      toast.error("Failed to fetch duplicates");
    } finally {
      setIsLoading(false);
    }
  };

  const removeDuplicates = async () => {
    if (!duplicatePhotos.length && !duplicateSpecies.length) {
      toast.error("No duplicates found to remove");
      return;
    }

    setIsRemoving(true);
    
    try {
      // Remove duplicate photos
      if (duplicatePhotos.length > 0) {
        const duplicatesToRemove: number[] = [];
        duplicatePhotos.forEach(group => {
          // Keep the first photo, remove the rest
          const photosToRemove = group.slice(1);
          photosToRemove.forEach(photo => {
            duplicatesToRemove.push(photo.id);
          });
        });

        const { error: photoError } = await supabase
          .from('photos_v2')
          .delete()
          .in('id', duplicatesToRemove);

        if (photoError) throw photoError;
      }

      // Remove duplicate species
      if (duplicateSpecies.length > 0) {
        const duplicatesToRemove: string[] = [];
        duplicateSpecies.forEach(group => {
          // Keep the first species, remove the rest
          const speciesToRemove = group.slice(1);
          speciesToRemove.forEach(species => {
            duplicatesToRemove.push(species.id);
          });
        });

        const { error: speciesError } = await supabase
          .from('species_v2')
          .delete()
          .in('id', duplicatesToRemove);

        if (speciesError) throw speciesError;
      }

      toast.success(`Successfully removed ${duplicatePhotos.flat().length - duplicatePhotos.length + duplicateSpecies.flat().length - duplicateSpecies.length} duplicate records`);
      
      // Refresh the data
      await fetchDuplicates();
      
    } catch (error) {
      console.error('Error removing duplicates:', error);
      toast.error("Failed to remove duplicates");
    } finally {
      setIsRemoving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Duplicate Finder</h2>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={fetchDuplicates} 
            disabled={isLoading}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button 
            variant="destructive" 
            onClick={removeDuplicates} 
            disabled={isRemoving || isLoading || (!duplicatePhotos.length && !duplicateSpecies.length)}
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Remove All Duplicates
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Duplicate Photos */}
          <Card>
            <CardHeader>
              <CardTitle>Duplicate Photos ({duplicatePhotos.reduce((acc, group) => acc + group.length - 1, 0)})</CardTitle>
            </CardHeader>
            <CardContent>
              {duplicatePhotos.length === 0 ? (
                <p>No duplicate photos found.</p>
              ) : (
                <div className="space-y-6">
                  {duplicatePhotos.map((group, groupIndex) => (
                    <div key={groupIndex} className="border p-4 rounded-lg">
                      <h3 className="font-medium mb-2">Group {groupIndex + 1} - Hash: {group[0].hash?.substring(0, 8)}...</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {group.map((photo, photoIndex) => (
                          <div key={photo.id} className="border rounded-md overflow-hidden">
                            <img src={photo.url} alt={photo.title || `Photo ${photo.id}`} className="w-full h-48 object-cover" />
                            <div className="p-2 bg-gray-50">
                              <p className="text-sm font-medium">{photoIndex === 0 ? '✅ Keep' : '❌ Remove'} - ID: {photo.id}</p>
                              {photo.title && <p className="text-xs text-gray-500">{photo.title}</p>}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Duplicate Species */}
          <Card>
            <CardHeader>
              <CardTitle>Duplicate Species ({duplicateSpecies.reduce((acc, group) => acc + group.length - 1, 0)})</CardTitle>
            </CardHeader>
            <CardContent>
              {duplicateSpecies.length === 0 ? (
                <p>No duplicate species found.</p>
              ) : (
                <div className="space-y-4">
                  {duplicateSpecies.map((group, groupIndex) => (
                    <div key={groupIndex} className="border p-4 rounded-lg">
                      <h3 className="font-medium mb-2">Group {groupIndex + 1} - Scientific Name: {group[0].scientific_name}</h3>
                      <div className="space-y-2">
                        {group.map((species, speciesIndex) => (
                          <div key={species.id} className={`p-2 rounded ${speciesIndex === 0 ? 'bg-green-50' : 'bg-red-50'}`}>
                            <p className="font-medium">{speciesIndex === 0 ? '✅ Keep' : '❌ Remove'} - {species.name}</p>
                            <p className="text-sm text-gray-500">ID: {species.id}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
