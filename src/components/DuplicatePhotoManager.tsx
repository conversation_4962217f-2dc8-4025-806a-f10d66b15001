import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Trash2, 
  AlertTriangle, 
  Eye, 
  Search, 
  RefreshCw,
  CheckCircle,
  X,
  Copy,
  Image as ImageIcon,
  Calendar,
  User,
  MapPin,
  Tag
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface DuplicatePhoto {
  id: string;
  url: string;
  title?: string;
  description?: string;
  photographer?: string;
  location?: string;
  species_id?: string;
  published: boolean;
  created_at: string;
  hash?: string;
  species?: {
    id: string;
    name: string;
    common_name?: string;
  };
}

interface DuplicateGroup {
  hash: string;
  photos: DuplicatePhoto[];
  count: number;
}

export function DuplicatePhotoManager() {
  const [duplicateGroups, setDuplicateGroups] = useState<DuplicateGroup[]>([]);
  const [loading, setLoading] = useState(false);
  const [scanning, setScanning] = useState(false);
  const [selectedPhotos, setSelectedPhotos] = useState<Set<string>>(new Set());
  const [deleting, setDeleting] = useState(false);
  const [stats, setStats] = useState({
    totalDuplicates: 0,
    duplicateGroups: 0,
    potentialSavings: 0
  });

  useEffect(() => {
    findDuplicates();
  }, []);

  const findDuplicates = async () => {
    setLoading(true);
    try {
      // Find photos with duplicate URLs (most common case)
      let urlDuplicates, hashDuplicates;

      const { data: urlData, error: urlError } = await supabase
        .from('photos_v2')
        .select('id, url, title, description, photographer, location, species_id, published, created_at, hash')
        .not('url', 'is', null)
        .order('url', { ascending: true });

      if (urlError) {
        console.error('URL duplicates query error:', urlError);
        throw urlError;
      }

      urlDuplicates = urlData;

      // Find photos with duplicate hashes (if available)
      const { data: hashData, error: hashError } = await supabase
        .from('photos_v2')
        .select('id, url, title, description, photographer, location, species_id, published, created_at, hash')
        .not('hash', 'is', null)
        .order('hash', { ascending: true });

      if (hashError) {
        console.error('Hash duplicates query error:', hashError);
        hashDuplicates = [];
      } else {
        hashDuplicates = hashData || [];
      }

      // Group by URL
      const urlGroups = groupDuplicates(urlDuplicates || [], 'url');
      
      // Group by hash (if available)
      const hashGroups = groupDuplicates(hashDuplicates || [], 'hash');

      // Combine and deduplicate groups
      const allGroups = [...urlGroups, ...hashGroups];
      const uniqueGroups = deduplicateGroups(allGroups);

      setDuplicateGroups(uniqueGroups);
      
      // Calculate stats
      const totalDuplicates = uniqueGroups.reduce((sum, group) => sum + group.count - 1, 0);
      setStats({
        totalDuplicates,
        duplicateGroups: uniqueGroups.length,
        potentialSavings: totalDuplicates
      });

    } catch (error) {
      console.error('Error finding duplicates:', error);
      toast.error('Failed to find duplicate photos');
    } finally {
      setLoading(false);
    }
  };

  const groupDuplicates = (photos: any[], groupBy: 'url' | 'hash'): DuplicateGroup[] => {
    const groups: Record<string, DuplicatePhoto[]> = {};
    
    photos.forEach(photo => {
      const key = photo[groupBy];
      if (!key) return;
      
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push({
        ...photo,
        species: photo.species_v2 || null
      });
    });

    // Only return groups with more than one photo
    return Object.entries(groups)
      .filter(([_, photos]) => photos.length > 1)
      .map(([key, photos]) => ({
        hash: key,
        photos: photos.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()),
        count: photos.length
      }));
  };

  const deduplicateGroups = (groups: DuplicateGroup[]): DuplicateGroup[] => {
    const seen = new Set<string>();
    const unique: DuplicateGroup[] = [];

    groups.forEach(group => {
      const photoIds = group.photos.map(p => p.id).sort().join(',');
      if (!seen.has(photoIds)) {
        seen.add(photoIds);
        unique.push(group);
      }
    });

    return unique;
  };

  const togglePhotoSelection = (photoId: string) => {
    const newSelected = new Set(selectedPhotos);
    if (newSelected.has(photoId)) {
      newSelected.delete(photoId);
    } else {
      newSelected.add(photoId);
    }
    setSelectedPhotos(newSelected);
  };

  const selectAllInGroup = (group: DuplicateGroup, keepFirst: boolean = true) => {
    const newSelected = new Set(selectedPhotos);
    const photosToSelect = keepFirst ? group.photos.slice(1) : group.photos;
    
    photosToSelect.forEach(photo => {
      newSelected.add(photo.id);
    });
    
    setSelectedPhotos(newSelected);
  };

  const deleteSelectedPhotos = async () => {
    if (selectedPhotos.size === 0) {
      toast.error('No photos selected for deletion');
      return;
    }

    const confirmed = confirm(
      `Are you sure you want to delete ${selectedPhotos.size} selected photos? This action cannot be undone.`
    );

    if (!confirmed) return;

    setDeleting(true);
    try {
      const photoIds = Array.from(selectedPhotos);
      
      // Delete from database
      const { error } = await supabase
        .from('photos_v2')
        .delete()
        .in('id', photoIds);

      if (error) throw error;

      toast.success(`Successfully deleted ${photoIds.length} photos`);
      setSelectedPhotos(new Set());
      
      // Refresh the duplicate list
      await findDuplicates();
      
    } catch (error) {
      console.error('Error deleting photos:', error);
      toast.error('Failed to delete selected photos');
    } finally {
      setDeleting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin mr-2" />
        <span>Scanning for duplicate photos...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Duplicate Photo Manager</h2>
          <p className="text-muted-foreground">
            Find and manage duplicate photos in your wildlife database
          </p>
        </div>
        <Button onClick={findDuplicates} disabled={scanning}>
          <RefreshCw className={`w-4 h-4 mr-2 ${scanning ? 'animate-spin' : ''}`} />
          Rescan
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Copy className="w-5 h-5 text-orange-500" />
              <div>
                <p className="text-sm text-muted-foreground">Duplicate Groups</p>
                <p className="text-2xl font-bold">{stats.duplicateGroups}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-red-500" />
              <div>
                <p className="text-sm text-muted-foreground">Total Duplicates</p>
                <p className="text-2xl font-bold">{stats.totalDuplicates}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Trash2 className="w-5 h-5 text-green-500" />
              <div>
                <p className="text-sm text-muted-foreground">Can Remove</p>
                <p className="text-2xl font-bold">{stats.potentialSavings}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      {selectedPhotos.size > 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>{selectedPhotos.size} photos selected for deletion</span>
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setSelectedPhotos(new Set())}
              >
                <X className="w-4 h-4 mr-1" />
                Clear Selection
              </Button>
              <Button 
                variant="destructive" 
                size="sm" 
                onClick={deleteSelectedPhotos}
                disabled={deleting}
              >
                <Trash2 className="w-4 h-4 mr-1" />
                {deleting ? 'Deleting...' : 'Delete Selected'}
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Duplicate Groups */}
      {duplicateGroups.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Duplicates Found!</h3>
            <p className="text-muted-foreground">
              Your photo library appears to be clean of duplicates.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {duplicateGroups.map((group, groupIndex) => (
            <Card key={group.hash} className="overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">
                    Duplicate Group #{groupIndex + 1}
                    <Badge variant="destructive" className="ml-2">
                      {group.count} copies
                    </Badge>
                  </CardTitle>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => selectAllInGroup(group, true)}
                    >
                      Select All Except First
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => selectAllInGroup(group, false)}
                    >
                      Select All
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
                  {group.photos.map((photo, photoIndex) => (
                    <div
                      key={photo.id}
                      className={`border rounded-lg p-3 ${
                        selectedPhotos.has(photo.id) 
                          ? 'border-red-500 bg-red-50' 
                          : photoIndex === 0 
                            ? 'border-green-500 bg-green-50' 
                            : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <Checkbox
                          checked={selectedPhotos.has(photo.id)}
                          onCheckedChange={() => togglePhotoSelection(photo.id)}
                          className="mt-1"
                        />
                        
                        <div className="flex-1 min-w-0">
                          {/* Photo Preview */}
                          <div className="aspect-square w-20 h-20 rounded-md overflow-hidden bg-gray-100 mb-2">
                            {photo.url ? (
                              <img
                                src={photo.url}
                                alt={photo.title || 'Photo'}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <ImageIcon className="w-8 h-8 text-gray-400" />
                              </div>
                            )}
                          </div>
                          
                          {/* Photo Info */}
                          <div className="space-y-1 text-sm">
                            {photoIndex === 0 && (
                              <Badge variant="secondary" className="text-xs">
                                Original (Keep)
                              </Badge>
                            )}
                            
                            <p className="font-medium truncate">
                              {photo.title || 'Untitled'}
                            </p>
                            
                            {photo.species && (
                              <div className="flex items-center text-muted-foreground">
                                <Tag className="w-3 h-3 mr-1" />
                                <span className="truncate">{photo.species.name}</span>
                              </div>
                            )}
                            
                            {photo.photographer && (
                              <div className="flex items-center text-muted-foreground">
                                <User className="w-3 h-3 mr-1" />
                                <span className="truncate">{photo.photographer}</span>
                              </div>
                            )}
                            
                            {photo.location && (
                              <div className="flex items-center text-muted-foreground">
                                <MapPin className="w-3 h-3 mr-1" />
                                <span className="truncate">{photo.location}</span>
                              </div>
                            )}
                            
                            <div className="flex items-center text-muted-foreground">
                              <Calendar className="w-3 h-3 mr-1" />
                              <span className="text-xs">{formatDate(photo.created_at)}</span>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                              <Badge variant={photo.published ? "default" : "secondary"}>
                                {photo.published ? "Published" : "Draft"}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
