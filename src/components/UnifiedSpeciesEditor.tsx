import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { 
  Save, 
  X, 
  Plus, 
  Trash2, 
  Upload, 
  Brain, 
  Eye, 
  EyeOff,
  MapPin,
  Tag,
  Camera,
  FileText,
  <PERSON>rk<PERSON>,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { CategoryIcons, ActionIcons, getCategoryIcon, getConservationIcon } from '@/components/ui/wildlife-icons';
import { supabase } from '@/integrations/supabase/client';
import { getServiceRoleClient } from '@/lib/supabaseServiceRole';
import { toast } from 'sonner';

interface SpeciesData {
  id?: string;
  name: string;
  common_name?: string;
  scientific_name?: string;
  category?: string;
  conservation_status?: string;
  description?: string;
  habitat?: string;
  diet?: string;
  behavior?: string;
  reproduction?: string;
  conservation_actions?: string;
  fun_facts?: string[];
  tags?: string[];
  countries?: string[];
  states_provinces?: string[];
  primary_region?: string;
  geographic_scope?: string;
  size_length?: string;
  size_weight?: string;
  size_wingspan?: string;
  lifespan?: string;
  published?: boolean;
  featured?: boolean;
  photo_count?: number;
  created_at?: string;
  updated_at?: string;
}

interface UnifiedSpeciesEditorProps {
  species?: SpeciesData | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (species: SpeciesData) => void;
  mode?: 'create' | 'edit';
  enableAI?: boolean;
}

const CATEGORIES = [
  'Mammal', 'Bird', 'Reptile', 'Amphibian', 'Fish', 'Insect', 'Marine Life', 'Plant'
];

const CONSERVATION_STATUSES = [
  'Least Concern', 'Near Threatened', 'Vulnerable', 'Endangered', 'Critically Endangered', 'Extinct'
];

export const UnifiedSpeciesEditor: React.FC<UnifiedSpeciesEditorProps> = ({
  species,
  isOpen,
  onClose,
  onSave,
  mode = 'edit',
  enableAI = true
}) => {
  const [formData, setFormData] = useState<SpeciesData>({
    name: '',
    common_name: '',
    scientific_name: '',
    category: '',
    conservation_status: '',
    description: '',
    habitat: '',
    diet: '',
    behavior: '',
    reproduction: '',
    conservation_actions: '',
    fun_facts: [],
    tags: [],
    countries: [],
    states_provinces: [],
    primary_region: '',
    geographic_scope: '',
    size_length: '',
    size_weight: '',
    size_wingspan: '',
    lifespan: '',
    published: false,
    featured: false
  });

  const [activeTab, setActiveTab] = useState('basic');
  const [isLoading, setIsLoading] = useState(false);
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const [newFunFact, setNewFunFact] = useState('');
  const [newTag, setNewTag] = useState('');
  const [newCountry, setNewCountry] = useState('');
  const [newStateProvince, setNewStateProvince] = useState('');

  // Initialize form data when species changes
  useEffect(() => {
    if (species) {
      setFormData({
        ...species,
        fun_facts: species.fun_facts || [],
        tags: species.tags || [],
        countries: species.countries || [],
        states_provinces: species.states_provinces || []
      });
    } else if (mode === 'create') {
      setFormData({
        name: '',
        common_name: '',
        scientific_name: '',
        category: '',
        conservation_status: '',
        description: '',
        habitat: '',
        diet: '',
        behavior: '',
        reproduction: '',
        conservation_actions: '',
        fun_facts: [],
        tags: [],
        countries: [],
        states_provinces: [],
        primary_region: '',
        geographic_scope: '',
        size_length: '',
        size_weight: '',
        size_wingspan: '',
        lifespan: '',
        published: false,
        featured: false
      });
    }
  }, [species, mode]);

  const handleInputChange = (field: keyof SpeciesData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addArrayItem = (field: 'fun_facts' | 'tags' | 'countries' | 'states_provinces', value: string) => {
    if (value.trim()) {
      setFormData(prev => ({
        ...prev,
        [field]: [...(prev[field] || []), value.trim()]
      }));
      
      // Clear the input
      if (field === 'fun_facts') setNewFunFact('');
      if (field === 'tags') setNewTag('');
      if (field === 'countries') setNewCountry('');
      if (field === 'states_provinces') setNewStateProvince('');
    }
  };

  const removeArrayItem = (field: 'fun_facts' | 'tags' | 'countries' | 'states_provinces', index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field]?.filter((_, i) => i !== index) || []
    }));
  };

  const generateAIContent = async () => {
    if (!formData.name) {
      toast.error('Please enter a species name first');
      return;
    }

    setIsGeneratingAI(true);
    try {
      // Call AI generation service
      const response = await fetch('/api/ai-improve-species', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          species_name: formData.name,
          scientific_name: formData.scientific_name,
          existing_data: formData
        }),
      });

      if (response.ok) {
        const aiData = await response.json();
        setFormData(prev => ({
          ...prev,
          ...aiData,
          // Preserve user-entered data
          name: prev.name,
          id: prev.id
        }));
        toast.success('AI content generated successfully!');
      } else {
        throw new Error('Failed to generate AI content');
      }
    } catch (error) {
      console.error('AI generation error:', error);
      toast.error('Failed to generate AI content');
    } finally {
      setIsGeneratingAI(false);
    }
  };

  const handleSave = async () => {
    if (!formData.name) {
      toast.error('Species name is required');
      return;
    }

    setIsLoading(true);
    try {
      const serviceRoleClient = getServiceRoleClient();
      
      if (mode === 'create') {
        const { data, error } = await serviceRoleClient
          .from('species_v2')
          .insert({
            ...formData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (error) throw error;
        onSave(data);
        toast.success('Species created successfully!');
      } else {
        const { data, error } = await serviceRoleClient
          .from('species_v2')
          .update({
            ...formData,
            updated_at: new Date().toISOString()
          })
          .eq('id', formData.id)
          .select()
          .single();

        if (error) throw error;
        onSave(data);
        toast.success('Species updated successfully!');
      }
      
      onClose();
    } catch (error) {
      console.error('Save error:', error);
      toast.error('Failed to save species');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {formData.category && React.createElement(getCategoryIcon(formData.category), { size: 24 })}
              <DialogTitle className="text-2xl">
                {mode === 'create' ? 'Create New Species' : `Edit ${formData.name}`}
              </DialogTitle>
            </div>
            <div className="flex items-center gap-2">
              {enableAI && (
                <Button
                  variant="wildlife"
                  size="sm"
                  onClick={generateAIContent}
                  loading={isGeneratingAI}
                  icon={<ActionIcons.AI size={16} />}
                >
                  Generate AI Content
                </Button>
              )}
              <Badge variant={formData.published ? "default" : "secondary"}>
                {formData.published ? "Published" : "Draft"}
              </Badge>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-5 mb-4">
              <TabsTrigger value="basic" className="flex items-center gap-2">
                <FileText className="w-4 h-4" />
                Basic Info
              </TabsTrigger>
              <TabsTrigger value="details" className="flex items-center gap-2">
                <Brain className="w-4 h-4" />
                Details
              </TabsTrigger>
              <TabsTrigger value="geography" className="flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                Geography
              </TabsTrigger>
              <TabsTrigger value="facts" className="flex items-center gap-2">
                <Sparkles className="w-4 h-4" />
                Fun Facts
              </TabsTrigger>
              <TabsTrigger value="settings" className="flex items-center gap-2">
                <Eye className="w-4 h-4" />
                Settings
              </TabsTrigger>
            </TabsList>

            <div className="flex-1 overflow-y-auto pr-2">
              <TabsContent value="basic" className="space-y-6 mt-0">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="name">Species Name *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="Enter species name"
                        className="mt-1"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="common_name">Common Name</Label>
                      <Input
                        id="common_name"
                        value={formData.common_name || ''}
                        onChange={(e) => handleInputChange('common_name', e.target.value)}
                        placeholder="Enter common name"
                        className="mt-1"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="scientific_name">Scientific Name</Label>
                      <Input
                        id="scientific_name"
                        value={formData.scientific_name || ''}
                        onChange={(e) => handleInputChange('scientific_name', e.target.value)}
                        placeholder="Enter scientific name"
                        className="mt-1 italic"
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="category">Category</Label>
                      <Select value={formData.category || ''} onValueChange={(value) => handleInputChange('category', value)}>
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          {CATEGORIES.map(category => (
                            <SelectItem key={category} value={category}>
                              <div className="flex items-center gap-2">
                                {React.createElement(getCategoryIcon(category), { size: 16 })}
                                {category}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label htmlFor="conservation_status">Conservation Status</Label>
                      <Select value={formData.conservation_status || ''} onValueChange={(value) => handleInputChange('conservation_status', value)}>
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select conservation status" />
                        </SelectTrigger>
                        <SelectContent>
                          {CONSERVATION_STATUSES.map(status => (
                            <SelectItem key={status} value={status}>
                              <div className="flex items-center gap-2">
                                {React.createElement(getConservationIcon(status), { size: 16 })}
                                {status}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description || ''}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Enter species description"
                    className="mt-1 min-h-[120px]"
                  />
                </div>
              </TabsContent>

              <TabsContent value="details" className="space-y-6 mt-0">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="habitat">Habitat</Label>
                      <Textarea
                        id="habitat"
                        value={formData.habitat || ''}
                        onChange={(e) => handleInputChange('habitat', e.target.value)}
                        placeholder="Describe the species habitat"
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="diet">Diet</Label>
                      <Textarea
                        id="diet"
                        value={formData.diet || ''}
                        onChange={(e) => handleInputChange('diet', e.target.value)}
                        placeholder="Describe the species diet"
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="behavior">Behavior</Label>
                      <Textarea
                        id="behavior"
                        value={formData.behavior || ''}
                        onChange={(e) => handleInputChange('behavior', e.target.value)}
                        placeholder="Describe the species behavior"
                        className="mt-1"
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="reproduction">Reproduction</Label>
                      <Textarea
                        id="reproduction"
                        value={formData.reproduction || ''}
                        onChange={(e) => handleInputChange('reproduction', e.target.value)}
                        placeholder="Describe reproduction patterns"
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="conservation_actions">Conservation Actions</Label>
                      <Textarea
                        id="conservation_actions"
                        value={formData.conservation_actions || ''}
                        onChange={(e) => handleInputChange('conservation_actions', e.target.value)}
                        placeholder="Describe conservation efforts"
                        className="mt-1"
                      />
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="size_length">Size/Length</Label>
                    <Input
                      id="size_length"
                      value={formData.size_length || ''}
                      onChange={(e) => handleInputChange('size_length', e.target.value)}
                      placeholder="e.g., 15-20 cm"
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="size_weight">Weight</Label>
                    <Input
                      id="size_weight"
                      value={formData.size_weight || ''}
                      onChange={(e) => handleInputChange('size_weight', e.target.value)}
                      placeholder="e.g., 2-5 kg"
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="lifespan">Lifespan</Label>
                    <Input
                      id="lifespan"
                      value={formData.lifespan || ''}
                      onChange={(e) => handleInputChange('lifespan', e.target.value)}
                      placeholder="e.g., 10-15 years"
                      className="mt-1"
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="geography" className="space-y-6 mt-0">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="primary_region">Primary Region</Label>
                      <Input
                        id="primary_region"
                        value={formData.primary_region || ''}
                        onChange={(e) => handleInputChange('primary_region', e.target.value)}
                        placeholder="e.g., North America"
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="geographic_scope">Geographic Scope</Label>
                      <Input
                        id="geographic_scope"
                        value={formData.geographic_scope || ''}
                        onChange={(e) => handleInputChange('geographic_scope', e.target.value)}
                        placeholder="e.g., Global, Regional, Local"
                        className="mt-1"
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <Label>Countries</Label>
                      <div className="flex gap-2 mt-1">
                        <Input
                          value={newCountry}
                          onChange={(e) => setNewCountry(e.target.value)}
                          placeholder="Add country"
                          onKeyPress={(e) => e.key === 'Enter' && addArrayItem('countries', newCountry)}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => addArrayItem('countries', newCountry)}
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                      </div>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {formData.countries?.map((country, index) => (
                          <Badge key={index} variant="secondary" className="flex items-center gap-1">
                            {country}
                            <button
                              onClick={() => removeArrayItem('countries', index)}
                              className="ml-1 hover:text-red-500"
                            >
                              <X className="w-3 h-3" />
                            </button>
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <Label>States/Provinces</Label>
                      <div className="flex gap-2 mt-1">
                        <Input
                          value={newStateProvince}
                          onChange={(e) => setNewStateProvince(e.target.value)}
                          placeholder="Add state/province"
                          onKeyPress={(e) => e.key === 'Enter' && addArrayItem('states_provinces', newStateProvince)}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => addArrayItem('states_provinces', newStateProvince)}
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                      </div>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {formData.states_provinces?.map((state, index) => (
                          <Badge key={index} variant="secondary" className="flex items-center gap-1">
                            {state}
                            <button
                              onClick={() => removeArrayItem('states_provinces', index)}
                              className="ml-1 hover:text-red-500"
                            >
                              <X className="w-3 h-3" />
                            </button>
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="facts" className="space-y-6 mt-0">
                <div className="space-y-6">
                  <div>
                    <Label>Fun Facts</Label>
                    <div className="flex gap-2 mt-1">
                      <Textarea
                        value={newFunFact}
                        onChange={(e) => setNewFunFact(e.target.value)}
                        placeholder="Add an interesting fun fact"
                        className="min-h-[80px]"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => addArrayItem('fun_facts', newFunFact)}
                        className="self-start"
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="space-y-2 mt-4">
                      {formData.fun_facts?.map((fact, index) => (
                        <Card key={index} className="p-3">
                          <div className="flex justify-between items-start gap-2">
                            <p className="text-sm flex-1">{fact}</p>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeArrayItem('fun_facts', index)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label>Tags</Label>
                    <div className="flex gap-2 mt-1">
                      <Input
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        placeholder="Add tag"
                        onKeyPress={(e) => e.key === 'Enter' && addArrayItem('tags', newTag)}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => addArrayItem('tags', newTag)}
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {formData.tags?.map((tag, index) => (
                        <Badge key={index} variant="outline" className="flex items-center gap-1">
                          <Tag className="w-3 h-3" />
                          {tag}
                          <button
                            onClick={() => removeArrayItem('tags', index)}
                            className="ml-1 hover:text-red-500"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="settings" className="space-y-6 mt-0">
                <Card className="p-6">
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold">Publication Status</h3>
                        <p className="text-sm text-gray-600">Control whether this species is visible to the public</p>
                      </div>
                      <Switch
                        checked={formData.published || false}
                        onCheckedChange={(checked) => handleInputChange('published', checked)}
                      />
                    </div>

                    <Separator />

                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold">Featured Species</h3>
                        <p className="text-sm text-gray-600">Highlight this species on the homepage</p>
                      </div>
                      <Switch
                        checked={formData.featured || false}
                        onCheckedChange={(checked) => handleInputChange('featured', checked)}
                      />
                    </div>

                    <Separator />

                    <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                      <div>
                        <strong>Photo Count:</strong> {formData.photo_count || 0}
                      </div>
                      <div>
                        <strong>Created:</strong> {formData.created_at ? new Date(formData.created_at).toLocaleDateString() : 'N/A'}
                      </div>
                      <div>
                        <strong>Last Updated:</strong> {formData.updated_at ? new Date(formData.updated_at).toLocaleDateString() : 'N/A'}
                      </div>
                      <div>
                        <strong>Species ID:</strong> {formData.id || 'New'}
                      </div>
                    </div>
                  </div>
                </Card>
              </TabsContent>
            </div>
          </Tabs>
        </div>

        <DialogFooter className="pt-4 border-t">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <Switch
                checked={formData.published || false}
                onCheckedChange={(checked) => handleInputChange('published', checked)}
              />
              <Label>Published</Label>
              
              <Switch
                checked={formData.featured || false}
                onCheckedChange={(checked) => handleInputChange('featured', checked)}
                className="ml-4"
              />
              <Label>Featured</Label>
            </div>
            
            <div className="flex gap-2">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button 
                variant="wildlife" 
                onClick={handleSave} 
                loading={isLoading}
                icon={<Save className="w-4 h-4" />}
              >
                {mode === 'create' ? 'Create Species' : 'Save Changes'}
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
