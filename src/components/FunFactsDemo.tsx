import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Lightbulb } from 'lucide-react';

interface FunFactsDemoProps {
  title?: string;
}

export function FunFactsDemo({ title = "Fun Facts - Before vs After Fix" }: FunFactsDemoProps) {
  // Simulate the current problematic data (large text block)
  const problematicFunFacts = [
    "The Brown Falcon (Falco berigora) is a medium-sized bird of prey that is known for its broad wings and short tail. It is a versatile species that can adapt to a variety of habitats. It is predominantly brown in color, with a lighter underside and a dark brown vertical streak under the eye. The Brown Falcon is a solitary bird, often seen alone or in pairs. It is a diurnal raptor, hunting during the day and resting at night. Its flight is distinctive, characterized by a series of rapid wingbeats followed by a glide. 🦅 FUN FACTS: • The Brown Falcon is also known as the 'Clown Falcon' due to its distinctive facial markings. • Unlike many other birds of prey, the Brown Falcon does not build its own nest. Instead, it often takes over the abandoned nests of other birds. • The Brown Falcon has a unique hunting technique known as 'still-hunting', where it sits motionlessly on a perch for long periods before striking at prey. • Brown Falcons are known to pair for life, with both parents participating in raising the young. • During courtship, the male Brown Falcon performs a spectacular aerial display, involving high-speed dives and loops. 🦅 FUN FACTS: • The Brown Falcon is also known as the 'Clown Falcon' due to its distinctive facial markings. • Unlike many other birds of prey, the Brown Falcon does not build its own nest. Instead, it often takes over the abandoned nests of other birds. • The Brown Falcon has a unique hunting technique known as 'still-hunting', where it sits motionlessly on a perch for long periods before striking at prey."
  ];

  // Simulate the fixed data (properly structured individual facts)
  const fixedFunFacts = [
    "The Brown Falcon is also known as the 'Clown Falcon' due to its distinctive facial markings.",
    "Unlike many other birds of prey, the Brown Falcon does not build its own nest. Instead, it often takes over the abandoned nests of other birds.",
    "The Brown Falcon has a unique hunting technique known as 'still-hunting', where it sits motionlessly on a perch for long periods before striking at prey.",
    "Brown Falcons are known to pair for life, with both parents participating in raising the young.",
    "During courtship, the male Brown Falcon performs a spectacular aerial display, involving high-speed dives and loops."
  ];

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">{title}</h2>
      
      {/* Before: Problematic display */}
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="text-red-700 flex items-center gap-2">
            <Lightbulb className="w-5 h-5" />
            BEFORE: Fun Facts (Problematic - Large Text Block)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {problematicFunFacts.map((fact, idx) => (
              <div key={idx} className="bg-red-50 p-3 rounded border border-red-200">
                <p className="text-sm text-gray-700">{fact}</p>
              </div>
            ))}
          </div>
          <p className="text-red-600 text-sm mt-2 italic">
            ❌ Issues: Repetitive content, poor formatting, hard to read
          </p>
        </CardContent>
      </Card>

      {/* After: Fixed display */}
      <Card className="border-green-200">
        <CardHeader>
          <CardTitle className="text-green-700 flex items-center gap-2">
            <Lightbulb className="w-5 h-5" />
            AFTER: Fun Facts (Fixed - Individual Facts)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {fixedFunFacts.map((fact, idx) => (
              <Card key={idx} className="bg-green-50 hover:shadow-sm transition-shadow border-green-200">
                <CardContent className="p-4 flex items-start text-sm">
                  <Lightbulb className="w-5 h-5 mr-3 mt-1 text-yellow-500 flex-shrink-0" />
                  <span className="text-gray-700">{fact}</span>
                </CardContent>
              </Card>
            ))}
          </div>
          <p className="text-green-600 text-sm mt-2 italic">
            ✅ Benefits: Clean individual facts, easy to read, no repetition
          </p>
        </CardContent>
      </Card>

      {/* Technical explanation */}
      <Card className="border-blue-200">
        <CardHeader>
          <CardTitle className="text-blue-700">Technical Solution</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <h4 className="font-semibold text-gray-900">Problem:</h4>
            <p className="text-sm text-gray-600">
              Fun facts were stored in JSONB fields as large text blocks, causing display issues and poor user experience.
            </p>
          </div>
          <div>
            <h4 className="font-semibold text-gray-900">Solution:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Created dedicated <code className="bg-gray-100 px-1 rounded">fun_facts</code> table</li>
              <li>• Migrated data from JSONB fields to individual records</li>
              <li>• Updated frontend to fetch from the new table structure</li>
              <li>• Enhanced data entry components to use the new table</li>
              <li>• Updated API endpoints to include structured fun facts</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold text-gray-900">Benefits:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Better data structure and normalization</li>
              <li>• Improved display formatting</li>
              <li>• Easier content management</li>
              <li>• No more repetitive or malformed content</li>
              <li>• Better performance and maintainability</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
