import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { DraggablePhoto } from '@/components/DraggablePhoto';
import { DropTargetSpecies } from '@/components/DropTargetSpecies';
import { EnhancedPhotoUpload } from '@/components/EnhancedPhotoUpload';
import { PhotoUploadManager } from '@/lib/photoUpload';
import { PhotoEditModal } from '@/components/PhotoEditModal';
import { getPhotosWithFilter, getAllSpecies, getSpeciesWithPhotoCounts, getAISuggestions, reassignPhotoSpecies, bulkReassignPhotos } from '@/lib/photoApi';

import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { 
  Search, 
  Filter, 
  Grid3X3, 
  List, 
  RefreshCw,
  ImageIcon,
  Tag,
  AlertCircle,
  CheckCircle,
  CheckSquare,
  Square,
  Brain,
  Sparkles,
  Upload,
  Trash2,
  Edit,
  Eye,
  Download,
  Loader2,
  Camera,
  User,
  MapPin,
  Calendar,
  Link as LinkIcon,
  Database
} from 'lucide-react';
import { Photo } from '@/lib/api';

type PhotoManagerMode = 'assign' | 'rematch' | 'gallery';
type FilterType = 'all' | 'assigned' | 'unassigned' | 'published' | 'unpublished' | 'needs_recovery';
type ViewMode = 'grid' | 'list';

interface Species {
  id: string;
  name: string;
  common_name: string | null;
  scientific_name: string | null;
  category: string | null;
  conservation_status: string | null;
  published: boolean;
}

interface AISuggestion {
  speciesId: string;
  speciesName: string;
  confidenceScore: number;
  reason: string;
}

interface RematchResult {
  totalPhotos: number;
  matched: number;
  unmatched: number;
  updated: number;
  message: string;
}

interface PhotoManagerProps {
  mode: PhotoManagerMode;
  title?: string;
  className?: string;
  onPhotoUpdate?: () => void;
  onSpeciesUpdate?: () => void;
}

/**
 * PhotoManager - Unified component for photo management operations
 * 
 * Consolidates logic from PhotoAssignment, PhotoRematch, and PhotoGalleryManager components.
 * Provides mode-driven functionality for different photo management tasks.
 * 
 * @example
 * ```tsx
 * // Assignment mode
 * <PhotoManager mode="assign" title="Photo Assignment" />
 * 
 * // Rematch mode
 * <PhotoManager mode="rematch" title="Photo Rematching" />
 * 
 * // Gallery mode
 * <PhotoManager mode="gallery" title="Photo Gallery" />
 * ```
 */
export function PhotoManager({ 
  mode, 
  title,
  className = '',
  onPhotoUpdate,
  onSpeciesUpdate 
}: PhotoManagerProps) {
  // Shared state
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [species, setSpecies] = useState<Species[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [selectedPhotoIds, setSelectedPhotoIds] = useState<string[]>([]);
  const [isMultiSelectMode, setIsMultiSelectMode] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set());
  const [editingPhoto, setEditingPhoto] = useState<Photo | null>(null);

  // Mode-specific state
  const [assignmentFilter, setAssignmentFilter] = useState<FilterType>('unassigned');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [aiSuggestions, setAiSuggestions] = useState<AISuggestion[]>([]);
  const [rematchResult, setRematchResult] = useState<RematchResult | null>(null);
  const [isRematching, setIsRematching] = useState(false);
  const [isAIReviewing, setIsAIReviewing] = useState(false);
  const [aiReviewResults, setAiReviewResults] = useState<any[]>([]);



  // Load data based on mode
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (mode === 'assign') {
        const [photosData, speciesData] = await Promise.all([
          getPhotosWithFilter(assignmentFilter),
          getAllSpecies()
        ]);
        setPhotos(photosData);
        setSpecies(speciesData);
      } else if (mode === 'rematch') {
        const [photosData, speciesData] = await Promise.all([
          supabase.from('photos_v2').select('*'),
          supabase.from('species_v2').select('*')
        ]);
        if (photosData.error) throw photosData.error;
        if (speciesData.error) throw speciesData.error;
        setPhotos(photosData.data || []);
        setSpecies(speciesData.data || []);
      } else if (mode === 'gallery') {
        // Get photos and species separately, then join them
        const [photosData, speciesData] = await Promise.all([
          supabase.from('photos_v2').select('*').order('created_at', { ascending: false }),
          supabase.from('species_v2').select('id, name, common_name, scientific_name, category, conservation_status')
        ]);

        if (photosData.error) throw photosData.error;
        if (speciesData.error) throw speciesData.error;

        // Create a map for quick species lookup
        const speciesMap = new Map(speciesData.data?.map(s => [s.id, s]) || []);

        // Transform the data to include species information
        const transformedData = photosData.data?.map(p => ({
          ...p,
          species: p.species_id ? speciesMap.get(p.species_id) || null : null
        })) || [];

        setPhotos(transformedData);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load data');
      toast.error("Failed to load photos and species data");
    } finally {
      setLoading(false);
    }
  }, [mode, assignmentFilter, toast]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // Shared handlers
  const handlePhotoUpdate = () => {
    loadData();
    onPhotoUpdate?.();
  };

  const handleSpeciesUpdate = () => {
    loadData();
    onSpeciesUpdate?.();
  };

  const handlePhotoSelection = (photoId: string, selected: boolean) => {
    if (selected) {
      setSelectedPhotoIds(prev => [...prev, photoId]);
    } else {
      setSelectedPhotoIds(prev => prev.filter(id => id !== photoId));
    }
  };

  const handleSelectAll = () => {
    if (selectedPhotoIds.length === filteredPhotos.length) {
      setSelectedPhotoIds([]);
    } else {
      setSelectedPhotoIds(filteredPhotos.map(p => p.id));
    }
  };

  const handleClearSelection = () => {
    setSelectedPhotoIds([]);
  };

  // Mode-specific handlers
  const handleDragStart = async (photoId: string) => {
    if (mode === 'assign') {
      setIsDragging(true);
      try {
        const suggestions = await getAISuggestions(photoId);
        setAiSuggestions(suggestions);
      } catch (error) {
        console.error('Error getting AI suggestions:', error);
      }
    }
  };

  const handleDragEnd = () => {
    setIsDragging(false);
    setAiSuggestions([]);
  };

  const handleRematch = async () => {
    if (mode !== 'rematch') return;
    
    setIsRematching(true);
    setRematchResult(null);
    
    try {
      // Simplified rematch logic
      let matched = 0;
      let unmatched = 0;
      
      for (const photo of photos) {
        if (photo.species_id && species.find(s => s.id === photo.species_id)) {
          matched++;
        } else {
          unmatched++;
        }
      }

      setRematchResult({
        totalPhotos: photos.length,
        matched,
        unmatched,
        updated: 0,
        message: "Rematch completed successfully"
      });
    } catch (error) {
      console.error('Rematch error:', error);
      setRematchResult({
        totalPhotos: photos.length,
        matched: 0,
        unmatched: photos.length,
        updated: 0,
        message: "Rematch failed"
      });
    } finally {
      setIsRematching(false);
    }
  };

  const handleDeletePhoto = async (photoId: string) => {
    if (!confirm('Are you sure you want to delete this photo? This action cannot be undone.')) {
      return;
    }

    setDeletingIds(prev => new Set(prev).add(photoId));

    try {
      const result = await PhotoUploadManager.deletePhoto(photoId);
      
      if (result.success) {
        setPhotos(prev => prev.filter(p => p.id !== photoId));
        toast.success("Photo deleted successfully");
      } else {
        toast.error(`Failed to delete photo: ${result.error}`);
      }
    } catch (error) {
      console.error('Delete error:', error);
      toast.error("Failed to delete photo");
    } finally {
      setDeletingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(photoId);
        return newSet;
      });
    }
  };

  const handleDownloadPhoto = async (photo: Photo) => {
    try {
      const response = await fetch(photo.url!);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${photo.title || 'photo'}-${photo.id}.jpg`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success("Photo downloaded");
    } catch (error) {
      console.error('Download error:', error);
      toast.error("Failed to download photo");
    }
  };

  const handleAIReview = async () => {
    setIsAIReviewing(true);
    setAiReviewResults([]);

    try {
      // Get unassigned photos
      const unassignedPhotos = photos.filter(photo => !photo.species_id);

      if (unassignedPhotos.length === 0) {
        toast.info("No unassigned photos to review");
        return;
      }

      toast.info(`Starting AI review of ${unassignedPhotos.length} photos...`);

      const results = [];

      // Process photos in batches to avoid overwhelming the API
      const batchSize = 5;
      for (let i = 0; i < unassignedPhotos.length; i += batchSize) {
        const batch = unassignedPhotos.slice(i, i + batchSize);

        const batchPromises = batch.map(async (photo) => {
          try {
            // Simulate AI analysis - in real implementation, this would call an AI service
            const suggestions = await getAISuggestions(photo.id);

            if (suggestions.length > 0) {
              const topSuggestion = suggestions[0];

              // Update photo with AI suggestion
              const { error } = await supabase
                .from('photos_v2')
                .update({
                  ai_suggested_id: topSuggestion.speciesId,
                  ai_confidence: topSuggestion.confidenceScore / 100,
                  ai_reviewed: false
                })
                .eq('id', photo.id);

              if (error) throw error;

              return {
                photoId: photo.id,
                photoUrl: photo.url,
                photoTitle: photo.title,
                suggestion: topSuggestion,
                success: true
              };
            }

            return {
              photoId: photo.id,
              photoUrl: photo.url,
              photoTitle: photo.title,
              suggestion: null,
              success: false,
              reason: 'No suggestions found'
            };
          } catch (error) {
            console.error(`Error processing photo ${photo.id}:`, error);
            return {
              photoId: photo.id,
              photoUrl: photo.url,
              photoTitle: photo.title,
              suggestion: null,
              success: false,
              reason: error instanceof Error ? error.message : 'Unknown error'
            };
          }
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);

        // Update progress
        toast.info(`Processed ${Math.min(i + batchSize, unassignedPhotos.length)} of ${unassignedPhotos.length} photos`);
      }

      setAiReviewResults(results);

      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;

      toast.success(`AI review complete! ${successCount} photos identified, ${failCount} failed. Review suggestions below.`);

      // Refresh photos to show updated data
      await loadData();

    } catch (error) {
      console.error('AI review error:', error);
      toast.error("Failed to complete AI review");
    } finally {
      setIsAIReviewing(false);
    }
  };

  const handleAcceptAISuggestion = async (result: any) => {
    try {
      const { error } = await supabase
        .from('photos_v2')
        .update({
          species_id: result.suggestion.speciesId,
          ai_reviewed: true
        })
        .eq('id', result.photoId);

      if (error) throw error;

      toast.success(`Accepted suggestion: ${result.suggestion.speciesName}`);

      // Remove from results
      setAiReviewResults(prev => prev.filter(r => r.photoId !== result.photoId));

      // Refresh photos
      await loadData();
    } catch (error) {
      console.error('Error accepting suggestion:', error);
      toast.error("Failed to accept suggestion");
    }
  };

  const handleRejectAISuggestion = async (result: any) => {
    try {
      const { error } = await supabase
        .from('photos_v2')
        .update({
          ai_suggested_id: null,
          ai_confidence: null,
          ai_reviewed: true
        })
        .eq('id', result.photoId);

      if (error) throw error;

      toast.success("Rejected AI suggestion");

      // Remove from results
      setAiReviewResults(prev => prev.filter(r => r.photoId !== result.photoId));

      // Refresh photos
      await loadData();
    } catch (error) {
      console.error('Error rejecting suggestion:', error);
      toast.error("Failed to reject suggestion");
    }
  };

  const handleAcceptAllAISuggestions = async () => {
    const successfulResults = aiReviewResults.filter(r => r.success);

    if (successfulResults.length === 0) {
      toast.info("No successful suggestions to accept");
      return;
    }

    try {
      // Update all photos in batch
      const updates = successfulResults.map(result => ({
        id: result.photoId,
        species_id: result.suggestion.speciesId,
        ai_reviewed: true
      }));

      for (const update of updates) {
        const { error } = await supabase
          .from('photos_v2')
          .update({
            species_id: update.species_id,
            ai_reviewed: update.ai_reviewed
          })
          .eq('id', update.id);

        if (error) throw error;
      }

      toast.success(`Accepted ${successfulResults.length} AI suggestions`);
      setAiReviewResults([]);
      await loadData();
    } catch (error) {
      console.error('Error accepting all suggestions:', error);
      toast.error("Failed to accept all suggestions");
    }
  };

  const handleEditPhoto = (photo: Photo) => {
    setEditingPhoto(photo);
  };

  const handlePhotoEditSuccess = () => {
    setEditingPhoto(null);
    loadData(); // Refresh the photos list
  };

  // Filtering logic
  const filteredPhotos = photos.filter(photo => {
    if (!photo) return false;
    const lowercasedTerm = searchTerm.toLowerCase();
    const matchesSearch = !searchTerm || 
      (photo.title && photo.title.toLowerCase().includes(lowercasedTerm)) ||
      (photo.description && photo.description.toLowerCase().includes(lowercasedTerm)) ||
      (photo.species && (
        photo.species.name?.toLowerCase().includes(lowercasedTerm) ||
        (photo.species.common_name && photo.species.common_name.toLowerCase().includes(lowercasedTerm))
      ));
    
    let matchesFilter = true;
    if (mode === 'assign' && assignmentFilter === 'published') matchesFilter = photo.published;
    if (mode === 'assign' && assignmentFilter === 'unpublished') matchesFilter = !photo.published;
    
    return matchesSearch && matchesFilter;
  });

  const filteredSpecies = species.filter(s => {
    if (!s) return false;
    if (selectedCategory === 'all') return true;
    return s.category === selectedCategory;
  });

  // Get unique categories for filter
  const categories = ['all', ...Array.from(new Set(species.map(s => s?.category).filter(Boolean))) as string[]];

  const getStats = () => {
    const totalPhotos = photos.length;
    const assignedPhotos = photos.filter(p => p.species_id).length;
    const unassignedPhotos = totalPhotos - assignedPhotos;
    
    return { totalPhotos, assignedPhotos, unassignedPhotos };
  };

  const stats = getStats();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="w-8 h-8 animate-spin mr-2" />
        Loading photos...
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center max-w-md">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Error Loading Data</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={loadData} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div className={`space-y-6 ${className}`}>
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold">{title || `Photo ${mode.charAt(0).toUpperCase() + mode.slice(1)}`}</h2>
            <p className="text-gray-600">
              {mode === 'assign' && `${stats.unassignedPhotos} unassigned of ${stats.totalPhotos} total photos`}
              {mode === 'rematch' && `${photos.length} photos available for rematching`}
              {mode === 'gallery' && `${filteredPhotos.length} of ${photos.length} photos`}
            </p>
          </div>
          
          <div className="flex gap-2">
            {mode === 'gallery' && (
              <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
                <DialogTrigger asChild>
                  <Button>
                    <Upload className="w-4 h-4 mr-2" />
                    Upload Photos
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Upload New Photos</DialogTitle>
                    <DialogDescription>
                      Upload and manage photos for your wildlife collection. AI can automatically generate captions and metadata.
                    </DialogDescription>
                  </DialogHeader>
                  <EnhancedPhotoUpload
                    onUploadComplete={() => {
                      setShowUploadDialog(false);
                      handlePhotoUpdate();
                    }}
                    onClose={() => setShowUploadDialog(false)}
                  />
                </DialogContent>
              </Dialog>
            )}

            {mode === 'assign' && (
              <Button
                onClick={handleAIReview}
                disabled={isAIReviewing || stats.unassignedPhotos === 0}
                className="flex items-center gap-2"
              >
                {isAIReviewing ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Brain className="w-4 h-4" />
                )}
                AI Review ({stats.unassignedPhotos} photos)
              </Button>
            )}

            {mode === 'rematch' && (
              <Button 
                onClick={handleRematch} 
                disabled={isRematching}
                className="bg-orange-600 hover:bg-orange-700"
              >
                {isRematching ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4 mr-2" />
                )}
                {isRematching ? 'Rematching...' : 'Start Rematch'}
              </Button>
            )}
            
            <Button variant="outline" onClick={loadData}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Controls */}
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search photos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Mode-specific filters */}
          {mode === 'assign' && (
            <Select value={assignmentFilter} onValueChange={(value: FilterType) => setAssignmentFilter(value)}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Photos</SelectItem>
                <SelectItem value="unassigned">Unassigned</SelectItem>
                <SelectItem value="assigned">Assigned</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="unpublished">Unpublished</SelectItem>
                <SelectItem value="needs_recovery" disabled>Needs Recovery (Coming Soon)</SelectItem>
              </SelectContent>
            </Select>
          )}

          {/* View mode toggle */}
          <div className="flex border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid3X3 className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-l-none"
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Rematch Results */}
        {mode === 'rematch' && rematchResult && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Rematch Results:</strong> {rematchResult.message}
              <br />
              Total: {rematchResult.totalPhotos} |
              Matched: {rematchResult.matched} |
              Unmatched: {rematchResult.unmatched} |
              Updated: {rematchResult.updated}
            </AlertDescription>
          </Alert>
        )}

        {/* AI Review Results */}
        {mode === 'assign' && aiReviewResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-4 h-4" />
                AI Review Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-sm text-gray-600">
                  {aiReviewResults.filter(r => r.success).length} photos identified, {aiReviewResults.filter(r => !r.success).length} failed
                </div>

                <div className="max-h-64 overflow-y-auto space-y-2">
                  {aiReviewResults.map((result, index) => (
                    <div key={index} className={`p-3 rounded border ${result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                      <div className="flex items-center gap-3">
                        <img
                          src={result.photoUrl}
                          alt={result.photoTitle || 'Photo'}
                          className="w-12 h-12 object-cover rounded"
                        />
                        <div className="flex-1">
                          <div className="font-medium text-sm">{result.photoTitle || 'Untitled Photo'}</div>
                          {result.success && result.suggestion ? (
                            <div className="text-sm text-green-700">
                              Suggested: {result.suggestion.speciesName} ({result.suggestion.confidenceScore}% confidence)
                            </div>
                          ) : (
                            <div className="text-sm text-red-700">
                              {result.reason || 'No identification found'}
                            </div>
                          )}
                        </div>
                        {result.success && (
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleAcceptAISuggestion(result)}
                            >
                              Accept
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleRejectAISuggestion(result)}
                            >
                              Reject
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                <div className="flex gap-2">
                  <Button
                    size="sm"
                    onClick={() => handleAcceptAllAISuggestions()}
                    disabled={aiReviewResults.filter(r => r.success).length === 0}
                  >
                    Accept All
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setAiReviewResults([])}
                  >
                    Clear Results
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Warning for needs_recovery filter */}
        {mode === 'assign' && assignmentFilter === 'needs_recovery' && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Feature Coming Soon:</strong> The "Needs Recovery" filter is not yet available. 
              This feature will be enabled once the database migration is applied.
            </AlertDescription>
          </Alert>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar - Species for assignment */}
          {mode === 'assign' && (
            <div className="lg:col-span-1 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Tag className="w-4 h-4" />
                    Species
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category} value={category}>
                          {category === 'all' ? 'All Categories' : category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  <div className="mt-4 space-y-2 max-h-96 overflow-y-auto">
                    {filteredSpecies.map(species => (
                      <DropTargetSpecies
                        key={species.id}
                        species={species}
                        aiSuggestions={aiSuggestions}
                        isDragging={isDragging}
                      />
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* AI Suggestions */}
              {aiSuggestions.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Brain className="w-4 h-4" />
                      AI Suggestions
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {aiSuggestions.map((suggestion, index) => (
                        <div key={index} className="p-2 bg-blue-50 rounded border">
                          <div className="font-medium">{suggestion.speciesName}</div>
                          <div className="text-sm text-gray-600">
                            Confidence: {suggestion.confidenceScore}%
                          </div>
                          <div className="text-xs text-gray-500">
                            {suggestion.reason}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {/* Photos Grid/List */}
          <div className={`${mode === 'assign' ? 'lg:col-span-3' : 'col-span-full'}`}>
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                {filteredPhotos.map(photo => (
                  <Card key={photo.id} className="overflow-hidden group">
                    <div className="aspect-square relative">
                      {photo.url ? (
                        <img
                          src={photo.url}
                          alt={photo.title || 'Photo'}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <ImageIcon className="w-12 h-12 text-gray-400" />
                        </div>
                      )}
                      
                      {/* Mode-specific overlays */}
                      {mode === 'assign' && (
                        <DraggablePhoto
                          photo={photo}
                          species={species}
                          onDragStart={() => handleDragStart(photo.id)}
                          onDragEnd={handleDragEnd}
                        />
                      )}

                      {/* Gallery mode action buttons */}
                      {mode === 'gallery' && (
                        <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button
                            size="sm"
                            variant="secondary"
                            className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditPhoto(photo);
                            }}
                          >
                            <Edit className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="secondary"
                            className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDownloadPhoto(photo);
                            }}
                          >
                            <Download className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            className="h-8 w-8 p-0 bg-red-500/90 hover:bg-red-600"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeletePhoto(photo.id);
                            }}
                            disabled={deletingIds.has(photo.id)}
                          >
                            {deletingIds.has(photo.id) ? (
                              <Loader2 className="w-3 h-3 animate-spin" />
                            ) : (
                              <Trash2 className="w-3 h-3" />
                            )}
                          </Button>
                        </div>
                      )}
                      
                      {mode === 'gallery' && (
                        <div className="absolute top-2 right-2 flex gap-1">
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => handleDownloadPhoto(photo)}
                          >
                            <Download className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleDeletePhoto(photo.id)}
                            disabled={deletingIds.has(photo.id)}
                          >
                            {deletingIds.has(photo.id) ? (
                              <Loader2 className="w-3 h-3 animate-spin" />
                            ) : (
                              <Trash2 className="w-3 h-3" />
                            )}
                          </Button>
                        </div>
                      )}
                    </div>
                    
                    <CardContent className="p-4">
                      <h3 className="font-semibold truncate">{photo.title || 'Untitled'}</h3>
                      {photo.species && (
                        <p className="text-sm text-gray-600">{photo.species.name}</p>
                      )}
                      {mode === 'gallery' && (
                        <div className="mt-2 text-xs text-gray-500">
                          {formatDate(photo.created_at)}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="space-y-2">
                {filteredPhotos.map(photo => (
                  <Card key={photo.id} className="p-4">
                    <div className="flex items-center gap-4">
                      <div className="w-16 h-16 relative">
                        {photo.url ? (
                          <img
                            src={photo.url}
                            alt={photo.title || 'Photo'}
                            className="w-full h-full object-cover rounded"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center">
                            <ImageIcon className="w-8 h-8 text-gray-400" />
                          </div>
                        )}
                      </div>
                      
                      <div className="flex-1">
                        <h3 className="font-semibold">{photo.title || 'Untitled'}</h3>
                        {photo.species && (
                          <p className="text-sm text-gray-600">{photo.species.name}</p>
                        )}
                        <p className="text-xs text-gray-500">{formatDate(photo.created_at)}</p>
                      </div>
                      
                      <div className="flex gap-2">
                        {mode === 'assign' && (
                          <DraggablePhoto
                            photo={photo}
                            species={species}
                            onDragStart={() => handleDragStart(photo.id)}
                            onDragEnd={handleDragEnd}
                          />
                        )}
                        
                        {mode === 'gallery' && (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEditPhoto(photo)}
                            >
                              <Edit className="w-3 h-3 mr-1" />
                              Edit
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDownloadPhoto(photo)}
                            >
                              <Download className="w-3 h-3 mr-1" />
                              Download
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDeletePhoto(photo.id)}
                              disabled={deletingIds.has(photo.id)}
                            >
                              {deletingIds.has(photo.id) ? (
                                <Loader2 className="w-3 h-3 animate-spin" />
                              ) : (
                                <Trash2 className="w-3 h-3" />
                              )}
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Photo Edit Modal */}
      <PhotoEditModal
        photo={editingPhoto}
        open={!!editingPhoto}
        onOpenChange={(open) => !open && setEditingPhoto(null)}
        onSuccess={handlePhotoEditSuccess}
      />
    </DndProvider>
  );
}