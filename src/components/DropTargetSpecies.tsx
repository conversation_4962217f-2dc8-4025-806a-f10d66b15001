import React, { useState, useEffect } from 'react';
import { useDrop } from 'react-dnd';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { reassignPhotoSpecies, bulkReassignPhotos } from '@/lib/photoApi';
import { SpeciesDetailModal } from '@/components/SpeciesDetailModal';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Leaf,
  Users,
  Calendar,
  Edit3,
  Brain,
  Sparkles
} from 'lucide-react';

interface Species {
  id: string;
  name: string;
  common_name: string | null;
  scientific_name: string | null;
  category: string | null;
  conservation_status: string | null;
  description?: string | null;
  published: boolean;
  total_photos?: number;
  published_photos?: number;
}

interface AISuggestion {
  speciesId: string;
  speciesName: string;
  confidenceScore: number;
  reason: string;
}

interface DropTargetSpeciesProps {
  species: Species;
  onSpeciesUpdate?: () => void;
  showPhotoCounts?: boolean;
  aiSuggestions?: AISuggestion[];
  isDragging?: boolean;
}

export function DropTargetSpecies({ 
  species, 
  onSpeciesUpdate,
  showPhotoCounts = false,
  aiSuggestions = [],
  isDragging = false
}: DropTargetSpeciesProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);


  // Add this log to debug AI suggestions
  if (isDragging) {
    console.log(`Suggestion overlays for species ${species.id}:`, aiSuggestions);
  }

  // Add enhanced logging for AI suggestions
  useEffect(() => {
    console.log(`AI suggestions for DropTargetSpecies ${species.id}:`, aiSuggestions);
  }, [aiSuggestions]);

  // Check if this species is in AI suggestions
  const aiSuggestion = aiSuggestions.find(s => s.speciesId === species.id);
  const isAISuggested = !!aiSuggestion;

  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: 'PHOTO',
    drop: async (item: { 
      photoId: number; 
      currentSpeciesId: string | null; 
      photoTitle: string;
      selectedPhotoIds?: number[];
    }) => {
      if (isProcessing) return;
      
      setIsProcessing(true);
      try {
        // Handle bulk assignment if multiple photos are selected
        if (item.selectedPhotoIds && item.selectedPhotoIds.length > 1) {
          await bulkReassignPhotos({
            photoIds: item.selectedPhotoIds,
            newSpeciesId: species.id,
            reason: 'bulk drag-and-drop reassignment'
          });

          toast.success(`${item.selectedPhotoIds.length} photos assigned to ${species.common_name || species.name}`);
        } else {
          // Single photo assignment
          await reassignPhotoSpecies({
            photoId: item.photoId,
            newSpeciesId: species.id,
            oldSpeciesId: item.currentSpeciesId,
            reason: 'drag-and-drop reassignment'
          });

          toast.success(`"${item.photoTitle}" assigned to ${species.common_name || species.name}`);
        }

        onSpeciesUpdate?.();
      } catch (error) {
        console.error('Error reassigning photo:', error);
        toast.error("Failed to reassign photo. Please try again.");
      } finally {
        setIsProcessing(false);
      }
    },
    canDrop: (item) => {
      // Can drop if photo is not already assigned to this species
      return item.currentSpeciesId !== species.id;
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  }));

  const getConservationStatusColor = (status: string | null) => {
    if (!status) return 'secondary';
    switch (status.toLowerCase()) {
      case 'endangered':
      case 'critically endangered':
        return 'destructive';
      case 'vulnerable':
      case 'near threatened':
        return 'default';
      case 'least concern':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getCategoryIcon = (category: string | null) => {
    if (!category) return <Leaf className="w-4 h-4" />;
    switch (category.toLowerCase()) {
      case 'mammal':
        return <Users className="w-4 h-4" />;
      case 'bird':
        return <Calendar className="w-4 h-4" />;
      default:
        return <Leaf className="w-4 h-4" />;
    }
  };

  const getConfidenceColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getGlowColor = (score: number) => {
    if (score >= 80) return 'ring-green-400';
    if (score >= 60) return 'ring-yellow-400';
    return 'ring-red-400';
  }

  return (
    <>
      <Card
        ref={drop}
        className={`
          transition-all duration-200 cursor-pointer relative p-3
          ${isOver && canDrop ? 'bg-green-50 border-green-300 scale-105' : ''}
          ${isOver && !canDrop ? 'bg-red-50 border-red-300' : ''}
          ${!isOver ? 'hover:bg-gray-50' : ''}
          ${isProcessing ? 'opacity-75' : ''}
          ${isAISuggested && isDragging ? `${getGlowColor(aiSuggestion.confidenceScore)} ring-2 animate-pulse` : ''}
        `}
        title={species.common_name || species.name}
      >
        {/* AI Suggestion Overlay */}
        {isAISuggested && isDragging && aiSuggestion && (
          <div className="absolute -top-2 -right-2 z-10">
            <div className={`flex items-center gap-1 text-white px-2 py-0.5 rounded-full text-xs font-medium shadow-lg ${getConfidenceColor(aiSuggestion.confidenceScore)}`}>
              <Brain className="w-3 h-3" />
              <span>{Math.round(aiSuggestion.confidenceScore)}%</span>
            </div>
          </div>
        )}

        <CardHeader className="p-0 mb-2">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <CardTitle className="text-base font-semibold truncate" title={species.common_name || species.name}>
                {species.common_name || species.name}
              </CardTitle>
            </div>
            
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => { e.stopPropagation(); setIsModalOpen(true); }}
              className="h-6 w-6"
              title="Edit Species"
            >
              <Edit3 className="w-4 h-4" />
            </Button>
          </div>

          <div className="flex items-center gap-1.5 mt-1">
            {!species.published && (
              <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-300">
                Draft
              </Badge>
            )}
            {species.category && (
              <Badge variant="outline" className="text-xs">{species.category}</Badge>
            )}
            {species.conservation_status && (
              <Badge
                variant={getConservationStatusColor(species.conservation_status)}
                className="text-xs"
              >
                {species.conservation_status}
              </Badge>
            )}
          </div>
        </CardHeader>

        <CardContent className="p-0">
          {/* Drop Zone Indicator */}
          <div className={`
            border-2 border-dashed rounded-md p-3 text-center transition-colors
            ${isOver && canDrop ? 'border-green-400 bg-green-100' : 'border-gray-300'}
            ${isOver && !canDrop ? 'border-red-400 bg-red-100' : ''}
          `}>
            {isProcessing ? (
              <div className="flex items-center justify-center gap-2 text-sm">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                <span>Processing...</span>
              </div>
            ) : isOver && canDrop ? (
              <div className="flex items-center justify-center gap-2 text-green-700 font-medium text-sm">
                <CheckCircle className="w-4 h-4" />
                <span>Drop to assign</span>
              </div>
            ) : isOver && !canDrop ? (
              <div className="flex items-center justify-center gap-2 text-red-700 font-medium text-sm">
                <XCircle className="w-4 h-4" />
                <span>Already assigned</span>
              </div>
            ) : (
              <p className="text-xs text-muted-foreground">Drop photo here</p>
            )}
          </div>
          
          {showPhotoCounts && (
            <div className="text-sm text-muted-foreground">
              Total Photos: {species.total_photos || 0}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Species Detail Modal */}
      <SpeciesDetailModal
        species={species}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSpeciesUpdate={onSpeciesUpdate}
      />
    </>
  );
} 