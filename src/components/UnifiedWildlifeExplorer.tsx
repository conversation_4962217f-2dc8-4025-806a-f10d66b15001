import React, { useState, useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { SkipLink } from '@/components/ui/skip-link';
import { supabase } from '@/integrations/supabase/client';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { useDebouncedSearch } from '@/hooks/useVirtualization';
import { useUnassignedPhotos } from '@/hooks/useUnassignedPhotos';
import { EditSpeciesModal } from '@/components/EditSpeciesModal';
import { toast } from 'sonner';
import {
  Search,
  Filter,
  Grid3X3,
  List,
  Camera,
  Upload,
  Brain,
  BarChart3,
  Leaf,
  MapPin,
  Calendar,
  Eye,
  Heart,
  Share2,
  Download,
  Info,
  Globe,
  Users,
  Shield,
  Zap,
  Star,
  ChevronDown,
  Loader2,
  Maximize2,
  Settings,
  ArrowUpDown,
  Sliders,
  Tag,
  Play,
  Pause,
  Edit,
  ImageIcon,
  Trash2,
  Sparkles,
  TrendingUp
} from 'lucide-react';
import { CategoryIcons, ActionIcons, getCategoryIcon, getConservationIcon } from '@/components/ui/wildlife-icons';

interface Species {
  id: string;
  name: string;
  common_name: string | null;
  scientific_name: string | null;
  category: string | null;
  conservation_status: string | null;
  description: string | null;
  habitat: string | null;
  published: boolean;
  photo_count?: number;
  photos?: Photo[];
}

interface Photo {
  id: number;
  species_id: string | null;
  url: string | null;
  title: string | null;
  description: string | null;
  location: string | null;
  photographer: string | null;
  published: boolean;
  created_at: string;
  species?: {
    id: string;
    name: string;
    category: string;
    conservation_status?: string;
  };
}

interface FilterState {
  search: string;
  categories: string[];
  conservationStatuses: string[];
  locations: string[];
  publishedOnly: boolean;
  hasPhotos: boolean;
}

type ViewMode = 'grid' | 'list' | 'detailed';
type SortOption = 'name' | 'category' | 'photos' | 'recent';

const CATEGORY_ICONS: Record<string, string> = {
  'Mammal': '🦌',
  'Bird': '🦅',
  'Reptile': '🦎',
  'Amphibian': '🐸',
  'Fish': '🐟',
  'Insect': '🦋',
  'Arachnid': '🕷️',
  'Crustacean': '🦀',
  'Mollusk': '🐚',
  'Other': '🔬'
};

const CONSERVATION_COLORS: Record<string, string> = {
  'Least Concern': 'bg-green-100 text-green-800',
  'Near Threatened': 'bg-yellow-100 text-yellow-800',
  'Vulnerable': 'bg-orange-100 text-orange-800',
  'Endangered': 'bg-red-100 text-red-800',
  'Critically Endangered': 'bg-red-200 text-red-900',
  'Extinct in the Wild': 'bg-gray-100 text-gray-800',
  'Extinct': 'bg-black text-white'
};

export function UnifiedWildlifeExplorer() {
  const [species, setSpecies] = useState<Species[]>([]);
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('species');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortBy, setSortBy] = useState<SortOption>('name');
  const [showFilters, setShowFilters] = useState(false);

  // Edit modal state
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [selectedSpecies, setSelectedSpecies] = useState<Species | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    categories: [],
    conservationStatuses: [],
    locations: [],
    publishedOnly: true,
    hasPhotos: false
  });

  const [metadata, setMetadata] = useState({
    categories: [] as string[],
    conservationStatuses: [] as string[],
    locations: [] as string[]
  });

  const { isAdmin } = useAdminAuth();
  const debouncedSearch = useDebouncedSearch(filters.search, 300);
  const { count: unassignedCount, loading: unassignedLoading } = useUnassignedPhotos();

  useEffect(() => {
    loadData();
    loadMetadata();
  }, [isAdmin]); // Reload when admin status changes

  const loadData = async () => {
    try {
      setLoading(true);

      // Load species - show all species for admin, only published for public
      let speciesQuery = supabase
        .from('species_v2')
        .select('*');

      if (!isAdmin) {
        speciesQuery = speciesQuery.eq('published', true);
      }

      const { data: speciesData, error: speciesError } = await speciesQuery.order('name');

      if (speciesError) throw speciesError;

      // Load photos with species info (using manual join due to FK constraint issues)
      const { data: photosData, error: photosError } = await supabase
        .from('photos_v2')
        .select('*')
        .eq('published', true)
        .order('created_at', { ascending: false })
        .limit(5000); // Increased limit to load more photos for better coverage

      if (photosError) throw photosError;

      // Calculate photo counts and attach photos to each species
      const speciesWithPhotosAndCounts = (speciesData || []).map(species => {
        const speciesPhotos = (photosData || []).filter(photo => photo.species_id === species.id);
        return {
          ...species,
          photo_count: speciesPhotos.length,
          photos: speciesPhotos // Add photos array to species object
        };
      });

      // Add species info to photos manually
      const photosWithSpecies = (photosData || []).map(photo => {
        const species = speciesData?.find(s => s.id === photo.species_id);
        return {
          ...photo,
          species: species ? {
            id: species.id,
            name: species.name,
            category: species.category,
            conservation_status: species.conservation_status
          } : null
        };
      });

      setSpecies(speciesWithPhotosAndCounts);
      setPhotos(photosWithSpecies);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadMetadata = async () => {
    try {
      // Get unique categories - show all for admin, only published for public
      let categoryQuery = supabase
        .from('species_v2')
        .select('category')
        .not('category', 'is', null);

      if (!isAdmin) {
        categoryQuery = categoryQuery.eq('published', true);
      }

      const { data: categoryData } = await categoryQuery;

      // Get unique conservation statuses - show all for admin, only published for public
      let statusQuery = supabase
        .from('species_v2')
        .select('conservation_status')
        .not('conservation_status', 'is', null);

      if (!isAdmin) {
        statusQuery = statusQuery.eq('published', true);
      }

      const { data: statusData } = await statusQuery;

      // Get unique locations from photos
      const { data: locationData } = await supabase
        .from('photos_v2')
        .select('location')
        .eq('published', true)
        .not('location', 'is', null);

      setMetadata({
        categories: [...new Set(categoryData?.map(d => d.category).filter(Boolean))],
        conservationStatuses: [...new Set(statusData?.map(d => d.conservation_status).filter(Boolean))],
        locations: [...new Set(locationData?.map(d => d.location).filter(Boolean))]
      });
    } catch (error) {
      console.error('Error loading metadata:', error);
    }
  };

  // Admin handlers
  const handleEditSpecies = (species: Species) => {
    setSelectedSpecies(species);
    setEditModalOpen(true);
  };

  const handleDeleteSpecies = async (species: Species) => {
    const confirmed = window.confirm(
      `Are you sure you want to delete "${species.common_name || species.name}"?\n\n` +
      `This will:\n` +
      `• Remove the species from the database\n` +
      `• Unassign all photos from this species\n` +
      `• This action cannot be undone`
    );

    if (!confirmed) return;

    setDeletingId(species.id);
    try {
      // First, check how many photos are assigned to this species
      const { count: photoCount, error: countError } = await supabase
        .from('photos_v2')
        .select('*', { count: 'exact', head: true })
        .eq('species_id', species.id);

      if (countError) {
        console.error('Error counting photos:', countError);
        toast.error('Failed to check photo assignments');
        return;
      }

      // Unassign photos first
      if (photoCount && photoCount > 0) {
        const { error: unassignError } = await supabase
          .from('photos_v2')
          .update({ species_id: null })
          .eq('species_id', species.id);

        if (unassignError) {
          console.error('Error unassigning photos:', unassignError);
          toast.error('Failed to unassign photos');
          return;
        }
      }

      // Delete the species
      const { error: deleteError } = await supabase
        .from('species_v2')
        .delete()
        .eq('id', species.id);

      if (deleteError) {
        console.error('Error deleting species:', deleteError);
        toast.error('Failed to delete species');
        return;
      }

      toast.success(`Species "${species.common_name || species.name}" deleted successfully`);
      loadData(); // Reload the data
    } catch (error) {
      console.error('Error deleting species:', error);
      toast.error('Failed to delete species');
    } finally {
      setDeletingId(null);
    }
  };

  const handleSpeciesUpdate = () => {
    loadData(); // Reload data after update
    setEditModalOpen(false);
    setSelectedSpecies(null);
  };

  const filteredSpecies = useMemo(() => {
    let filtered = species.filter(s => {
      // Search filter
      if (debouncedSearch) {
        const searchLower = debouncedSearch.toLowerCase();
        const matchesSearch = 
          s.name.toLowerCase().includes(searchLower) ||
          s.common_name?.toLowerCase().includes(searchLower) ||
          s.scientific_name?.toLowerCase().includes(searchLower) ||
          s.description?.toLowerCase().includes(searchLower);
        if (!matchesSearch) return false;
      }

      // Category filter
      if (filters.categories.length > 0 && !filters.categories.includes(s.category || '')) {
        return false;
      }

      // Conservation status filter
      if (filters.conservationStatuses.length > 0 && !filters.conservationStatuses.includes(s.conservation_status || '')) {
        return false;
      }

      // Has photos filter
      if (filters.hasPhotos && (!s.photo_count || s.photo_count === 0)) {
        return false;
      }

      return true;
    });

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'category':
          return (a.category || '').localeCompare(b.category || '');
        case 'photos':
          return (b.photo_count || 0) - (a.photo_count || 0);
        case 'recent':
          return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
        default:
          return 0;
      }
    });

    return filtered;
  }, [species, debouncedSearch, filters, sortBy]);

  const filteredPhotos = useMemo(() => {
    let filtered = photos.filter(p => {
      // Search filter
      if (debouncedSearch) {
        const searchLower = debouncedSearch.toLowerCase();
        const matchesSearch = 
          p.title?.toLowerCase().includes(searchLower) ||
          p.description?.toLowerCase().includes(searchLower) ||
          p.location?.toLowerCase().includes(searchLower) ||
          p.photographer?.toLowerCase().includes(searchLower) ||
          p.species?.name.toLowerCase().includes(searchLower);
        if (!matchesSearch) return false;
      }

      // Location filter
      if (filters.locations.length > 0 && !filters.locations.includes(p.location || '')) {
        return false;
      }

      // Category filter (through species)
      if (filters.categories.length > 0 && !filters.categories.includes(p.species?.category || '')) {
        return false;
      }

      return true;
    });

    return filtered;
  }, [photos, debouncedSearch, filters]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center space-y-6">
          <div className="relative">
            <div className="w-20 h-20 border-4 border-green-200 rounded-full animate-spin border-t-green-600 mx-auto"></div>
            <CategoryIcons.Bird className="absolute inset-0 m-auto w-8 h-8 text-green-600" />
          </div>
          <div className="space-y-2">
            <p className="text-2xl font-bold text-green-700">Loading Wildlife Explorer</p>
            <p className="text-green-600">Discovering amazing species and their stories...</p>
          </div>
          <div className="flex justify-center gap-4 text-sm text-gray-500">
            <span className="flex items-center gap-1">
              <CategoryIcons.Mammal size={16} />
              Mammals
            </span>
            <span className="flex items-center gap-1">
              <CategoryIcons.Bird size={16} />
              Birds
            </span>
            <span className="flex items-center gap-1">
              <CategoryIcons.Marine size={16} />
              Marine Life
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50/50 via-blue-50/50 to-purple-50/50">
      {/* Skip Links for Accessibility */}
      <SkipLink href="#main-content">Skip to main content</SkipLink>
      <SkipLink href="#filters">Skip to filters</SkipLink>

      {/* Hero Section */}
      <header className="relative bg-gradient-to-br from-green-600 via-blue-600 to-purple-600 text-white py-20 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10">
            <CategoryIcons.Bird size={60} className="text-white/20" />
          </div>
          <div className="absolute top-32 right-20">
            <CategoryIcons.Mammal size={80} className="text-white/15" />
          </div>
          <div className="absolute bottom-20 left-1/4">
            <CategoryIcons.Marine size={70} className="text-white/20" />
          </div>
        </div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center">
            <div className="flex items-center justify-center gap-4 mb-6">
              <div className="p-3 bg-white/20 rounded-2xl backdrop-blur-sm">
                <CategoryIcons.Bird size={40} className="text-white" />
              </div>
              <h1 className="text-6xl font-bold bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                Wildlife Explorer
              </h1>
            </div>
            <p className="text-xl text-white/90 mb-10 max-w-4xl mx-auto leading-relaxed">
              Discover and explore magnificent wildlife species from around the world.
              Browse comprehensive species data, stunning photography, and conservation information
              powered by cutting-edge AI technology.
            </p>

            {/* Enhanced Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10 max-w-2xl mx-auto">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <div className="flex items-center justify-center gap-3 mb-2">
                  <CategoryIcons.Bird size={24} className="text-white" />
                  <div className="text-3xl font-bold">{species.length}</div>
                </div>
                <div className="text-sm text-white/80">Species Documented</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <div className="flex items-center justify-center gap-3 mb-2">
                  <ActionIcons.Camera size={24} className="text-white" />
                  <div className="text-3xl font-bold">{photos.length}</div>
                </div>
                <div className="text-sm text-white/80">Photos Captured</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <div className="flex items-center justify-center gap-3 mb-2">
                  <Tag className="w-6 h-6 text-white" />
                  <div className="text-3xl font-bold">{metadata.categories.length}</div>
                </div>
                <div className="text-sm text-white/80">Categories</div>
              </div>
            </div>

            {/* Enhanced Admin Navigation */}
            {isAdmin && (
              <nav aria-label="Admin navigation" className="flex flex-wrap justify-center gap-4">
                <Link to="/admin/species-photo-matrix">
                  <Button variant="wildlife-outline" className="bg-white/10 hover:bg-white/20 border-white/30 text-white hover:text-white backdrop-blur-sm">
                    <Settings className="w-4 h-4 mr-2" />
                    CMS Dashboard
                  </Button>
                </Link>
                <Link to="/photo-assignment">
                  <Button variant="wildlife-outline" className="bg-white/10 hover:bg-white/20 border-white/30 text-white hover:text-white backdrop-blur-sm relative">
                    <ActionIcons.Camera size={16} className="mr-2" />
                    Assign Photos
                    {!unassignedLoading && unassignedCount > 0 && (
                      <Badge className="ml-2 bg-red-500 text-white border-0 animate-pulse">
                        {unassignedCount}
                      </Badge>
                    )}
                  </Button>
                </Link>
                <Link to="/ai-dashboard">
                  <Button variant="wildlife-outline" className="bg-white/10 hover:bg-white/20 border-white/30 text-white hover:text-white backdrop-blur-sm">
                    <ActionIcons.AI size={16} className="mr-2" />
                    AI Dashboard
                  </Button>
                </Link>
              </nav>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-12">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <TabsList className="grid w-full lg:w-auto grid-cols-3 bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200 shadow-lg p-2">
              <TabsTrigger value="species" className="flex items-center gap-3 h-14 rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-blue-500 data-[state=active]:text-white transition-all duration-200">
                <CategoryIcons.Bird size={20} />
                <span className="hidden sm:inline font-medium">Species</span>
                <Badge variant="secondary" className="ml-1 bg-white/20 text-current border-0">
                  {filteredSpecies.length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger value="photos" className="flex items-center gap-3 h-14 rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-blue-500 data-[state=active]:text-white transition-all duration-200">
                <ActionIcons.Camera size={20} />
                <span className="hidden sm:inline font-medium">Photos</span>
                <Badge variant="secondary" className="ml-1 bg-white/20 text-current border-0">
                  {filteredPhotos.length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger value="gallery" className="flex items-center gap-3 h-14 rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-blue-500 data-[state=active]:text-white transition-all duration-200">
                <Grid3X3 className="w-5 h-5" />
                <span className="hidden sm:inline font-medium">Gallery</span>
              </TabsTrigger>
            </TabsList>

            {/* Enhanced Controls */}
            <div className="flex items-center gap-3">
              <Button
                variant={showFilters ? "wildlife" : "outline"}
                size="lg"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 shadow-sm"
              >
                <Filter className="w-4 h-4" />
                <span className="hidden sm:inline">Filters</span>
                {(filters.categories.length > 0 || filters.conservationStatuses.length > 0 || filters.locations.length > 0) && (
                  <Badge variant="secondary" className="ml-1 bg-white/20 text-current border-0 animate-pulse">
                    {filters.categories.length + filters.conservationStatuses.length + filters.locations.length}
                  </Badge>
                )}
              </Button>
              
              <Select value={viewMode} onValueChange={(value: ViewMode) => setViewMode(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="grid">
                    <div className="flex items-center gap-2">
                      <Grid3X3 className="w-4 h-4" />
                      Grid
                    </div>
                  </SelectItem>
                  <SelectItem value="list">
                    <div className="flex items-center gap-2">
                      <List className="w-4 h-4" />
                      List
                    </div>
                  </SelectItem>
                  <SelectItem value="detailed">
                    <div className="flex items-center gap-2">
                      <Eye className="w-4 h-4" />
                      Detailed
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={(value: SortOption) => setSortBy(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="category">Category</SelectItem>
                  <SelectItem value="photos">Photos</SelectItem>
                  <SelectItem value="recent">Recent</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                placeholder="Search species, photos, locations..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="pl-10 h-12 text-lg"
              />
            </div>

            {showFilters && (
              <Card id="filters" className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {/* Categories */}
                  <div>
                    <h3 className="font-semibold mb-3 flex items-center gap-2">
                      <Tag className="w-4 h-4" />
                      Categories
                    </h3>
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {metadata.categories.map(category => (
                        <div key={category} className="flex items-center space-x-2">
                          <Checkbox
                            id={`category-${category}`}
                            checked={filters.categories.includes(category)}
                            onCheckedChange={(checked) => {
                              setFilters(prev => ({
                                ...prev,
                                categories: checked
                                  ? [...prev.categories, category]
                                  : prev.categories.filter(c => c !== category)
                              }));
                            }}
                          />
                          <label htmlFor={`category-${category}`} className="text-sm flex items-center gap-2">
                            <span>{CATEGORY_ICONS[category] || '🔬'}</span>
                            {category}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Conservation Status */}
                  <div>
                    <h3 className="font-semibold mb-3 flex items-center gap-2">
                      <Shield className="w-4 h-4" />
                      Conservation
                    </h3>
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {metadata.conservationStatuses.map(status => (
                        <div key={status} className="flex items-center space-x-2">
                          <Checkbox
                            id={`status-${status}`}
                            checked={filters.conservationStatuses.includes(status)}
                            onCheckedChange={(checked) => {
                              setFilters(prev => ({
                                ...prev,
                                conservationStatuses: checked
                                  ? [...prev.conservationStatuses, status]
                                  : prev.conservationStatuses.filter(s => s !== status)
                              }));
                            }}
                          />
                          <label htmlFor={`status-${status}`} className="text-sm">
                            {status}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Locations */}
                  <div>
                    <h3 className="font-semibold mb-3 flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      Locations
                    </h3>
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {metadata.locations.slice(0, 10).map(location => (
                        <div key={location} className="flex items-center space-x-2">
                          <Checkbox
                            id={`location-${location}`}
                            checked={filters.locations.includes(location)}
                            onCheckedChange={(checked) => {
                              setFilters(prev => ({
                                ...prev,
                                locations: checked
                                  ? [...prev.locations, location]
                                  : prev.locations.filter(l => l !== location)
                              }));
                            }}
                          />
                          <label htmlFor={`location-${location}`} className="text-sm">
                            {location}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Options */}
                  <div>
                    <h3 className="font-semibold mb-3 flex items-center gap-2">
                      <Settings className="w-4 h-4" />
                      Options
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="has-photos"
                          checked={filters.hasPhotos}
                          onCheckedChange={(checked) => {
                            setFilters(prev => ({ ...prev, hasPhotos: !!checked }));
                          }}
                        />
                        <label htmlFor="has-photos" className="text-sm">
                          Has Photos Only
                        </label>
                      </div>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setFilters({
                          search: '',
                          categories: [],
                          conservationStatuses: [],
                          locations: [],
                          publishedOnly: true,
                          hasPhotos: false
                        })}
                        className="w-full"
                      >
                        Clear All Filters
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            )}
          </div>

          {/* Species Tab */}
          <TabsContent value="species" id="main-content">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Wildlife Species
              </h2>
              <p className="text-gray-600" aria-live="polite">
                Showing {filteredSpecies.length} of {species.length} species
                {debouncedSearch && ` matching "${debouncedSearch}"`}
              </p>
            </div>

            {filteredSpecies.length === 0 ? (
              <Card className="p-12 text-center">
                <Leaf className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                <h3 className="text-xl font-semibold mb-2">No species found</h3>
                <p className="text-gray-600 mb-4">
                  Try adjusting your search terms or filters to find more species.
                </p>
                <Button onClick={() => setFilters({
                  search: '',
                  categories: [],
                  conservationStatuses: [],
                  locations: [],
                  publishedOnly: true,
                  hasPhotos: false
                })}>
                  Clear Filters
                </Button>
              </Card>
            ) : (
              <div className={
                viewMode === 'grid'
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                  : viewMode === 'list'
                  ? 'space-y-4'
                  : 'grid grid-cols-1 lg:grid-cols-2 gap-8'
              }>
                {filteredSpecies.map(species => (
                  <SpeciesCard
                    key={species.id}
                    species={species}
                    viewMode={viewMode}
                    isAdmin={isAdmin}
                    onEdit={handleEditSpecies}
                    onDelete={handleDeleteSpecies}
                    deletingId={deletingId}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          {/* Photos Tab */}
          <TabsContent value="photos">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Wildlife Photography
              </h2>
              <p className="text-gray-600" aria-live="polite">
                Showing {filteredPhotos.length} of {photos.length} photos
                {debouncedSearch && ` matching "${debouncedSearch}"`}
              </p>
            </div>

            {filteredPhotos.length === 0 ? (
              <Card className="p-12 text-center">
                <Camera className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                <h3 className="text-xl font-semibold mb-2">No photos found</h3>
                <p className="text-gray-600 mb-4">
                  Try adjusting your search terms or filters to find more photos.
                </p>
                <Button onClick={() => setFilters({
                  search: '',
                  categories: [],
                  conservationStatuses: [],
                  locations: [],
                  publishedOnly: true,
                  hasPhotos: false
                })}>
                  Clear Filters
                </Button>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredPhotos.map(photo => (
                  <PhotoCard key={photo.id} photo={photo} />
                ))}
              </div>
            )}
          </TabsContent>

          {/* Gallery Tab */}
          <TabsContent value="gallery">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Photo Gallery
              </h2>
              <p className="text-gray-600">
                Interactive gallery view with slideshow capabilities
              </p>
            </div>

            <PhotoGallery photos={filteredPhotos} />
          </TabsContent>
        </Tabs>
      </div>

      {/* Edit Species Modal */}
      {selectedSpecies && (
        <EditSpeciesModal
          open={editModalOpen}
          onOpenChange={setEditModalOpen}
          species={selectedSpecies}
          onSave={handleSpeciesUpdate}
        />
      )}
    </div>
  );
}

// Species Card Component
interface SpeciesCardProps {
  species: Species;
  viewMode: ViewMode;
  isAdmin: boolean;
  onEdit?: (species: Species) => void;
  onDelete?: (species: Species) => void;
  deletingId?: string | null;
}

function SpeciesCard({ species, viewMode, isAdmin, onEdit, onDelete, deletingId }: SpeciesCardProps) {
  if (viewMode === 'list') {
    return (
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Photo thumbnail or icon */}
            {species.photos && species.photos.length > 0 ? (
              <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
                <img
                  src={species.photos[0].url}
                  alt={species.common_name || species.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/placeholder.svg';
                  }}
                />
              </div>
            ) : (
              <div className="w-16 h-16 rounded-lg bg-gray-100 flex items-center justify-center flex-shrink-0">
                <div className="text-2xl">
                  {CATEGORY_ICONS[species.category || ''] || '🔬'}
                </div>
              </div>
            )}
            <div>
              <h3 className="font-semibold text-lg">
                <Link to={`/species/${species.id}`} className="hover:text-blue-600">
                  {species.common_name || species.name}
                </Link>
              </h3>
              <p className="text-sm text-gray-600 italic">{species.scientific_name}</p>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline">{species.category || 'Uncategorized'}</Badge>
                {species.conservation_status && (
                  <Badge className={CONSERVATION_COLORS[species.conservation_status] || 'bg-gray-100 text-gray-800'}>
                    {species.conservation_status}
                  </Badge>
                )}
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-600 flex items-center gap-1">
              <Camera className="w-4 h-4" />
              {species.photo_count || 0} photos
            </div>
            {isAdmin && (
              <div className="flex gap-1 mt-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onEdit?.(species);
                  }}
                  title="Edit species"
                >
                  <Edit className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onDelete?.(species);
                  }}
                  disabled={deletingId === species.id}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  title="Delete species"
                >
                  {deletingId === species.id ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Trash2 className="w-4 h-4" />
                  )}
                </Button>
              </div>
            )}
          </div>
        </div>
      </Card>
    );
  }

  return (
    <div className="relative group">
      <Link to={`/species/${species.id}`} className="block">
        <Card className="h-full transition-all duration-200 hover:shadow-lg hover:scale-105 group-hover:border-blue-300 overflow-hidden">
          {/* Photo Section */}
          {species.photos && species.photos.length > 0 ? (
            <div className="relative h-48 overflow-hidden">
              <img
                src={species.photos[0].url}
                alt={species.common_name || species.name}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = '/placeholder.svg';
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
              <div className="absolute top-2 right-2">
                <Badge variant="secondary" className="bg-white/90 text-gray-800 flex items-center gap-1">
                  <Camera className="w-3 h-3" />
                  {species.photo_count}
                </Badge>
              </div>
              <div className="absolute bottom-2 left-2">
                <div className="text-2xl">
                  {CATEGORY_ICONS[species.category || ''] || '🔬'}
                </div>
              </div>
            </div>
          ) : (
            <div className="relative h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
              <div className="text-center">
                <div className="text-4xl mb-2">
                  {CATEGORY_ICONS[species.category || ''] || '🔬'}
                </div>
                <Camera className="w-8 h-8 text-gray-400 mx-auto" />
              </div>
            </div>
          )}

          <CardHeader className="pb-3">
            <CardTitle className="text-lg group-hover:text-blue-600 transition-colors">
              {species.common_name || species.name}
            </CardTitle>
            <p className="text-sm text-gray-600 italic">{species.scientific_name}</p>
          </CardHeader>

          <CardContent>
            <div className="space-y-3">
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline">{species.category || 'Uncategorized'}</Badge>
                {species.conservation_status && (
                  <Badge className={CONSERVATION_COLORS[species.conservation_status] || 'bg-gray-100 text-gray-800'}>
                    {species.conservation_status}
                  </Badge>
                )}
              </div>

              {viewMode === 'detailed' && species.description && (
                <p className="text-sm text-gray-700 line-clamp-3">
                  {species.description}
                </p>
              )}

              {species.habitat && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <MapPin className="w-4 h-4" />
                  {species.habitat}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </Link>

      {/* Admin buttons overlay */}
      {isAdmin && (
        <div className="absolute top-2 left-2 z-10 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            variant="secondary"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onEdit?.(species);
            }}
            className="bg-white/90 hover:bg-white border border-gray-200 shadow-sm"
            title="Edit species"
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onDelete?.(species);
            }}
            disabled={deletingId === species.id}
            className="bg-white/90 hover:bg-red-50 border border-gray-200 shadow-sm text-red-600 hover:text-red-700"
            title="Delete species"
          >
            {deletingId === species.id ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Trash2 className="w-4 h-4" />
            )}
          </Button>
        </div>
      )}
    </div>
  );
}

// Photo Card Component
interface PhotoCardProps {
  photo: Photo;
}

function PhotoCard({ photo }: PhotoCardProps) {
  return (
    <Card className="overflow-hidden group hover:shadow-lg transition-all duration-200">
      <div className="aspect-square relative">
        {photo.url ? (
          <img
            src={photo.url}
            alt={photo.title || 'Wildlife photo'}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
            loading="lazy"
          />
        ) : (
          <div className="w-full h-full bg-gray-200 flex items-center justify-center">
            <Camera className="w-12 h-12 text-gray-400" />
          </div>
        )}

        {/* Overlay */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200" />

        {/* Photo info overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <h3 className="font-semibold text-sm">{photo.title || 'Untitled'}</h3>
          {photo.species && (
            <p className="text-xs text-white/80">{photo.species.name}</p>
          )}
          {photo.location && (
            <div className="flex items-center gap-1 text-xs text-white/80 mt-1">
              <MapPin className="w-3 h-3" />
              {photo.location}
            </div>
          )}
        </div>
      </div>
    </Card>
  );
}

// Photo Gallery Component
interface PhotoGalleryProps {
  photos: Photo[];
}

function PhotoGallery({ photos }: PhotoGalleryProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  useEffect(() => {
    if (isPlaying && photos.length > 1) {
      const interval = setInterval(() => {
        setCurrentIndex(prev => (prev + 1) % photos.length);
      }, 3000);
      return () => clearInterval(interval);
    }
  }, [isPlaying, photos.length]);

  if (photos.length === 0) {
    return (
      <Card className="p-12 text-center">
        <Camera className="w-16 h-16 mx-auto mb-4 text-gray-400" />
        <h3 className="text-xl font-semibold mb-2">No photos available</h3>
        <p className="text-gray-600">No photos match your current filters.</p>
      </Card>
    );
  }

  const currentPhoto = photos[currentIndex];

  return (
    <div className="space-y-6">
      {/* Main Gallery View */}
      <Card className="overflow-hidden">
        <div className="aspect-video relative bg-black">
          {currentPhoto.url ? (
            <img
              src={currentPhoto.url}
              alt={currentPhoto.title || 'Wildlife photo'}
              className="w-full h-full object-contain"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <Camera className="w-24 h-24 text-gray-400" />
            </div>
          )}

          {/* Gallery Controls */}
          <div className="absolute bottom-4 left-4 right-4 flex items-center justify-between">
            <div className="bg-black/60 text-white px-3 py-2 rounded-lg">
              <h3 className="font-semibold">{currentPhoto.title || 'Untitled'}</h3>
              {currentPhoto.species && (
                <p className="text-sm text-white/80">{currentPhoto.species.name}</p>
              )}
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setIsPlaying(!isPlaying)}
                className="bg-black/60 text-white hover:bg-black/80"
              >
                {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              </Button>

              <div className="bg-black/60 text-white px-3 py-2 rounded-lg text-sm">
                {currentIndex + 1} / {photos.length}
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Thumbnail Strip */}
      <div className="flex gap-2 overflow-x-auto pb-2">
        {photos.map((photo, index) => (
          <button
            key={photo.id}
            onClick={() => setCurrentIndex(index)}
            className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all ${
              index === currentIndex
                ? 'border-blue-500 scale-105'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            {photo.url ? (
              <img
                src={photo.url}
                alt={photo.title || 'Thumbnail'}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                <Camera className="w-6 h-6 text-gray-400" />
              </div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
}
