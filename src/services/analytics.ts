import { gtag } from 'gtag';

// Google Analytics configuration
const GA_MEASUREMENT_ID = import.meta.env.VITE_GA_MEASUREMENT_ID;

// Initialize Google Analytics
export const initializeAnalytics = () => {
  if (!GA_MEASUREMENT_ID) {
    console.warn('Google Analytics Measurement ID not found. Analytics will not be initialized.');
    return;
  }

  // Load Google Analytics script
  const script = document.createElement('script');
  script.async = true;
  script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
  document.head.appendChild(script);

  // Initialize gtag
  window.dataLayer = window.dataLayer || [];
  function gtag(...args: any[]) {
    window.dataLayer.push(args);
  }
  
  // Make gtag available globally
  (window as any).gtag = gtag;

  gtag('js', new Date());
  gtag('config', GA_MEASUREMENT_ID, {
    page_title: document.title,
    page_location: window.location.href,
  });

  console.log('Google Analytics initialized with ID:', GA_MEASUREMENT_ID);
};

// Track page views
export const trackPageView = (path: string, title?: string) => {
  if (!GA_MEASUREMENT_ID) return;

  const gtag = (window as any).gtag;
  if (gtag) {
    gtag('config', GA_MEASUREMENT_ID, {
      page_path: path,
      page_title: title || document.title,
    });
  }
};

// Track custom events
export const trackEvent = (action: string, category: string, label?: string, value?: number) => {
  if (!GA_MEASUREMENT_ID) return;

  const gtag = (window as any).gtag;
  if (gtag) {
    gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};

// Wildlife-specific tracking events
export const trackWildlifeEvent = {
  // Species interactions
  viewSpecies: (speciesName: string, speciesId: string) => {
    trackEvent('view_species', 'wildlife', `${speciesName} (${speciesId})`);
  },

  searchSpecies: (searchTerm: string, resultsCount: number) => {
    trackEvent('search_species', 'wildlife', searchTerm, resultsCount);
  },

  filterSpecies: (filterType: string, filterValue: string) => {
    trackEvent('filter_species', 'wildlife', `${filterType}: ${filterValue}`);
  },

  // Photo interactions
  viewPhoto: (photoId: string, speciesName?: string) => {
    trackEvent('view_photo', 'photos', speciesName ? `${speciesName} - ${photoId}` : photoId);
  },

  sharePhoto: (photoId: string, platform: string) => {
    trackEvent('share_photo', 'photos', `${platform} - ${photoId}`);
  },

  // Location interactions
  viewHotspot: (hotspotName: string, hotspotId: string) => {
    trackEvent('view_hotspot', 'locations', `${hotspotName} (${hotspotId})`);
  },

  searchHotspots: (searchTerm: string, resultsCount: number) => {
    trackEvent('search_hotspots', 'locations', searchTerm, resultsCount);
  },

  // Navigation tracking
  navigateToSection: (section: string) => {
    trackEvent('navigate', 'navigation', section);
  },

  // Admin actions (only track non-sensitive actions)
  adminLogin: () => {
    trackEvent('admin_login', 'admin', 'login_success');
  },

  adminLogout: () => {
    trackEvent('admin_logout', 'admin', 'logout');
  },

  // User engagement
  timeOnPage: (pageName: string, timeInSeconds: number) => {
    trackEvent('time_on_page', 'engagement', pageName, timeInSeconds);
  },

  scrollDepth: (pageName: string, percentage: number) => {
    trackEvent('scroll_depth', 'engagement', pageName, percentage);
  },
};

// Enhanced ecommerce tracking (for future use if needed)
export const trackPurchase = (transactionId: string, value: number, currency: string = 'USD') => {
  if (!GA_MEASUREMENT_ID) return;

  const gtag = (window as any).gtag;
  if (gtag) {
    gtag('event', 'purchase', {
      transaction_id: transactionId,
      value: value,
      currency: currency,
    });
  }
};

// User properties (for segmentation)
export const setUserProperties = (properties: Record<string, any>) => {
  if (!GA_MEASUREMENT_ID) return;

  const gtag = (window as any).gtag;
  if (gtag) {
    gtag('config', GA_MEASUREMENT_ID, {
      custom_map: properties,
    });
  }
};

// Consent management (GDPR compliance)
export const updateConsentSettings = (adStorage: boolean, analyticsStorage: boolean) => {
  const gtag = (window as any).gtag;
  if (gtag) {
    gtag('consent', 'update', {
      ad_storage: adStorage ? 'granted' : 'denied',
      analytics_storage: analyticsStorage ? 'granted' : 'denied',
    });
  }
};

// Initialize consent with default settings
export const initializeConsent = () => {
  const gtag = (window as any).gtag;
  if (gtag) {
    gtag('consent', 'default', {
      ad_storage: 'denied',
      analytics_storage: 'granted', // You may want to make this conditional based on user consent
    });
  }
};

export default {
  initializeAnalytics,
  trackPageView,
  trackEvent,
  trackWildlifeEvent,
  trackPurchase,
  setUserProperties,
  updateConsentSettings,
  initializeConsent,
};
