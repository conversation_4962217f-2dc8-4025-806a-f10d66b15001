import { GoogleGenerativeAI } from '@google/generative-ai';

const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;

interface LocationContent {
  name: string;
  description: string;
  highlights: string[];
  speciesCount?: number;
}

interface LocationData {
  name: string;
  type: 'country' | 'state' | 'region';
  searchTerm: string; // What to search for in the location field
}

const FEATURED_LOCATIONS: LocationData[] = [
  { name: 'Australia', type: 'country', searchTerm: 'Australia' },
  { name: 'United States', type: 'country', searchTerm: 'United States' },
  { name: 'Colombia', type: 'country', searchTerm: 'Colombia' },
  { name: 'Oregon', type: 'state', searchTerm: 'Oregon' },
  { name: 'Nevada', type: 'state', searchTerm: 'Nevada' },
];

export class LocationContentService {
  private genAI: GoogleGenerativeAI | null = null;

  constructor() {
    if (GEMINI_API_KEY) {
      this.genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
    }
  }

  async generateLocationDescription(location: LocationData): Promise<string> {
    if (!this.genAI) {
      return this.getFallbackDescription(location);
    }

    try {
      const model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
      
      const prompt = `Write a compelling 2-3 sentence description about the wildlife and biodiversity of ${location.name}. 
      Focus on what makes this ${location.type} unique for wildlife enthusiasts and nature lovers. 
      Mention specific ecosystems, notable species, or conservation highlights. 
      Keep it engaging and informative, around 50-80 words.`;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      return text.trim();
    } catch (error) {
      console.error('Error generating location description:', error);
      return this.getFallbackDescription(location);
    }
  }

  async generateLocationHighlights(location: LocationData): Promise<string[]> {
    if (!this.genAI) {
      return this.getFallbackHighlights(location);
    }

    try {
      const model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
      
      const prompt = `List 3-4 key wildlife highlights for ${location.name}. 
      Each highlight should be 3-5 words describing notable species, ecosystems, or conservation features.
      Format as a simple list, one item per line, no bullets or numbers.
      Examples: "Unique marsupial species", "Ancient rainforest ecosystems", "Migratory bird corridors"`;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      return text.trim().split('\n').filter(line => line.trim()).slice(0, 4);
    } catch (error) {
      console.error('Error generating location highlights:', error);
      return this.getFallbackHighlights(location);
    }
  }

  async generateLocationContent(location: LocationData, speciesCount?: number): Promise<LocationContent> {
    const [description, highlights] = await Promise.all([
      this.generateLocationDescription(location),
      this.generateLocationHighlights(location)
    ]);

    return {
      name: location.name,
      description,
      highlights,
      speciesCount
    };
  }

  private getFallbackDescription(location: LocationData): string {
    const fallbacks: Record<string, string> = {
      'Australia': 'Home to unique marsupials, diverse marine life, and ancient ecosystems. From the Great Barrier Reef to the Outback, Australia offers unparalleled wildlife diversity found nowhere else on Earth.',
      'United States': 'Spanning diverse ecosystems from Arctic tundra to tropical wetlands. The US hosts incredible biodiversity including iconic species like bald eagles, grizzly bears, and countless migratory birds.',
      'Colombia': 'One of the world\'s most biodiverse countries with over 1,900 bird species. From Amazon rainforests to Andean mountains, Colombia is a paradise for wildlife enthusiasts and conservationists.',
      'Oregon': 'Pacific Northwest wilderness featuring old-growth forests, coastal ecosystems, and diverse wildlife. Home to salmon runs, spotted owls, and pristine natural habitats along the rugged coastline.',
      'Nevada': 'Desert landscapes supporting unique adapted species and important migratory corridors. From bighorn sheep in mountain ranges to diverse bird life around alpine lakes and wetlands.'
    };

    return fallbacks[location.name] || `Discover the unique wildlife and natural beauty of ${location.name}.`;
  }

  private getFallbackHighlights(location: LocationData): string[] {
    const fallbacks: Record<string, string[]> = {
      'Australia': ['Unique marsupials', 'Great Barrier Reef', 'Endemic bird species', 'Ancient ecosystems'],
      'United States': ['Iconic predators', 'Migratory corridors', 'Protected wilderness', 'Diverse ecosystems'],
      'Colombia': ['Highest bird diversity', 'Amazon rainforest', 'Endemic species', 'Conservation hotspot'],
      'Oregon': ['Old-growth forests', 'Salmon migrations', 'Coastal wildlife', 'Mountain ecosystems'],
      'Nevada': ['Desert adaptations', 'Alpine lakes', 'Migratory routes', 'Bighorn sheep']
    };

    return fallbacks[location.name] || ['Diverse wildlife', 'Natural habitats', 'Conservation areas'];
  }

  getFeaturedLocations(): LocationData[] {
    return FEATURED_LOCATIONS;
  }
}

export const locationContentService = new LocationContentService();
