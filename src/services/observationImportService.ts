import { supabase } from '@/lib/supabase';

// Types for API responses
interface EBirdObservation {
  speciesCode: string;
  comName: string;
  sciName: string;
  locId: string;
  locName: string;
  obsDt: string;
  howMany: number;
  lat: number;
  lng: number;
  obsValid: boolean;
  obsReviewed: boolean;
  locationPrivate: boolean;
  subId: string; // checklist ID
}

interface EBirdChecklist {
  subId: string;
  userDisplayName: string;
  obsDt: string;
  locId: string;
  locName: string;
  durationHrs: number;
  allObsReported: boolean;
  creationDt: string;
  lastEditedDt: string;
  lat: number;
  lng: number;
}

interface iNatObservation {
  id: number;
  species_guess: string;
  taxon: {
    id: number;
    name: string;
    preferred_common_name: string;
    rank: string;
  };
  place_guess: string;
  latitude: number;
  longitude: number;
  observed_on: string;
  time_observed_at: string;
  quality_grade: string;
  photos: Array<{
    id: number;
    url: string;
    attribution: string;
  }>;
}

export class ObservationImportService {
  private ebirdApiKey: string;
  private ebirdUserId: string;
  private inatUserId: string;

  constructor() {
    // These would come from environment variables
    this.ebirdApiKey = import.meta.env.VITE_EBIRD_API_KEY || '';
    this.ebirdUserId = import.meta.env.VITE_EBIRD_USER_ID || '';
    this.inatUserId = import.meta.env.VITE_INAT_USER_ID || '';
  }

  // eBird API methods
  async fetchEBirdObservations(
    regionCode: string = 'US', 
    days: number = 30,
    maxResults: number = 1000
  ): Promise<EBirdObservation[]> {
    const url = `https://api.ebird.org/v2/data/obs/${regionCode}/recent?back=${days}&maxResults=${maxResults}`;
    
    const response = await fetch(url, {
      headers: {
        'X-eBirdApiToken': this.ebirdApiKey
      }
    });

    if (!response.ok) {
      throw new Error(`eBird API error: ${response.statusText}`);
    }

    return response.json();
  }

  async fetchPersonalEBirdObservations(days: number = 30): Promise<EBirdObservation[]> {
    if (!this.ebirdUserId) {
      throw new Error('eBird user ID not configured');
    }

    const url = `https://api.ebird.org/v2/data/obs/${this.ebirdUserId}/recent?back=${days}`;
    
    const response = await fetch(url, {
      headers: {
        'X-eBirdApiToken': this.ebirdApiKey
      }
    });

    if (!response.ok) {
      throw new Error(`eBird API error: ${response.statusText}`);
    }

    return response.json();
  }

  async fetchEBirdChecklists(days: number = 30): Promise<EBirdChecklist[]> {
    if (!this.ebirdUserId) {
      throw new Error('eBird user ID not configured');
    }

    const url = `https://api.ebird.org/v2/product/lists/${this.ebirdUserId}?maxResults=200`;
    
    const response = await fetch(url, {
      headers: {
        'X-eBirdApiToken': this.ebirdApiKey
      }
    });

    if (!response.ok) {
      throw new Error(`eBird API error: ${response.statusText}`);
    }

    return response.json();
  }

  // iNaturalist API methods
  async fetchiNatObservations(
    userId?: string,
    days: number = 30,
    perPage: number = 200
  ): Promise<iNatObservation[]> {
    const targetUserId = userId || this.inatUserId;
    if (!targetUserId) {
      throw new Error('iNaturalist user ID not configured');
    }

    const sinceDate = new Date();
    sinceDate.setDate(sinceDate.getDate() - days);
    
    const url = `https://api.inaturalist.org/v1/observations?user_id=${targetUserId}&per_page=${perPage}&created_d1=${sinceDate.toISOString().split('T')[0]}`;
    
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`iNaturalist API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.results;
  }

  // Database import methods
  async importEBirdData(observations: EBirdObservation[]): Promise<void> {
    for (const obs of observations) {
      try {
        // 1. Create or get location
        const location = await this.createOrGetLocation({
          name: obs.locName,
          latitude: obs.lat,
          longitude: obs.lng,
          ebird_location_id: obs.locId
        });

        // 2. Find or create species by eBird code
        let species = await this.findSpeciesByEBirdCode(obs.speciesCode);
        if (!species) {
          species = await this.createSpeciesFromEBird(obs);
        }

        // 3. Create observation record
        await this.createObservation({
          species_id: species.id,
          location_id: location.id,
          observer_id: this.ebirdUserId,
          source: 'ebird',
          external_id: obs.subId,
          observation_date: obs.obsDt,
          count: obs.howMany,
          notes: `eBird observation - ${obs.obsValid ? 'Valid' : 'Needs review'}`
        });

      } catch (error) {
        console.error(`Error importing eBird observation:`, error);
      }
    }
  }

  async importiNatData(observations: iNatObservation[]): Promise<void> {
    for (const obs of observations) {
      try {
        // 1. Create or get location
        const location = await this.createOrGetLocation({
          name: obs.place_guess || 'Unknown location',
          latitude: obs.latitude,
          longitude: obs.longitude,
          inat_place_id: null // Would need additional API call to get place ID
        });

        // 2. Find or create species by scientific name
        let species = await this.findSpeciesByScientificName(obs.taxon.name);
        if (!species) {
          species = await this.createSpeciesFromiNat(obs);
        }

        // 3. Create observation record
        await this.createObservation({
          species_id: species.id,
          location_id: location.id,
          observer_id: this.inatUserId,
          source: 'inaturalist',
          external_id: obs.id.toString(),
          observation_date: obs.observed_on,
          observation_time: obs.time_observed_at ? new Date(obs.time_observed_at).toTimeString().split(' ')[0] : null,
          count: 1,
          confidence_level: obs.quality_grade === 'research' ? 'certain' : 'likely',
          photo_url: obs.photos[0]?.url || null,
          notes: `iNaturalist observation - Quality: ${obs.quality_grade}`
        });

      } catch (error) {
        console.error(`Error importing iNaturalist observation:`, error);
      }
    }
  }

  // Helper methods
  private async createOrGetLocation(locationData: any) {
    const { data: existing } = await supabase
      .from('locations')
      .select('*')
      .eq('latitude', locationData.latitude)
      .eq('longitude', locationData.longitude)
      .eq('name', locationData.name)
      .single();

    if (existing) {
      return existing;
    }

    const { data: newLocation, error } = await supabase
      .from('locations')
      .insert(locationData)
      .select()
      .single();

    if (error) throw error;
    return newLocation;
  }

  private async findSpeciesByEBirdCode(ebirdCode: string) {
    const { data } = await supabase
      .from('species_v2')
      .select('*')
      .eq('ebird_code', ebirdCode)
      .single();

    return data;
  }

  private async findSpeciesByScientificName(scientificName: string) {
    const { data } = await supabase
      .from('species_v2')
      .select('*')
      .eq('scientific_name', scientificName)
      .single();

    return data;
  }

  private async createSpeciesFromEBird(obs: EBirdObservation) {
    const { data, error } = await supabase
      .from('species_v2')
      .insert({
        name: obs.comName,
        common_name: obs.comName,
        scientific_name: obs.sciName,
        ebird_code: obs.speciesCode,
        category: 'Birds', // eBird is birds only
        published: false, // Draft until reviewed
        ai_generated: false
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  private async createSpeciesFromiNat(obs: iNatObservation) {
    const { data, error } = await supabase
      .from('species_v2')
      .insert({
        name: obs.taxon.preferred_common_name || obs.species_guess,
        common_name: obs.taxon.preferred_common_name,
        scientific_name: obs.taxon.name,
        inat_id: obs.taxon.id.toString(),
        category: this.mapTaxonRankToCategory(obs.taxon.rank),
        published: false, // Draft until reviewed
        ai_generated: false
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  private async createObservation(observationData: any) {
    const { error } = await supabase
      .from('observations')
      .insert(observationData);

    if (error) throw error;
  }

  private mapTaxonRankToCategory(rank: string): string {
    const rankMap: { [key: string]: string } = {
      'species': 'Unknown',
      'genus': 'Unknown',
      'family': 'Unknown',
      'order': 'Unknown',
      'class': 'Unknown'
    };
    
    return rankMap[rank] || 'Unknown';
  }

  // Main import method
  async importAllData(days: number = 30): Promise<{ success: boolean; message: string }> {
    try {
      console.log('Starting data import...');
      
      // Import eBird data
      if (this.ebirdApiKey && this.ebirdUserId) {
        const ebirdObs = await this.fetchPersonalEBirdObservations(days);
        await this.importEBirdData(ebirdObs);
        console.log(`Imported ${ebirdObs.length} eBird observations`);
      }

      // Import iNaturalist data
      if (this.inatUserId) {
        const inatObs = await this.fetchiNatObservations(undefined, days);
        await this.importiNatData(inatObs);
        console.log(`Imported ${inatObs.length} iNaturalist observations`);
      }

      return { success: true, message: 'Data import completed successfully' };
    } catch (error) {
      console.error('Import error:', error);
      return { success: false, message: `Import failed: ${error}` };
    }
  }
}
