@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    /* Wildlife theme colors */
    --wildlife-primary: 142 76% 36%;     /* Green-600 equivalent */
    --wildlife-primary-dark: 142 72% 29%; /* Green-700 equivalent */
    --wildlife-primary-darker: 142 69% 19%; /* Green-800 equivalent */
    --wildlife-secondary: 142 33% 96%;   /* Green-50 equivalent */
    --wildlife-accent: 217 91% 60%;      /* Blue-500 equivalent */
    --wildlife-success: 142 76% 36%;     /* Green-600 */
    --wildlife-warning: 45 93% 47%;      /* Yellow-500 */
    --wildlife-danger: 0 84% 60%;        /* Red-500 */

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Wildlife theme utility classes */
@layer utilities {
  .bg-wildlife-primary {
    background-color: hsl(var(--wildlife-primary));
  }

  .bg-wildlife-primary-dark {
    background-color: hsl(var(--wildlife-primary-dark));
  }

  .bg-wildlife-primary-darker {
    background-color: hsl(var(--wildlife-primary-darker));
  }

  .bg-wildlife-secondary {
    background-color: hsl(var(--wildlife-secondary));
  }

  .bg-wildlife-accent {
    background-color: hsl(var(--wildlife-accent));
  }

  .bg-wildlife-success {
    background-color: hsl(var(--wildlife-success));
  }

  .bg-wildlife-warning {
    background-color: hsl(var(--wildlife-warning));
  }

  .bg-wildlife-danger {
    background-color: hsl(var(--wildlife-danger));
  }

  .text-wildlife-primary {
    color: hsl(var(--wildlife-primary));
  }

  .text-wildlife-primary-dark {
    color: hsl(var(--wildlife-primary-dark));
  }

  .text-wildlife-primary-darker {
    color: hsl(var(--wildlife-primary-darker));
  }

  .text-wildlife-secondary {
    color: hsl(var(--wildlife-secondary));
  }

  .text-wildlife-accent {
    color: hsl(var(--wildlife-accent));
  }

  .text-wildlife-success {
    color: hsl(var(--wildlife-success));
  }

  .text-wildlife-warning {
    color: hsl(var(--wildlife-warning));
  }

  .text-wildlife-danger {
    color: hsl(var(--wildlife-danger));
  }

  .border-wildlife-primary {
    border-color: hsl(var(--wildlife-primary));
  }

  .border-wildlife-primary\/30 {
    border-color: hsl(var(--wildlife-primary) / 0.3);
  }

  .hover\:bg-wildlife-primary:hover {
    background-color: hsl(var(--wildlife-primary));
  }

  .hover\:bg-wildlife-primary-dark:hover {
    background-color: hsl(var(--wildlife-primary-dark));
  }

  .hover\:bg-wildlife-secondary:hover {
    background-color: hsl(var(--wildlife-secondary));
  }

  .hover\:text-wildlife-primary:hover {
    color: hsl(var(--wildlife-primary));
  }

  .focus\:ring-wildlife-primary:focus {
    --tw-ring-color: hsl(var(--wildlife-primary));
  }
}