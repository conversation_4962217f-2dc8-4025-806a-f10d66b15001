import { supabase } from '../integrations/supabase/client';

async function runFunFactsMigration() {
  console.log('🚀 Running Fun Facts Migration...\n');

  try {
    // First, check current state
    console.log('1. Checking current state...');
    
    const { data: currentFacts, error: currentError, count } = await supabase
      .from('fun_facts')
      .select('*', { count: 'exact' })
      .limit(1);

    if (currentError) {
      console.error('❌ Error checking current fun facts:', currentError);
      return;
    }

    console.log(`   Current fun facts in table: ${count || 0}`);

    // Check species with JSONB fun facts
    const { data: speciesWithFacts, error: speciesError } = await supabase
      .from('species_v2')
      .select('id, name, ai_fun_facts, fun_facts_field')
      .or('ai_fun_facts.not.is.null,fun_facts_field.not.is.null')
      .limit(5);

    if (speciesError) {
      console.error('❌ Error checking species JSONB facts:', speciesError);
      return;
    }

    console.log(`   Species with JSONB fun facts: ${speciesWithFacts?.length || 0}`);
    
    if (speciesWithFacts && speciesWithFacts.length > 0) {
      console.log('   Sample species with JSONB facts:');
      speciesWithFacts.forEach((species, index) => {
        console.log(`   ${index + 1}. ${species.name} (${species.id})`);
      });
    }

    // 2. Run the migration
    console.log('\n2. Running migration function...');
    
    const { data: migrationResult, error: migrationError } = await supabase
      .rpc('migrate_fun_facts_to_table');

    if (migrationError) {
      console.error('❌ Migration failed:', migrationError);
      return;
    }

    console.log('✅ Migration completed successfully!');
    
    if (migrationResult && migrationResult.length > 0) {
      console.log('\n📊 Migration Results:');
      let totalMigrated = 0;
      
      migrationResult.forEach((result: any) => {
        console.log(`   ${result.species_name}: ${result.migrated_facts_count} facts (${result.status})`);
        totalMigrated += result.migrated_facts_count || 0;
      });
      
      console.log(`\n🎉 Total facts migrated: ${totalMigrated}`);
    }

    // 3. Verify the migration
    console.log('\n3. Verifying migration...');
    
    const { data: newFacts, error: verifyError, count: newCount } = await supabase
      .from('fun_facts')
      .select('*', { count: 'exact' })
      .limit(5);

    if (verifyError) {
      console.error('❌ Verification failed:', verifyError);
      return;
    }

    console.log(`✅ Verification complete:`);
    console.log(`   Total fun facts now in table: ${newCount || 0}`);
    
    if (newFacts && newFacts.length > 0) {
      console.log('   Sample migrated facts:');
      newFacts.forEach((fact: any, index: number) => {
        console.log(`   ${index + 1}. ${fact.fact.substring(0, 80)}...`);
      });
    }

    // 4. Test with Red-capped Parrot
    console.log('\n4. Testing with Red-capped Parrot...');
    
    const { data: parrot, error: parrotError } = await supabase
      .from('species_v2')
      .select('id, name')
      .ilike('name', '%red-capped%parrot%')
      .single();

    if (parrotError) {
      console.log('❌ Red-capped Parrot not found:', parrotError.message);
    } else if (parrot) {
      const { data: parrotFacts, error: parrotFactsError } = await supabase
        .from('fun_facts')
        .select('fact')
        .eq('species_id', parrot.id);

      if (parrotFactsError) {
        console.log(`❌ Error fetching parrot facts: ${parrotFactsError.message}`);
      } else {
        console.log(`🦜 ${parrot.name} now has ${parrotFacts?.length || 0} fun facts:`);
        if (parrotFacts && parrotFacts.length > 0) {
          parrotFacts.forEach((fact: any, index: number) => {
            console.log(`   ${index + 1}. ${fact.fact}`);
          });
        }
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the migration
runFunFactsMigration().then(() => {
  console.log('\n✅ Fun Facts migration process complete');
}).catch((error) => {
  console.error('❌ Migration script failed:', error);
});
