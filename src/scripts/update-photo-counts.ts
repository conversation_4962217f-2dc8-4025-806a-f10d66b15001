#!/usr/bin/env tsx

/**
 * Update Photo Counts Script
 *
 * This script updates the photo_count field in species_v2 table to match
 * the actual count of published photos in photos_v2 table.
 *
 * Run after photo cleanup to ensure accurate counts.
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://raqxptgrugnmxdunbuty.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJhcXhwdGdydWdubXhkdW5idXR5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ2NTU5NzQsImV4cCI6MjA1MDIzMTk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

const supabase = createClient(supabaseUrl, supabaseKey);

async function updatePhotoCounts() {
  console.log('🔄 Updating photo counts in species_v2 table...');

  try {
    // Update photo counts for all species based on published photos in photos_v2
    const { error: updateError } = await supabase.rpc('update_species_photo_counts');
    
    if (updateError) {
      // If the RPC doesn't exist, do it manually
      console.log('📝 Updating photo counts manually...');
      
      // Get all species
      const { data: allSpecies, error: speciesError } = await supabase
        .from('species_v2')
        .select('id, name');

      if (speciesError) throw speciesError;

      let updatedCount = 0;
      
      for (const species of allSpecies || []) {
        // Count published photos for this species
        const { count, error: countError } = await supabase
          .from('photos_v2')
          .select('*', { count: 'exact', head: true })
          .eq('species_id', species.id)
          .eq('published', true);

        if (countError) {
          console.error(`❌ Error counting photos for ${species.name}:`, countError);
          continue;
        }

        // Update the species photo count
        const { error: updateSpeciesError } = await supabase
          .from('species_v2')
          .update({ photo_count: count || 0 })
          .eq('id', species.id);

        if (updateSpeciesError) {
          console.error(`❌ Error updating photo count for ${species.name}:`, updateSpeciesError);
          continue;
        }

        updatedCount++;
        if (updatedCount % 10 === 0) {
          console.log(`✅ Updated ${updatedCount} species...`);
        }
      }

      console.log(`✅ Updated photo counts for ${updatedCount} species`);
    } else {
      console.log('✅ Photo counts updated using database function');
    }

    // Verify the update worked by checking some species
    console.log('\n📊 Verifying photo counts...');
    
    const { data: verification, error: verifyError } = await supabase
      .from('species_v2')
      .select(`
        name,
        photo_count,
        photos_v2!inner(id)
      `)
      .eq('photos_v2.published', true)
      .limit(10);

    if (verifyError) {
      console.error('❌ Error verifying counts:', verifyError);
      return;
    }

    // Group by species and count photos
    const speciesCounts = verification?.reduce((acc, row) => {
      if (!acc[row.name]) {
        acc[row.name] = { stored: row.photo_count, actual: 0 };
      }
      acc[row.name].actual++;
      return acc;
    }, {} as Record<string, { stored: number; actual: number }>);

    console.log('\nTop species with photos:');
    Object.entries(speciesCounts || {}).forEach(([name, counts]) => {
      const status = counts.stored === counts.actual ? '✅' : '❌';
      console.log(`${status} ${name}: stored=${counts.stored}, actual=${counts.actual}`);
    });

  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

// Run the script
updatePhotoCounts().then(() => {
  console.log('\n🎉 Photo count update completed!');
});
