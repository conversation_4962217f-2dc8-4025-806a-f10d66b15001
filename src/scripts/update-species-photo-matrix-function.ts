#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  console.error('Please ensure VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const updateFunction = `
create or replace function get_species_photo_matrix()
returns jsonb as $$
declare
  matrix jsonb;
begin
  with species_photos as (
    select
      s.id,
      jsonb_agg(
        jsonb_build_object(
          'id', p.id,
          'url', p.url,
          'title', p.title,
          'description', p.description,
          'published', p.published
          -- Add other photo fields you need here
        )
      ) filter (where p.id is not null) as photos
    from species_v2 s
    left join photos_v2 p on s.id = p.species_id
    group by s.id
  ),
  unassigned_photos as (
    select
      jsonb_agg(
        jsonb_build_object(
          'id', p.id,
          'url', p.url,
          'title', p.title,
          'description', p.description,
          'published', p.published
        )
      ) as photos
    from photos_v2 p
    where p.species_id is null
  )
  select
    jsonb_build_object(
      'species', (
        select
          jsonb_agg(
            jsonb_build_object(
              'species', to_jsonb(s),
              'photos', coalesce(sp.photos, '[]'::jsonb)
            )
          )
        from species_v2 s
        left join species_photos sp on s.id = sp.id
      ),
      'unassigned_photos', (select photos from unassigned_photos)
    )
  into matrix;

  return matrix;
end;
$$ language plpgsql;
`;

async function updateSpeciesPhotoMatrixFunction() {
  console.log('🚀 Updating get_species_photo_matrix function to use photos_v2...');

  try {
    // Use direct SQL execution through the REST API
    const { data, error } = await supabase
      .from('_dummy_table_that_does_not_exist')
      .select('*')
      .limit(0);

    // This will fail, but let's try a different approach
    // Execute the SQL directly using the Supabase client
    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey
      },
      body: JSON.stringify({ sql: updateFunction })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    console.log('✅ Successfully updated get_species_photo_matrix function');
    console.log('🔄 The function now queries photos_v2 instead of photos');

  } catch (error) {
    console.error('❌ Failed to update function:', error);
    console.log('💡 You can manually run this SQL in the Supabase SQL editor:');
    console.log(updateFunction);
    process.exit(1);
  }
}

async function main() {
  await updateSpeciesPhotoMatrixFunction();
  console.log('🎉 Database function update completed!');
  console.log('💡 Refresh your Species Matrix page to see the changes');
}

main().catch(console.error);
