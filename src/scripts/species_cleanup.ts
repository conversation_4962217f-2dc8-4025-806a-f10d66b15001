import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import type { Tables } from '@/integrations/supabase/types';
import { extractCleanDescription, extractAllFunFacts } from '../lib/textUtils';

// Load environment variables
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase URL or SUPABASE_SERVICE_KEY. Make sure your .env file is configured.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Manual Type Definitions as a fallback
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      species: {
        Row: {
          id: string;
          description: string | null;
          [key: string]: string | number | boolean | null | undefined | Json | Json[];
        };
      };
    };
  }
}

type Species = Database['public']['Tables']['species']['Row'];

interface CleanedData {
  description: string;
  habitat: string;
  regions: string;
  diet: string;
  behavior: string;
  fun_facts: string[];
}

/**
 * Cleans a legacy species description block, extracting fun facts.
 * @param {Species} species - The full species object from the database.
 * @returns {CleanedData | null} - A structured object or null if input is invalid.
 */
function cleanAndStructure(species: Species): CleanedData | null {
  if (!species || !species.description) return null;
  
  const fun_facts = extractAllFunFacts(species);
  const description = extractCleanDescription(species.description);

  // This part is now simplified as extraction is handled by textUtils
  const cleanedData: CleanedData = {
    description: description,
    habitat: "Not extracted", // Placeholder
    regions: "Not extracted", // Placeholder
    diet: "Not extracted",    // Placeholder
    behavior: "Not extracted",// Placeholder
    fun_facts,
  };

  return cleanedData;
}

/**
 * Updates a species record and its associated fun facts.
 * @param {string} speciesId - The ID of the species to update.
 * @param {CleanedData} data - The structured data to upsert.
 */
async function upsertSpeciesData(speciesId: string, data: CleanedData) {
  console.log(`Updating species ID: ${speciesId}`);

  const { error: speciesError } = await supabase
    .from('species_v2')
    .update({
      description: data.description,
      habitat: data.habitat,
      regions: data.regions,
      diet: data.diet,
      behavior: data.behavior,
    })
    .eq('id', speciesId);
  
  if (speciesError) {
    console.error(`Error updating species ${speciesId}:`, speciesError.message);
    return;
  }

  // Clear old facts before inserting new ones
  await supabase.from('fun_facts').delete().eq('species_id', speciesId);

  if (data.fun_facts.length > 0) {
    const factsToInsert = data.fun_facts.map(fact => ({
      species_id: speciesId,
      fact
    }));
    
    const { error: factsError } = await supabase.from('fun_facts').insert(factsToInsert);
    if (factsError) {
      console.error(`Error inserting fun facts for species ${speciesId}:`, factsError.message);
    } else {
      console.log(`- Inserted ${data.fun_facts.length} fun facts.`);
    }
  }
}

const cleanupSpeciesData = async () => {
  console.log('Fetching all species for data cleanup...');
  const { data: speciesList, error } = await supabase
    .from('species_v2')
    .select('*');

  if (error) {
    console.error('❌ Failed to fetch species:', error.message);
    throw new Error('Failed to fetch species: ' + error.message);
  }

  console.log(`Found ${speciesList.length} species. Starting cleanup...`);

  for (const species of speciesList) {
    const updates: Partial<Tables<'species_v2'>> = {};

    // 1. Fix 'Family' field with placeholder values
    if (species.family?.toLowerCase().includes('pending')) {
      updates.family = null;
    }

    // 2. Clean malformed 'conservation_actions' from JSON strings
    if (typeof species.conservation_actions === 'string' && species.conservation_actions.includes('"state":"empty"')) {
      updates.conservation_actions = null;
    }

    // 3. Normalize vague 'size_description'
    if (species.size_description?.toLowerCase().includes('medium size')) {
      updates.size_description = null;
    }
    
    // 4. Clean 'behavior' field from placeholder group labels
    if (species.behavior?.includes('Group:') || species.behavior?.includes('Related:')) {
      updates.behavior = null;
    }

    if (Object.keys(updates).length > 0) {
      console.log(`\nFound issues for ${species.name} (ID: ${species.id}). Preparing update:`);
      console.log(updates);

      const { error: updateError } = await supabase
        .from('species_v2')
        .update(updates)
        .eq('id', species.id);

      if (updateError) {
        console.error(`❌ Failed to update ${species.name}:`, updateError.message);
      } else {
        console.log(`✅ Cleaned ${species.name}`);
      }
    }
  }
  console.log('\nCleanup process complete.');
};

cleanupSpeciesData(); 