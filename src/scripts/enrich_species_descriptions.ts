import { createClient } from '@supabase/supabase-js';
import OpenAI from 'openai';
import { config } from 'dotenv';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

// Load environment variables
config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'http://127.0.0.1:54321';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const supabase = createClient(supabaseUrl, supabaseKey);

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

interface Species {
  id: string;
  common_name: string;
  scientific_name: string;
  description: string | null;
}

async function generateSpeciesDescription(commonName: string, scientificName: string): Promise<string> {
  const prompt = `Generate a detailed, educational description for ${commonName} (${scientificName}). 

The description should be written in a natural, flowing paragraph that includes:
- Habitat information
- Diet details  
- Behavioral characteristics
- At least 1 interesting fun fact

Write in a tone suitable for a nature field guide - factual, educational, and engaging. Aim for 3-4 sentences that naturally weave together habitat, diet, behavior, and fun facts.

Example format:
"The [species] inhabits [habitat description], where it [behavior]. This species primarily feeds on [diet], and [fun fact about behavior/adaptation/characteristic]."

Generate only the description paragraph, no additional formatting or labels.`;

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',  // Updated to newer, more cost-effective model
      messages: [
        {
          role: 'system',
          content: 'You are a wildlife expert writing educational content for a nature field guide. Be factual, engaging, and include specific details about habitat, diet, behavior, and interesting facts.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 200,
      temperature: 0.7,
    });

    return completion.choices[0]?.message?.content?.trim() || '';
  } catch (error) {
    console.error(`Error generating description for ${commonName}:`, error);
    return '';
  }
}

async function enrichSpeciesDescriptions() {
  console.log('🔍 Scanning for species missing descriptions...');

  // Get all species with null or empty descriptions
  const { data: speciesList, error } = await supabase
    .from('species_v2')
    .select('id, common_name, scientific_name, description')
    .or('description.is.null,description.eq.');

  if (error) {
    console.error('Error fetching species:', error);
    return;
  }

  if (!speciesList || speciesList.length === 0) {
    console.log('✅ No species found missing descriptions!');
    return;
  }

  console.log(`📝 Found ${speciesList.length} species missing descriptions`);

  let updatedCount = 0;
  let failedCount = 0;

  // Process each species
  for (const species of speciesList) {
    console.log(`\n🔄 Processing: ${species.common_name} (${species.scientific_name})`);
    
    try {
      const description = await generateSpeciesDescription(species.common_name, species.scientific_name);
      
      if (description) {
        // Update the species with the new description
        const { error: updateError } = await supabase
          .from('species_v2')
          .update({ description })
          .eq('id', species.id);

        if (updateError) {
          console.error(`❌ Failed to update ${species.common_name}:`, updateError);
          failedCount++;
        } else {
          console.log(`✅ Updated: ${species.common_name}`);
          console.log(`   Description: ${description.substring(0, 100)}...`);
          updatedCount++;
        }
      } else {
        console.log(`⚠️  No description generated for ${species.common_name}`);
        failedCount++;
      }

      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`❌ Error processing ${species.common_name}:`, error);
      failedCount++;
    }
  }

  console.log(`\n📊 Summary:`);
  console.log(`   ✅ Successfully updated: ${updatedCount} species`);
  console.log(`   ❌ Failed: ${failedCount} species`);

  if (updatedCount > 0) {
    console.log('\n🧹 Running cleanup script to extract structured fields...');
    
    // Import and run the cleanup script
    try {
      execSync('npx tsx src/scripts/species_cleanup.ts', { 
        stdio: 'inherit',
        cwd: process.cwd()
      });
      console.log('✅ Cleanup script completed successfully!');
    } catch (cleanupError) {
      console.error('❌ Cleanup script failed:', cleanupError);
    }
  }
}

// Run the enrichment process
const __filename = fileURLToPath(import.meta.url);
if (import.meta.url === `file://${process.argv[1]}`) {
  enrichSpeciesDescriptions()
    .then(() => {
      console.log('\n🎉 Species enrichment process completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Fatal error:', error);
      process.exit(1);
    });
}

export { enrichSpeciesDescriptions }; 