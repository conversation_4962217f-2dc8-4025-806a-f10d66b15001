import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client for Node.js environment
const supabaseUrl = 'https://raqxptgrugnmxdunbuty.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJhcXhwdGdydWdubXhkdW5idXR5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzM0NTAsImV4cCI6MjA2NTg0OTQ1MH0.0xVgSqjqch5a48Nxq4ZhaCZrMx0yaQ32qqi3YR1cLGQ';
const supabase = createClient(supabaseUrl, supabaseKey);

async function debugFunFacts() {
  console.log('🔍 Debugging Fun Facts Issue...\n');

  try {
    // 1. Find Desert Cottontail specifically
    const { data: cottontail, error: cottontailError } = await supabase
      .from('species_v2')
      .select('id, name, scientific_name')
      .ilike('name', '%desert%cottontail%')
      .single();

    if (cottontailError) {
      console.log('❌ Desert Cottontail not found:', cottontailError.message);
      
      // Try to find any cottontail
      const { data: anyCottontail, error: anyError } = await supabase
        .from('species_v2')
        .select('id, name, scientific_name')
        .ilike('name', '%cottontail%')
        .limit(1)
        .single();

      if (anyError) {
        console.log('❌ No cottontail species found:', anyError.message);
        return;
      } else {
        console.log('✅ Found cottontail species:', anyCottontail.name);
        cottontail = anyCottontail;
      }
    } else {
      console.log('✅ Found Desert Cottontail:', cottontail.name, cottontail.id);
    }

    // 2. Check fun facts for this species
    const { data: funFacts, error: funFactsError } = await supabase
      .from('fun_facts')
      .select('id, fact, created_at')
      .eq('species_id', cottontail.id)
      .order('created_at', { ascending: true });

    if (funFactsError) {
      console.error('❌ Error fetching fun facts:', funFactsError);
      return;
    }

    console.log(`\n🎯 Fun Facts for ${cottontail.name}:`);
    console.log(`   Species ID: ${cottontail.id}`);
    console.log(`   Fun Facts Count: ${funFacts?.length || 0}`);

    if (funFacts && funFacts.length > 0) {
      funFacts.forEach((fact, index) => {
        console.log(`   ${index + 1}. ${fact.fact}`);
        console.log(`      ID: ${fact.id}, Created: ${fact.created_at}`);
      });
    } else {
      console.log('   ❌ No fun facts found for this species');
    }

    // 3. Test the fetchSpeciesFunFacts function directly
    console.log(`\n🧪 Testing fetchSpeciesFunFacts function...`);
    
    const { data: directFacts, error: directError } = await supabase
      .from('fun_facts')
      .select('fact')
      .eq('species_id', cottontail.id)
      .order('created_at', { ascending: true });

    if (directError) {
      console.error('❌ Direct fetch error:', directError);
    } else {
      const factStrings = directFacts?.map(item => item.fact) || [];
      console.log(`   Direct fetch result: ${factStrings.length} facts`);
      factStrings.forEach((fact, index) => {
        console.log(`   ${index + 1}. ${fact}`);
      });
    }

    // 4. Check if there are any fun facts in the database at all
    console.log(`\n📊 Overall Fun Facts Statistics:`);
    
    const { count: totalFacts, error: countError } = await supabase
      .from('fun_facts')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('❌ Error counting total facts:', countError);
    } else {
      console.log(`   Total fun facts in database: ${totalFacts || 0}`);
    }

    // 5. Check a few species that should have fun facts
    const { data: speciesWithFacts, error: speciesWithFactsError } = await supabase
      .from('fun_facts')
      .select(`
        species_id,
        species_v2!inner(name)
      `)
      .limit(5);

    if (speciesWithFactsError) {
      console.error('❌ Error fetching species with facts:', speciesWithFactsError);
    } else if (speciesWithFacts && speciesWithFacts.length > 0) {
      console.log(`\n✅ Sample species with fun facts:`);
      speciesWithFacts.forEach((item: any, index: number) => {
        console.log(`   ${index + 1}. ${item.species_v2.name} (${item.species_id})`);
      });
    }

    // 6. Test the exact same query that useSpeciesDetail would use
    console.log(`\n🔄 Simulating useSpeciesDetail fetch...`);
    
    const { data: speciesData, error: speciesError } = await supabase
      .from('species_v2')
      .select('*')
      .eq('id', cottontail.id)
      .single();

    if (speciesError) {
      console.error('❌ Error fetching species data:', speciesError);
    } else {
      console.log(`✅ Species data fetched: ${speciesData.name}`);
      
      // Simulate the fun facts fetch
      const { data: simulatedFacts, error: simulatedError } = await supabase
        .from('fun_facts')
        .select('fact')
        .eq('species_id', cottontail.id)
        .order('created_at', { ascending: true });

      if (simulatedError) {
        console.error('❌ Simulated fun facts fetch error:', simulatedError);
      } else {
        const simulatedFactStrings = simulatedFacts?.map(item => item.fact) || [];
        console.log(`✅ Simulated fun facts result: ${simulatedFactStrings.length} facts`);
        
        const finalSpeciesObject = {
          ...speciesData,
          fun_facts: simulatedFactStrings
        };
        
        console.log(`✅ Final species object fun_facts:`, finalSpeciesObject.fun_facts);
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the debug
debugFunFacts().then(() => {
  console.log('\n✅ Fun Facts debugging complete');
}).catch((error) => {
  console.error('❌ Debug script failed:', error);
});
