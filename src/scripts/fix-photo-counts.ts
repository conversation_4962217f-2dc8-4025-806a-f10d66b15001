import { createClient } from '@supabase/supabase-js';

// Load environment variables manually for Node.js
const SUPABASE_URL = "https://raqxptgrugnmxdunbuty.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJhcXhwdGdydWdubXhkdW5idXR5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzM0NTAsImV4cCI6MjA2NTg0OTQ1MH0.0xVgSqjqch5a48Nxq4ZhaCZrMx0yaQ32qqi3YR1cLGQ";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function investigatePhotoCounts() {
  console.log('🔍 Investigating photo count discrepancies...');

  try {
    // Get all species with their current photo counts
    const { data: allSpecies, error: speciesError } = await supabase
      .from('species_v2')
      .select('id, name, published')
      .eq('published', true)
      .order('name');

    if (speciesError) {
      console.error('❌ Error fetching species:', speciesError);
      return;
    }

    console.log(`📊 Found ${allSpecies?.length || 0} published species`);

    // Get all published photos
    const { data: allPhotos, error: photosError } = await supabase
      .from('photos_v2')
      .select('*')
      .eq('published', true);

    if (photosError) {
      console.error('❌ Error fetching photos:', photosError);
      return;
    }

    console.log(`📊 Found ${allPhotos?.length || 0} published photos`);

    // Count photos per species
    const photoCountsBySpecies = new Map();
    allPhotos?.forEach(photo => {
      if (photo.species_id) {
        const currentCount = photoCountsBySpecies.get(photo.species_id) || 0;
        photoCountsBySpecies.set(photo.species_id, currentCount + 1);
      }
    });

    console.log(`📊 Photos distributed across ${photoCountsBySpecies.size} species`);

    // Find species with 0 photos but should have some
    const speciesWithZeroPhotos = allSpecies?.filter(species => 
      !photoCountsBySpecies.has(species.id)
    ) || [];

    console.log(`⚠️ Species with 0 photos: ${speciesWithZeroPhotos.length}`);

    // Show sample species with 0 photos
    console.log('\n📝 Sample species with 0 photos:');
    speciesWithZeroPhotos.slice(0, 10).forEach(species => {
      console.log(`   - ${species.name} (ID: ${species.id})`);
    });

    // Check for photos with null/invalid species_id
    const photosWithoutSpecies = allPhotos?.filter(photo => !photo.species_id) || [];
    console.log(`\n🔗 Photos without species_id: ${photosWithoutSpecies.length}`);

    if (photosWithoutSpecies.length > 0) {
      console.log('📝 Sample photos without species:');
      photosWithoutSpecies.slice(0, 5).forEach(photo => {
        console.log(`   - Photo ID: ${photo.id}, URL: ${photo.url?.substring(0, 60)}...`);
      });
    }

    // Check for species_id mismatches
    const uniqueSpeciesIdsInPhotos = new Set(allPhotos?.map(p => p.species_id).filter(Boolean));
    const uniqueSpeciesIdsInSpecies = new Set(allSpecies?.map(s => s.id));

    const orphanedPhotoSpeciesIds = [...uniqueSpeciesIdsInPhotos].filter(id => 
      !uniqueSpeciesIdsInSpecies.has(id)
    );

    console.log(`\n🔗 Photos referencing non-existent species: ${orphanedPhotoSpeciesIds.length}`);
    if (orphanedPhotoSpeciesIds.length > 0) {
      console.log('📝 Orphaned species IDs in photos:');
      orphanedPhotoSpeciesIds.slice(0, 5).forEach(id => {
        const photoCount = allPhotos?.filter(p => p.species_id === id).length || 0;
        console.log(`   - Species ID: ${id} (${photoCount} photos)`);
      });
    }

    // Show species with highest photo counts
    const speciesWithPhotos = allSpecies?.map(species => ({
      ...species,
      photo_count: photoCountsBySpecies.get(species.id) || 0
    })).sort((a, b) => b.photo_count - a.photo_count) || [];

    console.log('\n📊 Top 10 species by photo count:');
    speciesWithPhotos.slice(0, 10).forEach(species => {
      console.log(`   - ${species.name}: ${species.photo_count} photos`);
    });

    return {
      totalSpecies: allSpecies?.length || 0,
      totalPhotos: allPhotos?.length || 0,
      speciesWithPhotos: photoCountsBySpecies.size,
      speciesWithZeroPhotos: speciesWithZeroPhotos.length,
      photosWithoutSpecies: photosWithoutSpecies.length,
      orphanedPhotoSpeciesIds: orphanedPhotoSpeciesIds.length
    };

  } catch (error) {
    console.error('❌ Error investigating photo counts:', error);
  }
}

async function fixPhotoSpeciesLinks() {
  console.log('\n🔧 Attempting to fix photo-species links...');

  try {
    // Get photos without species_id
    const { data: orphanedPhotos, error: orphanedError } = await supabase
      .from('photos_v2')
      .select('*')
      .is('species_id', null)
      .eq('published', true);

    if (orphanedError) {
      console.error('❌ Error fetching orphaned photos:', orphanedError);
      return;
    }

    console.log(`📊 Found ${orphanedPhotos?.length || 0} photos without species_id`);

    if (!orphanedPhotos || orphanedPhotos.length === 0) {
      console.log('✅ No orphaned photos to fix');
      return;
    }

    // Get all species for matching
    const { data: allSpecies, error: speciesError } = await supabase
      .from('species_v2')
      .select('id, name')
      .eq('published', true);

    if (speciesError) {
      console.error('❌ Error fetching species:', speciesError);
      return;
    }

    let fixedCount = 0;

    // Try to match photos to species based on URL patterns
    for (const photo of orphanedPhotos) {
      if (!photo.url) continue;

      // Extract species name from URL path
      const urlParts = photo.url.split('/');
      const speciesFolderIndex = urlParts.findIndex(part => part === 'species');
      
      if (speciesFolderIndex >= 0 && speciesFolderIndex < urlParts.length - 1) {
        const folderName = urlParts[speciesFolderIndex + 1];
        
        // Try to match folder name to species
        const matchedSpecies = allSpecies?.find(species => {
          const speciesNameNormalized = species.name.toLowerCase()
            .replace(/[^a-z0-9]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
          
          return folderName.toLowerCase() === speciesNameNormalized ||
                 folderName.toLowerCase().includes(speciesNameNormalized) ||
                 speciesNameNormalized.includes(folderName.toLowerCase());
        });

        if (matchedSpecies) {
          console.log(`🔗 Linking photo ${photo.id} to species "${matchedSpecies.name}"`);
          
          const { error: updateError } = await supabase
            .from('photos_v2')
            .update({ species_id: matchedSpecies.id })
            .eq('id', photo.id);

          if (updateError) {
            console.error(`❌ Error updating photo ${photo.id}:`, updateError);
          } else {
            fixedCount++;
          }
        }
      }
    }

    console.log(`\n🎉 Fixed ${fixedCount} photo-species links`);

  } catch (error) {
    console.error('❌ Error fixing photo-species links:', error);
  }
}

// Run the investigation and fix
async function main() {
  const results = await investigatePhotoCounts();
  
  if (results && results.photosWithoutSpecies > 0) {
    await fixPhotoSpeciesLinks();
    
    // Re-run investigation to see improvements
    console.log('\n🔄 Re-checking after fixes...');
    await investigatePhotoCounts();
  }
}

main();
