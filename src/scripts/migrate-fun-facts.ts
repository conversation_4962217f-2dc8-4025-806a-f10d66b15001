import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Execute the fun facts migration from JSONB fields to the dedicated fun_facts table
 */
async function migrateFunFacts() {
  console.log('🚀 Starting fun facts migration...');
  
  try {
    // Execute the migration function
    const { data, error } = await supabase.rpc('migrate_fun_facts_to_table');
    
    if (error) {
      console.error('❌ Migration failed:', error);
      return;
    }
    
    console.log('✅ Migration completed successfully!');
    console.log('\n📊 Migration Results:');
    console.log('Species ID | Species Name | Facts Migrated | Status');
    console.log('-----------|--------------|----------------|--------');
    
    let totalFacts = 0;
    let speciesProcessed = 0;
    
    if (data && Array.isArray(data)) {
      data.forEach((result: any) => {
        console.log(`${result.species_id.substring(0, 8)}... | ${result.species_name.substring(0, 20).padEnd(20)} | ${result.migrated_facts_count.toString().padStart(14)} | ${result.status}`);
        totalFacts += result.migrated_facts_count;
        speciesProcessed++;
      });
    }
    
    console.log('\n📈 Summary:');
    console.log(`- Species processed: ${speciesProcessed}`);
    console.log(`- Total facts migrated: ${totalFacts}`);
    
    // Verify the migration by checking some sample data
    console.log('\n🔍 Verifying migration...');
    const { data: sampleFacts, error: verifyError } = await supabase
      .from('fun_facts')
      .select('species_id, fact')
      .limit(5);
    
    if (verifyError) {
      console.error('❌ Verification failed:', verifyError);
    } else {
      console.log('✅ Sample migrated facts:');
      sampleFacts?.forEach((fact, index) => {
        console.log(`${index + 1}. ${fact.fact.substring(0, 80)}...`);
      });
    }
    
  } catch (error) {
    console.error('❌ Unexpected error during migration:', error);
  }
}

/**
 * Check current state before migration
 */
async function checkCurrentState() {
  console.log('🔍 Checking current state...');
  
  // Check species with JSONB fun facts
  const { data: speciesWithJsonbFacts, error: jsonbError } = await supabase
    .from('species_v2')
    .select('id, name, ai_fun_facts, fun_facts_field')
    .or('ai_fun_facts.not.is.null,fun_facts_field.not.is.null');
  
  if (jsonbError) {
    console.error('❌ Error checking JSONB facts:', jsonbError);
    return;
  }
  
  // Check existing fun_facts table
  const { data: existingFacts, error: factsError } = await supabase
    .from('fun_facts')
    .select('species_id', { count: 'exact' });
  
  if (factsError) {
    console.error('❌ Error checking existing facts:', factsError);
    return;
  }
  
  console.log(`📊 Current state:`);
  console.log(`- Species with JSONB fun facts: ${speciesWithJsonbFacts?.length || 0}`);
  console.log(`- Existing facts in fun_facts table: ${existingFacts?.length || 0}`);
  
  return { speciesWithJsonbFacts, existingFactsCount: existingFacts?.length || 0 };
}

/**
 * Main execution function
 */
async function main() {
  console.log('🎯 Fun Facts Migration Tool');
  console.log('============================\n');
  
  // Check current state
  const currentState = await checkCurrentState();
  if (!currentState) return;
  
  // Ask for confirmation if there are existing facts
  if (currentState.existingFactsCount > 0) {
    console.log('\n⚠️  Warning: There are already facts in the fun_facts table.');
    console.log('This migration will clear existing facts and replace them with migrated data.');
    console.log('Continue? (This is a safe operation as it preserves the original JSONB data)');
  }
  
  // Execute migration
  await migrateFunFacts();
  
  console.log('\n🎉 Migration process completed!');
  console.log('\nNext steps:');
  console.log('1. Test the frontend to ensure fun facts display correctly');
  console.log('2. Verify that new fun facts are being saved to the fun_facts table');
  console.log('3. Consider removing the JSONB fields after confirming everything works');
}

// Run the migration
main().catch(console.error);
