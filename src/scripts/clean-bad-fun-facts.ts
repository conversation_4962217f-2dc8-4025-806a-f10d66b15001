import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client for Node.js environment
const supabaseUrl = 'https://raqxptgrugnmxdunbuty.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJhcXhwdGdydWdubXhkdW5idXR5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzM0NTAsImV4cCI6MjA2NTg0OTQ1MH0.0xVgSqjqch5a48Nxq4ZhaCZrMx0yaQ32qqi3YR1cLGQ';
const supabase = createClient(supabaseUrl, supabaseKey);

async function cleanBadFunFacts() {
  console.log('🧹 Cleaning up bad fun facts data...\n');

  try {
    // 1. Find all fun facts that contain debugging/state information
    const badPatterns = [
      '{state:',
      'state: empty',
      'value: null',
      'isStale: true',
      'isState: true',
      'undefined',
      'null',
      '[object Object]'
    ];

    console.log('🔍 Searching for bad fun facts...');
    
    let totalBadFacts = 0;
    const badFactsToDelete: string[] = [];

    for (const pattern of badPatterns) {
      const { data: badFacts, error } = await supabase
        .from('fun_facts')
        .select('id, fact, species_id, species_v2!inner(name)')
        .ilike('fact', `%${pattern}%`);

      if (error) {
        console.error(`❌ Error searching for pattern "${pattern}":`, error);
        continue;
      }

      if (badFacts && badFacts.length > 0) {
        console.log(`\n📋 Found ${badFacts.length} bad facts with pattern "${pattern}":`);
        badFacts.forEach((fact: any) => {
          console.log(`   - ${fact.species_v2.name}: "${fact.fact.substring(0, 50)}..."`);
          badFactsToDelete.push(fact.id);
        });
        totalBadFacts += badFacts.length;
      }
    }

    // Remove duplicates
    const uniqueBadFactIds = [...new Set(badFactsToDelete)];
    
    console.log(`\n📊 Summary:`);
    console.log(`   Total bad facts found: ${totalBadFacts}`);
    console.log(`   Unique bad facts to delete: ${uniqueBadFactIds.length}`);

    if (uniqueBadFactIds.length === 0) {
      console.log('✅ No bad fun facts found!');
      return;
    }

    // 2. Show some examples before deletion
    console.log('\n🔍 Examples of bad facts to be deleted:');
    const { data: examples, error: examplesError } = await supabase
      .from('fun_facts')
      .select('id, fact, species_v2!inner(name)')
      .in('id', uniqueBadFactIds.slice(0, 5));

    if (examplesError) {
      console.error('❌ Error fetching examples:', examplesError);
    } else if (examples) {
      examples.forEach((fact: any, index: number) => {
        console.log(`   ${index + 1}. ${fact.species_v2.name}: "${fact.fact}"`);
      });
    }

    // 3. Delete the bad facts
    console.log(`\n🗑️  Deleting ${uniqueBadFactIds.length} bad fun facts...`);
    
    const { error: deleteError } = await supabase
      .from('fun_facts')
      .delete()
      .in('id', uniqueBadFactIds);

    if (deleteError) {
      console.error('❌ Error deleting bad facts:', deleteError);
      return;
    }

    console.log('✅ Bad fun facts deleted successfully!');

    // 4. Verify the cleanup
    console.log('\n🔍 Verifying cleanup...');
    
    const { count: remainingBadFacts, error: countError } = await supabase
      .from('fun_facts')
      .select('*', { count: 'exact', head: true })
      .or(badPatterns.map(pattern => `fact.ilike.%${pattern}%`).join(','));

    if (countError) {
      console.error('❌ Error counting remaining bad facts:', countError);
    } else {
      console.log(`   Remaining bad facts: ${remainingBadFacts || 0}`);
    }

    // 5. Show updated statistics
    const { count: totalFacts, error: totalError } = await supabase
      .from('fun_facts')
      .select('*', { count: 'exact', head: true });

    if (totalError) {
      console.error('❌ Error counting total facts:', totalError);
    } else {
      console.log(`   Total fun facts remaining: ${totalFacts || 0}`);
    }

    // 6. Check Desert Cottontail specifically
    console.log('\n🐰 Checking Desert Cottontail after cleanup...');
    
    const { data: cottontail, error: cottontailError } = await supabase
      .from('species_v2')
      .select('id, name')
      .ilike('name', '%desert%cottontail%')
      .single();

    if (cottontailError) {
      console.log('❌ Desert Cottontail not found');
    } else {
      const { data: cottontailFacts, error: factsError } = await supabase
        .from('fun_facts')
        .select('fact')
        .eq('species_id', cottontail.id);

      if (factsError) {
        console.error('❌ Error fetching Desert Cottontail facts:', factsError);
      } else {
        console.log(`   Desert Cottontail now has ${cottontailFacts?.length || 0} fun facts`);
        if (cottontailFacts && cottontailFacts.length > 0) {
          cottontailFacts.forEach((fact, index) => {
            console.log(`   ${index + 1}. ${fact.fact}`);
          });
        }
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the cleanup
cleanBadFunFacts().then(() => {
  console.log('\n✅ Fun facts cleanup complete');
}).catch((error) => {
  console.error('❌ Cleanup script failed:', error);
});
