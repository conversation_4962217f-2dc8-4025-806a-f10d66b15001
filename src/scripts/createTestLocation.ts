import { supabase } from '@/integrations/supabase/client';

export async function createTestLocation() {
  try {
    // Check if we already have locations
    const { data: existingLocations } = await supabase
      .from('locations')
      .select('id')
      .limit(1);

    if (existingLocations && existingLocations.length > 0) {
      console.log('Locations already exist');
      return;
    }

    // Create a test location
    const { data, error } = await supabase
      .from('locations')
      .insert([
        {
          name: 'Yellowstone National Park',
          latitude: 44.4280,
          longitude: -110.5885,
          country: 'United States',
          state_province: 'Wyoming',
          description: 'America\'s first national park, known for its wildlife and geothermal features.',
          habitat_types: ['Forest', 'Grassland', 'Wetland'],
          visitor_tips: 'Best visited early morning or late evening for wildlife viewing.',
          best_times: 'May through September for optimal wildlife viewing.',
          featured: true,
          published: true
        },
        {
          name: 'Kruger National Park',
          latitude: -24.0094,
          longitude: 31.4987,
          country: 'South Africa',
          state_province: 'Mpumalanga',
          description: 'One of Africa\'s largest game reserves, home to the Big Five.',
          habitat_types: ['Savanna', 'Woodland', 'Riverine'],
          visitor_tips: 'Bring binoculars and visit water holes during dry season.',
          best_times: 'April to September for dry season wildlife viewing.',
          featured: true,
          published: true
        },
        {
          name: 'Banff National Park',
          latitude: 51.4968,
          longitude: -115.9281,
          country: 'Canada',
          state_province: 'Alberta',
          description: 'Canada\'s oldest national park, featuring mountain wildlife and pristine lakes.',
          habitat_types: ['Mountain', 'Forest', 'Alpine'],
          visitor_tips: 'Layer clothing and be bear aware. Carry bear spray.',
          best_times: 'June to September for hiking and wildlife viewing.',
          featured: true,
          published: true
        }
      ])
      .select();

    if (error) {
      console.error('Error creating test locations:', error);
    } else {
      console.log('Test locations created:', data);
    }
  } catch (error) {
    console.error('Error in createTestLocation:', error);
  }
}

// Auto-run if this file is imported
if (typeof window !== 'undefined') {
  createTestLocation();
}
