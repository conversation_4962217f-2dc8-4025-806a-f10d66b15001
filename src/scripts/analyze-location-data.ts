import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client for Node.js environment
const supabaseUrl = 'https://raqxptgrugnmxdunbuty.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJhcXhwdGdydWdubXhkdW5idXR5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzM0NTAsImV4cCI6MjA2NTg0OTQ1MH0.0xVgSqjqch5a48Nxq4ZhaCZrMx0yaQ32qqi3YR1cLGQ';
const supabase = createClient(supabaseUrl, supabaseKey);

async function analyzeLocationData() {
  console.log('🌍 Analyzing location data across species and photos...\n');

  try {
    // 1. Check species_v2 location fields
    console.log('📊 Species Location Data:');
    
    const { data: speciesLocations, error: speciesError } = await supabase
      .from('species_v2')
      .select('id, name, regions, countries, states_provinces, primary_region, geographic_scope')
      .eq('published', true)
      .limit(10);

    if (speciesError) {
      console.error('❌ Error fetching species locations:', speciesError);
    } else {
      console.log(`   Found ${speciesLocations?.length || 0} published species with location data`);
      speciesLocations?.forEach((species, index) => {
        if (index < 5) { // Show first 5 examples
          console.log(`   ${index + 1}. ${species.name}:`);
          console.log(`      Regions: ${species.regions || 'null'}`);
          console.log(`      Countries: ${species.countries || 'null'}`);
          console.log(`      States/Provinces: ${species.states_provinces || 'null'}`);
          console.log(`      Primary Region: ${species.primary_region || 'null'}`);
          console.log(`      Geographic Scope: ${species.geographic_scope || 'null'}`);
          console.log('');
        }
      });
    }

    // 2. Check photos_v2 location data
    console.log('\n📸 Photo Location Data:');
    
    const { data: photoLocations, error: photoError } = await supabase
      .from('photos_v2')
      .select('id, title, location, species_id')
      .eq('published', true)
      .not('location', 'is', null)
      .limit(20);

    if (photoError) {
      console.error('❌ Error fetching photo locations:', photoError);
    } else {
      console.log(`   Found ${photoLocations?.length || 0} published photos with location data`);
      
      // Group locations by frequency
      const locationCounts = new Map();
      photoLocations?.forEach(photo => {
        if (photo.location) {
          locationCounts.set(photo.location, (locationCounts.get(photo.location) || 0) + 1);
        }
      });

      // Sort by frequency
      const sortedLocations = Array.from(locationCounts.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 15);

      console.log('\n   Top photo locations by frequency:');
      sortedLocations.forEach(([location, count], index) => {
        console.log(`   ${index + 1}. "${location}" (${count} photos)`);
      });
    }

    // 3. Get total counts for metadata
    console.log('\n📈 Location Statistics:');
    
    // Count unique photo locations
    const { data: uniquePhotoLocations, error: photoLocationError } = await supabase
      .from('photos_v2')
      .select('location')
      .eq('published', true)
      .not('location', 'is', null);

    if (photoLocationError) {
      console.error('❌ Error counting photo locations:', photoLocationError);
    } else {
      const uniqueLocations = new Set(uniquePhotoLocations?.map(p => p.location));
      console.log(`   Unique photo locations: ${uniqueLocations.size}`);
    }

    // Count species with regions data
    const { data: speciesWithRegions, error: regionsError } = await supabase
      .from('species_v2')
      .select('regions, countries, states_provinces, primary_region')
      .eq('published', true);

    if (regionsError) {
      console.error('❌ Error counting species regions:', regionsError);
    } else {
      const withRegions = speciesWithRegions?.filter(s => s.regions).length || 0;
      const withCountries = speciesWithRegions?.filter(s => s.countries && s.countries.length > 0).length || 0;
      const withStates = speciesWithRegions?.filter(s => s.states_provinces && s.states_provinces.length > 0).length || 0;
      const withPrimaryRegion = speciesWithRegions?.filter(s => s.primary_region).length || 0;
      
      console.log(`   Species with regions field: ${withRegions}`);
      console.log(`   Species with countries array: ${withCountries}`);
      console.log(`   Species with states/provinces: ${withStates}`);
      console.log(`   Species with primary region: ${withPrimaryRegion}`);
    }

    // 4. Check for inconsistencies
    console.log('\n🔍 Location Data Quality Issues:');
    
    // Find photos with very short location names (likely incomplete)
    const { data: shortLocations, error: shortError } = await supabase
      .from('photos_v2')
      .select('location')
      .eq('published', true)
      .not('location', 'is', null);

    if (shortError) {
      console.error('❌ Error checking short locations:', shortError);
    } else {
      const shortLocationNames = shortLocations?.filter(p => 
        p.location && p.location.length < 5
      ) || [];
      console.log(`   Photos with very short location names: ${shortLocationNames.length}`);
      
      if (shortLocationNames.length > 0) {
        console.log('   Examples:');
        shortLocationNames.slice(0, 5).forEach((p, index) => {
          console.log(`     ${index + 1}. "${p.location}"`);
        });
      }
    }

    // Find locations that might need standardization
    const allLocations = uniquePhotoLocations?.map(p => p.location).filter(Boolean) || [];
    const locationVariations = new Map();
    
    allLocations.forEach(location => {
      const normalized = location.toLowerCase().trim();
      if (!locationVariations.has(normalized)) {
        locationVariations.set(normalized, []);
      }
      locationVariations.get(normalized).push(location);
    });

    const duplicateVariations = Array.from(locationVariations.entries())
      .filter(([_, variations]) => variations.length > 1)
      .slice(0, 10);

    if (duplicateVariations.length > 0) {
      console.log('\n   Potential location variations that could be standardized:');
      duplicateVariations.forEach(([normalized, variations], index) => {
        console.log(`   ${index + 1}. "${normalized}": ${variations.join(', ')}`);
      });
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the analysis
analyzeLocationData().then(() => {
  console.log('\n✅ Location data analysis complete');
}).catch((error) => {
  console.error('❌ Analysis script failed:', error);
});
