import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client for Node.js environment
const supabaseUrl = 'https://raqxptgrugnmxdunbuty.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJhcXhwdGdydWdubXhkdW5idXR5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzM0NTAsImV4cCI6MjA2NTg0OTQ1MH0.0xVgSqjqch5a48Nxq4ZhaCZrMx0yaQ32qqi3YR1cLGQ';
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkGoodFunFacts() {
  console.log('🔍 Checking species with good fun facts...\n');

  try {
    // Find species that have fun facts
    const { data: speciesWithFacts, error } = await supabase
      .from('fun_facts')
      .select(`
        species_id,
        fact,
        species_v2!inner(name, scientific_name)
      `)
      .limit(10);

    if (error) {
      console.error('❌ Error fetching species with facts:', error);
      return;
    }

    if (!speciesWithFacts || speciesWithFacts.length === 0) {
      console.log('❌ No species with fun facts found');
      return;
    }

    console.log(`✅ Found ${speciesWithFacts.length} fun facts. Sample species with good facts:\n`);

    // Group by species
    const speciesMap = new Map();
    speciesWithFacts.forEach((item: any) => {
      const speciesName = item.species_v2.name;
      if (!speciesMap.has(speciesName)) {
        speciesMap.set(speciesName, []);
      }
      speciesMap.get(speciesName).push(item.fact);
    });

    let count = 1;
    for (const [speciesName, facts] of speciesMap.entries()) {
      console.log(`${count}. ${speciesName}:`);
      facts.forEach((fact: string, index: number) => {
        console.log(`   ${index + 1}. ${fact.substring(0, 100)}${fact.length > 100 ? '...' : ''}`);
      });
      console.log('');
      count++;
      if (count > 5) break; // Show only first 5 species
    }

    // Check total statistics
    const { count: totalFacts, error: countError } = await supabase
      .from('fun_facts')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('❌ Error counting total facts:', countError);
    } else {
      console.log(`📊 Total fun facts in database: ${totalFacts || 0}`);
    }

    // Check how many species have fun facts
    const { data: speciesCount, error: speciesCountError } = await supabase
      .from('fun_facts')
      .select('species_id')
      .then(result => {
        if (result.error) throw result.error;
        const uniqueSpecies = new Set(result.data?.map(item => item.species_id));
        return { count: uniqueSpecies.size, error: null };
      });

    if (speciesCountError) {
      console.error('❌ Error counting species with facts:', speciesCountError);
    } else {
      console.log(`📊 Number of species with fun facts: ${speciesCount.count || 0}`);
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the check
checkGoodFunFacts().then(() => {
  console.log('\n✅ Fun facts check complete');
}).catch((error) => {
  console.error('❌ Check script failed:', error);
});
