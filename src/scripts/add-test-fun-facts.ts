import { supabase } from '../integrations/supabase/client';

async function addTestFunFacts() {
  console.log('🎯 Adding Test Fun Facts...\n');

  try {
    // Get some published species to add fun facts to
    const { data: species, error: speciesError } = await supabase
      .from('species_v2')
      .select('id, name, scientific_name, category')
      .eq('published', true)
      .limit(5);

    if (speciesError) {
      console.error('❌ Error fetching species:', speciesError);
      return;
    }

    if (!species || species.length === 0) {
      console.log('❌ No published species found');
      return;
    }

    console.log(`✅ Found ${species.length} published species to add fun facts to\n`);

    // Define fun facts for different categories
    const funFactsTemplates = {
      Birds: [
        'can fly at speeds up to 60 mph during migration',
        'has excellent color vision and can see ultraviolet light',
        'builds intricate nests using hundreds of twigs and leaves',
        'can live up to 20 years in the wild',
        'plays an important role in seed dispersal for many plants',
        'has a unique call that can be heard from over a mile away'
      ],
      Mammals: [
        'has an excellent sense of smell that is 100 times better than humans',
        'can jump up to 10 times their body length',
        'is highly social and lives in complex family groups',
        'has adapted to survive in extreme weather conditions',
        'plays a crucial role in maintaining ecosystem balance',
        'can live up to 15-25 years in the wild'
      ],
      Reptiles: [
        'can regulate their body temperature by basking in the sun',
        'sheds their skin several times a year as they grow',
        'has excellent camouflage abilities to avoid predators',
        'can survive for weeks without food during cooler months',
        'has specialized scales that help prevent water loss',
        'plays an important role in controlling insect populations'
      ],
      Fish: [
        'can detect electrical fields generated by other fish',
        'has specialized gills that extract oxygen from water',
        'can change color to communicate with other fish',
        'migrates hundreds of miles during spawning season',
        'has a lateral line system that detects water movement',
        'is an important food source for many marine predators'
      ],
      default: [
        'is perfectly adapted to its natural habitat',
        'plays an important role in the local ecosystem',
        'has fascinating behaviors that scientists are still studying',
        'faces various conservation challenges in the wild',
        'is an indicator species for environmental health',
        'has unique adaptations that help it survive'
      ]
    };

    let totalAdded = 0;

    for (const sp of species) {
      console.log(`🔄 Adding fun facts for ${sp.name}...`);

      // Clear existing fun facts for this species
      const { error: deleteError } = await supabase
        .from('fun_facts')
        .delete()
        .eq('species_id', sp.id);

      if (deleteError) {
        console.error(`❌ Error clearing existing facts for ${sp.name}:`, deleteError);
        continue;
      }

      // Get appropriate fun facts template
      const category = sp.category || 'default';
      const template = funFactsTemplates[category as keyof typeof funFactsTemplates] || funFactsTemplates.default;

      // Create species-specific fun facts
      const speciesFacts = [
        `${sp.name} ${template[0]}`,
        `${sp.name} ${template[1]}`,
        `${sp.name} ${template[2]}`,
        `${sp.name} ${template[3]}`,
        `${sp.name} ${template[4]}`
      ];

      // Insert fun facts
      const factsToInsert = speciesFacts.map(fact => ({
        species_id: sp.id,
        fact: fact
      }));

      const { data: insertedFacts, error: insertError } = await supabase
        .from('fun_facts')
        .insert(factsToInsert)
        .select();

      if (insertError) {
        console.error(`❌ Error inserting facts for ${sp.name}:`, insertError);
      } else {
        const inserted = insertedFacts?.length || 0;
        console.log(`✅ Added ${inserted} fun facts for ${sp.name}`);
        totalAdded += inserted;
      }
    }

    console.log(`\n🎉 Successfully added ${totalAdded} fun facts total!`);

    // Verify with a sample
    console.log('\n🔍 Verification - Sample fun facts:');
    const { data: sampleFacts, error: sampleError } = await supabase
      .from('fun_facts')
      .select(`
        fact,
        species_v2!inner(name)
      `)
      .limit(5);

    if (sampleError) {
      console.error('❌ Error fetching sample facts:', sampleError);
    } else if (sampleFacts && sampleFacts.length > 0) {
      sampleFacts.forEach((item: any, index: number) => {
        console.log(`${index + 1}. ${item.species_v2.name}: ${item.fact}`);
      });
    }

    // Check Red-capped Parrot specifically
    const { data: parrot, error: parrotError } = await supabase
      .from('species_v2')
      .select('id, name')
      .ilike('name', '%red-capped%parrot%')
      .single();

    if (!parrotError && parrot) {
      const { data: parrotFacts, error: parrotFactsError } = await supabase
        .from('fun_facts')
        .select('fact')
        .eq('species_id', parrot.id);

      if (!parrotFactsError && parrotFacts) {
        console.log(`\n🦜 ${parrot.name} fun facts (${parrotFacts.length}):`);
        parrotFacts.forEach((fact: any, index: number) => {
          console.log(`   ${index + 1}. ${fact.fact}`);
        });
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the script
addTestFunFacts().then(() => {
  console.log('\n✅ Test fun facts addition complete');
}).catch((error) => {
  console.error('❌ Script failed:', error);
});
