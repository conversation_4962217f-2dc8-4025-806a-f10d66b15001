import { supabase } from '../integrations/supabase/client';

async function checkGeographicData() {
  console.log('🔍 Checking geographic data for species...\n');

  try {
    // Get a few species to check their geographic data
    const { data: species, error } = await supabase
      .from('species_v2')
      .select('id, name, scientific_name, regions, countries, states_provinces, primary_region, geographic_scope, habitat_specificity')
      .eq('published', true)
      .limit(5);

    if (error) {
      console.error('❌ Error fetching species:', error);
      return;
    }

    if (!species || species.length === 0) {
      console.log('❌ No published species found');
      return;
    }

    console.log(`✅ Found ${species.length} published species\n`);

    species.forEach((sp, index) => {
      console.log(`${index + 1}. ${sp.name} (${sp.scientific_name})`);
      console.log(`   ID: ${sp.id}`);
      console.log(`   Legacy regions: ${sp.regions || 'None'}`);
      console.log(`   Countries: ${sp.countries ? JSON.stringify(sp.countries) : 'None'}`);
      console.log(`   States/Provinces: ${sp.states_provinces ? JSON.stringify(sp.states_provinces) : 'None'}`);
      console.log(`   Primary region: ${sp.primary_region || 'None'}`);
      console.log(`   Geographic scope: ${sp.geographic_scope || 'None'}`);
      console.log(`   Habitat specificity: ${sp.habitat_specificity || 'None'}`);
      console.log('');
    });

    // Check specifically for Red-capped Parrot
    const { data: parrot, error: parrotError } = await supabase
      .from('species_v2')
      .select('*')
      .ilike('name', '%red-capped%parrot%')
      .single();

    if (parrotError) {
      console.log('❌ Red-capped Parrot not found or error:', parrotError.message);
    } else if (parrot) {
      console.log('🦜 Red-capped Parrot Details:');
      console.log(`   Name: ${parrot.name}`);
      console.log(`   Scientific: ${parrot.scientific_name}`);
      console.log(`   Legacy regions: ${parrot.regions || 'None'}`);
      console.log(`   Countries: ${parrot.countries ? JSON.stringify(parrot.countries) : 'None'}`);
      console.log(`   States/Provinces: ${parrot.states_provinces ? JSON.stringify(parrot.states_provinces) : 'None'}`);
      console.log(`   Primary region: ${parrot.primary_region || 'None'}`);
      console.log(`   Geographic scope: ${parrot.geographic_scope || 'None'}`);
      console.log(`   Habitat specificity: ${parrot.habitat_specificity || 'None'}`);
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the check
checkGeographicData().then(() => {
  console.log('\n✅ Geographic data check complete');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
