import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://raqxptgrugnmxdunbuty.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJhcXhwdGdydWdubXhkdW5idXR5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDI3MzQ1MCwiZXhwIjoyMDY1ODQ5NDUwfQ.4S9i9qeZeydZ5LzG_kg6bndBeYG09cXne_yniYJFmyA';

const supabase = createClient(supabaseUrl, supabaseKey);

interface ValidationIssue {
  id: string;
  name: string;
  issue: string;
  originalValue: any;
  fixedValue: any;
}

async function fixDataValidationIssues(): Promise<ValidationIssue[]> {
  console.log('🔧 Fixing species data validation issues...');
  const fixes: ValidationIssue[] = [];

  try {
    // Get all published species to check for validation issues
    const { data: species, error } = await supabase
      .from('species_v2')
      .select('*')
      .eq('published', true);

    if (error) {
      console.error('Error fetching species:', error);
      throw error;
    }

    console.log(`Found ${species?.length || 0} published species to validate`);

    if (!species) return fixes;

    for (const sp of species) {
      const updates: any = {};
      let hasUpdates = false;

      // 1. Fix malformed ai_fun_facts JSON
      if (sp.ai_fun_facts) {
        try {
          // Try to parse as JSON
          const parsed = JSON.parse(sp.ai_fun_facts);
          // If it's already valid JSON, no need to fix
        } catch (e) {
          // If it's not valid JSON, try to clean it up
          let cleaned = String(sp.ai_fun_facts);
          
          // Remove common formatting issues
          cleaned = cleaned
            .replace(/^["'\s]+|["'\s]+$/g, '') // Remove leading/trailing quotes and spaces
            .replace(/\\"/g, '"') // Unescape quotes
            .replace(/\n/g, ' ') // Replace newlines with spaces
            .replace(/\s{2,}/g, ' ') // Replace multiple spaces with single space
            .trim();

          // If it looks like it should be an array, wrap it
          if (cleaned && !cleaned.startsWith('[') && !cleaned.startsWith('{')) {
            cleaned = `["${cleaned}"]`;
          }

          // Try to parse the cleaned version
          try {
            JSON.parse(cleaned);
            updates.ai_fun_facts = cleaned;
            hasUpdates = true;
            fixes.push({
              id: sp.id,
              name: sp.name || 'Unknown',
              issue: 'malformed ai_fun_facts JSON',
              originalValue: sp.ai_fun_facts,
              fixedValue: cleaned
            });
          } catch (e2) {
            // If still can't parse, set to null
            updates.ai_fun_facts = null;
            hasUpdates = true;
            fixes.push({
              id: sp.id,
              name: sp.name || 'Unknown',
              issue: 'removed invalid ai_fun_facts',
              originalValue: sp.ai_fun_facts,
              fixedValue: null
            });
          }
        }
      }

      // 2. Fix empty or whitespace-only names
      if (!sp.name || sp.name.trim() === '') {
        const newName = sp.scientific_name || sp.common_name || `Species ${sp.id.substring(0, 8)}`;
        updates.name = newName;
        hasUpdates = true;
        fixes.push({
          id: sp.id,
          name: newName,
          issue: 'empty name field',
          originalValue: sp.name,
          fixedValue: newName
        });
      }

      // 3. Truncate extremely long descriptions that might cause rendering issues
      if (sp.description && sp.description.length > 5000) {
        const truncated = sp.description.substring(0, 4900) + '...';
        updates.description = truncated;
        hasUpdates = true;
        fixes.push({
          id: sp.id,
          name: sp.name || 'Unknown',
          issue: 'truncated long description',
          originalValue: `${sp.description.length} characters`,
          fixedValue: `${truncated.length} characters`
        });
      }

      // 4. Clean up habitat field formatting
      if (sp.habitat && typeof sp.habitat === 'string') {
        let cleanedHabitat = sp.habitat
          .replace(/^\["|"\]$/g, '') // Remove array-like brackets and quotes
          .replace(/^"|"$/g, '') // Remove standalone quotes
          .replace(/\\"/g, '"') // Unescape quotes
          .replace(/\[|\]/g, '') // Remove any remaining brackets
          .trim();

        if (cleanedHabitat !== sp.habitat) {
          updates.habitat = cleanedHabitat;
          hasUpdates = true;
          fixes.push({
            id: sp.id,
            name: sp.name || 'Unknown',
            issue: 'cleaned habitat formatting',
            originalValue: sp.habitat,
            fixedValue: cleanedHabitat
          });
        }
      }

      // 5. Clean up diet field formatting
      if (sp.diet && typeof sp.diet === 'string') {
        let cleanedDiet = sp.diet
          .replace(/^\["|"\]$/g, '') // Remove array-like brackets and quotes
          .replace(/^"|"$/g, '') // Remove standalone quotes
          .replace(/\\"/g, '"') // Unescape quotes
          .replace(/\[|\]/g, '') // Remove any remaining brackets
          .trim();

        if (cleanedDiet !== sp.diet) {
          updates.diet = cleanedDiet;
          hasUpdates = true;
          fixes.push({
            id: sp.id,
            name: sp.name || 'Unknown',
            issue: 'cleaned diet formatting',
            originalValue: sp.diet,
            fixedValue: cleanedDiet
          });
        }
      }

      // Apply updates if any
      if (hasUpdates) {
        console.log(`Fixing species ${sp.name} (${sp.id}):`, Object.keys(updates));
        const { error: updateError } = await supabase
          .from('species_v2')
          .update(updates)
          .eq('id', sp.id);

        if (updateError) {
          console.error(`Error updating species ${sp.id}:`, updateError);
        }
      }
    }

    console.log(`✅ Fixed ${fixes.length} data validation issues`);
    return fixes;

  } catch (error) {
    console.error('Error during data validation fixes:', error);
    throw error;
  }
}

async function main() {
  try {
    console.log('🚀 Starting species data validation fixes...\n');
    
    const fixes = await fixDataValidationIssues();
    
    console.log('\n📊 Validation Fix Results:');
    console.log('===========================');
    
    if (fixes.length === 0) {
      console.log('✅ No data validation issues found!');
    } else {
      // Group fixes by issue type
      const fixesByType = fixes.reduce((acc, fix) => {
        if (!acc[fix.issue]) acc[fix.issue] = [];
        acc[fix.issue].push(fix);
        return acc;
      }, {} as Record<string, ValidationIssue[]>);

      Object.entries(fixesByType).forEach(([issueType, issueFixes]) => {
        console.log(`\n${issueType}: ${issueFixes.length} fixes`);
        issueFixes.slice(0, 3).forEach(fix => {
          console.log(`  - ${fix.name} (${fix.id.substring(0, 8)})`);
        });
        if (issueFixes.length > 3) {
          console.log(`  ... and ${issueFixes.length - 3} more`);
        }
      });
    }

    console.log('\n🎉 Data validation fixes completed!');
    console.log('\n💡 Next steps:');
    console.log('1. Refresh your browser to see the fixes');
    console.log('2. Check if the "Error loading species card" messages are reduced');
    console.log('3. Monitor the browser console for any remaining React errors');

  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

// Run the script
main();
