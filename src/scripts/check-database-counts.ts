import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://raqxptgrugnmxdunbuty.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJhcXhwdGdydWdubXhkdW5idXR5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5OTI5NzQsImV4cCI6MjA1MDU2ODk3NH0.YNJBaVJmLBih_eqU7dCbJ-lJW7VZvVOGAhUOLdtKBJo';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkDatabaseCounts() {
  console.log('🔍 Checking database counts...\n');

  try {
    // Get species counts
    const { count: totalSpecies } = await supabase
      .from('species_v2')
      .select('*', { count: 'exact', head: true });

    const { count: publishedSpecies } = await supabase
      .from('species_v2')
      .select('*', { count: 'exact', head: true })
      .eq('published', true);

    // Get photo counts
    const { count: totalPhotos } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true });

    const { count: publishedPhotos } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true })
      .eq('published', true);

    // Get species with photos count
    const { data: speciesWithPhotos } = await supabase
      .from('species_v2')
      .select('id')
      .eq('published', true)
      .gt('photo_count', 0);

    console.log('📊 Database Counts:');
    console.log('==================');
    console.log(`Total Species: ${totalSpecies}`);
    console.log(`Published Species: ${publishedSpecies}`);
    console.log(`Species with Photos: ${speciesWithPhotos?.length || 0}`);
    console.log(`Total Photos: ${totalPhotos}`);
    console.log(`Published Photos: ${publishedPhotos}`);

    console.log('\n🔍 Updated Limits in Code:');
    console.log('==========================');
    console.log('useSpeciesData.tsx fetchSpeciesWithPhotos():');
    console.log('  - Species limit: 500 (increased from 100)');
    console.log('  - Photos limit: 2000 (increased from 1000)');
    console.log('UnifiedWildlifeExplorer.tsx loadData():');
    console.log('  - Photos limit: 2000 (increased from 100)');
    console.log('SpeciesManager.tsx:');
    console.log('  - Species limit: 500 (increased from 100)');
    console.log('EnhancedPhotoManager.tsx:');
    console.log('  - Species limit: 500 (increased from 100)');

    console.log('\n💡 Recommendations:');
    console.log('===================');
    if (publishedSpecies && publishedSpecies > 100) {
      console.log(`❌ Species limit (100) is too low! You have ${publishedSpecies} published species.`);
      console.log(`   Recommend increasing to ${Math.max(publishedSpecies + 50, 500)}`);
    } else {
      console.log('✅ Species limit (100) is adequate');
    }

    if (publishedPhotos && publishedPhotos > 1000) {
      console.log(`❌ Photos limit (1000) is too low! You have ${publishedPhotos} published photos.`);
      console.log(`   Recommend increasing to ${Math.max(publishedPhotos + 200, 2000)}`);
    } else {
      console.log('✅ Photos limit (1000) is adequate');
    }

  } catch (error) {
    console.error('❌ Error checking database counts:', error);
  }
}

checkDatabaseCounts();