import { supabase } from '@/integrations/supabase/client';

// Run the photos_v2 migration to add missing columns
export const runPhotosV2Migration = async () => {
  console.log('🔄 Running photos_v2 migration to add missing columns...');
  
  try {
    // SQL to add missing columns
    const migrationSQL = `
      -- Add missing AI fields to photos_v2 table
      ALTER TABLE photos_v2 
      ADD COLUMN IF NOT EXISTS ai_generated_metadata BOOLEAN DEFAULT FALSE,
      ADD COLUMN IF NOT EXISTS needs_review BOOLEAN DEFAULT FALSE,
      ADD COLUMN IF NOT EXISTS ai_suggested_id UUID,
      ADD COLUMN IF NOT EXISTS ai_confidence DECIMAL(3,2),
      ADD COLUMN IF NOT EXISTS ai_reviewed BOOLEAN DEFAULT FALSE,
      ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}',
      ADD COLUMN IF NOT EXISTS hash TEXT;

      -- Create indexes
      CREATE INDEX IF NOT EXISTS idx_photos_v2_hash ON photos_v2(hash);
      CREATE INDEX IF NOT EXISTS idx_photos_v2_ai_generated_metadata ON photos_v2(ai_generated_metadata);
      CREATE INDEX IF NOT EXISTS idx_photos_v2_needs_review ON photos_v2(needs_review);
      CREATE INDEX IF NOT EXISTS idx_photos_v2_ai_reviewed ON photos_v2(ai_reviewed);
    `;

    const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL });
    
    if (error) {
      console.error('❌ Migration failed:', error);
      return { success: false, error: error.message };
    }
    
    console.log('✅ Photos_v2 migration completed successfully!');
    return { success: true };
    
  } catch (error) {
    console.error('❌ Migration error:', error);
    return { success: false, error: String(error) };
  }
};

// Auto-run migration when script is loaded
if (typeof window !== 'undefined') {
  console.log('🔧 Photos V2 migration functions loaded!');
  console.log('- Run runPhotosV2Migration() to add missing columns to photos_v2');
  
  // Make function available globally for console use
  (window as any).runPhotosV2Migration = runPhotosV2Migration;
}
