import { createClient } from '@supabase/supabase-js';

// Load environment variables manually for Node.js
const SUPABASE_URL = "https://raqxptgrugnmxdunbuty.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJhcXhwdGdydWdubXhkdW5idXR5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzM0NTAsImV4cCI6MjA2NTg0OTQ1MH0.0xVgSqjqch5a48Nxq4ZhaCZrMx0yaQ32qqi3YR1cLGQ";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function investigatePhotoTable() {
  console.log('🔍 Investigating photos_v2 table structure...');

  try {
    // Get a sample of photos to see the structure
    const { data: samplePhotos, error: sampleError } = await supabase
      .from('photos_v2')
      .select('*')
      .limit(5);

    if (sampleError) {
      console.error('❌ Error fetching sample photos:', sampleError);
      return;
    }

    console.log('📊 Sample photos structure:');
    console.log(JSON.stringify(samplePhotos, null, 2));

    // Count total photos
    const { count: totalPhotos, error: countError } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('❌ Error counting photos:', countError);
      return;
    }

    console.log(`📊 Total photos in photos_v2: ${totalPhotos}`);

    // Count photos with null IDs
    const { count: nullIdPhotos, error: nullIdError } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true })
      .is('id', null);

    if (nullIdError) {
      console.error('❌ Error counting null ID photos:', nullIdError);
    } else {
      console.log(`📊 Photos with null IDs: ${nullIdPhotos}`);
    }

    // Count photos with valid IDs
    const { count: validIdPhotos, error: validIdError } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true })
      .not('id', 'is', null);

    if (validIdError) {
      console.error('❌ Error counting valid ID photos:', validIdError);
    } else {
      console.log(`📊 Photos with valid IDs: ${validIdPhotos}`);
    }

    // Get photos with valid IDs to see their structure
    const { data: validPhotos, error: validPhotosError } = await supabase
      .from('photos_v2')
      .select('*')
      .not('id', 'is', null)
      .limit(5);

    if (validPhotosError) {
      console.error('❌ Error fetching valid photos:', validPhotosError);
    } else {
      console.log('\n📊 Sample photos with valid IDs:');
      console.log(JSON.stringify(validPhotos, null, 2));
    }

    // Check if there are photos without species_id but with valid IDs
    const { count: orphanedValidPhotos, error: orphanedValidError } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true })
      .not('id', 'is', null)
      .is('species_id', null);

    if (orphanedValidError) {
      console.error('❌ Error counting orphaned valid photos:', orphanedValidError);
    } else {
      console.log(`📊 Valid photos without species_id: ${orphanedValidPhotos}`);
    }

  } catch (error) {
    console.error('❌ Error investigating photo table:', error);
  }
}

async function fixPhotoIds() {
  console.log('\n🔧 Attempting to fix photo IDs...');

  try {
    // First, let's see if we can identify photos that need IDs
    const { data: photosNeedingIds, error: fetchError } = await supabase
      .from('photos_v2')
      .select('url, title, species_id, created_at')
      .is('id', null)
      .limit(10);

    if (fetchError) {
      console.error('❌ Error fetching photos needing IDs:', fetchError);
      return;
    }

    console.log(`📊 Found ${photosNeedingIds?.length || 0} photos needing IDs (showing first 10)`);

    if (photosNeedingIds && photosNeedingIds.length > 0) {
      console.log('📝 Sample photos needing IDs:');
      photosNeedingIds.forEach((photo, index) => {
        console.log(`   ${index + 1}. URL: ${photo.url?.substring(0, 60)}...`);
        console.log(`      Title: ${photo.title || 'No title'}`);
        console.log(`      Species ID: ${photo.species_id || 'None'}`);
        console.log(`      Created: ${photo.created_at || 'Unknown'}`);
        console.log('');
      });
    }

    // The issue might be that the ID column is not properly set as a primary key with auto-generation
    // Let's try to understand the table structure better
    console.log('\n🔍 Checking table constraints and structure...');
    
    // This would require admin access to check table structure
    // For now, let's try a different approach - check if we can insert a test record
    
  } catch (error) {
    console.error('❌ Error fixing photo IDs:', error);
  }
}

async function fixPhotoSpeciesLinks() {
  console.log('\n🔧 Attempting to fix photo-species links for photos with valid IDs...');

  try {
    // Get photos with valid IDs but no species_id
    const { data: orphanedPhotos, error: orphanedError } = await supabase
      .from('photos_v2')
      .select('id, url, title, species_id')
      .not('id', 'is', null)
      .is('species_id', null)
      .eq('published', true)
      .limit(50); // Process in batches

    if (orphanedError) {
      console.error('❌ Error fetching orphaned photos:', orphanedError);
      return;
    }

    console.log(`📊 Found ${orphanedPhotos?.length || 0} photos with valid IDs but no species_id`);

    if (!orphanedPhotos || orphanedPhotos.length === 0) {
      console.log('✅ No orphaned photos with valid IDs to fix');
      return;
    }

    // Get all species for matching
    const { data: allSpecies, error: speciesError } = await supabase
      .from('species_v2')
      .select('id, name')
      .eq('published', true);

    if (speciesError) {
      console.error('❌ Error fetching species:', speciesError);
      return;
    }

    let fixedCount = 0;

    // Try to match photos to species based on URL patterns
    for (const photo of orphanedPhotos) {
      if (!photo.url) continue;

      // Extract species name from URL path
      const urlParts = photo.url.split('/');
      const speciesFolderIndex = urlParts.findIndex(part => part === 'species');
      
      if (speciesFolderIndex >= 0 && speciesFolderIndex < urlParts.length - 1) {
        const folderName = urlParts[speciesFolderIndex + 1];
        
        // Try to match folder name to species
        const matchedSpecies = allSpecies?.find(species => {
          const speciesNameNormalized = species.name.toLowerCase()
            .replace(/[^a-z0-9]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
          
          return folderName.toLowerCase() === speciesNameNormalized ||
                 folderName.toLowerCase().includes(speciesNameNormalized) ||
                 speciesNameNormalized.includes(folderName.toLowerCase());
        });

        if (matchedSpecies) {
          console.log(`🔗 Linking photo ${photo.id} to species "${matchedSpecies.name}"`);
          
          const { error: updateError } = await supabase
            .from('photos_v2')
            .update({ species_id: matchedSpecies.id })
            .eq('id', photo.id);

          if (updateError) {
            console.error(`❌ Error updating photo ${photo.id}:`, updateError);
          } else {
            fixedCount++;
          }
        }
      }
    }

    console.log(`\n🎉 Fixed ${fixedCount} photo-species links`);

  } catch (error) {
    console.error('❌ Error fixing photo-species links:', error);
  }
}

// Run the investigation and fix
async function main() {
  await investigatePhotoTable();
  await fixPhotoIds();
  await fixPhotoSpeciesLinks();
}

main();
