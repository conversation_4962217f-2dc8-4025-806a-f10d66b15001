import { supabase } from '../integrations/supabase/client';

async function checkFunFacts() {
  console.log('🔍 Checking Fun Facts Status...\n');

  try {
    // 1. Check if fun_facts table has any data
    const { data: funFactsData, error: funFactsError, count } = await supabase
      .from('fun_facts')
      .select('*', { count: 'exact' })
      .limit(5);

    if (funFactsError) {
      console.error('❌ Error accessing fun_facts table:', funFactsError);
      return;
    }

    console.log(`📊 Fun Facts Table Status:`);
    console.log(`   Total records: ${count || 0}`);
    
    if (funFactsData && funFactsData.length > 0) {
      console.log(`   Sample records:`);
      funFactsData.forEach((fact, index) => {
        console.log(`   ${index + 1}. Species ID: ${fact.species_id}`);
        console.log(`      Fact: ${fact.fact.substring(0, 80)}...`);
      });
    } else {
      console.log('   ❌ No fun facts found in dedicated table');
    }

    // 2. Check species_v2 for JSONB fun facts data
    const { data: speciesData, error: speciesError } = await supabase
      .from('species_v2')
      .select('id, name, ai_fun_facts, fun_facts_field')
      .not('ai_fun_facts', 'is', null)
      .limit(5);

    if (speciesError) {
      console.error('❌ Error checking species JSONB fun facts:', speciesError);
      return;
    }

    console.log(`\n📊 Species JSONB Fun Facts:`);
    if (speciesData && speciesData.length > 0) {
      console.log(`   Found ${speciesData.length} species with JSONB fun facts:`);
      speciesData.forEach((species, index) => {
        console.log(`   ${index + 1}. ${species.name} (${species.id})`);
        console.log(`      ai_fun_facts: ${species.ai_fun_facts ? 'Present' : 'None'}`);
        console.log(`      fun_facts_field: ${species.fun_facts_field ? 'Present' : 'None'}`);
        
        // Try to parse ai_fun_facts if it exists
        if (species.ai_fun_facts) {
          try {
            const parsed = JSON.parse(species.ai_fun_facts);
            if (Array.isArray(parsed)) {
              console.log(`      Parsed facts count: ${parsed.length}`);
              if (parsed.length > 0) {
                console.log(`      First fact: ${parsed[0].substring(0, 60)}...`);
              }
            }
          } catch (e) {
            console.log(`      ai_fun_facts parsing failed: ${e.message}`);
          }
        }
      });
    } else {
      console.log('   ❌ No species found with JSONB fun facts');
    }

    // 3. Check a specific species (Red-capped Parrot)
    const { data: parrotData, error: parrotError } = await supabase
      .from('species_v2')
      .select('id, name, ai_fun_facts, fun_facts_field')
      .ilike('name', '%red-capped%parrot%')
      .single();

    if (parrotError) {
      console.log('\n❌ Red-capped Parrot not found or error:', parrotError.message);
    } else if (parrotData) {
      console.log(`\n🦜 Red-capped Parrot Fun Facts Check:`);
      console.log(`   Species ID: ${parrotData.id}`);
      console.log(`   Name: ${parrotData.name}`);
      console.log(`   ai_fun_facts: ${parrotData.ai_fun_facts ? 'Present' : 'None'}`);
      console.log(`   fun_facts_field: ${parrotData.fun_facts_field ? 'Present' : 'None'}`);

      // Check dedicated fun_facts table for this species
      const { data: parrotFunFacts, error: parrotFunFactsError } = await supabase
        .from('fun_facts')
        .select('fact')
        .eq('species_id', parrotData.id);

      if (parrotFunFactsError) {
        console.log(`   ❌ Error fetching dedicated fun facts: ${parrotFunFactsError.message}`);
      } else {
        console.log(`   Dedicated table facts: ${parrotFunFacts?.length || 0}`);
        if (parrotFunFacts && parrotFunFacts.length > 0) {
          parrotFunFacts.forEach((fact, index) => {
            console.log(`   ${index + 1}. ${fact.fact}`);
          });
        }
      }
    }

    // 4. Check if migration function exists
    console.log(`\n🔧 Checking Migration Function:`);
    try {
      const { data: migrationResult, error: migrationError } = await supabase
        .rpc('migrate_fun_facts_to_table');

      if (migrationError) {
        console.log(`   ❌ Migration function error: ${migrationError.message}`);
      } else {
        console.log(`   ✅ Migration function available and executed`);
        console.log(`   Migration result:`, migrationResult);
      }
    } catch (error) {
      console.log(`   ❌ Migration function not available or failed: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the check
checkFunFacts().then(() => {
  console.log('\n✅ Fun Facts check complete');
}).catch((error) => {
  console.error('❌ Script failed:', error);
});
