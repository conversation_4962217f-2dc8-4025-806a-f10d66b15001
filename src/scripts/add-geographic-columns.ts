import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables');
  console.log('Available env vars:', {
    url: !!supabaseUrl,
    anonKey: !!supabaseAnonKey
  });
}

// Use anon key for now - we'll provide SQL commands to run manually
const supabase = createClient(supabaseUrl || '', supabaseAnonKey || '');

export async function addGeographicColumns() {
  console.log('🔧 Checking geographic columns in species_v2 table...');

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('❌ Missing Supabase environment variables');
    return { success: false, message: 'Missing environment variables' };
  }

  try {
    // First, test if columns already exist by trying to select them
    const { data: testData, error: testError } = await supabase
      .from('species_v2')
      .select('id, countries, states_provinces, primary_region, geographic_scope')
      .limit(1);

    if (!testError) {
      console.log('✅ Geographic columns already exist!', testData);
      return { success: true, message: 'Columns already exist', data: testData };
    }

    console.log('❌ Geographic columns do not exist');
    console.log('Error:', testError.message);

    // Since we can't run DDL directly through the client, we'll need to use the SQL editor
    // in the Supabase dashboard. Let's provide the SQL commands to run manually.
    
    const sqlCommands = `
-- Add geographic columns to species_v2 table
ALTER TABLE species_v2 
ADD COLUMN IF NOT EXISTS countries TEXT[],
ADD COLUMN IF NOT EXISTS states_provinces TEXT[],
ADD COLUMN IF NOT EXISTS geographic_scope TEXT DEFAULT 'regional',
ADD COLUMN IF NOT EXISTS primary_region TEXT;

-- Create indexes for geographic searches
CREATE INDEX IF NOT EXISTS idx_species_v2_countries ON species_v2 USING gin(countries);
CREATE INDEX IF NOT EXISTS idx_species_v2_states_provinces ON species_v2 USING gin(states_provinces);
CREATE INDEX IF NOT EXISTS idx_species_v2_geographic_scope ON species_v2(geographic_scope);
CREATE INDEX IF NOT EXISTS idx_species_v2_primary_region ON species_v2(primary_region);

-- Add helpful comments
COMMENT ON COLUMN species_v2.countries IS 'Array of countries where this species is found';
COMMENT ON COLUMN species_v2.states_provinces IS 'Array of states/provinces for more granular location data';
COMMENT ON COLUMN species_v2.geographic_scope IS 'Scope of species distribution: global, continental, national, regional, local';
COMMENT ON COLUMN species_v2.primary_region IS 'Primary geographic region where species is most commonly found';
`;

    console.log('📋 Please run the following SQL in the Supabase SQL Editor:');
    console.log(sqlCommands);
    
    return { 
      success: false, 
      message: 'Columns need to be added manually', 
      sql: sqlCommands,
      instructions: 'Go to Supabase Dashboard > SQL Editor and run the provided SQL commands'
    };

  } catch (error) {
    console.error('❌ Failed to check/add geographic columns:', error);
    throw error;
  }
}

export async function testGeographicColumns() {
  console.log('🔍 Testing if geographic columns exist...');

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('❌ Missing Supabase environment variables');
    return { exists: false, error: 'Missing environment variables' };
  }

  try {
    // Try to select geographic columns from species_v2
    const { data, error } = await supabase
      .from('species_v2')
      .select('id, name, countries, states_provinces, primary_region, geographic_scope')
      .limit(5);

    if (error) {
      console.error('❌ Geographic columns do not exist:', error.message);
      return { exists: false, error: error.message };
    }

    console.log('✅ Geographic columns exist! Sample data:', data);
    return { exists: true, data };

  } catch (error) {
    console.error('❌ Error testing geographic columns:', error);
    return { exists: false, error: error.message };
  }
}

// Add functions to window for easy testing
if (typeof window !== 'undefined') {
  (window as any).addGeographicColumns = addGeographicColumns;
  (window as any).testGeographicColumns = testGeographicColumns;
  
  console.log('🔧 Geographic column functions loaded!');
  console.log('- Run testGeographicColumns() to check if columns exist');
  console.log('- Run addGeographicColumns() to get SQL commands for adding columns');
}
