import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';

// Load environment variables
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupFunFactsTable() {
  console.log('🚀 Setting up fun_facts table and migration functions...');
  
  try {
    // Read the SQL file
    const sqlPath = join(__dirname, 'create-fun-facts-table.sql');
    const sql = readFileSync(sqlPath, 'utf8');
    
    // Split SQL into individual statements
    const statements = sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 Executing ${statements.length} SQL statements...`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`⏳ Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        // Use a simple query to execute the SQL
        const { error } = await supabase.rpc('sql', { query: statement });
        
        if (error) {
          console.error(`❌ Error in statement ${i + 1}:`, error);
          // Try alternative approach for table creation
          if (statement.includes('CREATE TABLE')) {
            console.log('🔄 Trying alternative table creation...');
            // We'll handle this manually through the dashboard
          }
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`);
        }
      } catch (err) {
        console.error(`❌ Exception in statement ${i + 1}:`, err);
      }
    }
    
    // Test if the table was created
    console.log('🔍 Testing table creation...');
    const { data, error } = await supabase
      .from('fun_facts')
      .select('*', { count: 'exact' })
      .limit(0);
    
    if (error) {
      console.error('❌ Table test failed:', error.message);
      console.log('📋 Please manually create the table using the SQL in create-fun-facts-table.sql');
    } else {
      console.log('✅ fun_facts table is ready!');
      
      // Now run the migration
      console.log('🔄 Running migration...');
      const { data: migrationData, error: migrationError } = await supabase.rpc('migrate_fun_facts_to_table');
      
      if (migrationError) {
        console.error('❌ Migration failed:', migrationError);
      } else {
        console.log('✅ Migration completed successfully!');
        console.log('📊 Results:', migrationData);
      }
    }
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
  }
}

// Run the setup
setupFunFactsTable().catch(console.error);
