import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

// Load environment variables manually for Node.js
const SUPABASE_URL = "https://raqxptgrugnmxdunbuty.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJhcXhwdGdydWdubXhkdW5idXR5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzM0NTAsImV4cCI6MjA2NTg0OTQ1MH0.0xVgSqjqch5a48Nxq4ZhaCZrMx0yaQ32qqi3YR1cLGQ";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function fixPhotoIds() {
  console.log('🔧 Fixing photo IDs...');

  try {
    // Get photos with null IDs in batches
    let offset = 0;
    const batchSize = 50;
    let totalFixed = 0;

    while (true) {
      const { data: photosWithoutIds, error: fetchError } = await supabase
        .from('photos_v2')
        .select('url, title, species_id, created_at, published, photographer, location, description, tags')
        .is('id', null)
        .range(offset, offset + batchSize - 1);

      if (fetchError) {
        console.error('❌ Error fetching photos without IDs:', fetchError);
        break;
      }

      if (!photosWithoutIds || photosWithoutIds.length === 0) {
        break;
      }

      console.log(`📊 Processing batch ${Math.floor(offset / batchSize) + 1}: ${photosWithoutIds.length} photos`);

      // Process each photo in the batch
      for (const photo of photosWithoutIds) {
        const newId = uuidv4();
        
        // Delete the old record and insert a new one with proper ID
        const { error: deleteError } = await supabase
          .from('photos_v2')
          .delete()
          .eq('url', photo.url)
          .is('id', null);

        if (deleteError) {
          console.error(`❌ Error deleting photo with URL ${photo.url}:`, deleteError);
          continue;
        }

        // Insert the photo with a new ID
        const { error: insertError } = await supabase
          .from('photos_v2')
          .insert({
            id: newId,
            url: photo.url,
            title: photo.title,
            species_id: photo.species_id,
            created_at: photo.created_at,
            published: photo.published,
            photographer: photo.photographer,
            location: photo.location,
            description: photo.description,
            tags: photo.tags
          });

        if (insertError) {
          console.error(`❌ Error inserting photo with new ID:`, insertError);
          // Try to restore the original record
          await supabase
            .from('photos_v2')
            .insert(photo);
        } else {
          totalFixed++;
          if (totalFixed % 10 === 0) {
            console.log(`✅ Fixed ${totalFixed} photos so far...`);
          }
        }
      }

      offset += batchSize;
    }

    console.log(`🎉 Fixed ${totalFixed} photo IDs`);
    return totalFixed;

  } catch (error) {
    console.error('❌ Error fixing photo IDs:', error);
    return 0;
  }
}

async function fixPhotoSpeciesLinks() {
  console.log('\n🔧 Fixing photo-species links...');

  try {
    // Get all species for matching
    const { data: allSpecies, error: speciesError } = await supabase
      .from('species_v2')
      .select('id, name')
      .eq('published', true);

    if (speciesError) {
      console.error('❌ Error fetching species:', speciesError);
      return 0;
    }

    // Get photos without species_id
    const { data: orphanedPhotos, error: orphanedError } = await supabase
      .from('photos_v2')
      .select('id, url, title')
      .is('species_id', null)
      .eq('published', true)
      .not('id', 'is', null)
      .limit(100); // Process in batches

    if (orphanedError) {
      console.error('❌ Error fetching orphaned photos:', orphanedError);
      return 0;
    }

    console.log(`📊 Found ${orphanedPhotos?.length || 0} photos without species_id`);

    if (!orphanedPhotos || orphanedPhotos.length === 0) {
      console.log('✅ No orphaned photos to fix');
      return 0;
    }

    let fixedCount = 0;

    // Try to match photos to species based on URL patterns
    for (const photo of orphanedPhotos) {
      if (!photo.url) continue;

      // Extract species name from URL path
      const urlParts = photo.url.split('/');
      const speciesFolderIndex = urlParts.findIndex(part => part === 'species');
      
      if (speciesFolderIndex >= 0 && speciesFolderIndex < urlParts.length - 1) {
        const folderName = urlParts[speciesFolderIndex + 1];
        
        // Try to match folder name to species
        const matchedSpecies = allSpecies?.find(species => {
          const speciesNameNormalized = species.name.toLowerCase()
            .replace(/[^a-z0-9]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
          
          return folderName.toLowerCase() === speciesNameNormalized ||
                 folderName.toLowerCase().includes(speciesNameNormalized) ||
                 speciesNameNormalized.includes(folderName.toLowerCase());
        });

        if (matchedSpecies) {
          console.log(`🔗 Linking photo ${photo.id} to species "${matchedSpecies.name}"`);
          
          const { error: updateError } = await supabase
            .from('photos_v2')
            .update({ species_id: matchedSpecies.id })
            .eq('id', photo.id);

          if (updateError) {
            console.error(`❌ Error updating photo ${photo.id}:`, updateError);
          } else {
            fixedCount++;
          }
        }
      }
    }

    console.log(`🎉 Fixed ${fixedCount} photo-species links`);
    return fixedCount;

  } catch (error) {
    console.error('❌ Error fixing photo-species links:', error);
    return 0;
  }
}

async function updateSpeciesPhotoCounts() {
  console.log('\n🔧 Updating species photo counts...');

  try {
    // Get all species
    const { data: allSpecies, error: speciesError } = await supabase
      .from('species_v2')
      .select('id, name')
      .eq('published', true);

    if (speciesError) {
      console.error('❌ Error fetching species:', speciesError);
      return;
    }

    console.log(`📊 Updating photo counts for ${allSpecies?.length || 0} species`);

    let updatedCount = 0;

    for (const species of allSpecies || []) {
      // Count photos for this species
      const { count: photoCount, error: countError } = await supabase
        .from('photos_v2')
        .select('*', { count: 'exact', head: true })
        .eq('species_id', species.id)
        .eq('published', true)
        .not('id', 'is', null);

      if (countError) {
        console.error(`❌ Error counting photos for species ${species.name}:`, countError);
        continue;
      }

      // Update the species with the correct photo count
      const { error: updateError } = await supabase
        .from('species_v2')
        .update({ photo_count: photoCount || 0 })
        .eq('id', species.id);

      if (updateError) {
        console.error(`❌ Error updating photo count for species ${species.name}:`, updateError);
      } else {
        if (photoCount && photoCount > 0) {
          console.log(`✅ Updated ${species.name}: ${photoCount} photos`);
        }
        updatedCount++;
      }
    }

    console.log(`🎉 Updated photo counts for ${updatedCount} species`);

  } catch (error) {
    console.error('❌ Error updating species photo counts:', error);
  }
}

async function verifyResults() {
  console.log('\n🔍 Verifying results...');

  try {
    // Count total photos
    const { count: totalPhotos, error: totalError } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true });

    // Count photos with valid IDs
    const { count: validIdPhotos, error: validError } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true })
      .not('id', 'is', null);

    // Count photos with species_id
    const { count: linkedPhotos, error: linkedError } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true })
      .not('species_id', 'is', null)
      .not('id', 'is', null);

    // Count species with photos
    const { data: speciesWithPhotos, error: speciesError } = await supabase
      .from('species_v2')
      .select('name, photo_count')
      .gt('photo_count', 0)
      .eq('published', true)
      .order('photo_count', { ascending: false });

    console.log(`📊 Total photos: ${totalPhotos}`);
    console.log(`📊 Photos with valid IDs: ${validIdPhotos}`);
    console.log(`📊 Photos linked to species: ${linkedPhotos}`);
    console.log(`📊 Species with photos: ${speciesWithPhotos?.length || 0}`);

    if (speciesWithPhotos && speciesWithPhotos.length > 0) {
      console.log('\n📊 Top 10 species by photo count:');
      speciesWithPhotos.slice(0, 10).forEach(species => {
        console.log(`   - ${species.name}: ${species.photo_count} photos`);
      });
    }

  } catch (error) {
    console.error('❌ Error verifying results:', error);
  }
}

// Run the complete fix
async function main() {
  console.log('🚀 Starting comprehensive photo count fix...\n');
  
  const fixedIds = await fixPhotoIds();
  
  if (fixedIds > 0) {
    const fixedLinks = await fixPhotoSpeciesLinks();
    await updateSpeciesPhotoCounts();
    await verifyResults();
  } else {
    console.log('⚠️ No photo IDs were fixed, skipping subsequent steps');
  }
  
  console.log('\n✅ Photo count fix complete!');
}

main();
