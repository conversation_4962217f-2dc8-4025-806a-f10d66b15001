import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables from .env file
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
// Use the SERVICE_KEY for this admin-level update
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase URL or SUPABASE_SERVICE_KEY. Make sure your .env file is configured.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

const fixAmericanBeaverData = async () => {
  console.log('Attempting to fix data for "American Beaver"...');

  const { data, error } = await supabase
    .from('species_v2')
    .update({
      description: 'The American Beaver (Castor canadensis) is a large, semiaquatic rodent known for its dam-building behavior. It plays a crucial ecological role by creating wetlands, which support biodiversity. Beavers are monogamous, territorial, and typically active during dusk and night. They use their strong incisors to fell trees and build elaborate lodges.',
      family: 'Castoridae',
      behavior: 'Monogamous; territorial; known for dam-building; nocturnal; uses trees to build lodges and modify aquatic environments.',
      size_description: '90–120 cm in length',
      lifespan_years: 12,
      regions: 'North America, including Canada and the United States',
      conservation_actions: 'Habitat preservation is encouraged in riparian zones.',
    })
    .eq('id', 'd9e25c19-007b-4e11-811c-8fc753412f48')
    .select(); // .select() returns the updated record

  if (error) {
    console.error('❌ Update failed:', error.message);
    return;
  }

  if (!data || data.length === 0) {
    console.warn('⚠️ No matching species found for ID "d9e25c19-007b-4e11-811c-8fc753412f48". No records were updated.');
    return;
  }

  console.log('✅ Species "American Beaver" updated successfully:');
  console.log(JSON.stringify(data[0], null, 2));
};

fixAmericanBeaverData(); 