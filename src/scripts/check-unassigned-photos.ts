import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://raqxptgrugnmxdunbuty.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJhcXhwdGdydWdubXhkdW5idXR5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5OTI5NzQsImV4cCI6MjA1MDU2ODk3NH0.YNJBaVJmLBih_eqU7dCbJ-lJW7VZvVOGAhUOLdtKBJo';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkUnassignedPhotos() {
  console.log('🔍 Checking unassigned photos...\n');

  try {
    // Get unassigned photos count
    const { count: unassignedCount } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true })
      .is('species_id', null);

    // Get published unassigned photos count
    const { count: publishedUnassignedCount } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true })
      .is('species_id', null)
      .eq('published', true);

    // Get unpublished unassigned photos count
    const { count: unpublishedUnassignedCount } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true })
      .is('species_id', null)
      .eq('published', false);

    // Get total photos count
    const { count: totalPhotos } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true });

    // Get assigned photos count
    const { count: assignedCount } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true })
      .not('species_id', 'is', null);

    console.log('📊 Photo Assignment Status:');
    console.log('===========================');
    console.log(`Total Photos: ${totalPhotos}`);
    console.log(`Assigned Photos: ${assignedCount}`);
    console.log(`Unassigned Photos: ${unassignedCount}`);
    console.log(`  - Published Unassigned: ${publishedUnassignedCount}`);
    console.log(`  - Unpublished Unassigned: ${unpublishedUnassignedCount}`);

    if (unassignedCount && unassignedCount > 0) {
      console.log('\n🎯 Assignment Recommendations:');
      console.log('==============================');
      console.log('✅ Photo assignment system already exists!');
      console.log('📍 Access via: /photo-assignment');
      console.log('🔧 Features available:');
      console.log('  - Drag & drop assignment');
      console.log('  - Bulk assignment');
      console.log('  - AI suggestions');
      console.log('  - Search and filter');
      
      // Get sample unassigned photos
      const { data: samplePhotos } = await supabase
        .from('photos_v2')
        .select('id, title, url, published')
        .is('species_id', null)
        .limit(5);

      if (samplePhotos && samplePhotos.length > 0) {
        console.log('\n📸 Sample Unassigned Photos:');
        console.log('============================');
        samplePhotos.forEach((photo, index) => {
          console.log(`${index + 1}. ID: ${photo.id} | Title: ${photo.title || 'Untitled'} | Published: ${photo.published}`);
        });
      }
    } else {
      console.log('\n🎉 All photos are assigned!');
    }

  } catch (error) {
    console.error('❌ Error checking unassigned photos:', error);
  }
}

checkUnassignedPhotos();
