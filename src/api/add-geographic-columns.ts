import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseServiceKey = import.meta.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export async function addGeographicColumns() {
  console.log('🔧 Adding geographic columns to species_v2 table...');

  try {
    // Add the geographic columns
    const { error: alterError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Add geographic columns to species_v2 table
        ALTER TABLE species_v2 
        ADD COLUMN IF NOT EXISTS countries TEXT[],
        ADD COLUMN IF NOT EXISTS states_provinces TEXT[],
        ADD COLUMN IF NOT EXISTS geographic_scope TEXT DEFAULT 'regional',
        ADD COLUMN IF NOT EXISTS primary_region TEXT;
      `
    });

    if (alterError) {
      console.error('❌ Error adding columns:', alterError);
      throw alterError;
    }

    // Create indexes
    const { error: indexError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Create indexes for geographic searches
        CREATE INDEX IF NOT EXISTS idx_species_v2_countries ON species_v2 USING gin(countries);
        CREATE INDEX IF NOT EXISTS idx_species_v2_states_provinces ON species_v2 USING gin(states_provinces);
        CREATE INDEX IF NOT EXISTS idx_species_v2_geographic_scope ON species_v2(geographic_scope);
        CREATE INDEX IF NOT EXISTS idx_species_v2_primary_region ON species_v2(primary_region);
      `
    });

    if (indexError) {
      console.error('❌ Error creating indexes:', indexError);
      throw indexError;
    }

    // Verify columns were added
    const { data: columns, error: verifyError } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT 
          column_name,
          data_type,
          is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'species_v2' 
          AND column_name IN ('countries', 'states_provinces', 'geographic_scope', 'primary_region')
        ORDER BY column_name;
      `
    });

    if (verifyError) {
      console.error('❌ Error verifying columns:', verifyError);
      throw verifyError;
    }

    console.log('✅ Geographic columns added successfully:', columns);
    return { success: true, columns };

  } catch (error) {
    console.error('❌ Failed to add geographic columns:', error);
    throw error;
  }
}

// Alternative approach using direct SQL execution
export async function addGeographicColumnsDirectSQL() {
  console.log('🔧 Adding geographic columns using direct SQL...');

  try {
    // Check if columns already exist
    const { data: existingColumns } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'species_v2')
      .in('column_name', ['countries', 'states_provinces', 'geographic_scope', 'primary_region']);

    const existingColumnNames = existingColumns?.map(col => col.column_name) || [];
    console.log('📋 Existing geographic columns:', existingColumnNames);

    // If all columns exist, we're done
    if (existingColumnNames.length === 4) {
      console.log('✅ All geographic columns already exist');
      return { success: true, message: 'Columns already exist' };
    }

    // Try to add columns one by one using raw SQL
    const columnsToAdd = [
      { name: 'countries', sql: 'ALTER TABLE species_v2 ADD COLUMN IF NOT EXISTS countries TEXT[]' },
      { name: 'states_provinces', sql: 'ALTER TABLE species_v2 ADD COLUMN IF NOT EXISTS states_provinces TEXT[]' },
      { name: 'geographic_scope', sql: 'ALTER TABLE species_v2 ADD COLUMN IF NOT EXISTS geographic_scope TEXT DEFAULT \'regional\'' },
      { name: 'primary_region', sql: 'ALTER TABLE species_v2 ADD COLUMN IF NOT EXISTS primary_region TEXT' }
    ];

    for (const column of columnsToAdd) {
      if (!existingColumnNames.includes(column.name)) {
        console.log(`🔧 Adding column: ${column.name}`);
        
        // Use a simple approach - try to update a record to see if column exists
        const { error } = await supabase
          .from('species_v2')
          .update({ [column.name]: null })
          .eq('id', '00000000-0000-0000-0000-000000000000'); // Non-existent ID
        
        if (error && error.message.includes('column')) {
          console.log(`❌ Column ${column.name} does not exist, needs manual addition`);
        } else {
          console.log(`✅ Column ${column.name} exists or was added`);
        }
      }
    }

    return { success: true, message: 'Geographic columns setup completed' };

  } catch (error) {
    console.error('❌ Failed to add geographic columns:', error);
    throw error;
  }
}
