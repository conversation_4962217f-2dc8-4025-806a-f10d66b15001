// Gemini AI integration for species data improvement
import { GoogleGenerativeAI } from '@google/generative-ai';
import { supabase } from '@/lib/supabase';

interface SpeciesImprovementRequest {
  speciesId: string;
  fields: string[];
  currentData?: {
    name?: string;
    common_name?: string;
    scientific_name?: string;
    description?: string;
    habitat?: string;
    diet?: string;
    conservation_status?: string;
    family?: string;
    category?: string;
    size?: string;
    weight?: string;
    lifespan?: string;
    behavior?: string;
    reproduction?: string;
    fun_facts?: string[];
  };
}

interface SpeciesImprovementResponse {
  success: boolean;
  improvements: Record<string, any>;
  error?: string;
}

export const improveSpeciesWithAI = async (
  speciesId: string,
  fields: string[]
): Promise<SpeciesImprovementResponse> => {
  console.log('🤖 Starting AI species improvement for:', speciesId, 'fields:', fields);

  const apiKey = import.meta.env.VITE_GEMINI_API_KEY;

  if (!apiKey) {
    throw new Error('Gemini API key not configured. Please add VITE_GEMINI_API_KEY to your .env file.');
  }

  try {
    // First, fetch current species data
    const { data: speciesData, error: fetchError } = await supabase
      .from('species_v2')
      .select('*')
      .eq('id', speciesId)
      .single();

    if (fetchError || !speciesData) {
      throw new Error(`Failed to fetch species data: ${fetchError?.message}`);
    }

    console.log('📋 Current species data:', speciesData);

    // Generate improvements using Gemini
    const improvements = await generateSpeciesImprovements({
      speciesId,
      fields,
      currentData: speciesData
    });

    // Separate fun_facts from other fields since they go to a different table
    const { fun_facts, ...speciesUpdates } = improvements;

    // Update the species fields (excluding fun_facts)
    if (Object.keys(speciesUpdates).length > 0) {
      const { error: updateError } = await supabase
        .from('species_v2')
        .update(speciesUpdates)
        .eq('id', speciesId);

      if (updateError) {
        throw new Error(`Failed to update species: ${updateError.message}`);
      }
    }

    // Handle fun_facts separately - insert into fun_facts table
    if (fun_facts && Array.isArray(fun_facts) && fun_facts.length > 0) {
      // First, delete existing fun facts for this species
      await supabase
        .from('fun_facts')
        .delete()
        .eq('species_id', speciesId);

      // Insert new fun facts
      const funFactsToInsert = fun_facts.map(fact => ({
        species_id: speciesId,
        fact: fact
      }));

      const { error: funFactsError } = await supabase
        .from('fun_facts')
        .insert(funFactsToInsert);

      if (funFactsError) {
        console.warn('Failed to insert fun facts:', funFactsError.message);
        // Don't throw error for fun facts failure - continue with other updates
      }
    }

    console.log('✅ Species updated successfully with AI improvements');

    return {
      success: true,
      improvements
    };

  } catch (error) {
    console.error('❌ Error improving species with AI:', error);
    return {
      success: false,
      improvements: {},
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

async function generateSpeciesImprovements(request: SpeciesImprovementRequest): Promise<Record<string, any>> {
  const apiKey = import.meta.env.VITE_GEMINI_API_KEY!;
  const genAI = new GoogleGenerativeAI(apiKey);
  const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

  const { currentData, fields } = request;

  // Map frontend field names to database column names
  const fieldMapping: Record<string, string> = {
    'size': 'size_description',
    'weight': 'weight_g',
    'lifespan': 'lifespan_years', // INTEGER - needs special handling
    'reproduction': 'breeding_season',
    'fun_facts': 'fun_facts', // This will be handled separately
    // Handle AI response variations (capitalized)
    'Size': 'size_description',
    'Weight': 'weight_g',
    'Lifespan': 'lifespan_years',
    'Reproduction': 'breeding_season',
    'Description': 'description',
    'Habitat': 'habitat',
    'Diet': 'diet',
    'Conservation Status': 'conservation_status',
    'Family': 'family',
    'Behavior': 'behavior',
    'Fun Facts': 'fun_facts',
    'Countries': 'countries',
    'States/Provinces': 'states_provinces',
    'Primary Region': 'primary_region',
    'Geographic Scope': 'geographic_scope',
    // Lowercase versions for consistency
    'countries': 'countries',
    'states_provinces': 'states_provinces',
    'primary_region': 'primary_region',
    'geographic_scope': 'geographic_scope'
  };

  const prompt = `You are a wildlife expert tasked with improving species data quality.

CURRENT SPECIES DATA:
- Name: ${currentData?.name || 'Unknown'}
- Common Name: ${currentData?.common_name || 'Not provided'}
- Scientific Name: ${currentData?.scientific_name || 'Not provided'}
- Category: ${currentData?.category || 'Not provided'}
- Current Description: ${currentData?.description || 'Not provided'}
- Current Habitat: ${currentData?.habitat || 'Not provided'}
- Current Diet: ${currentData?.diet || 'Not provided'}
- Current Conservation Status: ${currentData?.conservation_status || 'Not provided'}
- Current Family: ${currentData?.family || 'Not provided'}
- Current Size: ${currentData?.size_description || 'Not provided'}
- Current Weight: ${currentData?.weight_g || 'Not provided'}
- Current Lifespan: ${currentData?.lifespan_years || 'Not provided'}
- Current Behavior: ${currentData?.behavior || 'Not provided'}
- Current Breeding: ${currentData?.breeding_season || 'Not provided'}
- Current Countries: ${currentData?.countries ? currentData.countries.join(', ') : 'Not provided'}
- Current States/Provinces: ${currentData?.states_provinces ? currentData.states_provinces.join(', ') : 'Not provided'}
- Current Primary Region: ${currentData?.primary_region || 'Not provided'}
- Current Geographic Scope: ${currentData?.geographic_scope || 'Not provided'}

FIELDS TO IMPROVE: ${fields.join(', ')}

Please provide improved, accurate, and detailed content for the specified fields. Follow these guidelines:

1. **Description**: 2-3 sentences covering key identifying features, behavior, and habitat
2. **Habitat**: Specific ecosystems, geographic regions, and environmental preferences
3. **Diet**: Detailed feeding habits, prey items, and foraging behavior
4. **Conservation Status**: Use IUCN categories (Least Concern, Near Threatened, Vulnerable, Endangered, Critically Endangered, Extinct in Wild, Extinct)
5. **Family**: Correct taxonomic family name
6. **Size**: Specific measurements (length, height, wingspan as appropriate) - return as descriptive text
7. **Weight**: Typical weight ranges for adults - return as descriptive text
8. **Lifespan**: Average lifespan in years - return as INTEGER number only (e.g., 7, 12, 15)
9. **Behavior**: Social structure, activity patterns, notable behaviors
10. **Reproduction**: Breeding season, gestation/incubation, offspring details
11. **Fun Facts**: Array of 3-5 interesting, engaging facts
12. **Countries**: Array of specific countries where this species is found (be granular - instead of just "Australia", include specific regions like "Eastern Australia", "Southeastern Australia")
13. **States/Provinces**: Array of states, provinces, or regions within countries where this species occurs
14. **Primary Region**: Main continental/regional area (e.g., "Australia", "North America", "Europe", "Southeast Asia", "Southern Africa")
15. **Geographic Scope**: Choose from 'global', 'continental', 'national', 'regional', 'local' based on species distribution

Ensure all information is scientifically accurate and specific to this exact species. Use metric measurements and provide ranges where appropriate.

IMPORTANT: Return field names exactly as they appear in the FIELDS TO IMPROVE list (lowercase). Do not capitalize field names.

SPECIAL FORMATTING REQUIREMENTS:
- **lifespan**: Return ONLY a number (integer) representing average years in wild (e.g., 7, 12, 15)
- **fun_facts**: Return as an array of strings
- **countries**: Return as an array of strings (e.g., ["Australia", "New Zealand"])
- **states_provinces**: Return as an array of strings (e.g., ["New South Wales", "Victoria"])
- **primary_region**: Return as a string (e.g., "Australia and New Zealand")
- **geographic_scope**: Return as a string - choose from: "global", "continental", "national", "regional", "local"
- **All other fields**: Return as descriptive text strings

Format your response as valid JSON with only the fields that need improvement:

{
  "field_name": "improved_content_here"
}

Examples:
- "lifespan": 8 (number only, not "8 years" or descriptive text)
- "weight": "Adults typically weigh 20-30 grams" (descriptive text)
- "size": "Measures 15-18 cm in length" (descriptive text)
- "description": "Detailed species description..." (descriptive text)
- "habitat": "Specific habitat information..." (descriptive text)
- "countries": ["Australia", "New Zealand"] (array of strings)
- "states_provinces": ["New South Wales", "Victoria", "Queensland"] (array of strings)
- "primary_region": "Australia and New Zealand" (string)
- "geographic_scope": "regional" (string: global/continental/national/regional/local)

CRITICAL: Use lowercase field names exactly as specified in FIELDS TO IMPROVE. Do not use "Description", "Habitat", "Diet" etc. Use "description", "habitat", "diet" etc.

Only include the fields specified in the FIELDS TO IMPROVE list.`;

  try {
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    console.log('🤖 Gemini raw response:', text);

    // Try to extract JSON from the response
    let jsonContent;
    try {
      // Look for JSON block in the response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        jsonContent = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('No JSON found in response');
      }
    } catch (parseError) {
      console.error('❌ Failed to parse JSON response:', parseError);
      throw new Error('Invalid JSON response from AI');
    }

    console.log('✅ Parsed AI improvements:', jsonContent);

    // Map frontend field names to database column names and handle data type conversions
    const mappedContent: Record<string, any> = {};
    for (const [key, value] of Object.entries(jsonContent)) {
      const dbColumnName = fieldMapping[key] || key.toLowerCase();

      // Log field mapping for debugging
      console.log(`🔄 Mapping field: "${key}" -> "${dbColumnName}"`);

      // Special handling for lifespan_years - ensure it's an integer
      if (dbColumnName === 'lifespan_years') {
        // Extract number from text if needed, or use the number directly
        let lifespanValue = value;
        if (typeof value === 'string') {
          // Try to extract the first number from the string
          const match = value.match(/(\d+)/);
          lifespanValue = match ? parseInt(match[1], 10) : null;
        } else if (typeof value === 'number') {
          lifespanValue = Math.round(value);
        }

        if (lifespanValue && lifespanValue > 0 && lifespanValue < 200) {
          mappedContent[dbColumnName] = lifespanValue;
        } else {
          console.warn(`Invalid lifespan value: ${value}, skipping field`);
          continue;
        }
      } else {
        // Skip unknown fields that aren't in our mapping
        if (!fieldMapping[key] && !['description', 'habitat', 'diet', 'conservation_status', 'family', 'behavior', 'fun_facts', 'countries', 'states_provinces', 'primary_region', 'geographic_scope'].includes(key.toLowerCase())) {
          console.warn(`Unknown field "${key}", skipping`);
          continue;
        }
        mappedContent[dbColumnName] = value;
      }
    }

    console.log('🔄 Mapped to database columns:', mappedContent);
    return mappedContent;

  } catch (error) {
    console.error('❌ Error generating improvements with Gemini:', error);

    // Check if it's a rate limit error
    if (error instanceof Error && error.message.includes('429')) {
      throw new Error('🚫 AI API rate limit exceeded. The free Gemini tier allows 50 requests per day. Please wait 24 hours or upgrade your API plan.');
    }

    throw new Error(`AI generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
