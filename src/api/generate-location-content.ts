// API endpoint for generating location content using Gemini AI
import { GoogleGenerativeAI } from '@google/generative-ai';

interface LocationContentRequest {
  locationName: string;
  latitude: number;
  longitude: number;
  stateProvince?: string;
  country?: string;
  habitatTypes?: string[];
  speciesCount: number;
  categories: string[];
}

interface LocationContentResponse {
  description: string;
  visitorTips: string;
  bestTimes: string;
}

export async function generateLocationContent(request: LocationContentRequest): Promise<LocationContentResponse> {
  const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
  
  if (!apiKey) {
    throw new Error('Gemini API key not configured');
  }

  const genAI = new GoogleGenerativeAI(apiKey);
  const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

  const prompt = `
You are a wildlife and nature expert writing content for a wildlife hotspot location page. 

Location Details:
- Name: ${request.locationName}
- Location: ${request.stateProvince ? `${request.stateProvince}, ` : ''}${request.country}
- Coordinates: ${request.latitude}, ${request.longitude}
- Habitat Types: ${request.habitatTypes?.join(', ') || 'Not specified'}
- Species Count: ${request.speciesCount} species recorded
- Wildlife Categories: ${request.categories.join(', ') || 'Various'}

Please generate comprehensive content for this wildlife location with the following sections:

1. DESCRIPTION (2-3 paragraphs):
Write an engaging description of this location that covers:
- What makes this location special for wildlife viewing
- The types of habitats and ecosystems present
- Key wildlife viewing opportunities
- Any unique features or characteristics
- Why wildlife enthusiasts should visit

2. VISITOR TIPS (practical advice):
Provide helpful tips for visitors including:
- Best times of day for wildlife viewing
- What to bring (binoculars, camera, etc.)
- Recommended clothing and gear
- Behavior guidelines around wildlife
- Safety considerations
- Photography tips specific to this location

3. BEST TIMES TO VISIT (seasonal and timing guidance):
Explain the optimal times to visit including:
- Best seasons for different types of wildlife
- Monthly breakdown of what to expect
- Weather considerations
- Migration patterns if relevant
- Breeding seasons and special viewing opportunities
- Crowd levels and when to visit for solitude

Make the content informative, engaging, and practical. Write in a friendly but professional tone. Focus on the wildlife and nature aspects. Keep each section concise but comprehensive.

Format your response as JSON with these exact keys:
{
  "description": "...",
  "visitorTips": "...",
  "bestTimes": "..."
}
`;

  try {
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    // Try to parse JSON response
    try {
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          description: parsed.description || '',
          visitorTips: parsed.visitorTips || '',
          bestTimes: parsed.bestTimes || ''
        };
      }
    } catch (parseError) {
      console.warn('Failed to parse JSON response, using fallback parsing');
    }
    
    // Fallback parsing if JSON parsing fails
    const sections = text.split(/\d+\.\s*(?:DESCRIPTION|VISITOR TIPS|BEST TIMES)/i);
    
    return {
      description: sections[1]?.trim() || 'AI-generated description not available.',
      visitorTips: sections[2]?.trim() || 'AI-generated visitor tips not available.',
      bestTimes: sections[3]?.trim() || 'AI-generated timing information not available.'
    };
    
  } catch (error) {
    console.error('Error generating content with Gemini:', error);
    throw new Error('Failed to generate location content');
  }
}

// Express.js API handler (if using Express)
export async function handleGenerateLocationContent(req: any, res: any) {
  try {
    const content = await generateLocationContent(req.body);
    res.json(content);
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({ 
      error: 'Failed to generate content',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

// Vite/React API handler (if using Vite's API routes)
export default async function handler(req: Request): Promise<Response> {
  if (req.method !== 'POST') {
    return new Response('Method not allowed', { status: 405 });
  }

  try {
    const body = await req.json();
    const content = await generateLocationContent(body);
    
    return new Response(JSON.stringify(content), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.error('API Error:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate content',
        message: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}
