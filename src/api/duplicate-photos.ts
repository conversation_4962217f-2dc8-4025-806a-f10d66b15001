import { supabase } from '@/integrations/supabase/client';

export interface DuplicatePhoto {
  id: string;
  url: string;
  title?: string;
  description?: string;
  photographer?: string;
  location?: string;
  species_id?: string;
  published: boolean;
  created_at: string;
  hash?: string;
  file_size?: number;
  species?: {
    id: string;
    name: string;
    common_name?: string;
  };
}

export interface DuplicateGroup {
  identifier: string; // URL or hash
  type: 'url' | 'hash' | 'filename';
  photos: DuplicatePhoto[];
  count: number;
  totalSize?: number;
}

export interface DuplicateStats {
  totalDuplicates: number;
  duplicateGroups: number;
  potentialSavings: number;
  storageWasted?: number;
}

/**
 * Find duplicate photos using multiple detection methods
 */
export async function findDuplicatePhotos(): Promise<{
  groups: DuplicateGroup[];
  stats: DuplicateStats;
}> {
  try {
    // Get all photos with related data
    const { data: photos, error } = await supabase
      .from('photos_v2')
      .select(`
        id, url, title, description, photographer, location, 
        species_id, published, created_at, hash, file_size,
        species_v2:species_id (id, name, common_name)
      `)
      .not('url', 'is', null)
      .order('created_at', { ascending: true });

    if (error) throw error;

    const duplicateGroups: DuplicateGroup[] = [];
    
    // Method 1: Group by exact URL match
    const urlGroups = groupByField(photos || [], 'url', 'url');
    duplicateGroups.push(...urlGroups);

    // Method 2: Group by hash (if available)
    const photosWithHash = (photos || []).filter(p => p.hash);
    if (photosWithHash.length > 0) {
      const hashGroups = groupByField(photosWithHash, 'hash', 'hash');
      duplicateGroups.push(...hashGroups);
    }

    // Method 3: Group by filename extracted from URL
    const filenameGroups = groupByFilename(photos || []);
    duplicateGroups.push(...filenameGroups);

    // Remove duplicate groups (same photos in multiple groups)
    const uniqueGroups = deduplicateGroups(duplicateGroups);

    // Calculate statistics
    const stats = calculateStats(uniqueGroups);

    return {
      groups: uniqueGroups,
      stats
    };

  } catch (error) {
    console.error('Error finding duplicate photos:', error);
    throw new Error('Failed to find duplicate photos');
  }
}

/**
 * Group photos by a specific field
 */
function groupByField(
  photos: any[], 
  field: string, 
  type: 'url' | 'hash'
): DuplicateGroup[] {
  const groups: Record<string, DuplicatePhoto[]> = {};
  
  photos.forEach(photo => {
    const value = photo[field];
    if (!value) return;
    
    if (!groups[value]) {
      groups[value] = [];
    }
    
    groups[value].push({
      ...photo,
      species: photo.species_v2
    });
  });

  // Only return groups with more than one photo
  return Object.entries(groups)
    .filter(([_, photos]) => photos.length > 1)
    .map(([identifier, photos]) => ({
      identifier,
      type,
      photos: photos.sort((a, b) => 
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      ),
      count: photos.length,
      totalSize: photos.reduce((sum, p) => sum + (p.file_size || 0), 0)
    }));
}

/**
 * Group photos by filename extracted from URL
 */
function groupByFilename(photos: any[]): DuplicateGroup[] {
  const groups: Record<string, DuplicatePhoto[]> = {};
  
  photos.forEach(photo => {
    if (!photo.url) return;
    
    try {
      // Extract filename from URL
      const url = new URL(photo.url);
      const pathname = url.pathname;
      const filename = pathname.split('/').pop();
      
      if (!filename || filename.length < 5) return; // Skip very short filenames
      
      if (!groups[filename]) {
        groups[filename] = [];
      }
      
      groups[filename].push({
        ...photo,
        species: photo.species_v2
      });
    } catch (error) {
      // Skip invalid URLs
      return;
    }
  });

  // Only return groups with more than one photo
  return Object.entries(groups)
    .filter(([_, photos]) => photos.length > 1)
    .map(([filename, photos]) => ({
      identifier: filename,
      type: 'filename' as const,
      photos: photos.sort((a, b) => 
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      ),
      count: photos.length,
      totalSize: photos.reduce((sum, p) => sum + (p.file_size || 0), 0)
    }));
}

/**
 * Remove duplicate groups that contain the same photos
 */
function deduplicateGroups(groups: DuplicateGroup[]): DuplicateGroup[] {
  const seen = new Set<string>();
  const unique: DuplicateGroup[] = [];

  groups.forEach(group => {
    // Create a signature for this group based on photo IDs
    const photoIds = group.photos.map(p => p.id).sort().join(',');
    
    if (!seen.has(photoIds)) {
      seen.add(photoIds);
      unique.push(group);
    }
  });

  return unique.sort((a, b) => b.count - a.count); // Sort by count descending
}

/**
 * Calculate statistics about duplicates
 */
function calculateStats(groups: DuplicateGroup[]): DuplicateStats {
  const totalDuplicates = groups.reduce((sum, group) => sum + (group.count - 1), 0);
  const storageWasted = groups.reduce((sum, group) => {
    const avgSize = (group.totalSize || 0) / group.count;
    return sum + (avgSize * (group.count - 1));
  }, 0);

  return {
    totalDuplicates,
    duplicateGroups: groups.length,
    potentialSavings: totalDuplicates,
    storageWasted: Math.round(storageWasted)
  };
}

/**
 * Delete multiple photos by their IDs
 */
export async function deleteDuplicatePhotos(photoIds: string[]): Promise<{
  success: boolean;
  deletedCount: number;
  error?: string;
}> {
  try {
    if (photoIds.length === 0) {
      return { success: false, deletedCount: 0, error: 'No photos selected' };
    }

    // Delete from database
    const { error, count } = await supabase
      .from('photos_v2')
      .delete()
      .in('id', photoIds);

    if (error) throw error;

    return {
      success: true,
      deletedCount: count || photoIds.length
    };

  } catch (error) {
    console.error('Error deleting duplicate photos:', error);
    return {
      success: false,
      deletedCount: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Generate hash for a photo URL (simple implementation)
 */
export function generatePhotoHash(url: string): string {
  // Simple hash function for URLs
  let hash = 0;
  for (let i = 0; i < url.length; i++) {
    const char = url.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}

/**
 * Update photo hashes for better duplicate detection
 */
export async function updatePhotoHashes(): Promise<{
  success: boolean;
  updatedCount: number;
  error?: string;
}> {
  try {
    // Get photos without hashes
    const { data: photos, error: fetchError } = await supabase
      .from('photos_v2')
      .select('id, url')
      .is('hash', null)
      .not('url', 'is', null);

    if (fetchError) throw fetchError;

    if (!photos || photos.length === 0) {
      return { success: true, updatedCount: 0 };
    }

    // Update hashes in batches
    const batchSize = 100;
    let updatedCount = 0;

    for (let i = 0; i < photos.length; i += batchSize) {
      const batch = photos.slice(i, i + batchSize);
      
      const updates = batch.map(photo => ({
        id: photo.id,
        hash: generatePhotoHash(photo.url)
      }));

      const { error: updateError } = await supabase
        .from('photos_v2')
        .upsert(updates);

      if (updateError) throw updateError;
      
      updatedCount += batch.length;
    }

    return {
      success: true,
      updatedCount
    };

  } catch (error) {
    console.error('Error updating photo hashes:', error);
    return {
      success: false,
      updatedCount: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Smart duplicate resolution - keeps the best photo from each group
 */
export async function smartResolveDuplicates(
  groups: DuplicateGroup[],
  strategy: 'keep_first' | 'keep_published' | 'keep_best_metadata' = 'keep_published'
): Promise<{
  success: boolean;
  deletedCount: number;
  keptCount: number;
  error?: string;
}> {
  try {
    const photosToDelete: string[] = [];
    let keptCount = 0;

    groups.forEach(group => {
      let photoToKeep: DuplicatePhoto;

      switch (strategy) {
        case 'keep_published':
          // Keep the first published photo, or first photo if none published
          photoToKeep = group.photos.find(p => p.published) || group.photos[0];
          break;
        
        case 'keep_best_metadata':
          // Keep photo with most complete metadata
          photoToKeep = group.photos.reduce((best, current) => {
            const bestScore = getMetadataScore(best);
            const currentScore = getMetadataScore(current);
            return currentScore > bestScore ? current : best;
          });
          break;
        
        case 'keep_first':
        default:
          // Keep the oldest photo (first uploaded)
          photoToKeep = group.photos[0];
          break;
      }

      // Mark others for deletion
      group.photos.forEach(photo => {
        if (photo.id !== photoToKeep.id) {
          photosToDelete.push(photo.id);
        } else {
          keptCount++;
        }
      });
    });

    // Delete the selected photos
    const deleteResult = await deleteDuplicatePhotos(photosToDelete);
    
    return {
      success: deleteResult.success,
      deletedCount: deleteResult.deletedCount,
      keptCount,
      error: deleteResult.error
    };

  } catch (error) {
    console.error('Error in smart duplicate resolution:', error);
    return {
      success: false,
      deletedCount: 0,
      keptCount: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Calculate metadata completeness score for a photo
 */
function getMetadataScore(photo: DuplicatePhoto): number {
  let score = 0;
  
  if (photo.title) score += 2;
  if (photo.description) score += 2;
  if (photo.photographer) score += 1;
  if (photo.location) score += 1;
  if (photo.species_id) score += 3;
  if (photo.published) score += 1;
  
  return score;
}
