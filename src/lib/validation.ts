import { createValidationError } from './errorHandling';

// Validation result type
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// Common validation patterns
export const ValidationPatterns = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  PHONE: /^\+?[\d\s\-\(\)]+$/,
  SLUG: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
} as const;

// Basic validators
export const validators = {
  required: (value: unknown, fieldName: string): void => {
    if (value === null || value === undefined || value === '') {
      throw createValidationError(fieldName, `${fieldName} is required`);
    }
  },

  string: (value: unknown, fieldName: string): void => {
    if (typeof value !== 'string') {
      throw createValidationError(fieldName, `${fieldName} must be a string`);
    }
  },

  number: (value: unknown, fieldName: string): void => {
    if (typeof value !== 'number' || isNaN(value)) {
      throw createValidationError(fieldName, `${fieldName} must be a valid number`);
    }
  },

  boolean: (value: unknown, fieldName: string): void => {
    if (typeof value !== 'boolean') {
      throw createValidationError(fieldName, `${fieldName} must be a boolean`);
    }
  },

  email: (value: string, fieldName: string = 'Email'): void => {
    validators.string(value, fieldName);
    if (!ValidationPatterns.EMAIL.test(value)) {
      throw createValidationError(fieldName, `${fieldName} must be a valid email address`);
    }
  },

  uuid: (value: string, fieldName: string = 'ID'): void => {
    validators.string(value, fieldName);
    if (!ValidationPatterns.UUID.test(value)) {
      throw createValidationError(fieldName, `${fieldName} must be a valid UUID`);
    }
  },

  url: (value: string, fieldName: string = 'URL'): void => {
    validators.string(value, fieldName);
    if (!ValidationPatterns.URL.test(value)) {
      throw createValidationError(fieldName, `${fieldName} must be a valid URL`);
    }
  },

  minLength: (value: string, min: number, fieldName: string): void => {
    validators.string(value, fieldName);
    if (value.length < min) {
      throw createValidationError(fieldName, `${fieldName} must be at least ${min} characters long`);
    }
  },

  maxLength: (value: string, max: number, fieldName: string): void => {
    validators.string(value, fieldName);
    if (value.length > max) {
      throw createValidationError(fieldName, `${fieldName} must be no more than ${max} characters long`);
    }
  },

  range: (value: number, min: number, max: number, fieldName: string): void => {
    validators.number(value, fieldName);
    if (value < min || value > max) {
      throw createValidationError(fieldName, `${fieldName} must be between ${min} and ${max}`);
    }
  },

  oneOf: <T>(value: T, options: T[], fieldName: string): void => {
    if (!options.includes(value)) {
      throw createValidationError(fieldName, `${fieldName} must be one of: ${options.join(', ')}`);
    }
  },

  array: (value: unknown, fieldName: string): void => {
    if (!Array.isArray(value)) {
      throw createValidationError(fieldName, `${fieldName} must be an array`);
    }
  },

  arrayMinLength: (value: unknown[], min: number, fieldName: string): void => {
    validators.array(value, fieldName);
    if (value.length < min) {
      throw createValidationError(fieldName, `${fieldName} must contain at least ${min} items`);
    }
  },

  arrayMaxLength: (value: unknown[], max: number, fieldName: string): void => {
    validators.array(value, fieldName);
    if (value.length > max) {
      throw createValidationError(fieldName, `${fieldName} must contain no more than ${max} items`);
    }
  },
};

// Validation schema type
export interface ValidationSchema {
  [key: string]: Array<(value: unknown, fieldName: string) => void>;
}

// Schema validator
export const validateSchema = (data: Record<string, unknown>, schema: ValidationSchema): ValidationResult => {
  const errors: string[] = [];

  for (const [fieldName, fieldValidators] of Object.entries(schema)) {
    const value = data[fieldName];

    for (const validator of fieldValidators) {
      try {
        validator(value, fieldName);
      } catch (error) {
        if (error instanceof Error) {
          errors.push(error.message);
        }
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Species validation schema
export const speciesValidationSchema: ValidationSchema = {
  name: [validators.required, (value) => validators.minLength(value as string, 2, 'Name')],
  scientific_name: [(value) => {
    if (value) validators.minLength(value as string, 3, 'Scientific name');
  }],
  category: [validators.required, validators.string],
  conservation_status: [(value) => {
    if (value) {
      validators.oneOf(value, [
        'Least Concern',
        'Near Threatened',
        'Vulnerable',
        'Endangered',
        'Critically Endangered',
        'Extinct in the Wild',
        'Extinct'
      ], 'Conservation status');
    }
  }],
  description: [(value) => {
    if (value) validators.maxLength(value as string, 5000, 'Description');
  }],
};

// Photo validation schema
export const photoValidationSchema: ValidationSchema = {
  url: [validators.required, validators.url],
  title: [(value) => {
    if (value) validators.maxLength(value as string, 200, 'Title');
  }],
  description: [(value) => {
    if (value) validators.maxLength(value as string, 1000, 'Description');
  }],
  photographer: [(value) => {
    if (value) validators.maxLength(value as string, 100, 'Photographer');
  }],
  location: [(value) => {
    if (value) validators.maxLength(value as string, 200, 'Location');
  }],
  species_id: [(value) => {
    if (value) validators.uuid(value as string, 'Species ID');
  }],
};

// User validation schema
export const userValidationSchema: ValidationSchema = {
  email: [validators.required, validators.email],
  password: [
    validators.required,
    (value) => validators.minLength(value as string, 8, 'Password'),
    (value) => {
      const password = value as string;
      if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
        throw createValidationError('Password', 'Password must contain at least one lowercase letter, one uppercase letter, and one number');
      }
    }
  ],
};

// Utility functions
export const isValidUUID = (value: string): boolean => {
  return ValidationPatterns.UUID.test(value);
};

export const isValidEmail = (value: string): boolean => {
  return ValidationPatterns.EMAIL.test(value);
};

export const isValidURL = (value: string): boolean => {
  return ValidationPatterns.URL.test(value);
};

export const sanitizeString = (value: string): string => {
  return value.trim().replace(/\s+/g, ' ');
};

export const sanitizeHTML = (value: string): string => {
  return value
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};

// File validation
export const validateFile = (file: File, options: {
  maxSize?: number; // in bytes
  allowedTypes?: string[];
  allowedExtensions?: string[];
}): ValidationResult => {
  const errors: string[] = [];

  if (options.maxSize && file.size > options.maxSize) {
    errors.push(`File size must be less than ${Math.round(options.maxSize / 1024 / 1024)}MB`);
  }

  if (options.allowedTypes && !options.allowedTypes.includes(file.type)) {
    errors.push(`File type must be one of: ${options.allowedTypes.join(', ')}`);
  }

  if (options.allowedExtensions) {
    const extension = file.name.split('.').pop()?.toLowerCase();
    if (!extension || !options.allowedExtensions.includes(extension)) {
      errors.push(`File extension must be one of: ${options.allowedExtensions.join(', ')}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Image file validation
export const validateImageFile = (file: File): ValidationResult => {
  return validateFile(file, {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
    allowedExtensions: ['jpg', 'jpeg', 'png', 'webp', 'gif']
  });
};
