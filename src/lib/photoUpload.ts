import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface PhotoUploadData {
  title: string;
  description?: string;
  photographer?: string;
  location?: string;
  species_id?: string;
  camera_settings?: string;
  weather_conditions?: string;
  time_of_day?: string;
  tags?: string[];
}

export interface UploadProgress {
  progress: number;
  status: 'uploading' | 'processing' | 'complete' | 'error';
  message?: string;
}

export class PhotoUploadManager {
  private static readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  private static readonly ALLOWED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  private static readonly BUCKET_NAME = 'species';

  static validateFile(file: File): { valid: boolean; error?: string } {
    if (!this.ALLOWED_TYPES.includes(file.type)) {
      return {
        valid: false,
        error: 'Invalid file type. Please upload JPG, PNG, or WebP images only.'
      };
    }

    if (file.size > this.MAX_FILE_SIZE) {
      return {
        valid: false,
        error: 'File size too large. Maximum size is 5MB.'
      };
    }

    return { valid: true };
  }

  static generateUniqueFilename(originalName: string): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg';
    return `photos/${timestamp}-${randomString}.${extension}`;
  }

  static async getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const url = URL.createObjectURL(file);
      
      img.onload = () => {
        URL.revokeObjectURL(url);
        resolve({ width: img.naturalWidth, height: img.naturalHeight });
      };
      
      img.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Failed to load image'));
      };
      
      img.src = url;
    });
  }

  static async uploadPhoto(
    file: File,
    metadata: PhotoUploadData,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<{ success: boolean; photoId?: number; error?: string }> {
    try {
      // Validate file
      const validation = this.validateFile(file);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      onProgress?.({ progress: 10, status: 'uploading', message: 'Validating file...' });

      // Get image dimensions
      let dimensions = { width: 0, height: 0 };
      try {
        dimensions = await this.getImageDimensions(file);
      } catch (error) {
        console.warn('Could not get image dimensions:', error);
      }

      onProgress?.({ progress: 20, status: 'uploading', message: 'Preparing upload...' });

      // Generate unique filename
      const filename = this.generateUniqueFilename(file.name);

      onProgress?.({ progress: 30, status: 'uploading', message: 'Uploading to storage...' });

      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(filename, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        console.error('Storage upload error:', uploadError);
        return { success: false, error: `Upload failed: ${uploadError.message}` };
      }

      onProgress?.({ progress: 70, status: 'processing', message: 'Saving metadata...' });

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(filename);

      // Save to database
      const photoRecord = {
        url: urlData.publicUrl,
        title: metadata.title,
        description: metadata.description,
        photographer: metadata.photographer,
        location: metadata.location,
        species_id: metadata.species_id,
        camera_settings: metadata.camera_settings,
        weather_conditions: metadata.weather_conditions,
        time_of_day: metadata.time_of_day,
        tags: metadata.tags || [],
        published: true,
        notes: `Uploaded: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB, ${dimensions.width}x${dimensions.height})`
      };

      const { data: dbData, error: dbError } = await supabase
        .from('photos')
        .insert(photoRecord)
        .select()
        .single();

      if (dbError) {
        console.error('Database insert error:', dbError);
        // Try to clean up uploaded file
        await supabase.storage.from(this.BUCKET_NAME).remove([filename]);
        return { success: false, error: `Database error: ${dbError.message}` };
      }

      onProgress?.({ progress: 100, status: 'complete', message: 'Upload complete!' });

      return { success: true, photoId: dbData.id };

    } catch (error) {
      console.error('Upload error:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown upload error' 
      };
    }
  }

  static async deletePhoto(photoId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Get photo data first
      const { data: photo, error: fetchError } = await supabase
        .from('photos_v2')
        .select('url')
        .eq('id', photoId)
        .single();

      if (fetchError) {
        return { success: false, error: `Failed to fetch photo: ${fetchError.message}` };
      }

      // Extract filename from URL
      const url = new URL(photo.url);
      const pathParts = url.pathname.split('/');
      const filename = pathParts[pathParts.length - 1];
      const fullPath = `photos/${filename}`;

      // Delete from database first
      const { error: dbError } = await supabase
        .from('photos_v2')
        .delete()
        .eq('id', photoId);

      if (dbError) {
        return { success: false, error: `Database deletion failed: ${dbError.message}` };
      }

      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([fullPath]);

      if (storageError) {
        console.warn('Storage deletion failed:', storageError);
        // Don't fail the operation since database deletion succeeded
      }

      return { success: true };

    } catch (error) {
      console.error('Delete error:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown deletion error' 
      };
    }
  }
}