// Modern Design System for Fauna Focus
// Comprehensive design tokens and utilities for consistent UI

export const colors = {
  // Primary Wildlife Theme
  primary: {
    50: '#f0f9ff',
    100: '#e0f2fe', 
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9', // Main brand color
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
  },
  
  // Nature-inspired Secondary Colors
  forest: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },
  
  earth: {
    50: '#fefaf5',
    100: '#fef3e2',
    200: '#fde4c4',
    300: '#fbcf9b',
    400: '#f7b26f',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },
  
  ocean: {
    50: '#ecfeff',
    100: '#cffafe',
    200: '#a5f3fc',
    300: '#67e8f9',
    400: '#22d3ee',
    500: '#06b6d4',
    600: '#0891b2',
    700: '#0e7490',
    800: '#155e75',
    900: '#164e63',
  },
  
  // Status Colors
  success: {
    50: '#f0fdf4',
    500: '#22c55e',
    600: '#16a34a',
  },
  
  warning: {
    50: '#fffbeb',
    500: '#f59e0b',
    600: '#d97706',
  },
  
  danger: {
    50: '#fef2f2',
    500: '#ef4444',
    600: '#dc2626',
  },
  
  // Neutral Grays
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  }
};

export const spacing = {
  xs: '0.25rem',    // 4px
  sm: '0.5rem',     // 8px
  md: '1rem',       // 16px
  lg: '1.5rem',     // 24px
  xl: '2rem',       // 32px
  '2xl': '3rem',    // 48px
  '3xl': '4rem',    // 64px
  '4xl': '6rem',    // 96px
};

export const borderRadius = {
  none: '0',
  sm: '0.125rem',   // 2px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  full: '9999px',
};

export const shadows = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
};

export const typography = {
  fontFamily: {
    sans: ['Inter', 'system-ui', 'sans-serif'],
    mono: ['JetBrains Mono', 'Consolas', 'monospace'],
  },
  
  fontSize: {
    xs: ['0.75rem', { lineHeight: '1rem' }],
    sm: ['0.875rem', { lineHeight: '1.25rem' }],
    base: ['1rem', { lineHeight: '1.5rem' }],
    lg: ['1.125rem', { lineHeight: '1.75rem' }],
    xl: ['1.25rem', { lineHeight: '1.75rem' }],
    '2xl': ['1.5rem', { lineHeight: '2rem' }],
    '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
    '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
    '5xl': ['3rem', { lineHeight: '1' }],
  },
  
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  }
};

// Component Variants
export const buttonVariants = {
  primary: {
    base: `bg-primary-500 hover:bg-primary-600 text-white border-transparent`,
    disabled: `bg-gray-300 text-gray-500 cursor-not-allowed`,
  },
  
  secondary: {
    base: `bg-white hover:bg-gray-50 text-gray-900 border-gray-300`,
    disabled: `bg-gray-100 text-gray-400 cursor-not-allowed`,
  },
  
  success: {
    base: `bg-success-500 hover:bg-success-600 text-white border-transparent`,
    disabled: `bg-gray-300 text-gray-500 cursor-not-allowed`,
  },
  
  danger: {
    base: `bg-danger-500 hover:bg-danger-600 text-white border-transparent`,
    disabled: `bg-gray-300 text-gray-500 cursor-not-allowed`,
  },
  
  ghost: {
    base: `bg-transparent hover:bg-gray-100 text-gray-700 border-transparent`,
    disabled: `bg-transparent text-gray-400 cursor-not-allowed`,
  }
};

export const cardVariants = {
  default: {
    base: `bg-white border border-gray-200 rounded-lg shadow-sm`,
    hover: `hover:shadow-md transition-shadow duration-200`,
  },
  
  elevated: {
    base: `bg-white border border-gray-200 rounded-xl shadow-lg`,
    hover: `hover:shadow-xl transition-shadow duration-200`,
  },
  
  interactive: {
    base: `bg-white border border-gray-200 rounded-lg shadow-sm cursor-pointer`,
    hover: `hover:shadow-md hover:border-primary-300 transition-all duration-200`,
  }
};

// Animation Utilities
export const animations = {
  fadeIn: 'animate-in fade-in duration-200',
  slideIn: 'animate-in slide-in-from-bottom-4 duration-300',
  scaleIn: 'animate-in zoom-in-95 duration-200',
  
  // Custom keyframes for wildlife-themed animations
  float: 'animate-pulse',
  bounce: 'animate-bounce',
};

// Responsive Breakpoints
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
};

// Grid System
export const grid = {
  cols: {
    1: 'grid-cols-1',
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
    6: 'grid-cols-6',
    12: 'grid-cols-12',
  },
  
  responsive: {
    sm: {
      1: 'sm:grid-cols-1',
      2: 'sm:grid-cols-2',
      3: 'sm:grid-cols-3',
      4: 'sm:grid-cols-4',
    },
    md: {
      1: 'md:grid-cols-1',
      2: 'md:grid-cols-2',
      3: 'md:grid-cols-3',
      4: 'md:grid-cols-4',
      6: 'md:grid-cols-6',
    },
    lg: {
      1: 'lg:grid-cols-1',
      2: 'lg:grid-cols-2',
      3: 'lg:grid-cols-3',
      4: 'lg:grid-cols-4',
      6: 'lg:grid-cols-6',
      8: 'lg:grid-cols-8',
    }
  }
};

// Utility Functions
export const getColorClass = (color: string, shade: number = 500) => {
  return `${color}-${shade}`;
};

export const getSpacingClass = (type: 'p' | 'm' | 'px' | 'py' | 'mx' | 'my', size: keyof typeof spacing) => {
  return `${type}-${size}`;
};

export const combineClasses = (...classes: (string | undefined | null | false)[]) => {
  return classes.filter(Boolean).join(' ');
};
