import { supabase } from '@/integrations/supabase/client';

export interface PhotoFilters {
  published?: boolean;
  species_id?: string | null;
  needs_recovery?: boolean;
  category?: string;
  conservation_status?: string;
  search?: string;
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

export interface SpeciesFilters {
  published?: boolean;
  category?: string;
  conservation_status?: string;
  has_photos?: boolean;
  search?: string;
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

/**
 * QueryHelpers - Consolidated query utilities for common filtering operations
 * 
 * Provides reusable functions for building Supabase queries with common filters
 * used across the application. Reduces code duplication and improves maintainability.
 */
export class QueryHelpers {
  /**
   * Get photos with specific filter type
   */
  static async getPhotosWithFilter(filterType: 'all' | 'assigned' | 'unassigned' | 'published' | 'unpublished' | 'needs_recovery') {
    let query = supabase
      .from('photos_v2')
      .select(`
        *,
        species:species_id (
          id,
          name,
          common_name,
          scientific_name,
          category,
          conservation_status
        )
      `);

    switch (filterType) {
      case 'assigned':
        query = query.not('species_id', 'is', null);
        break;
      case 'unassigned':
        query = query.is('species_id', null);
        break;
      case 'published':
        query = query.eq('published', true);
        break;
      case 'unpublished':
        query = query.eq('published', false);
        break;
      case 'needs_recovery':
        query = query.eq('needs_recovery', true);
        break;
      case 'all':
      default:
        // No additional filters
        break;
    }

    const { data, error } = await query.order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching photos with filter:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Get all species
   */
  static async getAllSpecies() {
    const { data, error } = await supabase
      .from('species_v2')
      .select('*')
      .order('name', { ascending: true });

    if (error) {
      console.error('Error fetching species:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Get species with photo counts
   */
  static async getSpeciesWithPhotoCounts(filters: SpeciesFilters = {}) {
    let query = supabase
      .from('species_v2')
      .select(`
        *,
        photos:photos(count)
      `);

    // Apply filters
    if (filters.published !== undefined) {
      query = query.eq('published', filters.published);
    }

    if (filters.category) {
      query = query.eq('category', filters.category);
    }

    if (filters.conservation_status) {
      query = query.eq('conservation_status', filters.conservation_status);
    }

    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      query = query.or(`
        name.ilike.%${searchTerm}%,
        common_name.ilike.%${searchTerm}%,
        scientific_name.ilike.%${searchTerm}%
      `);
    }

    // Pagination
    if (filters.limit) {
      query = query.limit(filters.limit);
    }

    if (filters.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
    }

    // Ordering
    const orderBy = filters.orderBy || 'name';
    const orderDirection = filters.orderDirection || 'asc';
    query = query.order(orderBy, { ascending: orderDirection === 'asc' });

    const { data, error } = await query;
    
    if (error) {
      console.error('Error fetching species with photo counts:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Get photos requiring review
   */
  static async getPhotosRequiringReview() {
    const { data, error } = await supabase
      .from('photos_requiring_review')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching photos requiring review:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Get data consistency summary
   */
  static async getDataConsistencySummary() {
    const [
      { count: totalPhotos },
      { count: assignedPhotos },
      { count: unassignedPhotos },
      { count: publishedPhotos },
      { count: unpublishedPhotos },
      { count: needsRecoveryPhotos },
      { count: totalSpecies },
      { count: speciesWithPhotos }
    ] = await Promise.all([
      supabase.from('photos_v2').select('*', { count: 'exact', head: true }),
      supabase.from('photos_v2').select('*', { count: 'exact', head: true }).not('species_id', 'is', null),
      supabase.from('photos_v2').select('*', { count: 'exact', head: true }).is('species_id', null),
      supabase.from('photos_v2').select('*', { count: 'exact', head: true }).eq('published', true),
      supabase.from('photos_v2').select('*', { count: 'exact', head: true }).eq('published', false),
      supabase.from('photos_v2').select('*', { count: 'exact', head: true }).eq('needs_recovery', true),
      supabase.from('species_v2').select('*', { count: 'exact', head: true }),
      supabase.from('species_v2').select('*', { count: 'exact', head: true }).gt('photo_count', 0)
    ]);

    return {
      photos: {
        total: totalPhotos || 0,
        assigned: assignedPhotos || 0,
        unassigned: unassignedPhotos || 0,
        published: publishedPhotos || 0,
        unpublished: unpublishedPhotos || 0,
        needsRecovery: needsRecoveryPhotos || 0
      },
      species: {
        total: totalSpecies || 0,
        withPhotos: speciesWithPhotos || 0,
        withoutPhotos: (totalSpecies || 0) - (speciesWithPhotos || 0)
      }
    };
  }

  /**
   * Get category distribution
   */
  static async getCategoryDistribution() {
    const { data, error } = await supabase
      .from('species_v2')
      .select('category')
      .not('category', 'is', null);

    if (error) {
      console.error('Error fetching category distribution:', error);
      throw error;
    }

    const distribution = data?.reduce((acc, species) => {
      const category = species.category;
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    return Object.entries(distribution).map(([category, count]) => ({
      category,
      count
    }));
  }

  /**
   * Get conservation status distribution
   */
  static async getConservationStatusDistribution() {
    const { data, error } = await supabase
      .from('species_v2')
      .select('conservation_status')
      .not('conservation_status', 'is', null);

    if (error) {
      console.error('Error fetching conservation status distribution:', error);
      throw error;
    }

    const distribution = data?.reduce((acc, species) => {
      const status = species.conservation_status;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    return Object.entries(distribution).map(([status, count]) => ({
      status,
      count
    }));
  }
} 