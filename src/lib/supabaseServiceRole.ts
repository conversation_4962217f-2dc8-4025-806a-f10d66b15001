import { createClient } from '@supabase/supabase-js';

// Singleton service role client for admin operations
let serviceRoleClient: ReturnType<typeof createClient> | null = null;

export const getServiceRoleClient = () => {
  if (!serviceRoleClient) {
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || "https://raqxptgrugnmxdunbuty.supabase.co";
    const serviceRoleKey = import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY || import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJhcXhwdGdydWdubXhkdW5idXR5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNzM0NTAsImV4cCI6MjA2NTg0OTQ1MH0.0xVgSqjqch5a48Nxq4ZhaCZrMx0yaQ32qqi3YR1cLGQ";
    
    serviceRoleClient = createClient(supabaseUrl, serviceRoleKey);
  }
  
  return serviceRoleClient;
};
