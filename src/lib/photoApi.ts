import { supabase } from '@/integrations/supabase/client';
import { analyzePhotoWithAI, testAIAnalysis, enhancedAnalyzePhoto, logDetailedAnalysis } from './aiAnalysis';

// Cache for AI suggestions to avoid re-running analysis
interface CachedSuggestion {
  photoId: string;
  suggestions: any[];
  timestamp: number;
  aiResult: any;
}

const aiSuggestionsCache = new Map<string, CachedSuggestion>();
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

// Cache management functions
function getCachedSuggestions(photoId: string): CachedSuggestion | null {
  const cached = aiSuggestionsCache.get(photoId);
  if (!cached) return null;

  // Check if cache is still valid
  if (Date.now() - cached.timestamp > CACHE_DURATION) {
    aiSuggestionsCache.delete(photoId);
    return null;
  }

  return cached;
}

function setCachedSuggestions(photoId: string, suggestions: any[], aiResult: any): void {
  aiSuggestionsCache.set(photoId, {
    photoId,
    suggestions,
    aiResult,
    timestamp: Date.now()
  });
}

function clearCachedSuggestions(photoId: string): void {
  aiSuggestionsCache.delete(photoId);
  console.log(`🗑️ Cleared cached suggestions for photo ${photoId}`);
}

function clearAllCachedSuggestions(): void {
  aiSuggestionsCache.clear();
  console.log(`🗑️ Cleared all cached AI suggestions`);
}

interface ReassignPhotoSpeciesParams {
  photoId: string;
  newSpeciesId: string | null;
  oldSpeciesId: string | null;
  reason?: string;
}

// Helper function to update species photo count
async function updateSpeciesPhotoCount(speciesId: string): Promise<void> {
  try {
    // Count published photos for this species
    const { count, error: countError } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true })
      .eq('species_id', speciesId)
      .eq('published', true);

    if (countError) {
      console.error('Error counting photos for species:', countError);
      return;
    }

    // Update species photo count
    const { error: updateError } = await supabase
      .from('species_v2')
      .update({ photo_count: count || 0 })
      .eq('id', speciesId);

    if (updateError) {
      console.error('Error updating species photo count:', updateError);
    }
  } catch (error) {
    console.error('Error in updateSpeciesPhotoCount:', error);
  }
}

export async function reassignPhotoSpecies({
  photoId,
  newSpeciesId,
  oldSpeciesId,
  reason = 'drag-and-drop reassignment'
}: ReassignPhotoSpeciesParams) {
  try {
    // Update the photo's species_id
    const { error: updateError } = await supabase
      .from('photos_v2')
      .update({ species_id: newSpeciesId })
      .eq('id', photoId);

    if (updateError) throw updateError;

    // Update species photo counts if species actually changed
    if (oldSpeciesId !== newSpeciesId) {
      // Update old species count (if it had one)
      if (oldSpeciesId) {
        await updateSpeciesPhotoCount(oldSpeciesId);
      }

      // Update new species count (if assigning to one)
      if (newSpeciesId) {
        await updateSpeciesPhotoCount(newSpeciesId);
      }

      // Log the override
      const { error: logError } = await supabase
        .from('ai_override_log')
        .insert({
          table_name: 'photos_v2',
          record_id: photoId,
          field_name: 'species_id',
          old_value: oldSpeciesId,
          new_value: newSpeciesId,
          override_type: 'species_assignment',
          override_reason: reason,
          user_id: (await supabase.auth.getUser()).data.user?.id || null
        });

      if (logError) {
        console.error('Failed to log override:', logError);
        // Don't throw here - the main operation succeeded
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error reassigning photo species:', error);
    throw error;
  }
}

// Helper to get photos with filtering
export async function getPhotosWithFilter(filter: 'all' | 'assigned' | 'unassigned' | 'published' | 'unpublished' | 'needs_recovery') {
  let query = supabase
    .from('photos_v2')
    .select(`
      id,
      url,
      title,
      description,
      species_id,
      published,
      created_at
    `)
    .order('created_at', { ascending: false });

  // Apply filter
  switch (filter) {
    case 'assigned':
      query = query.not('species_id', 'is', null);
      break;
    case 'unassigned':
      query = query.is('species_id', null);
      break;
    case 'published':
      query = query.eq('published', true);
      break;
    case 'unpublished':
      query = query.eq('published', false);
      break;
    case 'needs_recovery':
      // Temporarily disable this filter until needs_recovery column is added
      // query = query.eq('needs_recovery', true);
      console.warn('needs_recovery filter is temporarily disabled - column not yet added to database');
      break;
    case 'all':
    default:
      // No filter applied
      break;
  }

  const { data: photos, error } = await query;

  if (error) throw error;

  // Get all species data to join with photos
  const { data: allSpecies, error: speciesError } = await supabase
    .from('species_v2')
    .select('id, name, common_name, scientific_name');

  if (speciesError) throw speciesError;

  // Create a map for quick species lookup
  const speciesMap = new Map(allSpecies?.map(s => [s.id, s]) || []);

  // Transform the data to include species information
  return (photos || []).map(photo => ({
    ...photo,
    species: photo.species_id ? speciesMap.get(photo.species_id) || null : null
  }));
}

// Helper to get all species for drop targets
export async function getAllSpecies() {
  const { data, error } = await supabase
    .from('species_v2')
    .select(`
      id,
      name,
      common_name,
      scientific_name,
      category,
      conservation_status,
      published
    `)
    .order('name');

  if (error) throw error;
  return data || [];
}

// Helper to get species with photo counts
export async function getSpeciesWithPhotoCounts() {
  const { data, error } = await supabase
    .from('species_photo_counts')
    .select('*')
    .order('total_photos', { ascending: false });

  if (error) throw error;
  return data || [];
}

// Helper to get AI suggestions for a photo using real AI analysis
export async function getAISuggestions(photoId: string) {
  try {
    // Check cache first
    const cached = getCachedSuggestions(photoId);
    if (cached) {
      console.log(`💾 Using cached AI suggestions for photo ${photoId}`);
      console.log(`📊 Cached suggestions (${cached.suggestions.length}):`);
      cached.suggestions.forEach((s, i) => {
        console.log(`   ${i + 1}. ${s.speciesName} (${s.confidenceScore}%)`);
      });
      return cached.suggestions;
    }

    // Get the photo to analyze
    const { data: photo, error: photoError } = await supabase
      .from('photos_v2')
      .select('id, url, title, description, species_id')
      .eq('id', photoId)
      .single();

    if (photoError) throw photoError;

    if (!photo.url) {
      console.error('Photo has no URL for analysis');
      return [];
    }

    console.log(`🔍 Analyzing photo ${photoId} with enhanced AI (not cached)...`);

    // Use enhanced multi-pass AI analysis
    const aiResult = await enhancedAnalyzePhoto(photo.url);

    // Log detailed analysis results
    logDetailedAnalysis(aiResult, photoId, photo.url);

    if (aiResult.identifications.length === 0) {
      console.log(`❌ No species identified in photo ${photoId}`);
      return [];
    }

    // Get all species from database for matching
    const { data: species, error: speciesError } = await supabase
      .from('species_v2')
      .select('id, name, common_name, scientific_name, category')
      .eq('published', true);

    if (speciesError) throw speciesError;

    // Match AI identifications to database species
    const suggestions = [];

    for (const identification of aiResult.identifications) {
      console.log(`\n🔍 PROCESSING IDENTIFICATION FOR PHOTO ${photoId}:`);
      console.log(`   Species: ${identification.speciesName}`);
      console.log(`   Scientific: ${identification.scientificName}`);
      console.log(`   Category: ${identification.category}`);
      console.log(`   Confidence: ${identification.confidence}%`);

      // Try to find matching species in database
      const matchingSpecies = findMatchingSpecies(identification, species);

      if (matchingSpecies) {
        console.log(`✅ DATABASE MATCH FOUND:`);
        console.log(`   Database Species: ${matchingSpecies.common_name || matchingSpecies.name}`);
        console.log(`   Database ID: ${matchingSpecies.id}`);
        console.log(`   Database Scientific: ${matchingSpecies.scientific_name}`);

        suggestions.push({
          speciesId: matchingSpecies.id,
          speciesName: matchingSpecies.common_name || matchingSpecies.name,
          confidenceScore: identification.confidence,
          reason: identification.reasoning,
          aiCategory: identification.category,
          scientificName: identification.scientificName
        });
      } else {
        console.log(`❌ NO DATABASE MATCH FOUND`);
        console.log(`🆕 Unmatched species: ${identification.speciesName} (${identification.scientificName})`);
        console.log(`💡 Consider adding this species to your database`);
      }
    }

    console.log(`✅ Found ${suggestions.length} matching suggestions for photo ${photoId}`);

    // Cache the suggestions for future use
    setCachedSuggestions(photoId, suggestions, aiResult);
    console.log(`💾 Cached AI suggestions for photo ${photoId}`);

    return suggestions;

  } catch (error) {
    console.error('Error getting AI suggestions:', error);
    return [];
  }
}

// Helper function to match AI identification to database species
function findMatchingSpecies(identification: any, species: any[]) {
  const { speciesName, scientificName, category } = identification;

  console.log(`\n🔍 DETAILED MATCHING PROCESS:`);
  console.log(`   Target Species: "${speciesName}"`);
  console.log(`   Target Scientific: "${scientificName}"`);
  console.log(`   Target Category: "${category}"`);
  console.log(`   Database has ${species.length} species to search`);

  // Try exact matches first
  console.log(`\n🎯 Step 1: Trying exact matches...`);
  let match = species.find(s =>
    s.common_name?.toLowerCase() === speciesName.toLowerCase() ||
    s.name?.toLowerCase() === speciesName.toLowerCase() ||
    (scientificName && s.scientific_name?.toLowerCase() === scientificName.toLowerCase())
  );

  if (match) {
    console.log(`✅ EXACT MATCH FOUND:`);
    console.log(`   Matched: ${match.common_name || match.name}`);
    console.log(`   Scientific: ${match.scientific_name}`);
    console.log(`   Match Type: ${match.common_name?.toLowerCase() === speciesName.toLowerCase() ? 'Common Name' :
                                  match.name?.toLowerCase() === speciesName.toLowerCase() ? 'Name' : 'Scientific Name'}`);
    return match;
  }
  console.log(`❌ No exact matches found`);

  // Try partial matches for common names
  console.log(`\n🎯 Step 2: Trying partial matches...`);
  const partialMatches = species.filter(s =>
    s.common_name?.toLowerCase().includes(speciesName.toLowerCase()) ||
    speciesName.toLowerCase().includes(s.common_name?.toLowerCase() || '') ||
    s.name?.toLowerCase().includes(speciesName.toLowerCase()) ||
    speciesName.toLowerCase().includes(s.name?.toLowerCase() || '')
  );

  if (partialMatches.length > 0) {
    console.log(`🔍 Found ${partialMatches.length} partial matches:`);
    partialMatches.forEach((m, i) => {
      console.log(`   ${i + 1}. ${m.common_name || m.name} (${m.scientific_name})`);
    });

    match = partialMatches[0];
    console.log(`✅ Using first partial match: ${match.common_name || match.name}`);
    return match;
  }
  console.log(`❌ No partial matches found`);

  // Try partial matches for common names
  match = species.find(s =>
    s.common_name?.toLowerCase().includes(speciesName.toLowerCase()) ||
    speciesName.toLowerCase().includes(s.common_name?.toLowerCase())
  );

  if (match) {
    console.log(`✅ Partial match found: ${match.common_name || match.name}`);
    return match;
  }

  // For violetears specifically, try matching any violetear species
  if (speciesName.toLowerCase().includes('violetear')) {
    console.log(`\n🎯 Step 3: Violetear-specific matching...`);
    const violetearSpecies = species.filter(s =>
      s.common_name?.toLowerCase().includes('violetear') ||
      s.name?.toLowerCase().includes('violetear') ||
      s.scientific_name?.toLowerCase().includes('colibri')
    );

    console.log(`💜 Found ${violetearSpecies.length} violetear species in database:`);
    violetearSpecies.forEach((v, i) => {
      console.log(`   ${i + 1}. ${v.common_name || v.name} (${v.scientific_name})`);
    });

    if (violetearSpecies.length > 0) {
      // Try to match by scientific name genus
      if (scientificName && scientificName.toLowerCase().includes('colibri')) {
        console.log(`🧬 Trying genus match with "colibri"...`);
        match = violetearSpecies.find(s =>
          s.scientific_name?.toLowerCase().includes('colibri')
        );

        if (match) {
          console.log(`✅ VIOLETEAR GENUS MATCH FOUND:`);
          console.log(`   Species: ${match.common_name || match.name}`);
          console.log(`   Scientific: ${match.scientific_name}`);
          return match;
        }
        console.log(`❌ No genus match found`);
      }

      // Use first violetear as fallback
      match = violetearSpecies[0];
      console.log(`⚠️ FALLBACK: Using first violetear species: ${match.common_name || match.name}`);
      return match;
    }
    console.log(`❌ No violetear species in database`);
  }

  // For hawks specifically, try matching any hawk species if AI identified a hawk
  if (speciesName.toLowerCase().includes('hawk')) {
    console.log(`\n🎯 Step 4: Hawk-specific matching...`);
    const hawkSpecies = species.filter(s =>
      s.common_name?.toLowerCase().includes('hawk') ||
      s.name?.toLowerCase().includes('hawk')
    );

    console.log(`🦅 Found ${hawkSpecies.length} hawk species in database:`);
    hawkSpecies.forEach((h, i) => {
      console.log(`   ${i + 1}. ${h.common_name || h.name} (${h.scientific_name})`);
    });

    // Try to match by genus (Buteo, Accipiter, etc.)
    if (scientificName) {
      const genus = scientificName.split(' ')[0].toLowerCase();
      console.log(`🧬 Trying genus match with "${genus}"...`);
      match = hawkSpecies.find(s =>
        s.scientific_name?.toLowerCase().startsWith(genus)
      );

      if (match) {
        console.log(`✅ HAWK GENUS MATCH FOUND:`);
        console.log(`   Species: ${match.common_name || match.name}`);
        console.log(`   Scientific: ${match.scientific_name}`);
        return match;
      }
      console.log(`❌ No genus match found`);
    }

    // If we have multiple hawk options, prefer the first one as a reasonable match
    if (hawkSpecies.length > 0) {
      match = hawkSpecies[0];
      console.log(`⚠️ FALLBACK: Using first hawk species: ${match.common_name || match.name}`);
      return match;
    }
    console.log(`❌ No hawk species in database`);
  }

  // Try category-based fuzzy matching
  const categoryMatches = species.filter(s =>
    s.category?.toLowerCase() === category?.toLowerCase()
  );

  // Look for partial name matches within the same category
  match = categoryMatches.find(s =>
    s.common_name?.toLowerCase().includes(speciesName.split(' ')[0].toLowerCase()) ||
    s.name?.toLowerCase().includes(speciesName.split(' ')[0].toLowerCase())
  );

  if (match) {
    console.log(`✅ Category match found: ${match.common_name || match.name}`);
    return match;
  }

  console.log(`❌ No match found for: ${speciesName}`);
  return null;
}

// Test function to analyze a single photo and see results
export async function testSinglePhotoAI(photoId: string) {
  console.log(`🧪 Testing AI analysis for photo ${photoId}`);

  try {
    const { data: photo, error } = await supabase
      .from('photos_v2')
      .select('id, url, title')
      .eq('id', photoId)
      .single();

    if (error) throw error;

    console.log(`📸 Photo: ${photo.title} - ${photo.url}`);

    // Test the raw AI analysis
    console.log('🔍 Running AI analysis...');
    await testAIAnalysis(photo.url);

    // Test the suggestion matching
    console.log('🎯 Getting suggestions...');
    const suggestions = await getAISuggestions(photoId);

    console.log(`✅ Final suggestions (${suggestions.length}):`);
    suggestions.forEach((suggestion, index) => {
      console.log(`  ${index + 1}. ${suggestion.speciesName} (${suggestion.confidenceScore}%)`);
      console.log(`     Reason: ${suggestion.reason}`);
      console.log(`     Scientific: ${suggestion.scientificName || 'Unknown'}`);
    });

    return suggestions;

  } catch (error) {
    console.error('❌ Test failed:', error);
    return [];
  }
}

// Debug function to check what hawk species are in the database
export async function checkHawkSpecies() {
  try {
    const { data: hawks, error } = await supabase
      .from('species_v2')
      .select('id, name, common_name, scientific_name')
      .or('name.ilike.%hawk%,common_name.ilike.%hawk%')
      .eq('published', true)
      .order('common_name');

    if (error) throw error;

    console.log('🦅 Hawk species in database:');
    hawks.forEach((hawk, index) => {
      console.log(`  ${index + 1}. ${hawk.common_name || hawk.name}`);
      console.log(`     Scientific: ${hawk.scientific_name || 'Unknown'}`);
      console.log(`     ID: ${hawk.id}`);
    });

    return hawks;
  } catch (error) {
    console.error('Error checking hawk species:', error);
    return [];
  }
}

// Test function to analyze a specific photo by filename pattern
export async function testSpecificPhoto(filenamePattern: string) {
  console.log(`🔍 Looking for photo with filename containing: "${filenamePattern}"`);

  try {
    const { data: photos, error } = await supabase
      .from('photos_v2')
      .select('id, url, title')
      .ilike('title', `%${filenamePattern}%`)
      .limit(5);

    if (error) throw error;

    console.log(`📸 Found ${photos.length} photos matching "${filenamePattern}":`);
    photos.forEach((photo, index) => {
      console.log(`  ${index + 1}. ${photo.title} (ID: ${photo.id})`);
    });

    if (photos.length > 0) {
      const photo = photos[0];
      console.log(`\n🧪 Testing AI analysis on: ${photo.title}`);
      return await testSinglePhotoAI(photo.id);
    } else {
      console.log('❌ No photos found with that filename pattern');
      return [];
    }

  } catch (error) {
    console.error('❌ Error finding photo:', error);
    return [];
  }
}

// Test the Crowned Woodnymph photo specifically
export async function testWoodnymphPhoto() {
  console.log('🧪 Testing AI analysis on Crowned Woodnymph photo');

  try {
    const { data: photos, error } = await supabase
      .from('photos_v2')
      .select('id, url, title')
      .ilike('title', '%crowned-woodnymph%')
      .limit(1);

    if (error) throw error;

    if (photos.length > 0) {
      const photo = photos[0];
      console.log(`📸 Found Crowned Woodnymph photo: ${photo.title}`);
      console.log(`🔗 URL: ${photo.url}`);
      return await testSinglePhotoAI(photo.id);
    } else {
      console.log('❌ Crowned Woodnymph photo not found');
      return [];
    }

  } catch (error) {
    console.error('❌ Error finding Crowned Woodnymph photo:', error);
    return [];
  }
}

// Bulk reassignment function
export async function bulkReassignPhotos({
  photoIds,
  newSpeciesId,
  reason = 'bulk drag-and-drop reassignment'
}: {
  photoIds: string[];
  newSpeciesId: string | null;
  reason?: string;
}) {
  try {
    // Get current species assignments for logging
    const { data: currentPhotos, error: fetchError } = await supabase
      .from('photos_v2')
      .select('id, species_id')
      .in('id', photoIds);

    if (fetchError) throw fetchError;

    // Update all photos
    const { error: updateError } = await supabase
      .from('photos_v2')
      .update({ species_id: newSpeciesId })
      .in('id', photoIds);

    if (updateError) throw updateError;

    // Update species photo counts for affected species
    const affectedSpeciesIds = new Set<string>();

    // Collect old species IDs
    currentPhotos?.forEach(photo => {
      if (photo.species_id) {
        affectedSpeciesIds.add(photo.species_id);
      }
    });

    // Add new species ID if assigning to one
    if (newSpeciesId) {
      affectedSpeciesIds.add(newSpeciesId);
    }

    // Update photo counts for all affected species
    for (const speciesId of affectedSpeciesIds) {
      await updateSpeciesPhotoCount(speciesId);
    }

    // Log overrides for each photo
    const overrideLogs = currentPhotos?.map(photo => ({
      table_name: 'photos_v2',
      record_id: photo.id,
      field_name: 'species_id',
      old_value: photo.species_id,
      new_value: newSpeciesId,
      override_type: 'bulk_species_assignment',
      override_reason: reason,
      user_id: null // Could be enhanced to get actual user ID
    })) || [];

    if (overrideLogs.length > 0) {
      const { error: logError } = await supabase
        .from('ai_override_log')
        .insert(overrideLogs);

      if (logError) {
        console.error('Failed to log bulk overrides:', logError);
        // Don't throw here - the main operation succeeded
      }
    }

    return { success: true, updatedCount: photoIds.length };
  } catch (error) {
    console.error('Error bulk reassigning photos:', error);
    throw error;
  }
}

/**
 * Assign a photo to a species and clear its cached suggestions
 */
export async function assignPhotoToSpecies(photoId: string, speciesId: string): Promise<boolean> {
  try {
    console.log(`📌 Assigning photo ${photoId} to species ${speciesId}`);

    const { error } = await supabase
      .from('photos_v2')
      .update({ species_id: speciesId })
      .eq('id', photoId);

    if (error) throw error;

    // Clear cached suggestions since photo is now assigned
    clearCachedSuggestions(photoId);

    console.log(`✅ Photo ${photoId} assigned to species ${speciesId}`);
    return true;

  } catch (error) {
    console.error('Error assigning photo to species:', error);
    return false;
  }
}

/**
 * Reject AI suggestions for a photo (clears cache so it can be re-analyzed)
 */
export async function rejectAISuggestions(photoId: string): Promise<void> {
  console.log(`❌ Rejecting AI suggestions for photo ${photoId}`);
  clearCachedSuggestions(photoId);
  console.log(`🔄 Photo ${photoId} can now be re-analyzed with fresh AI`);
}

/**
 * Get cache statistics
 */
export function getCacheStats(): { totalCached: number; cacheEntries: string[] } {
  const entries = Array.from(aiSuggestionsCache.keys());
  return {
    totalCached: aiSuggestionsCache.size,
    cacheEntries: entries
  };
}

/**
 * Get count of photos that need AI review
 */
export async function getPhotosNeedingReviewCount(): Promise<number> {
  try {
    const { count, error } = await supabase
      .from('photos_v2')
      .select('*', { count: 'exact', head: true })
      .not('ai_suggested_id', 'is', null)
      .eq('ai_reviewed', false);

    if (error) throw error;
    return count || 0;
  } catch (error) {
    console.error('Error getting photos needing review count:', error);
    return 0;
  }
}

/**
 * Get photos that need AI review
 */
export async function getPhotosNeedingReview(limit: number = 50): Promise<any[]> {
  try {
    const { data, error } = await supabase
      .from('photos_v2')
      .select(`
        id,
        url,
        title,
        filename,
        ai_suggested_id,
        ai_confidence,
        species_v2:ai_suggested_id (
          id,
          common_name,
          name,
          scientific_name
        )
      `)
      .not('ai_suggested_id', 'is', null)
      .eq('ai_reviewed', false)
      .limit(limit)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting photos needing review:', error);
    return [];
  }
}

/**
 * Export cache management functions for external use
 */
export { clearCachedSuggestions, clearAllCachedSuggestions };