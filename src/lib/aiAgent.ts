import { supabase } from "@/integrations/supabase/client";
import { Tables, TablesInsert } from "@/integrations/supabase/types";
import { withErrorHandling, AppError, ErrorType } from './errorHandling';
import { withRetry } from './asyncUtils';

// Confidence thresholds as defined in the prompt
const CONFIDENCE_THRESHOLDS = {
  species_assignment: 0.75,    // Assign if ≥75%
  location_extraction: 0.60,   // Accept location string if ≥60%
  metadata_generation: 0.70,   // Accept tags, notes if ≥70%
  duplicate_detection: 0.90,   // Match photo if hash matches
  manual_review: 0.50          // Flag anything <50% confidence
};

// Standardized tag vocabulary
const STANDARD_TAGS = {
  habitats: ['desert', 'forest', 'grassland', 'wetland', 'mountain', 'coastal', 'urban', 'suburban', 'rural'],
  behaviors: ['nocturnal', 'diurnal', 'migratory', 'resident', 'territorial', 'social', 'solitary', 'nesting', 'feeding'],
  conservation: ['endangered', 'threatened', 'vulnerable', 'near_threatened', 'least_concern', 'extinct_in_wild'],
  size: ['small', 'medium', 'large', 'tiny', 'huge'],
  activity: ['flying', 'perching', 'hunting', 'resting', 'feeding', 'displaying', 'courting']
};

export interface AIProcessingResult {
  photo_id?: string;
  hash: string;
  species_id?: string;
  species_name?: string;
  species_confidence: number;
  location?: string;
  metadata: {
    title: string;
    tags: string[];
    notes: string;
  };
  needs_review: boolean;
  warnings: string[];
  errors: string[];
}

export interface SpeciesMatch {
  species_id: string;
  species_name: string;
  confidence: number;
  match_type: 'exact_scientific' | 'exact_common' | 'fuzzy_scientific' | 'fuzzy_common' | 'partial' | 'new_species';
}

export interface ImageAnalysis {
  species_suggestions: Array<{
    name: string;
    scientific_name: string;
    confidence: number;
  }>;
  description: string;
  location_hints: string[];
  time_of_day: string;
  behavior: string;
  habitat: string;
}

export interface SpeciesSuggestion {
  name: string;
  scientific_name: string;
  confidence: number;
  habitat?: string;
  behavior?: string;
}

export class WildlifeAIAgent {
  private static speciesCache: Map<string, Tables<'species'>> = new Map();

  /**
   * Process a wildlife photo with AI-powered species identification and metadata generation
   */
  static async processPhoto(
    file: File,
    existingMetadata?: Partial<TablesInsert<'photos'>>
  ): Promise<AIProcessingResult> {
    const result: AIProcessingResult = {
      hash: '',
      species_confidence: 0,
      metadata: { title: '', tags: [], notes: '' },
      needs_review: false,
      warnings: [],
      errors: []
    };

    try {
      // Step 1: Generate hash for deduplication
      result.hash = await this.generateImageHash(file);
      
      // Step 2: Check for duplicates
      const duplicateCheck = await this.checkForDuplicates(result.hash);
      if (duplicateCheck.isDuplicate) {
        result.warnings.push(`Photo appears to be a duplicate of existing photo(s): ${duplicateCheck.photoIds.join(', ')}`);
        return result;
      }

      // Step 3: Extract image metadata and analyze content
      const imageAnalysis = await this.analyzeImage(file);
      
      // Step 4: Match or create species
      const speciesMatch = await this.matchSpecies(imageAnalysis);
      result.species_id = speciesMatch.species_id;
      result.species_name = speciesMatch.species_name;
      result.species_confidence = speciesMatch.confidence;

      // Step 5: Extract location
      result.location = await this.extractLocation(imageAnalysis, existingMetadata?.location);

      // Step 6: Generate metadata
      result.metadata = await this.generateMetadata(imageAnalysis, speciesMatch, result.location);

      // Step 7: Determine if review is needed
      result.needs_review = this.determineReviewNeeded(speciesMatch, result.species_confidence);

      return result;

    } catch (error) {
      result.errors.push(`AI processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      result.needs_review = true;
      return result;
    }
  }

  /**
   * Generate SHA-256 hash of image file for deduplication
   */
  private static async generateImageHash(file: File): Promise<string> {
    const buffer = await file.arrayBuffer();
    const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Check if a photo with the same hash already exists
   */
  private static async checkForDuplicates(hash: string): Promise<{ isDuplicate: boolean; photoIds: string[] }> {
    const { data, error } = await supabase
      .from('photos_v2')
      .select('id')
      .eq('hash', hash);

    if (error) {
      console.error('Error checking for duplicates:', error);
      return { isDuplicate: false, photoIds: [] };
    }

    return {
      isDuplicate: data.length > 0,
      photoIds: data.map(p => p.id)
    };
  }

  /**
   * Analyze image content using AI (placeholder for actual AI implementation)
   */
  private static async analyzeImage(file: File): Promise<ImageAnalysis> {
    // This is a placeholder - in a real implementation, you would:
    // 1. Send image to AI service (e.g., OpenAI Vision, Google Vision API)
    // 2. Get species identification, confidence scores, and image description
    // 3. Extract GPS coordinates if available
    
    // For now, return mock analysis
    return {
      species_suggestions: [
        { name: 'Eastern Screech Owl', scientific_name: 'Megascops asio', confidence: 0.82 },
        { name: 'Great Horned Owl', scientific_name: 'Bubo virginianus', confidence: 0.15 },
        { name: 'Barred Owl', scientific_name: 'Strix varia', confidence: 0.03 }
      ],
      description: 'A small owl perched on a branch in a wooded area',
      location_hints: ['woodland', 'forest', 'tree'],
      time_of_day: 'dusk',
      behavior: 'perching',
      habitat: 'woodland'
    };
  }

  /**
   * Match species using the priority order defined in the prompt
   */
  private static async matchSpecies(imageAnalysis: ImageAnalysis): Promise<SpeciesMatch> {
    const suggestions = imageAnalysis.species_suggestions || [];
    
    for (const suggestion of suggestions) {
      // 1. Exact match by scientific_name
      const exactScientific = await this.findExactScientificMatch(suggestion.scientific_name);
      if (exactScientific) {
        return {
          species_id: exactScientific.id,
          species_name: exactScientific.name,
          confidence: suggestion.confidence,
          match_type: 'exact_scientific'
        };
      }

      // 2. Exact match by common_name
      const exactCommon = await this.findExactCommonMatch(suggestion.name);
      if (exactCommon) {
        return {
          species_id: exactCommon.id,
          species_name: exactCommon.name,
          confidence: suggestion.confidence,
          match_type: 'exact_common'
        };
      }

      // 3. Fuzzy match by scientific_name (≥80% similarity)
      if (suggestion.confidence >= 0.8) {
        const fuzzyScientific = await this.findFuzzyScientificMatch(suggestion.scientific_name, 0.8);
        if (fuzzyScientific) {
          return {
            species_id: fuzzyScientific.id,
            species_name: fuzzyScientific.name,
            confidence: suggestion.confidence * 0.9, // Reduce confidence for fuzzy match
            match_type: 'fuzzy_scientific'
          };
        }
      }

      // 4. Fuzzy match by common_name (≥70% similarity)
      if (suggestion.confidence >= 0.7) {
        const fuzzyCommon = await this.findFuzzyCommonMatch(suggestion.name, 0.7);
        if (fuzzyCommon) {
          return {
            species_id: fuzzyCommon.id,
            species_name: fuzzyCommon.name,
            confidence: suggestion.confidence * 0.85, // Reduce confidence for fuzzy match
            match_type: 'fuzzy_common'
          };
        }
      }
    }

    // 5. Partial text match in description fields (≥60% confidence)
    if (suggestions[0]?.confidence >= 0.6) {
      const partialMatch = await this.findPartialMatch(imageAnalysis.description);
      if (partialMatch) {
        return {
          species_id: partialMatch.id,
          species_name: partialMatch.name,
          confidence: suggestions[0].confidence * 0.7, // Reduce confidence for partial match
          match_type: 'partial'
        };
      }
    }

    // 6. Create new species if confidence ≥50%
    if (suggestions[0]?.confidence >= 0.5) {
      const newSpecies = await this.createNewSpecies(suggestions[0]);
      return {
        species_id: newSpecies.id,
        species_name: newSpecies.name,
        confidence: suggestions[0].confidence,
        match_type: 'new_species'
      };
    }

    // No match found
    return {
      species_id: '',
      species_name: 'Unknown Species',
      confidence: 0,
      match_type: 'new_species'
    };
  }

  /**
   * Find exact match by scientific name
   */
  private static async findExactScientificMatch(scientificName: string): Promise<Tables<'species_v2'> | null> {
    const { data, error } = await supabase
      .from('species_v2')
      .select('*')
      .eq('scientific_name', scientificName)
      .single();

    if (error || !data) return null;
    return data;
  }

  /**
   * Find exact match by common name
   */
  private static async findExactCommonMatch(commonName: string): Promise<Tables<'species_v2'> | null> {
    const { data, error } = await supabase
      .from('species_v2')
      .select('*')
      .eq('name', commonName)
      .single();

    if (error || !data) return null;
    return data;
  }

  /**
   * Find fuzzy match by scientific name using Levenshtein similarity
   */
  private static async findFuzzyScientificMatch(scientificName: string, threshold: number): Promise<Tables<'species'> | null> {
    // This would require a PostgreSQL function for Levenshtein distance
    // For now, implement a simple similarity check
    const { data, error } = await supabase
      .from('species_v2')
      .select('*')
      .not('scientific_name', 'is', null);

    if (error || !data) return null;

    for (const species of data) {
      if (species.scientific_name && this.calculateSimilarity(scientificName, species.scientific_name) >= threshold) {
        return species;
      }
    }

    return null;
  }

  /**
   * Find fuzzy match by common name
   */
  private static async findFuzzyCommonMatch(commonName: string, threshold: number): Promise<Tables<'species'> | null> {
    const { data, error } = await supabase
      .from('species_v2')
      .select('*')
      .not('common_name', 'is', null);

    if (error || !data) return null;

    for (const species of data) {
      if (species.common_name && this.calculateSimilarity(commonName, species.common_name) >= threshold) {
        return species;
      }
    }

    return null;
  }

  /**
   * Find partial match in description fields
   */
  private static async findPartialMatch(description: string): Promise<Tables<'species'> | null> {
    const { data, error } = await supabase
      .from('species_v2')
      .select('*')
      .or(`description.ilike.%${description}%,notes.ilike.%${description}%`);

    if (error || !data || data.length === 0) return null;

    // Return the first match (could be improved with ranking)
    return data[0];
  }

  /**
   * Create a new species record
   */
  private static async createNewSpecies(suggestion: SpeciesSuggestion): Promise<Tables<'species_v2'>> {
    const newSpecies: TablesInsert<'species_v2'> = {
      name: suggestion.name,
      scientific_name: suggestion.scientific_name,
      published: false, // New AI-created species start unpublished
      notes: `Species identified by AI with ${(suggestion.confidence * 100).toFixed(1)}% confidence`
    };

    const { data, error } = await supabase
      .from('species_v2')
      .insert(newSpecies)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create new species: ${error.message}`);
    }

    return data;
  }

  /**
   * Extract location from image metadata or existing data
   */
  private static async extractLocation(imageAnalysis: ImageAnalysis, existingLocation?: string): Promise<string | undefined> {
    if (existingLocation) {
      return this.standardizeLocation(existingLocation);
    }

    // In a real implementation, you would extract GPS coordinates from EXIF data
    // and reverse geocode to get a human-readable location
    // For now, return undefined
    return undefined;
  }

  /**
   * Generate metadata for the photo
   */
  private static async generateMetadata(
    imageAnalysis: ImageAnalysis,
    speciesMatch: SpeciesMatch,
    location?: string
  ): Promise<{ title: string; tags: string[]; notes: string }> {
    const title = this.generateTitle(speciesMatch, imageAnalysis, location);
    const tags = this.generateTags(speciesMatch, imageAnalysis);
    const notes = this.generateNotes(imageAnalysis, speciesMatch);

    return { title, tags, notes };
  }

  /**
   * Generate a title for the photo
   */
  private static generateTitle(speciesMatch: SpeciesMatch, imageAnalysis: ImageAnalysis, location?: string): string {
    const speciesName = speciesMatch.species_name || 'Wildlife';
    const habitat = imageAnalysis.habitat || 'natural setting';
    const locationPart = location ? ` in ${location}` : '';
    
    return `${speciesName} in ${habitat}${locationPart}`;
  }

  /**
   * Generate tags for the photo
   */
  private static generateTags(speciesMatch: SpeciesMatch, imageAnalysis: ImageAnalysis): string[] {
    const tags: string[] = [];

    // Add habitat tags
    if (imageAnalysis.habitat) {
      tags.push(imageAnalysis.habitat);
    }

    // Add behavior tags
    if (imageAnalysis.behavior) {
      tags.push(imageAnalysis.behavior);
    }

    // Add time of day tags
    if (imageAnalysis.time_of_day) {
      tags.push(imageAnalysis.time_of_day);
    }

    // Add species-specific tags
    if (speciesMatch.species_name) {
      tags.push(speciesMatch.species_name.toLowerCase().replace(/\s+/g, '_'));
    }

    return [...new Set(tags)]; // Remove duplicates
  }

  /**
   * Generate notes for the photo
   */
  private static generateNotes(imageAnalysis: ImageAnalysis, speciesMatch: SpeciesMatch): string {
    const notes: string[] = [];

    if (imageAnalysis.description) {
      notes.push(imageAnalysis.description);
    }

    if (speciesMatch.match_type === 'new_species') {
      notes.push('Species identified by AI - requires verification');
    }

    if (speciesMatch.confidence < CONFIDENCE_THRESHOLDS.species_assignment) {
      notes.push(`Low confidence species identification (${(speciesMatch.confidence * 100).toFixed(1)}%)`);
    }

    return notes.join('. ');
  }

  /**
   * Generate tags for a new species
   */
  private static generateSpeciesTags(suggestion: SpeciesSuggestion): string[] {
    const tags: string[] = [];

    // Add habitat tags based on analysis
    if (suggestion.habitat) {
      tags.push(suggestion.habitat);
    }

    // Add behavior tags
    if (suggestion.behavior) {
      tags.push(suggestion.behavior);
    }

    return tags;
  }

  /**
   * Standardize location format
   */
  private static standardizeLocation(location: string): string {
    // Convert to title case
    return location
      .toLowerCase()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  /**
   * Calculate similarity between two strings (simple implementation)
   */
  private static calculateSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const distance = this.levenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private static levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * Determine if the photo needs manual review
   */
  private static determineReviewNeeded(speciesMatch: SpeciesMatch, confidence: number): boolean {
    return (
      confidence < CONFIDENCE_THRESHOLDS.manual_review ||
      speciesMatch.match_type === 'new_species' ||
      speciesMatch.match_type === 'partial' ||
      speciesMatch.match_type === 'fuzzy_scientific' ||
      speciesMatch.match_type === 'fuzzy_common'
    );
  }

  /**
   * Save the processed photo to the database
   */
  static async saveProcessedPhoto(
    file: File,
    result: AIProcessingResult,
    additionalMetadata?: Partial<TablesInsert<'photos'>>
  ): Promise<{ success: boolean; photoId?: string; error?: string }> {
    try {
      // Upload file to storage first
      const filename = this.generateUniqueFilename(file.name);
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('species_v2')
        .upload(filename, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        return { success: false, error: `Upload failed: ${uploadError.message}` };
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('species_v2')
        .getPublicUrl(filename);

      // Create photo record
      const photoRecord: TablesInsert<'photos'> = {
        url: urlData.publicUrl,
        hash: result.hash,
        title: result.metadata.title,
        description: result.metadata.notes,
        notes: result.metadata.notes,
        location: result.location,
        species_id: result.species_id,
        published: !result.needs_review, // Only publish if no review needed
        ...additionalMetadata
      };

      const { data: dbData, error: dbError } = await supabase
        .from('photos_v2')
        .insert(photoRecord)
        .select()
        .single();

      if (dbError) {
        // Clean up uploaded file
        await supabase.storage.from('species_v2').remove([filename]);
        return { success: false, error: `Database error: ${dbError.message}` };
      }

      return { success: true, photoId: dbData.id };

    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Generate unique filename for upload
   */
  private static generateUniqueFilename(originalName: string): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg';
    return `photos/${timestamp}-${randomString}.${extension}`;
  }
} 