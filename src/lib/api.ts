import { supabase } from "@/integrations/supabase/client";

// API base URL - will be the Supabase Edge Functions URL
const API_BASE = "https://raqxptgrugnmxdunbuty.supabase.co/functions/v1/api/v1";

// Types for API responses
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code?: string;
    details?: Record<string, unknown>;
  };
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    totalPages?: number;
  };
}

export interface Species {
  id: string;
  name: string;
  common_name?: string;
  scientific_name?: string;
  category: string;
  conservation_status?: string;
  description?: string;
  habitat?: string;
  diet?: string;
  behavior?: string;
  family?: string;
  size_cm?: number;
  weight_g?: number;
  lifespan_years?: number;
  migration_pattern?: string;
  breeding_season?: string;
  threat_level?: string;
  population_trend?: string;
  featured?: boolean;
  photo_count?: number;
  published?: boolean;
  size_description?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
  photos?: Photo[];
  // Enhanced geographic fields
  regions?: string;
  countries?: string[];
  states_provinces?: string[];
  geographic_scope?: 'global' | 'continental' | 'national' | 'regional' | 'local';
  primary_region?: string;
  habitat_specificity?: 'endemic' | 'specialized' | 'general' | 'widespread';
}

export interface Photo {
  id: string;
  url: string;
  title?: string;
  description?: string;
  photographer?: string;
  location?: string;
  species_id?: string;
  published: boolean;
  tags?: string[];
  camera_settings?: string;
  weather_conditions?: string;
  time_of_day?: string;
  notes?: string;
  date_taken?: string;
  created_at?: string;
  species?: {
    id: string;
    name: string;
    common_name?: string;
    category: string;
    conservation_status?: string;
  };
}

export interface SearchResult {
  species: Species[];
  photos: Photo[];
  meta: {
    query: string;
    type: string;
    page: number;
    limit: number;
    totalSpecies: number;
    totalPhotos: number;
  };
}

export interface Category {
  category: string;
  count: number;
}

export interface ConservationStatus {
  status: string;
  count: number;
}

// API service class
export class WildlifeAPI {
  private static async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${API_BASE}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: {
            message: data.error?.message || `HTTP ${response.status}`,
            code: data.error?.code,
            details: data.error?.details,
          },
        };
      }

      return data;
    } catch (error) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Network error',
        },
      };
    }
  }

  // Species endpoints
  static async getSpecies(params?: {
    page?: number;
    limit?: number;
    category?: string;
    conservation_status?: string;
    featured?: boolean;
    published?: boolean;
  }): Promise<ApiResponse<Species[]>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.category) searchParams.set('category', params.category);
    if (params?.conservation_status) searchParams.set('conservation_status', params.conservation_status);
    if (params?.featured !== undefined) searchParams.set('featured', params.featured.toString());
    if (params?.published !== undefined) searchParams.set('published', params.published.toString());

    const queryString = searchParams.toString();
    return this.makeRequest<Species[]>(`/species${queryString ? `?${queryString}` : ''}`);
  }

  static async getSpeciesById(id: string): Promise<ApiResponse<Species>> {
    return this.makeRequest<Species>(`/species/${id}`);
  }

  static async createSpecies(species: Partial<Species>): Promise<ApiResponse<Species>> {
    return this.makeRequest<Species>('/species', {
      method: 'POST',
      body: JSON.stringify(species),
    });
  }

  static async updateSpecies(id: string, species: Partial<Species>): Promise<ApiResponse<Species>> {
    return this.makeRequest<Species>(`/species/${id}`, {
      method: 'PUT',
      body: JSON.stringify(species),
    });
  }

  static async deleteSpecies(id: string): Promise<ApiResponse<{ message: string }>> {
    return this.makeRequest<{ message: string }>(`/species/${id}`, {
      method: 'DELETE',
    });
  }

  // Photo endpoints
  static async getPhotos(params?: {
    page?: number;
    limit?: number;
    species_id?: string;
    photographer?: string;
    location?: string;
    published?: boolean;
  }): Promise<ApiResponse<Photo[]>> {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.species_id) searchParams.set('species_id', params.species_id);
    if (params?.photographer) searchParams.set('photographer', params.photographer);
    if (params?.location) searchParams.set('location', params.location);
    if (params?.published !== undefined) searchParams.set('published', params.published.toString());

    const queryString = searchParams.toString();
    return this.makeRequest<Photo[]>(`/photos${queryString ? `?${queryString}` : ''}`);
  }

  static async getPhotoById(id: number): Promise<ApiResponse<Photo>> {
    return this.makeRequest<Photo>(`/photos/${id}`);
  }

  static async createPhoto(photo: Partial<Photo>): Promise<ApiResponse<Photo>> {
    return this.makeRequest<Photo>('/photos', {
      method: 'POST',
      body: JSON.stringify(photo),
    });
  }

  static async updatePhoto(id: number, photo: Partial<Photo>): Promise<ApiResponse<Photo>> {
    return this.makeRequest<Photo>(`/photos/${id}`, {
      method: 'PUT',
      body: JSON.stringify(photo),
    });
  }

  static async deletePhoto(id: string): Promise<ApiResponse<{ message: string }>> {
    return this.makeRequest<{ message: string }>(`/photos/${id}`, {
      method: 'DELETE',
    });
  }

  // Search endpoint
  static async search(params: {
    q: string;
    type?: 'species' | 'photos' | 'all';
    page?: number;
    limit?: number;
    category?: string;
    conservation_status?: string;
  }): Promise<ApiResponse<SearchResult>> {
    const searchParams = new URLSearchParams();
    searchParams.set('q', params.q);
    if (params.type) searchParams.set('type', params.type);
    if (params.page) searchParams.set('page', params.page.toString());
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.category) searchParams.set('category', params.category);
    if (params.conservation_status) searchParams.set('conservation_status', params.conservation_status);

    return this.makeRequest<SearchResult>(`/search?${searchParams.toString()}`);
  }

  // Metadata endpoints
  static async getCategories(): Promise<ApiResponse<Category[]>> {
    return this.makeRequest<Category[]>('/categories');
  }

  static async getConservationStatuses(): Promise<ApiResponse<ConservationStatus[]>> {
    return this.makeRequest<ConservationStatus[]>('/conservation-statuses');
  }
}

// Convenience functions for common operations
export const api = {
  // Species
  getSpecies: WildlifeAPI.getSpecies,
  getSpeciesById: WildlifeAPI.getSpeciesById,
  createSpecies: WildlifeAPI.createSpecies,
  updateSpecies: WildlifeAPI.updateSpecies,
  deleteSpecies: WildlifeAPI.deleteSpecies,

  // Photos
  getPhotos: WildlifeAPI.getPhotos,
  getPhotoById: WildlifeAPI.getPhotoById,
  createPhoto: WildlifeAPI.createPhoto,
  updatePhoto: WildlifeAPI.updatePhoto,
  deletePhoto: WildlifeAPI.deletePhoto,

  // Search
  search: WildlifeAPI.search,

  // Metadata
  getCategories: WildlifeAPI.getCategories,
  getConservationStatuses: WildlifeAPI.getConservationStatuses,
}; 