import { toast } from 'sonner';

// Error types for better categorization
export enum ErrorType {
  NETWORK = 'NETWORK',
  DATABASE = 'DATABASE',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  PERMISSION = 'PERMISSION',
  NOT_FOUND = 'NOT_FOUND',
  UNKNOWN = 'UNKNOWN'
}

// Custom error class with additional context
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly code?: string;
  public readonly context?: Record<string, unknown>;
  public readonly timestamp: Date;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    code?: string,
    context?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.code = code;
    this.context = context;
    this.timestamp = new Date();
  }
}

// Error classification utility
export const classifyError = (error: unknown): AppError => {
  if (error instanceof AppError) {
    return error;
  }

  if (error instanceof Error) {
    // Supabase errors
    if (error.message.includes('JWT')) {
      return new AppError(
        'Authentication required. Please log in again.',
        ErrorType.AUTHENTICATION,
        'AUTH_EXPIRED',
        { originalError: error.message }
      );
    }

    if (error.message.includes('permission')) {
      return new AppError(
        'You do not have permission to perform this action.',
        ErrorType.PERMISSION,
        'INSUFFICIENT_PERMISSIONS',
        { originalError: error.message }
      );
    }

    if (error.message.includes('not found') || error.message.includes('404')) {
      return new AppError(
        'The requested resource was not found.',
        ErrorType.NOT_FOUND,
        'RESOURCE_NOT_FOUND',
        { originalError: error.message }
      );
    }

    if (error.message.includes('network') || error.message.includes('fetch')) {
      return new AppError(
        'Network error. Please check your connection and try again.',
        ErrorType.NETWORK,
        'NETWORK_ERROR',
        { originalError: error.message }
      );
    }

    // Database errors
    if (error.message.includes('duplicate') || error.message.includes('unique')) {
      return new AppError(
        'This record already exists.',
        ErrorType.DATABASE,
        'DUPLICATE_RECORD',
        { originalError: error.message }
      );
    }

    // Generic error
    return new AppError(
      error.message,
      ErrorType.UNKNOWN,
      'GENERIC_ERROR',
      { originalError: error.message }
    );
  }

  // Non-Error objects
  return new AppError(
    'An unexpected error occurred.',
    ErrorType.UNKNOWN,
    'UNKNOWN_ERROR',
    { originalError: String(error) }
  );
};

// User-friendly error messages
export const getUserFriendlyMessage = (error: AppError): string => {
  switch (error.type) {
    case ErrorType.NETWORK:
      return 'Connection problem. Please check your internet and try again.';
    case ErrorType.DATABASE:
      return 'Database error. Please try again or contact support.';
    case ErrorType.VALIDATION:
      return error.message; // Validation messages are usually user-friendly
    case ErrorType.AUTHENTICATION:
      return 'Please log in to continue.';
    case ErrorType.PERMISSION:
      return 'You do not have permission for this action.';
    case ErrorType.NOT_FOUND:
      return 'The requested item was not found.';
    default:
      return 'Something went wrong. Please try again.';
  }
};

// Error logging utility
export const logError = (error: AppError, context?: Record<string, unknown>): void => {
  const logData = {
    message: error.message,
    type: error.type,
    code: error.code,
    timestamp: error.timestamp,
    context: { ...error.context, ...context },
    stack: error.stack
  };

  // In development, log to console
  if (import.meta.env.DEV) {
    console.error('🚨 Application Error:', logData);
  }

  // In production, you might want to send to an error tracking service
  // Example: Sentry, LogRocket, etc.
  if (import.meta.env.PROD) {
    // sendToErrorTrackingService(logData);
  }
};

// Toast notification utility
export const showErrorToast = (error: AppError): void => {
  const message = getUserFriendlyMessage(error);
  
  toast.error(message, {
    description: import.meta.env.DEV ? error.message : undefined,
    duration: 5000,
  });
};

// Success toast utility
export const showSuccessToast = (message: string, description?: string): void => {
  toast.success(message, {
    description,
    duration: 3000,
  });
};

// Warning toast utility
export const showWarningToast = (message: string, description?: string): void => {
  toast.warning(message, {
    description,
    duration: 4000,
  });
};

// Async error handler wrapper
export const withErrorHandling = <T extends unknown[], R>(
  fn: (...args: T) => Promise<R>,
  context?: Record<string, unknown>
) => {
  return async (...args: T): Promise<R | undefined> => {
    try {
      return await fn(...args);
    } catch (error) {
      const appError = classifyError(error);
      logError(appError, context);
      showErrorToast(appError);
      return undefined;
    }
  };
};

// Sync error handler wrapper
export const withSyncErrorHandling = <T extends unknown[], R>(
  fn: (...args: T) => R,
  context?: Record<string, unknown>
) => {
  return (...args: T): R | undefined => {
    try {
      return fn(...args);
    } catch (error) {
      const appError = classifyError(error);
      logError(appError, context);
      showErrorToast(appError);
      return undefined;
    }
  };
};

// Validation error helper
export const createValidationError = (field: string, message: string): AppError => {
  return new AppError(
    message,
    ErrorType.VALIDATION,
    'VALIDATION_ERROR',
    { field }
  );
};

// Network error helper
export const createNetworkError = (operation: string): AppError => {
  return new AppError(
    `Failed to ${operation}. Please check your connection.`,
    ErrorType.NETWORK,
    'NETWORK_ERROR',
    { operation }
  );
};

// Database error helper
export const createDatabaseError = (operation: string, details?: string): AppError => {
  return new AppError(
    `Database error during ${operation}.`,
    ErrorType.DATABASE,
    'DATABASE_ERROR',
    { operation, details }
  );
};

// Error boundary helper
export const handleErrorBoundary = (error: Error, errorInfo: { componentStack: string }): void => {
  const appError = new AppError(
    'A component error occurred',
    ErrorType.UNKNOWN,
    'COMPONENT_ERROR',
    {
      originalError: error.message,
      componentStack: errorInfo.componentStack,
      stack: error.stack
    }
  );

  logError(appError);
  
  // Don't show toast in error boundary as it might cause infinite loops
  // The error boundary component should handle UI feedback
};
