import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(import.meta.env.VITE_GEMINI_API_KEY);

export interface AISpeciesIdentification {
  speciesName: string;
  scientificName?: string;
  confidence: number; // 0-100
  reasoning: string;
  category?: string; // bird, mammal, reptile, etc.
}

export interface AIAnalysisResult {
  identifications: AISpeciesIdentification[];
  analysisNotes: string;
  imageQuality: 'excellent' | 'good' | 'fair' | 'poor';
}

/**
 * Analyze a photo using Google Gemini Vision API to identify wildlife species
 */
export async function analyzePhotoWithAI(imageUrl: string): Promise<AIAnalysisResult> {
  try {
    // Get the generative model
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    // Fetch the image
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.statusText}`);
    }

    const imageBuffer = await response.arrayBuffer();

    // Convert ArrayBuffer to base64 using browser-compatible method
    const uint8Array = new Uint8Array(imageBuffer);
    let binaryString = '';
    for (let i = 0; i < uint8Array.length; i++) {
      binaryString += String.fromCharCode(uint8Array[i]);
    }
    const base64String = btoa(binaryString);

    const imageData = {
      inlineData: {
        data: base64String,
        mimeType: response.headers.get('content-type') || 'image/jpeg'
      }
    };

    // Create a detailed prompt for wildlife identification
    const prompt = `You are an expert ornithologist and wildlife photographer with decades of field experience. Analyze this image systematically to identify the bird species.

IDENTIFICATION PROCESS:
1. First, determine the bird family/group (hummingbird, hawk, songbird, etc.)
2. Note key physical features: size, bill shape, coloration patterns, tail shape, wing shape
3. Pay special attention to diagnostic features like crown color, throat patches, wing bars, tail patterns
4. Consider geographic range and habitat preferences
5. Compare against similar species to rule out confusion species

For HUMMINGBIRDS specifically, focus on:
- Crown coloration (blue, violet, green, etc.)
- Throat patch color and pattern
- Overall body coloration and iridescence
- Bill length and curvature
- Tail shape and coloration
- Size relative to other hummingbirds

Please provide your analysis in the following JSON format:
{
  "identifications": [
    {
      "speciesName": "Common name of the species",
      "scientificName": "Scientific name if known",
      "confidence": 85,
      "reasoning": "Step-by-step analysis of diagnostic features observed",
      "category": "bird/mammal/reptile/amphibian/fish/insect/other",
      "keyFeatures": ["feature1", "feature2", "feature3"],
      "similarSpecies": "Species this could be confused with and why ruled out"
    }
  ],
  "analysisNotes": "Systematic observations about size, behavior, habitat context",
  "imageQuality": "excellent/good/fair/poor"
}

CONFIDENCE SCORING:
- 95-100: Absolutely certain, unmistakable diagnostic features visible
- 85-94: Very confident, multiple diagnostic features clearly visible
- 70-84: Confident, key features visible but some uncertainty remains
- 50-69: Reasonable identification but significant uncertainty
- Below 50: Uncertain, could be multiple species

Focus on Central and South American hummingbird species, particularly those found in Costa Rica, Colombia, Ecuador, and surrounding tropical regions. Be especially careful with violet-eared hummingbirds, green-crowned brilliants, and similar iridescent species.`;

    // Generate content
    const result = await model.generateContent([prompt, imageData]);
    const responseText = result.response.text();

    // Parse the JSON response
    try {
      // Extract JSON from the response (in case there's extra text)
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in AI response');
      }

      const analysisResult: AIAnalysisResult = JSON.parse(jsonMatch[0]);
      
      // Validate the response structure
      if (!analysisResult.identifications || !Array.isArray(analysisResult.identifications)) {
        throw new Error('Invalid AI response structure');
      }

      // Ensure confidence scores are within valid range
      analysisResult.identifications = analysisResult.identifications.map(id => ({
        ...id,
        confidence: Math.max(0, Math.min(100, id.confidence))
      }));

      return analysisResult;

    } catch (parseError) {
      console.error('Failed to parse AI response:', parseError);
      console.error('Raw AI response:', responseText);
      
      // Return a fallback response
      return {
        identifications: [],
        analysisNotes: 'AI analysis failed to parse response properly',
        imageQuality: 'fair'
      };
    }

  } catch (error) {
    console.error('AI analysis error:', error);
    
    // Return empty result on error
    return {
      identifications: [],
      analysisNotes: `Analysis failed: ${error.message}`,
      imageQuality: 'poor'
    };
  }
}

/**
 * Enhanced multi-pass analysis for better accuracy
 */
export async function enhancedAnalyzePhoto(imageUrl: string): Promise<AIAnalysisResult> {
  console.log('🔍 Starting enhanced multi-pass analysis...');

  try {
    // Pass 1: General identification
    console.log('📋 Pass 1: General identification');
    const generalResult = await analyzePhotoWithAI(imageUrl);

    // Pass 2: Focused analysis based on first result
    if (generalResult.identifications.length > 0) {
      const firstId = generalResult.identifications[0];
      console.log(`📋 Pass 2: Focused ${firstId.category} analysis`);

      const focusedResult = await analyzeWithFocusedPrompt(imageUrl, firstId.category, firstId.speciesName);

      // Pass 3: Challenge the identification for hummingbirds
      if (firstId.category === 'bird' && (firstId.speciesName.toLowerCase().includes('hummingbird') || focusedResult.identifications[0]?.speciesName.toLowerCase().includes('hummingbird'))) {
        console.log(`📋 Pass 3: Challenge hummingbird identification`);
        const challengeResult = await challengeHummingbirdID(imageUrl, focusedResult.identifications[0]?.speciesName || firstId.speciesName);

        // Return the challenge result if it has higher confidence
        if (challengeResult.identifications.length > 0 && challengeResult.identifications[0].confidence >= focusedResult.identifications[0]?.confidence) {
          return challengeResult;
        }
      }

      // Combine results and return the most confident
      const allResults = [...generalResult.identifications, ...focusedResult.identifications];
      const bestResult = allResults.reduce((best, current) =>
        current.confidence > best.confidence ? current : best
      );

      return {
        identifications: [bestResult],
        analysisNotes: `Multi-pass analysis: ${generalResult.analysisNotes} | Focused: ${focusedResult.analysisNotes}`,
        imageQuality: generalResult.imageQuality
      };
    }

    return generalResult;

  } catch (error) {
    console.error('Enhanced analysis failed:', error);
    return await analyzePhotoWithAI(imageUrl); // Fallback to single pass
  }
}

/**
 * Focused analysis with category-specific prompts
 */
async function analyzeWithFocusedPrompt(imageUrl: string, category: string, initialGuess: string): Promise<AIAnalysisResult> {
  const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

  // Fetch the image (reuse the same logic)
  const response = await fetch(imageUrl);
  if (!response.ok) {
    throw new Error(`Failed to fetch image: ${response.statusText}`);
  }

  const imageBuffer = await response.arrayBuffer();
  const uint8Array = new Uint8Array(imageBuffer);
  let binaryString = '';
  for (let i = 0; i < uint8Array.length; i++) {
    binaryString += String.fromCharCode(uint8Array[i]);
  }
  const base64String = btoa(binaryString);

  const imageData = {
    inlineData: {
      data: base64String,
      mimeType: response.headers.get('content-type') || 'image/jpeg'
    }
  };

  let focusedPrompt = '';

  if (category === 'bird' && initialGuess.toLowerCase().includes('hummingbird')) {
    focusedPrompt = `You are a hummingbird specialist. The initial analysis suggested this might be a "${initialGuess}".

CRITICAL HUMMINGBIRD IDENTIFICATION FEATURES:
1. Crown color and pattern (solid blue, violet, green, or mixed)
2. Throat patch (gorget) - color, shape, extent
3. Body coloration - back, sides, belly
4. Tail shape and color
5. Bill characteristics
6. Overall size and proportions

VIOLET-EARED HUMMINGBIRDS specifically:
- Look for violet/purple ear patches or crown
- Note if violet extends to throat or is limited to ears/crown
- Check for green body coloration
- Observe tail coloration and shape

CRITICAL SPECIES DISTINCTIONS:
- **Sparkling Violetear**: Violet-blue crown AND throat, green body, forked tail, medium size
- **Green Violetear**: Green body with violet ear patches only, no violet throat
- **Violet-throated Hummingbird**: Small size, violet throat but greenish crown
- **Crowned Woodnymph**: Blue crown, green body, no violet
- **Green-crowned Brilliant**: Large, green crown, white throat patch

PAY SPECIAL ATTENTION TO:
1. Crown color (violet-blue vs green vs blue)
2. Throat color (violet vs green vs white)
3. Overall size (small vs medium vs large)
4. Tail shape (forked vs straight vs notched)

Provide detailed analysis focusing on these specific features. Be very precise about coloration patterns.`;
  } else {
    focusedPrompt = `You are a specialist in ${category} identification. The initial analysis suggested "${initialGuess}".

Please provide a more detailed, focused analysis of this ${category}, paying special attention to diagnostic features that distinguish between similar species. Consider field marks, proportions, and any behavioral cues visible in the image.`;
  }

  const fullPrompt = focusedPrompt + `

Return JSON format:
{
  "identifications": [
    {
      "speciesName": "Most likely species name",
      "scientificName": "Scientific name",
      "confidence": 85,
      "reasoning": "Detailed feature-by-feature analysis",
      "category": "${category}",
      "keyFeatures": ["specific features observed"],
      "similarSpecies": "Why other similar species were ruled out"
    }
  ],
  "analysisNotes": "Focused analysis notes",
  "imageQuality": "excellent/good/fair/poor"
}`;

  const result = await model.generateContent([fullPrompt, imageData]);
  const responseText = result.response.text();

  try {
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in focused analysis response');
    }
    return JSON.parse(jsonMatch[0]);
  } catch (parseError) {
    console.error('Failed to parse focused analysis:', parseError);
    return {
      identifications: [],
      analysisNotes: 'Focused analysis failed to parse',
      imageQuality: 'fair'
    };
  }
}

/**
 * Challenge hummingbird identification with specific alternatives
 */
async function challengeHummingbirdID(imageUrl: string, currentGuess: string): Promise<AIAnalysisResult> {
  const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

  // Fetch the image (reuse the same logic)
  const response = await fetch(imageUrl);
  if (!response.ok) {
    throw new Error(`Failed to fetch image: ${response.statusText}`);
  }

  const imageBuffer = await response.arrayBuffer();
  const uint8Array = new Uint8Array(imageBuffer);
  let binaryString = '';
  for (let i = 0; i < uint8Array.length; i++) {
    binaryString += String.fromCharCode(uint8Array[i]);
  }
  const base64String = btoa(binaryString);

  const imageData = {
    inlineData: {
      data: base64String,
      mimeType: response.headers.get('content-type') || 'image/jpeg'
    }
  };

  const challengePrompt = `You identified this hummingbird as "${currentGuess}". However, I want you to RECONSIDER this identification by carefully examining these specific alternatives:

**OPTION A: Sparkling Violetear (Colibri coruscans)**
- Violet-blue crown AND throat (both violet)
- Green iridescent body
- Medium size hummingbird
- Slightly forked tail
- Found in Andes mountains

**OPTION B: Violet-throated Hummingbird (Klais guimeti)**
- Violet throat but GREENISH crown (not violet crown)
- Small size
- Green body
- Found in Central America

**OPTION C: Green Violetear (Colibri thalassinus)**
- Green body with violet EAR PATCHES only
- NO violet throat (throat is green)
- Larger size

**CRITICAL QUESTION**: Look at the crown AND throat together:
- If BOTH crown AND throat are violet/purple → Sparkling Violetear
- If throat is violet but crown is greenish → Violet-throated Hummingbird
- If only ear patches are violet → Green Violetear

Examine the image again and determine:
1. Is the crown violet-blue or greenish?
2. Is the throat violet or green?
3. What is the overall size?

Return JSON with your RECONSIDERED identification:
{
  "identifications": [
    {
      "speciesName": "Your reconsidered species name",
      "scientificName": "Scientific name",
      "confidence": 85,
      "reasoning": "Why you changed or confirmed your identification",
      "category": "bird",
      "keyFeatures": ["crown color", "throat color", "size"],
      "similarSpecies": "Why other options were ruled out"
    }
  ],
  "analysisNotes": "Reconsideration analysis",
  "imageQuality": "excellent/good/fair/poor"
}`;

  const result = await model.generateContent([challengePrompt, imageData]);
  const responseText = result.response.text();

  try {
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in challenge response');
    }
    return JSON.parse(jsonMatch[0]);
  } catch (parseError) {
    console.error('Failed to parse challenge analysis:', parseError);
    return {
      identifications: [],
      analysisNotes: 'Challenge analysis failed to parse',
      imageQuality: 'fair'
    };
  }
}

/**
 * Test the AI analysis with a single photo
 */
export async function testAIAnalysis(imageUrl: string): Promise<void> {
  console.log('🔍 Testing AI analysis on:', imageUrl);

  try {
    const result = await enhancedAnalyzePhoto(imageUrl);
    console.log('✅ Enhanced AI Analysis Result:', result);

    if (result.identifications.length > 0) {
      console.log('🎯 Species identified:');
      result.identifications.forEach((id, index) => {
        console.log(`  ${index + 1}. ${id.speciesName} (${id.confidence}% confidence)`);
        console.log(`     Scientific: ${id.scientificName || 'Unknown'}`);
        console.log(`     Category: ${id.category || 'Unknown'}`);
        console.log(`     Key Features: ${id.keyFeatures?.join(', ') || 'None listed'}`);
        console.log(`     Similar Species: ${id.similarSpecies || 'None noted'}`);
        console.log(`     Reasoning: ${id.reasoning}`);
      });
    } else {
      console.log('❌ No species identified');
    }

    console.log('📝 Analysis Notes:', result.analysisNotes);
    console.log('📸 Image Quality:', result.imageQuality);

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

/**
 * Enhanced logging for detailed AI analysis
 */
export function logDetailedAnalysis(result: AIAnalysisResult, photoId: string, imageUrl: string): void {
  console.log(`\n🔬 DETAILED AI ANALYSIS FOR PHOTO ${photoId}`);
  console.log(`📸 Image URL: ${imageUrl}`);
  console.log(`📊 Image Quality: ${result.imageQuality}`);
  console.log(`📝 Analysis Notes: ${result.analysisNotes}`);

  if (result.identifications.length > 0) {
    console.log(`\n🎯 SPECIES IDENTIFICATIONS (${result.identifications.length}):`);

    result.identifications.forEach((id, index) => {
      console.log(`\n--- IDENTIFICATION ${index + 1} ---`);
      console.log(`🏷️  Species: ${id.speciesName}`);
      console.log(`🧬 Scientific: ${id.scientificName || 'Not provided'}`);
      console.log(`📂 Category: ${id.category || 'Not specified'}`);
      console.log(`📊 Confidence: ${id.confidence}%`);

      if (id.keyFeatures && id.keyFeatures.length > 0) {
        console.log(`🔍 Key Features:`);
        id.keyFeatures.forEach((feature, i) => {
          console.log(`   ${i + 1}. ${feature}`);
        });
      }

      if (id.similarSpecies) {
        console.log(`🔄 Similar Species Analysis: ${id.similarSpecies}`);
      }

      if (id.reasoning) {
        console.log(`💭 Reasoning: ${id.reasoning}`);
      }
    });
  } else {
    console.log(`\n❌ NO SPECIES IDENTIFIED`);
  }

  console.log(`\n${'='.repeat(80)}\n`);
}
