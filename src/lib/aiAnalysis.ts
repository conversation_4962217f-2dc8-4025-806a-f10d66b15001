import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(import.meta.env.VITE_GEMINI_API_KEY);

export interface AISpeciesIdentification {
  speciesName: string;
  scientificName?: string;
  confidence: number; // 0-100
  reasoning: string;
  category?: string; // bird, mammal, reptile, etc.
}

export interface AIAnalysisResult {
  identifications: AISpeciesIdentification[];
  analysisNotes: string;
  imageQuality: 'excellent' | 'good' | 'fair' | 'poor';
}

/**
 * Analyze a photo using Google Gemini Vision API to identify wildlife species
 */
export async function analyzePhotoWithAI(imageUrl: string): Promise<AIAnalysisResult> {
  try {
    // Get the more powerful generative model for better accuracy
    const model = genAI.getGenerativeModel({
      model: 'gemini-1.5-pro',
      generationConfig: {
        temperature: 0.1, // Lower temperature for more consistent, accurate results
        topP: 0.8,
        topK: 40,
        maxOutputTokens: 2048,
      }
    });

    // Fetch the image
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.statusText}`);
    }

    const imageBuffer = await response.arrayBuffer();

    // Convert ArrayBuffer to base64 using browser-compatible method
    const uint8Array = new Uint8Array(imageBuffer);
    let binaryString = '';
    for (let i = 0; i < uint8Array.length; i++) {
      binaryString += String.fromCharCode(uint8Array[i]);
    }
    const base64String = btoa(binaryString);

    const imageData = {
      inlineData: {
        data: base64String,
        mimeType: response.headers.get('content-type') || 'image/jpeg'
      }
    };

    // Create a detailed prompt for wildlife identification with enhanced accuracy
    const prompt = `You are a world-renowned wildlife identification expert with 30+ years of field experience across Central and South America. Your specialty is precise species identification using diagnostic field marks.

SYSTEMATIC IDENTIFICATION PROTOCOL:
1. **Image Quality Assessment**: Evaluate lighting, focus, angle, and visible features
2. **Taxonomic Classification**: Determine order, family, and subfamily first
3. **Morphological Analysis**: Document size, proportions, and structural features
4. **Diagnostic Features**: Identify species-specific field marks
5. **Geographic Context**: Consider known ranges and habitat preferences
6. **Differential Diagnosis**: Compare with similar species and rule out alternatives

CRITICAL ANALYSIS AREAS:

**For HUMMINGBIRDS (Priority focus):**
- **Crown**: Exact color (violet-blue, green, blue, mixed), pattern, extent
- **Throat (Gorget)**: Color, shape, iridescence pattern, extent to chest
- **Body**: Back color, side coloration, belly markings, overall iridescence
- **Bill**: Length, curvature, color (black, red, yellow)
- **Tail**: Shape (forked, square, rounded), color, pattern, relative length
- **Size**: Relative to other hummingbirds (tiny, small, medium, large)
- **Ear patches**: Presence and color of violet ear markings

**For BIRDS generally:**
- Bill shape and size (indicates feeding behavior)
- Wing patterns (bars, patches, solid colors)
- Tail patterns and length
- Leg color and length
- Eye color and orbital ring
- Overall proportions and posture

**GEOGRAPHIC FOCUS**: Central and South American species, especially:
- Costa Rica, Colombia, Ecuador, Peru, Venezuela
- Cloud forest and montane species
- Tropical lowland species

**CRITICAL SPECIES GROUPS** (pay special attention):
- **Violet-eared hummingbirds**: Sparkling Violetear vs Green Violetear vs Violet-throated
- **Brilliant hummingbirds**: Green-crowned Brilliant vs other brilliants
- **Emerald hummingbirds**: Various Amazilia species
- **Woodnymphs**: Crowned Woodnymph vs other woodnymphs

Provide analysis in this EXACT JSON format:
{
  "identifications": [
    {
      "speciesName": "Exact common name",
      "scientificName": "Binomial scientific name",
      "confidence": 85,
      "reasoning": "Detailed step-by-step analysis of each diagnostic feature observed, explaining why this species and not similar ones",
      "category": "bird/mammal/reptile/amphibian/fish/insect/other",
      "keyFeatures": ["specific feature 1", "specific feature 2", "specific feature 3"],
      "similarSpecies": "List similar species considered and specific reasons why they were ruled out",
      "habitat": "Observed or inferred habitat type",
      "behavior": "Any behavioral cues visible",
      "size": "Relative size assessment"
    }
  ],
  "analysisNotes": "Systematic observations about image quality, lighting, angle, and overall assessment",
  "imageQuality": "excellent/good/fair/poor"
}

**CONFIDENCE SCORING** (be conservative):
- 95-100: Absolutely certain, multiple unmistakable diagnostic features clearly visible
- 85-94: Very confident, key diagnostic features clearly visible, minimal uncertainty
- 70-84: Confident, important features visible but some ambiguity remains
- 50-69: Reasonable identification but significant uncertainty due to image quality or similar species
- 30-49: Uncertain, could be multiple species, requires better image
- Below 30: Cannot reliably identify, too many possibilities

**IMPORTANT**: Be extremely careful with similar species. If uncertain between two species, choose the lower confidence score and explain the uncertainty clearly.`;

    // Generate content
    const result = await model.generateContent([prompt, imageData]);
    const responseText = result.response.text();

    // Parse the JSON response
    try {
      // Extract JSON from the response (in case there's extra text)
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in AI response');
      }

      const analysisResult: AIAnalysisResult = JSON.parse(jsonMatch[0]);
      
      // Validate the response structure
      if (!analysisResult.identifications || !Array.isArray(analysisResult.identifications)) {
        throw new Error('Invalid AI response structure');
      }

      // Ensure confidence scores are within valid range
      analysisResult.identifications = analysisResult.identifications.map(id => ({
        ...id,
        confidence: Math.max(0, Math.min(100, id.confidence))
      }));

      return analysisResult;

    } catch (parseError) {
      console.error('Failed to parse AI response:', parseError);
      console.error('Raw AI response:', responseText);
      
      // Return a fallback response
      return {
        identifications: [],
        analysisNotes: 'AI analysis failed to parse response properly',
        imageQuality: 'fair'
      };
    }

  } catch (error) {
    console.error('AI analysis error:', error);
    
    // Return empty result on error
    return {
      identifications: [],
      analysisNotes: `Analysis failed: ${error.message}`,
      imageQuality: 'poor'
    };
  }
}

/**
 * Enhanced multi-pass analysis for better accuracy
 */
export async function enhancedAnalyzePhoto(imageUrl: string): Promise<AIAnalysisResult> {
  console.log('🔍 Starting enhanced multi-pass analysis...');

  try {
    // Pass 1: General identification
    console.log('📋 Pass 1: General identification');
    const generalResult = await analyzePhotoWithAI(imageUrl);

    // Pass 2: Focused analysis based on first result
    if (generalResult.identifications.length > 0) {
      const firstId = generalResult.identifications[0];
      console.log(`📋 Pass 2: Focused ${firstId.category} analysis`);

      const focusedResult = await analyzeWithFocusedPrompt(imageUrl, firstId.category, firstId.speciesName);

      // Pass 3: Challenge the identification for hummingbirds
      if (firstId.category === 'bird' && (firstId.speciesName.toLowerCase().includes('hummingbird') || focusedResult.identifications[0]?.speciesName.toLowerCase().includes('hummingbird'))) {
        console.log(`📋 Pass 3: Challenge hummingbird identification`);
        const challengeResult = await challengeHummingbirdID(imageUrl, focusedResult.identifications[0]?.speciesName || firstId.speciesName);

        // Return the challenge result if it has higher confidence
        if (challengeResult.identifications.length > 0 && challengeResult.identifications[0].confidence >= focusedResult.identifications[0]?.confidence) {
          return challengeResult;
        }
      }

      // Combine results and return the most confident
      const allResults = [...generalResult.identifications, ...focusedResult.identifications];
      const bestResult = allResults.reduce((best, current) =>
        current.confidence > best.confidence ? current : best
      );

      return {
        identifications: [bestResult],
        analysisNotes: `Multi-pass analysis: ${generalResult.analysisNotes} | Focused: ${focusedResult.analysisNotes}`,
        imageQuality: generalResult.imageQuality
      };
    }

    return generalResult;

  } catch (error) {
    console.error('Enhanced analysis failed:', error);
    return await analyzePhotoWithAI(imageUrl); // Fallback to single pass
  }
}

/**
 * Focused analysis with category-specific prompts
 */
async function analyzeWithFocusedPrompt(imageUrl: string, category: string, initialGuess: string): Promise<AIAnalysisResult> {
  const model = genAI.getGenerativeModel({
    model: 'gemini-1.5-pro',
    generationConfig: {
      temperature: 0.1,
      topP: 0.8,
      topK: 40,
      maxOutputTokens: 2048,
    }
  });

  // Fetch the image (reuse the same logic)
  const response = await fetch(imageUrl);
  if (!response.ok) {
    throw new Error(`Failed to fetch image: ${response.statusText}`);
  }

  const imageBuffer = await response.arrayBuffer();
  const uint8Array = new Uint8Array(imageBuffer);
  let binaryString = '';
  for (let i = 0; i < uint8Array.length; i++) {
    binaryString += String.fromCharCode(uint8Array[i]);
  }
  const base64String = btoa(binaryString);

  const imageData = {
    inlineData: {
      data: base64String,
      mimeType: response.headers.get('content-type') || 'image/jpeg'
    }
  };

  let focusedPrompt = '';

  if (category === 'bird' && initialGuess.toLowerCase().includes('hummingbird')) {
    focusedPrompt = `You are a hummingbird specialist. The initial analysis suggested this might be a "${initialGuess}".

CRITICAL HUMMINGBIRD IDENTIFICATION FEATURES:
1. Crown color and pattern (solid blue, violet, green, or mixed)
2. Throat patch (gorget) - color, shape, extent
3. Body coloration - back, sides, belly
4. Tail shape and color
5. Bill characteristics
6. Overall size and proportions

VIOLET-EARED HUMMINGBIRDS specifically:
- Look for violet/purple ear patches or crown
- Note if violet extends to throat or is limited to ears/crown
- Check for green body coloration
- Observe tail coloration and shape

CRITICAL SPECIES DISTINCTIONS:
- **Sparkling Violetear**: Violet-blue crown AND throat, green body, forked tail, medium size
- **Green Violetear**: Green body with violet ear patches only, no violet throat
- **Violet-throated Hummingbird**: Small size, violet throat but greenish crown
- **Crowned Woodnymph**: Blue crown, green body, no violet
- **Green-crowned Brilliant**: Large, green crown, white throat patch

PAY SPECIAL ATTENTION TO:
1. Crown color (violet-blue vs green vs blue)
2. Throat color (violet vs green vs white)
3. Overall size (small vs medium vs large)
4. Tail shape (forked vs straight vs notched)

Provide detailed analysis focusing on these specific features. Be very precise about coloration patterns.`;
  } else {
    focusedPrompt = `You are a specialist in ${category} identification. The initial analysis suggested "${initialGuess}".

Please provide a more detailed, focused analysis of this ${category}, paying special attention to diagnostic features that distinguish between similar species. Consider field marks, proportions, and any behavioral cues visible in the image.`;
  }

  const fullPrompt = focusedPrompt + `

Return JSON format:
{
  "identifications": [
    {
      "speciesName": "Most likely species name",
      "scientificName": "Scientific name",
      "confidence": 85,
      "reasoning": "Detailed feature-by-feature analysis",
      "category": "${category}",
      "keyFeatures": ["specific features observed"],
      "similarSpecies": "Why other similar species were ruled out"
    }
  ],
  "analysisNotes": "Focused analysis notes",
  "imageQuality": "excellent/good/fair/poor"
}`;

  const result = await model.generateContent([fullPrompt, imageData]);
  const responseText = result.response.text();

  try {
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in focused analysis response');
    }
    return JSON.parse(jsonMatch[0]);
  } catch (parseError) {
    console.error('Failed to parse focused analysis:', parseError);
    return {
      identifications: [],
      analysisNotes: 'Focused analysis failed to parse',
      imageQuality: 'fair'
    };
  }
}

/**
 * Challenge hummingbird identification with specific alternatives
 */
async function challengeHummingbirdID(imageUrl: string, currentGuess: string): Promise<AIAnalysisResult> {
  const model = genAI.getGenerativeModel({
    model: 'gemini-1.5-pro',
    generationConfig: {
      temperature: 0.1,
      topP: 0.8,
      topK: 40,
      maxOutputTokens: 2048,
    }
  });

  // Fetch the image (reuse the same logic)
  const response = await fetch(imageUrl);
  if (!response.ok) {
    throw new Error(`Failed to fetch image: ${response.statusText}`);
  }

  const imageBuffer = await response.arrayBuffer();
  const uint8Array = new Uint8Array(imageBuffer);
  let binaryString = '';
  for (let i = 0; i < uint8Array.length; i++) {
    binaryString += String.fromCharCode(uint8Array[i]);
  }
  const base64String = btoa(binaryString);

  const imageData = {
    inlineData: {
      data: base64String,
      mimeType: response.headers.get('content-type') || 'image/jpeg'
    }
  };

  const challengePrompt = `You identified this hummingbird as "${currentGuess}". However, I want you to RECONSIDER this identification by carefully examining these specific alternatives:

**OPTION A: Sparkling Violetear (Colibri coruscans)**
- Violet-blue crown AND throat (both violet)
- Green iridescent body
- Medium size hummingbird
- Slightly forked tail
- Found in Andes mountains

**OPTION B: Violet-throated Hummingbird (Klais guimeti)**
- Violet throat but GREENISH crown (not violet crown)
- Small size
- Green body
- Found in Central America

**OPTION C: Green Violetear (Colibri thalassinus)**
- Green body with violet EAR PATCHES only
- NO violet throat (throat is green)
- Larger size

**CRITICAL QUESTION**: Look at the crown AND throat together:
- If BOTH crown AND throat are violet/purple → Sparkling Violetear
- If throat is violet but crown is greenish → Violet-throated Hummingbird
- If only ear patches are violet → Green Violetear

Examine the image again and determine:
1. Is the crown violet-blue or greenish?
2. Is the throat violet or green?
3. What is the overall size?

Return JSON with your RECONSIDERED identification:
{
  "identifications": [
    {
      "speciesName": "Your reconsidered species name",
      "scientificName": "Scientific name",
      "confidence": 85,
      "reasoning": "Why you changed or confirmed your identification",
      "category": "bird",
      "keyFeatures": ["crown color", "throat color", "size"],
      "similarSpecies": "Why other options were ruled out"
    }
  ],
  "analysisNotes": "Reconsideration analysis",
  "imageQuality": "excellent/good/fair/poor"
}`;

  const result = await model.generateContent([challengePrompt, imageData]);
  const responseText = result.response.text();

  try {
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in challenge response');
    }
    return JSON.parse(jsonMatch[0]);
  } catch (parseError) {
    console.error('Failed to parse challenge analysis:', parseError);
    return {
      identifications: [],
      analysisNotes: 'Challenge analysis failed to parse',
      imageQuality: 'fair'
    };
  }
}

/**
 * Test the AI analysis with a single photo
 */
export async function testAIAnalysis(imageUrl: string): Promise<void> {
  console.log('🔍 Testing AI analysis on:', imageUrl);

  try {
    const result = await enhancedAnalyzePhoto(imageUrl);
    console.log('✅ Enhanced AI Analysis Result:', result);

    if (result.identifications.length > 0) {
      console.log('🎯 Species identified:');
      result.identifications.forEach((id, index) => {
        console.log(`  ${index + 1}. ${id.speciesName} (${id.confidence}% confidence)`);
        console.log(`     Scientific: ${id.scientificName || 'Unknown'}`);
        console.log(`     Category: ${id.category || 'Unknown'}`);
        console.log(`     Key Features: ${id.keyFeatures?.join(', ') || 'None listed'}`);
        console.log(`     Similar Species: ${id.similarSpecies || 'None noted'}`);
        console.log(`     Reasoning: ${id.reasoning}`);
      });
    } else {
      console.log('❌ No species identified');
    }

    console.log('📝 Analysis Notes:', result.analysisNotes);
    console.log('📸 Image Quality:', result.imageQuality);

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

/**
 * Enhanced logging for detailed AI analysis
 */
export function logDetailedAnalysis(result: AIAnalysisResult, photoId: string, imageUrl: string): void {
  console.log(`\n🔬 DETAILED AI ANALYSIS FOR PHOTO ${photoId}`);
  console.log(`📸 Image URL: ${imageUrl}`);
  console.log(`📊 Image Quality: ${result.imageQuality}`);
  console.log(`📝 Analysis Notes: ${result.analysisNotes}`);

  if (result.identifications.length > 0) {
    console.log(`\n🎯 SPECIES IDENTIFICATIONS (${result.identifications.length}):`);

    result.identifications.forEach((id, index) => {
      console.log(`\n--- IDENTIFICATION ${index + 1} ---`);
      console.log(`🏷️  Species: ${id.speciesName}`);
      console.log(`🧬 Scientific: ${id.scientificName || 'Not provided'}`);
      console.log(`📂 Category: ${id.category || 'Not specified'}`);
      console.log(`📊 Confidence: ${id.confidence}%`);

      if (id.keyFeatures && id.keyFeatures.length > 0) {
        console.log(`🔍 Key Features:`);
        id.keyFeatures.forEach((feature, i) => {
          console.log(`   ${i + 1}. ${feature}`);
        });
      }

      if (id.similarSpecies) {
        console.log(`🔄 Similar Species Analysis: ${id.similarSpecies}`);
      }

      if (id.reasoning) {
        console.log(`💭 Reasoning: ${id.reasoning}`);
      }
    });
  } else {
    console.log(`\n❌ NO SPECIES IDENTIFIED`);
  }

  console.log(`\n${'='.repeat(80)}\n`);
}
