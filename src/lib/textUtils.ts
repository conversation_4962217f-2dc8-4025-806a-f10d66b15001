/**
 * This file contains utility functions for cleaning, parsing, and transforming text 
 * content, especially for handling messy or inconsistent data from various sources.
 */

// Cleans text that might be a simple string or a JSON-formatted string.
const cleanHabitatText = (habitat: string): string => {
  if (!habitat) return '';
  return habitat
    .replace(/^\["|"\]$/g, '')
    .replace(/^"|"$/g, '')
    .replace(/\\"/g, '"')
    .replace(/\[|\]/g, '')
    .trim();
};

// A more robust function to clean various formatting issues and stray characters from text.
const cleanTextContent = (text: string): string => {
  if (!text) return '';
  
  return text
    .replace(/\{"[^"]*":\s*"[^"]*"[^}]*\}/g, '')
    .replace(/\[object Object\]/g, '')
    .replace(/\{"state":"empty","value":null,"isStale":true\}/g, '')
    .replace(/[{}[\]]/g, '')
    .replace(/•/g, '')
    .replace(/\s*-\s*/g, ' ')
    .replace(/\s*\*\s*/g, ' ')
    .replace(/^\d+\.\s*/gm, '')
    .replace(/\\"/g, '"')
    .replace(/^["'\s]+|["'\s]+$/g, '')
    .replace(/\s{2,}/g, ' ')
    .replace(/^(fun fact|fact|did you know)[:\s]*/i, '')
    .replace(/🎯\s*(fun facts?[:\s]*)?/i, '')
    .replace(/^AI Facts?[:\s]*/i, '')
    .trim();
};

// Safely extracts and cleans text from a value that could be a string, object, or malformed JSON.
export const safeExtractText = (value: unknown): string => {
  if (!value) return '';
  if (typeof value === 'string') {
    if (value.trim().startsWith('{') || value.trim().startsWith('[')) {
      try {
        const parsed = JSON.parse(value);
        if (parsed && typeof parsed === 'object') {
            const parsedObj = parsed as Record<string, unknown>;
            if (parsedObj.value !== undefined) return cleanTextContent(String(parsedObj.value));
            const textFields = ['text', 'content', 'description', 'value', 'name'];
            for (const field of textFields) {
                if (parsedObj[field] && typeof parsedObj[field] === 'string') return cleanTextContent(parsedObj[field] as string);
            }
        }
        if (Array.isArray(parsed)) return cleanTextContent(parsed.filter(item => typeof item === 'string').join(' '));
        return '';
      } catch (e) {
        return cleanTextContent(value);
      }
    }
    return cleanTextContent(value);
  }
  if (typeof value === 'object' && value !== null) {
    const valueObj = value as Record<string, unknown>;
    if (valueObj.value !== undefined) return cleanTextContent(String(valueObj.value));
    if (valueObj.text) return cleanTextContent(String(valueObj.text));
    if (valueObj.content) return cleanTextContent(String(valueObj.content));
    return '';
  }
  return cleanTextContent(String(value));
};

// Cleans an individual fun fact string.
const cleanFunFact = (fact: string): string => {
  const cleanedFact = safeExtractText(fact);
  if (!cleanedFact || cleanedFact.trim().length === 0) return '';
  
  return cleanedFact
    .replace(/^[•\-*\\s]+/, '')
    .replace(/^\d+\.\s*/, '')
    .replace(/^(fun fact|fact|did you know)[:\s]*/i, '')
    .replace(/🎯\s*(fun facts?[:\s]*)?/i, '')
    .replace(/^AI Facts?[:\s]*/i, '')
    .replace(/^["\s]+|["\s]+$/g, '')
    .trim();
};

// Compares two facts to see if they are similar enough to be considered duplicates.
const areFactsSimilar = (fact1: string, fact2: string): boolean => {
  if (!fact1 || !fact2) return false;
  
  const clean1 = fact1.toLowerCase().replace(/[^a-z0-9\s]/g, '').trim();
  const clean2 = fact2.toLowerCase().replace(/[^a-z0-9\s]/g, '').trim();
  
  if (clean1.length < 10 || clean2.length < 10) return false;
  
  const longer = clean1.length > clean2.length ? clean1 : clean2;
  const shorter = clean1.length > clean2.length ? clean2 : clean1;
  
  if (longer.includes(shorter)) return true;
  
  const words1 = clean1.split(/\s+/).filter(w => w.length > 3);
  const words2 = clean2.split(/\s+/).filter(w => w.length > 3);
  const commonWords = words1.filter(word => words2.includes(word));
  
  return commonWords.length >= Math.min(words1.length, words2.length) * 0.6;
};

// Extracts and deduplicates fun facts from multiple potential fields in a species object.
export const extractAllFunFacts = (species: Record<string, unknown>): string[] => {
  const allFunFacts: string[] = [];

  const fieldsToScan = ['ai_fun_facts', 'fun_facts_field', 'notes'];
  fieldsToScan.forEach(field => {
    if (species[field]) {
      const cleanedText = safeExtractText(species[field]);
      if (cleanedText) {
        const facts = cleanedText
          .split(/•|\n-|\n\*|\n\d+\.|\n/)
          .map(fact => cleanFunFact(fact))
          .filter(fact => fact.length > 15);
        allFunFacts.push(...facts);
      }
    }
  });
  
  const uniqueFacts: string[] = [];
  for (const fact of allFunFacts) {
    if (!fact || fact.length < 15) continue;
    const isDuplicate = uniqueFacts.some(existingFact => areFactsSimilar(fact, existingFact));
    if (!isDuplicate) {
      uniqueFacts.push(fact);
    }
  }
  
  return uniqueFacts.slice(0, 10); // Allow up to 10 facts since we have show more/less UI
};

// Extracts a clean, readable description, removing duplicated sentences and artifacts.
export const extractCleanDescription = (description: string): string => {
  if (!description) return '';

  let cleanedDescription = safeExtractText(description);
  if (!cleanedDescription) return '';

  // Remove everything from fun facts markers onwards
  const funFactsMarkers = [
    '🎯 FUN FACTS',
    '🦅 FUN FACTS',
    'FUN FACTS:',
    'AI Facts:',
    'Fun Facts:',
    'FACTS:',
    '• FUN FACTS',
    'Here are some fun facts'
  ];

  for (const marker of funFactsMarkers) {
    const index = cleanedDescription.indexOf(marker);
    if (index !== -1) {
      cleanedDescription = cleanedDescription.substring(0, index).trim();
    }
  }

  const sentences = cleanedDescription.split(/[.!?]+/).map(s => s.trim()).filter(s => s.length > 10);

  const uniqueSentences = new Set<string>();
  const finalSentences: string[] = [];

  for (const sentence of sentences) {
      const lowerSentence = sentence.toLowerCase();

      // Skip sentences that contain fun facts references
      if (lowerSentence.includes('fun facts') ||
          lowerSentence.includes('ai facts') ||
          lowerSentence.includes('here are some') ||
          lowerSentence.includes('did you know')) continue;

      const simplified = lowerSentence.replace(/[^a-z0-9]/g, '');
      if (!uniqueSentences.has(simplified) && sentence.length > 20) {
          uniqueSentences.add(simplified);
          finalSentences.push(sentence);
      }
  }

  // Limit to 3-4 sentences for conciseness
  const limitedSentences = finalSentences.slice(0, 4);
  return limitedSentences.join('. ') + (limitedSentences.length > 0 ? '.' : '');
};