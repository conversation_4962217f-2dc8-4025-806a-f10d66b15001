// Gemini AI integration for location content generation
import { GoogleGenerativeAI } from '@google/generative-ai';

interface LocationContentRequest {
  locationName: string;
  latitude: number;
  longitude: number;
  stateProvince?: string;
  country?: string;
  habitatTypes?: string[];
  speciesCount: number;
  categories: string[];
  // Enhanced data for better AI generation
  commonSpecies?: number;
  rareSpecies?: number;
  breedingSpecies?: number;
  keySpecies?: Array<{
    name: string;
    commonName?: string;
    scientificName?: string;
    category?: string;
    abundance: string;
    seasonalPresence: string[];
    breedingStatus: string;
    bestMonths: number[];
    conservationStatus?: string;
    notes?: string;
  }>;
}

interface LocationContentResponse {
  description: string;
  visitorTips: string;
  bestTimes: string;
}

export async function generateLocationContentWithGemini(request: LocationContentRequest): Promise<LocationContentResponse> {
  console.log('🤖 AI Generation Request Data:', request);
  console.log('🐾 Key Species Data:', request.keySpecies);

  const apiKey = import.meta.env.VITE_GEMINI_API_KEY;

  if (!apiKey) {
    throw new Error('Gemini API key not configured. Please add VITE_GEMINI_API_KEY to your .env file.');
  }

  const genAI = new GoogleGenerativeAI(apiKey);
  const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

  // Helper function to format species data for the prompt
  const formatSpeciesData = () => {
    if (!request.keySpecies || request.keySpecies.length === 0) {
      return 'No specific species data available.';
    }

    return request.keySpecies.slice(0, 10).map(species => {
      const months = species.bestMonths.length > 0
        ? species.bestMonths.map(m => ['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'][m-1]).join(', ')
        : 'Year-round';

      return `• ${species.name}${species.commonName && species.commonName !== species.name ? ` (${species.commonName})` : ''} - ${species.abundance}, ${species.breedingStatus} breeding, best: ${months}${species.conservationStatus ? `, conservation: ${species.conservationStatus}` : ''}`;
    }).join('\n');
  };

  const prompt = `
You are a wildlife and nature expert writing content for a wildlife hotspot location page. Use the detailed species and location data to create accurate, engaging content.

LOCATION DETAILS:
- Name: ${request.locationName}
- Location: ${request.stateProvince ? `${request.stateProvince}, ` : ''}${request.country}
- Coordinates: ${request.latitude}, ${request.longitude}
- Habitat Types: ${request.habitatTypes?.join(', ') || 'Mixed habitats'}

WILDLIFE STATISTICS:
- Total Species: ${request.speciesCount} species recorded
- Common Species: ${request.commonSpecies || 0}
- Rare Species: ${request.rareSpecies || 0}
- Breeding Species: ${request.breedingSpecies || 0}
- Wildlife Categories: ${request.categories.join(', ') || 'Various wildlife'}

KEY SPECIES AT THIS LOCATION:
${formatSpeciesData()}

INSTRUCTIONS:
Write comprehensive, accurate content based on the actual species data provided. Mention specific species by name when relevant. Use the abundance, seasonal, and breeding data to provide detailed timing advice.

Please provide exactly 3 sections:

1. DESCRIPTION: Write 2-3 engaging paragraphs highlighting:
   - What makes this location special for wildlife viewing
   - Specific habitats and their wildlife
   - Key species visitors can expect to see (mention actual species names)
   - Why this location is valuable for conservation and wildlife enthusiasts
   - Unique viewing opportunities based on the species present

2. VISITOR_TIPS: Provide practical, location-specific advice including:
   - Best times of day for wildlife activity
   - Recommended gear (binoculars, cameras, clothing)
   - Behavior guidelines and wildlife etiquette
   - Photography tips for the species present
   - Safety considerations
   - What to bring based on habitat types and weather

3. BEST_TIMES: Provide detailed timing information including:
   - Optimal seasons for different species (use the actual seasonal data)
   - Monthly breakdown of what to expect
   - Breeding season highlights (mention specific breeding species)
   - Migration patterns if applicable
   - Weather considerations for each season
   - Special events or peak viewing times

Format your response as valid JSON with these exact keys:
{
  "description": "your description here",
  "visitorTips": "your visitor tips here",
  "bestTimes": "your best times info here"
}

Ensure the JSON is properly formatted and escaped. Make the content specific to this location and its actual wildlife.
`;

  try {
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    console.log('Gemini raw response:', text);
    
    // Try to extract JSON from the response
    let jsonContent;
    try {
      // Look for JSON block in the response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        jsonContent = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('No JSON found in response');
      }
    } catch (parseError) {
      console.warn('Failed to parse JSON response, using fallback parsing');
      
      // Fallback: try to extract sections manually
      const descMatch = text.match(/(?:description["\s]*:[\s]*["])(.*?)(?:["])/is);
      const tipsMatch = text.match(/(?:visitorTips["\s]*:[\s]*["])(.*?)(?:["])/is);
      const timesMatch = text.match(/(?:bestTimes["\s]*:[\s]*["])(.*?)(?:["])/is);
      
      jsonContent = {
        description: descMatch?.[1] || 'This location offers excellent wildlife viewing opportunities with diverse habitats and species.',
        visitorTips: tipsMatch?.[1] || 'Bring binoculars, dress appropriately for the weather, and maintain respectful distance from wildlife.',
        bestTimes: timesMatch?.[1] || 'Early morning and late afternoon are typically the best times for wildlife activity.'
      };
    }
    
    return {
      description: jsonContent.description || 'AI-generated description not available.',
      visitorTips: jsonContent.visitorTips || 'AI-generated visitor tips not available.',
      bestTimes: jsonContent.bestTimes || 'AI-generated timing information not available.'
    };
    
  } catch (error) {
    console.error('Error generating content with Gemini:', error);
    
    // Return enhanced fallback content with available data
    const speciesInfo = request.keySpecies && request.keySpecies.length > 0
      ? `Notable species include ${request.keySpecies.slice(0, 3).map(s => s.commonName || s.name).join(', ')}.`
      : '';

    const abundanceInfo = request.commonSpecies && request.rareSpecies
      ? `The area hosts ${request.commonSpecies} commonly seen species and ${request.rareSpecies} rare species.`
      : '';

    return {
      description: `${request.locationName} is a significant wildlife viewing destination in ${request.stateProvince ? `${request.stateProvince}, ` : ''}${request.country}. With ${request.speciesCount} species documented, this location showcases remarkable biodiversity across ${request.habitatTypes?.join(', ') || 'diverse habitats'}. ${abundanceInfo} ${speciesInfo} The area is particularly renowned for ${request.categories.join(', ') || 'wildlife'} observation and offers exceptional opportunities for both casual nature lovers and serious wildlife photographers. The diverse ecosystem supports year-round wildlife activity, making it a valuable conservation area and educational resource.`,
      visitorTips: `For optimal wildlife viewing at ${request.locationName}, arrive during dawn or dusk when animals are most active. Essential gear includes quality binoculars (8x42 recommended), a camera with telephoto lens, and field guides for local species identification. Dress in earth-toned, quiet clothing and wear comfortable, sturdy footwear suitable for the terrain. Maintain a respectful distance from all wildlife - use the "thumb rule" (if your thumb covers the animal when your arm is extended, you're at a safe distance). Bring weather-appropriate gear, snacks, and plenty of water. Practice Leave No Trace principles and consider hiring a local guide for the best experience.`,
      bestTimes: `Wildlife activity at ${request.locationName} peaks during early morning (5:30-9:00 AM) and late afternoon (4:00-7:00 PM) when temperatures are cooler and animals are feeding. ${request.breedingSpecies ? `Spring and early summer offer excellent breeding behavior viewing with ${request.breedingSpecies} species actively nesting.` : 'Spring brings increased activity as animals emerge from winter dormancy.'} Fall migration periods can provide spectacular viewing opportunities, while winter offers unique cold-weather adaptations. Weather conditions significantly impact wildlife activity - overcast days often extend active periods, while extreme heat or storms reduce sightings. Plan visits around moon phases for nocturnal species, and check local wildlife reports for recent activity patterns.`
    };
  }
}

// Helper function to validate and clean generated content
export function validateLocationContent(content: LocationContentResponse): LocationContentResponse {
  return {
    description: content.description?.trim() || 'Description not available.',
    visitorTips: content.visitorTips?.trim() || 'Visitor tips not available.',
    bestTimes: content.bestTimes?.trim() || 'Best times information not available.'
  };
}
