import React, { useState, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Upload, 
  FileText, 
  Bot, 
  AlertCircle, 
  CheckCircle, 
  Clock,
  Download,
  RefreshCw,
  Plus,
  Trash2
} from 'lucide-react';
import { toast } from 'sonner';
import { SpeciesImportTable } from '@/components/SpeciesImportTable';
import { 
  EnrichedSpecies, 
  generateAISpeciesMetadata, 
  insertSpeciesToSupabase,
  parseCSVContent,
  parseJSONContent
} from '@/utils/aiSpeciesGenerator';

export function SpeciesImport() {
  const [speciesNames, setSpeciesNames] = useState<string[]>([]);
  const [enrichedSpecies, setEnrichedSpecies] = useState<EnrichedSpecies[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isInserting, setIsInserting] = useState(false);
  const [dryRunMode, setDryRunMode] = useState(false);
  const [inputText, setInputText] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);


  const handleTextInput = (text: string) => {
    setInputText(text);
    const names = text
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);
    setSpeciesNames(names);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      try {
        let names: string[];
        
        if (file.name.endsWith('.csv')) {
          names = parseCSVContent(content);
        } else if (file.name.endsWith('.json')) {
          names = parseJSONContent(content);
        } else {
          throw new Error('Unsupported file format. Please use .csv or .json files.');
        }

        setSpeciesNames(names);
        setInputText(names.join('\n'));
        
        toast.success(`Successfully parsed ${names.length} species names from ${file.name}`);
      } catch (error) {
        toast.error(error instanceof Error ? error.message : 'Failed to parse file');
      }
    };
    reader.readAsText(file);
  };

  const handleGenerateMetadata = async () => {
    if (speciesNames.length === 0) {
      toast.error("Please add some species names first");
      return;
    }

    setIsGenerating(true);
    try {
      const metadata = await generateAISpeciesMetadata(speciesNames);
      
      const enriched = metadata.map((meta, index) => ({
        ...meta,
        ai_generated: true,
        published: false
      }));

      setEnrichedSpecies(enriched);
      
      toast.success(`Successfully generated metadata for ${enriched.length} species`);
    } catch (error) {
      console.error('Error generating metadata:', error);
      toast.error("Failed to generate metadata. Check console for details.");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSpeciesUpdate = (index: number, updatedSpecies: EnrichedSpecies) => {
    const updated = [...enrichedSpecies];
    updated[index] = updatedSpecies;
    setEnrichedSpecies(updated);
  };

  const handleSpeciesDelete = (index: number) => {
    const updated = enrichedSpecies.filter((_, i) => i !== index);
    setEnrichedSpecies(updated);
  };

  const handleBulkPublish = (published: boolean) => {
    const updated = enrichedSpecies.map(species => ({
      ...species,
      published
    }));
    setEnrichedSpecies(updated);
  };

  const handleInsertToSupabase = async () => {
    if (enrichedSpecies.length === 0) {
      toast.error("Please generate metadata first");
      return;
    }

    setIsInserting(true);
    try {
      if (dryRunMode) {
        // Simulate dry run
        await new Promise(resolve => setTimeout(resolve, 1000));
        toast.success(`Would insert ${enrichedSpecies.length} species into database`);
      } else {
        const result = await insertSpeciesToSupabase(enrichedSpecies);

        if (result.success) {
          toast.success(`Successfully inserted ${result.inserted} species into database`);

          // Clear the form after successful import
          setEnrichedSpecies([]);
          setSpeciesNames([]);
          setInputText('');
        } else {
          toast.error(result.errors.join(', '));
        }
      }
    } catch (error) {
      console.error('Error inserting species:', error);
      toast.error("Failed to insert species into database");
    } finally {
      setIsInserting(false);
    }
  };

  const handleExportCSV = () => {
    if (enrichedSpecies.length === 0) return;

    const csvContent = [
      // Header
      ['Name', 'Scientific Name', 'Description', 'Habitat', 'Diet', 'Conservation Status', 'Category', 'Related Locations', 'AI Generated', 'Published'].join(','),
      // Data
      ...enrichedSpecies.map(species => [
        `"${species.name}"`,
        `"${species.scientific_name}"`,
        `"${species.description.replace(/"/g, '""')}"`,
        `"${species.habitat.replace(/"/g, '""')}"`,
        `"${species.diet}"`,
        `"${species.conservation_status}"`,
        `"${species.category}"`,
        `"${species.related_locations.join('; ')}"`,
        species.ai_generated ? 'Yes' : 'No',
        species.published ? 'Yes' : 'No'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `species-import-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success("Species data exported to CSV");
  };

  const clearAll = () => {
    setSpeciesNames([]);
    setEnrichedSpecies([]);
    setInputText('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Species Import Tool</h1>
          <p className="text-muted-foreground">
            Upload species names and auto-populate metadata with AI
          </p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Switch
              id="dry-run"
              checked={dryRunMode}
              onCheckedChange={setDryRunMode}
            />
            <Label htmlFor="dry-run">Dry Run Mode</Label>
          </div>
          <Button onClick={clearAll} variant="outline">
            <Trash2 className="w-4 h-4 mr-2" />
            Clear All
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <FileText className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-2xl font-bold">{speciesNames.length}</p>
                <p className="text-sm text-muted-foreground">Species Names</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Bot className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-2xl font-bold">{enrichedSpecies.length}</p>
                <p className="text-sm text-muted-foreground">Enriched Species</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-2xl font-bold">
                  {enrichedSpecies.filter(s => s.published).length}
                </p>
                <p className="text-sm text-muted-foreground">Published</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-yellow-600" />
              <div>
                <p className="text-2xl font-bold">
                  {enrichedSpecies.filter(s => !s.published).length}
                </p>
                <p className="text-sm text-muted-foreground">Drafts</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Input Methods */}
      <Tabs defaultValue="text" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="text">Text Input</TabsTrigger>
          <TabsTrigger value="file">File Upload</TabsTrigger>
        </TabsList>
        
        <TabsContent value="text" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Enter Species Names</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="Enter species names, one per line:&#10;African Elephant&#10;Bengal Tiger&#10;Bald Eagle&#10;Green Sea Turtle&#10;Giant Panda"
                value={inputText}
                onChange={(e) => handleTextInput(e.target.value)}
                rows={8}
                className="font-mono"
              />
              <div className="mt-2 text-sm text-muted-foreground">
                {speciesNames.length} species names detected
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="file" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Upload Species File</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="file-upload">Choose File</Label>
                  <Input
                    id="file-upload"
                    type="file"
                    accept=".csv,.json"
                    onChange={handleFileUpload}
                    ref={fileInputRef}
                  />
                </div>
                <div className="text-sm text-muted-foreground">
                  <p>Supported formats:</p>
                  <ul className="list-disc list-inside mt-1">
                    <li>CSV: First column should contain species names</li>
                    <li>JSON: Array of strings or objects with 'name' field</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Generate Metadata */}
      {speciesNames.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Generate Metadata</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Button
                  onClick={handleGenerateMetadata}
                  disabled={isGenerating || speciesNames.length === 0}
                  className="flex-1"
                >
                  {isGenerating ? (
                    <Clock className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Bot className="w-4 h-4 mr-2" />
                  )}
                  {isGenerating ? 'Generating...' : 'Fetch Metadata with AI'}
                </Button>
                
                {enrichedSpecies.length > 0 && (
                  <Button onClick={handleExportCSV} variant="outline">
                    <Download className="w-4 h-4 mr-2" />
                    Export CSV
                  </Button>
                )}
              </div>
              
              {enrichedSpecies.length > 0 && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CheckCircle className="w-4 h-4" />
                  <span>Metadata generated for {enrichedSpecies.length} species</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Review and Edit Table */}
      {enrichedSpecies.length > 0 && (
        <SpeciesImportTable
          species={enrichedSpecies}
          onSpeciesUpdate={handleSpeciesUpdate}
          onSpeciesDelete={handleSpeciesDelete}
          onBulkPublish={handleBulkPublish}
          onInsertToSupabase={handleInsertToSupabase}
          isInserting={isInserting}
          dryRunMode={dryRunMode}
        />
      )}

      {/* Instructions */}
      {enrichedSpecies.length === 0 && speciesNames.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Bot className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold">Get Started</h3>
            <p className="text-muted-foreground mb-4">
              Enter species names or upload a file to begin the AI-powered import process.
            </p>
            <div className="text-sm text-muted-foreground space-y-1">
              <p>• Enter species names manually or upload CSV/JSON files</p>
              <p>• Use AI to auto-populate missing metadata fields</p>
              <p>• Review and edit the generated data before import</p>
              <p>• Insert into Supabase with proper tracking</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default SpeciesImport; 