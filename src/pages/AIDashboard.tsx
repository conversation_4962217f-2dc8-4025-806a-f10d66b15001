import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { AIDashboardEditor } from '@/components/AIDashboardEditor';
import { PhotoAssignment } from '@/components/PhotoAssignment';
import { supabase } from '@/integrations/supabase/client';
import { useRealtimeOverrides, useRealtimeReviewQueue, useRealtimeAIMetrics } from '@/hooks/useRealtimeOverrides';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { 
  BarChart3, 
  Eye, 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp, 
  Activity,
  Database,
  Settings,
  RefreshCw,
  Bell,
  BellOff
} from 'lucide-react';

interface DashboardStats {
  total_photos: number;
  ai_tagged: number;
  flagged_for_review: number;
  avg_confidence: number;
  published_photos: number;
  unpublished_photos: number;
}

interface OverrideLog {
  id: number;
  table_name: string;
  record_id: string;
  field_name: string;
  old_value: string | null;
  new_value: string | null;
  override_type: string;
  override_reason: string | null;
  created_at: string;
  record_title: string | null;
  photo_url: string | null;
}

export default function AIDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentOverrides, setRecentOverrides] = useState<OverrideLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  // Auth status
  const { ready: authReady, isAdmin, loading: authLoading } = useAdminAuth();

  // Real-time hooks (gated by authReady)
  const {
    overrides: realtimeOverrides,
    loading: overridesLoading,
    error: overridesError,
    clearOverrides,
    refreshOverrides,
    hasNewOverrides
  } = useRealtimeOverrides({
    enabled: notificationsEnabled,
    showNotifications: notificationsEnabled,
    maxNotifications: 10,
    ready: authReady
  });

  const {
    reviewCounts,
    loading: reviewQueueLoading
  } = useRealtimeReviewQueue(authReady);

  const {
    metrics: realtimeMetrics,
    loading: metricsLoading
  } = useRealtimeAIMetrics(authReady);

  useEffect(() => {
    loadDashboardData();
  }, []);

  // Update stats when real-time metrics change
  useEffect(() => {
    if (realtimeMetrics && Object.values(realtimeMetrics).some(v => v > 0)) {
      setStats(realtimeMetrics);
    }
  }, [realtimeMetrics]);

  // Update overrides when real-time data changes
  useEffect(() => {
    if (realtimeOverrides.length > 0) {
      setRecentOverrides(realtimeOverrides);
    }
  }, [realtimeOverrides]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load AI assignment stats with fallback
      try {
        const { data: statsData, error: statsError } = await supabase
          .from('ai_assignment_stats')
          .select('*')
          .single();

        if (statsError) throw statsError;
        setStats(statsData);
      } catch (statsError) {
        // Fallback: calculate stats directly from photos_v2
        const [totalResult, publishedResult] = await Promise.all([
          supabase.from('photos_v2').select('*', { count: 'exact', head: true }),
          supabase.from('photos_v2').select('*', { count: 'exact', head: true }).eq('published', true)
        ]);

        const total = totalResult.count || 0;
        const published = publishedResult.count || 0;

        setStats({
          total_photos: total,
          ai_tagged: 0,
          flagged_for_review: 0,
          avg_confidence: 0,
          published_photos: published,
          unpublished_photos: total - published
        });
      }

      // Load recent overrides with fallback
      try {
        const { data: overridesData, error: overridesError } = await supabase
          .from('recent_ai_overrides')
          .select('*')
          .limit(10);

        if (overridesError) throw overridesError;
        setRecentOverrides(overridesData || []);
      } catch (overridesError) {
        // Fallback: try ai_override_log directly, or use empty array if table doesn't exist
        try {
          const { data: logData } = await supabase
            .from('ai_override_log')
            .select('*')
            .limit(10)
            .order('created_at', { ascending: false });

          setRecentOverrides(logData?.map(log => ({
            ...log,
            record_title: 'Unknown',
            photo_url: null
          })) || []);
        } catch (logError) {
          // If ai_override_log doesn't exist, use empty array
          console.log('ai_override_log table not found, using empty overrides');
          setRecentOverrides([]);
        }
      }

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // Set default values on complete failure
      setStats({
        total_photos: 0,
        ai_tagged: 0,
        flagged_for_review: 0,
        avg_confidence: 0,
        published_photos: 0,
        unpublished_photos: 0
      });
      setRecentOverrides([]);
    } finally {
      setLoading(false);
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getOverrideTypeIcon = (type: string) => {
    switch (type) {
      case 'species_assignment':
        return '🦋';
      case 'metadata_edit':
        return '✏️';
      case 'review_status':
        return '👁️';
      case 'confidence_override':
        return '🎯';
      default:
        return '📝';
    }
  };

  const toggleNotifications = () => {
    setNotificationsEnabled(!notificationsEnabled);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Status Bar */}
      <div className="flex items-center gap-4 text-xs mb-2">
        <Badge variant={authReady ? 'default' : 'secondary'}>
          {authLoading ? 'Checking Auth...' : authReady ? 'Auth Ready' : 'Waiting for Auth'}
        </Badge>
        {overridesError && (
          <Badge variant="destructive">Realtime Error</Badge>
        )}
        {!overridesError && authReady && (
          <Badge variant="default">Realtime Connected</Badge>
        )}
      </div>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">AI Wildlife Photo Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor and manage AI-generated content and review workflow
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button 
            onClick={toggleNotifications} 
            variant={notificationsEnabled ? "default" : "outline"}
            size="sm"
          >
            {notificationsEnabled ? <Bell className="w-4 h-4 mr-2" /> : <BellOff className="w-4 h-4 mr-2" />}
            {notificationsEnabled ? "Notifications On" : "Notifications Off"}
          </Button>
          <Button onClick={loadDashboardData} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Real-time Status Indicators */}
      <div className="flex items-center gap-4 text-sm">
        {overridesError && (
          <Badge variant="destructive">
            ⚠️ Real-time connection error
          </Badge>
        )}
        {hasNewOverrides && (
          <Badge variant="default" className="animate-pulse">
            🔔 New overrides detected
          </Badge>
        )}
        {reviewCounts.total > 0 && (
          <Badge variant="secondary">
            📋 {reviewCounts.total} items in review queue
          </Badge>
        )}
      </div>

      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Photos</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_photos}</div>
              <p className="text-xs text-muted-foreground">
                {stats.published_photos} published, {stats.unpublished_photos} draft
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">AI Tagged</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.ai_tagged}</div>
              <p className="text-xs text-muted-foreground">
                {stats.total_photos > 0 ? Math.round((stats.ai_tagged / stats.total_photos) * 100) : 0}% of total
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Needs Review</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.flagged_for_review}</div>
              <p className="text-xs text-muted-foreground">
                {stats.ai_tagged > 0 ? Math.round((stats.flagged_for_review / stats.ai_tagged) * 100) : 0}% of AI tagged
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Confidence</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getConfidenceColor(stats.avg_confidence)}`}>
                {Math.round(stats.avg_confidence * 100)}%
              </div>
              <p className="text-xs text-muted-foreground">
                AI confidence score
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="review">
            Review Queue
            {reviewCounts.photos > 0 && (
              <Badge variant="destructive" className="ml-2 text-xs">
                {reviewCounts.photos}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="species">
            AI Species
            {reviewCounts.species > 0 && (
              <Badge variant="destructive" className="ml-2 text-xs">
                {reviewCounts.species}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="overrides">
            Overrides
            {hasNewOverrides && (
              <Badge variant="default" className="ml-2 text-xs animate-pulse">
                New
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="photo_assignment">
            Photo Assignment
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-5 h-5" />
                  Recent AI Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="text-sm text-muted-foreground">
                    AI processing activity over the last 7 days
                  </p>
                  <div className="text-center py-8">
                    <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Activity data will appear here when AI processing is active
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={() => setActiveTab('review')}
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    Review Photos ({stats?.flagged_for_review || 0})
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={() => setActiveTab('species')}
                  >
                    <Database className="w-4 h-4 mr-2" />
                    Review AI Species
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={() => setActiveTab('overrides')}
                  >
                    <AlertTriangle className="w-4 h-4 mr-2" />
                    View Overrides ({recentOverrides.length})
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="review" className="space-y-4">
          <AIDashboardEditor 
            viewType="photos_needing_review" 
            onUpdate={loadDashboardData}
          />
        </TabsContent>

        <TabsContent value="species" className="space-y-4">
          <AIDashboardEditor 
            viewType="ai_created_species" 
            onUpdate={loadDashboardData}
          />
        </TabsContent>

        <TabsContent value="overrides" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5" />
                  Recent Manual Overrides
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Button 
                    onClick={clearOverrides} 
                    variant="outline" 
                    size="sm"
                  >
                    Clear
                  </Button>
                  <Button 
                    onClick={refreshOverrides} 
                    variant="outline" 
                    size="sm"
                  >
                    <RefreshCw className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {recentOverrides.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">
                    No manual overrides recorded yet
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {recentOverrides.map((override) => (
                    <div key={override.id} className="flex items-start gap-3 p-3 border rounded-lg">
                      <div className="text-2xl">
                        {getOverrideTypeIcon(override.override_type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="outline">{override.override_type}</Badge>
                          <span className="text-sm text-muted-foreground">
                            {new Date(override.created_at).toLocaleDateString()}
                          </span>
                        </div>
                        <p className="text-sm font-medium mb-1">
                          {override.record_title || `Record ${override.record_id}`}
                        </p>
                        <p className="text-sm text-muted-foreground mb-1">
                          {override.field_name}: "{override.old_value || 'empty'}" → "{override.new_value || 'empty'}"
                        </p>
                        {override.override_reason && (
                          <p className="text-xs text-muted-foreground">
                            Reason: {override.override_reason}
                          </p>
                        )}
                      </div>
                      {override.photo_url && (
                        <img
                          src={override.photo_url}
                          alt="Photo"
                          className="w-12 h-12 object-cover rounded"
                          onError={(e) => {
                            e.currentTarget.src = '/placeholder.svg';
                          }}
                        />
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="photo_assignment" className="space-y-4">
          <PhotoAssignment />
        </TabsContent>
      </Tabs>
    </div>
  );
} 