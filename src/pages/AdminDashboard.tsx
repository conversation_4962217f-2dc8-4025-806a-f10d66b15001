import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Link } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAdminAuth } from "@/hooks/useAdminAuth";
import {
  BarChart3,
  FileText,
  Camera,
  Users,
  Globe,
  Settings,
  Upload,
  Edit,
  Eye,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  Database,
  Zap,
  Shield,
  RefreshCw,
  Plus,
  Search,
  Filter,
  Download,
  Loader2,
  AlertCircle
} from 'lucide-react';

// Import our new CMS components
import { SpeciesManager } from '@/components/SpeciesManager';
import { EnhancedPhotoManager } from '@/components/EnhancedPhotoManager';
import { PublishingWorkflow } from '@/components/PublishingWorkflow';
import { DataIntegrityDashboard } from '@/components/DataIntegrityDashboard';
import { DevPerformanceMonitor } from '@/components/PerformanceMonitor';
import { FunFactsDemo } from '@/components/FunFactsDemo';
import { FunFactsMigrationTool } from '@/components/FunFactsMigrationTool';

interface DashboardStats {
  species: {
    total: number;
    published: number;
    draft: number;
    withPhotos: number;
    recentlyUpdated: number;
  };
  photos: {
    total: number;
    published: number;
    draft: number;
    assigned: number;
    unassigned: number;
    recentlyUploaded: number;
  };
  system: {
    totalUsers: number;
    activeUsers: number;
    storageUsed: number;
    apiCalls: number;
  };
}

interface RecentActivity {
  id: string;
  type: 'species_created' | 'species_updated' | 'photo_uploaded' | 'photo_assigned' | 'bulk_publish';
  description: string;
  timestamp: string;
  user?: string;
}

export function AdminDashboard() {
  // Deprecation notice for content management features
  console.info(
    '📢 NOTICE: Content management features have moved to the unified CMS Hub at /admin/cms. ' +
    'This dashboard now focuses on system monitoring. Please update your bookmarks.'
  );

  const { isAdmin, adminUser, ready, loading: authLoading } = useAdminAuth();
  const [stats, setStats] = useState<DashboardStats>({
    species: { total: 0, published: 0, draft: 0, withPhotos: 0, recentlyUpdated: 0 },
    photos: { total: 0, published: 0, draft: 0, assigned: 0, unassigned: 0, recentlyUploaded: 0 },
    system: { totalUsers: 0, activeUsers: 0, storageUsed: 0, apiCalls: 0 }
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (isAdmin) {
      loadDashboardData();
    }
  }, [isAdmin]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Load species stats with count queries for better performance
      const [
        { count: totalSpecies },
        { count: publishedSpecies },
        { count: speciesWithPhotos },
        { count: totalPhotos },
        { count: publishedPhotos },
        { count: assignedPhotos }
      ] = await Promise.all([
        supabase.from('species_v2').select('*', { count: 'exact', head: true }),
        supabase.from('species_v2').select('*', { count: 'exact', head: true }).eq('published', true),
        supabase.from('species_v2').select('*', { count: 'exact', head: true }).gt('photo_count', 0),
        supabase.from('photos_v2').select('*', { count: 'exact', head: true }),
        supabase.from('photos_v2').select('*', { count: 'exact', head: true }).eq('published', true),
        supabase.from('photos_v2').select('*', { count: 'exact', head: true }).not('species_id', 'is', null)
      ]);

      // Load recent updates (limited)
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
      const [
        { count: recentSpeciesUpdates },
        { count: recentPhotoUploads }
      ] = await Promise.all([
        supabase.from('species_v2').select('*', { count: 'exact', head: true }).gte('updated_at', weekAgo),
        supabase.from('photos_v2').select('*', { count: 'exact', head: true }).gte('created_at', weekAgo)
      ]);

      // Calculate stats from count queries
      const speciesStats = {
        total: totalSpecies || 0,
        published: publishedSpecies || 0,
        draft: (totalSpecies || 0) - (publishedSpecies || 0),
        withPhotos: speciesWithPhotos || 0,
        recentlyUpdated: recentSpeciesUpdates || 0,
      };

      const photosStats = {
        total: totalPhotos || 0,
        published: publishedPhotos || 0,
        draft: (totalPhotos || 0) - (publishedPhotos || 0),
        assigned: assignedPhotos || 0,
        unassigned: (totalPhotos || 0) - (assignedPhotos || 0),
        recentlyUploaded: recentPhotoUploads || 0,
      };

      setStats({
        species: speciesStats,
        photos: photosStats,
        system: {
          totalUsers: 1, // Placeholder
          activeUsers: 1, // Placeholder
          storageUsed: 75, // Placeholder percentage
          apiCalls: 1250 // Placeholder
        }
      });

      // Generate recent activity (simplified for demo)
      const activities: RecentActivity[] = [
        {
          id: '1',
          type: 'species_updated',
          description: `${speciesStats.recentlyUpdated} species updated this week`,
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          type: 'photo_uploaded',
          description: `${photosStats.recentlyUploaded} photos uploaded this week`,
          timestamp: new Date().toISOString(),
        },
        {
          id: '3',
          type: 'bulk_publish',
          description: `${speciesStats.published} species currently published`,
          timestamp: new Date().toISOString(),
        }
      ];

      setRecentActivity(activities);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
          <p className="text-lg text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <CardTitle>Access Denied</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-4">
              You need administrator privileges to access this dashboard.
            </p>
            <Link to="/admin/login">
              <Button>Go to Admin Login</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  const speciesPublishRate = stats.species.total > 0 ? (stats.species.published / stats.species.total) * 100 : 0;
  const photosPublishRate = stats.photos.total > 0 ? (stats.photos.published / stats.photos.total) * 100 : 0;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Wildlife CMS Admin</h1>
              <p className="text-gray-600">Content Management System Dashboard</p>
              {adminUser && (
                <div className="mt-2 flex items-center gap-2 text-sm text-gray-500">
                  <Users className="w-4 h-4" />
                  <span>Logged in as: {adminUser.email}</span>
                  <Badge variant={ready ? "default" : "secondary"}>
                    {ready ? "Ready" : "Initializing"}
                  </Badge>
                </div>
              )}
            </div>
            <div className="flex gap-2">
              <Link to="/">
                <Button variant="outline">
                  <Globe className="w-4 h-4 mr-2" />
                  View Public Site
                </Button>
              </Link>
              <Button onClick={loadDashboardData} variant="outline">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="species" className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Species
            </TabsTrigger>
            <TabsTrigger value="photos" className="flex items-center gap-2">
              <Camera className="w-4 h-4" />
              Photos
            </TabsTrigger>
            <TabsTrigger value="publishing" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Publishing
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <FileText className="w-5 h-5 text-blue-600" />
                    <h3 className="font-semibold">Species</h3>
                  </div>
                  <div className="text-2xl font-bold">{stats.species.total}</div>
                  <div className="text-sm text-gray-600">
                    {stats.species.published} published • {stats.species.draft} drafts
                  </div>
                  <Progress value={speciesPublishRate} className="mt-2 h-2" />
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Camera className="w-5 h-5 text-green-600" />
                    <h3 className="font-semibold">Photos</h3>
                  </div>
                  <div className="text-2xl font-bold">{stats.photos.total}</div>
                  <div className="text-sm text-gray-600">
                    {stats.photos.published} published • {stats.photos.draft} drafts
                  </div>
                  <Progress value={photosPublishRate} className="mt-2 h-2" />
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Globe className="w-5 h-5 text-purple-600" />
                    <h3 className="font-semibold">Coverage</h3>
                  </div>
                  <div className="text-2xl font-bold">{stats.species.withPhotos}</div>
                  <div className="text-sm text-gray-600">
                    Species with photos
                  </div>
                  <Progress value={(stats.species.withPhotos / stats.species.total) * 100} className="mt-2 h-2" />
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingUp className="w-5 h-5 text-orange-600" />
                    <h3 className="font-semibold">Activity</h3>
                  </div>
                  <div className="text-2xl font-bold">{stats.species.recentlyUpdated + stats.photos.recentlyUploaded}</div>
                  <div className="text-sm text-gray-600">
                    Updates this week
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Button className="h-20 flex-col gap-2" onClick={() => setActiveTab('species')}>
                    <Plus className="w-6 h-6" />
                    Add Species
                  </Button>
                  <Button className="h-20 flex-col gap-2" onClick={() => setActiveTab('photos')}>
                    <Upload className="w-6 h-6" />
                    Upload Photos
                  </Button>
                  <Button className="h-20 flex-col gap-2" onClick={() => setActiveTab('publishing')}>
                    <Eye className="w-6 h-6" />
                    Manage Publishing
                  </Button>
                  <Link to="/admin/species-photo-matrix">
                    <Button className="h-20 flex-col gap-2 w-full">
                      <Database className="w-6 h-6" />
                      Photo Matrix
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentActivity.map(activity => (
                    <div key={activity.id} className="flex items-center gap-3 p-3 border rounded-lg">
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{activity.description}</p>
                        <p className="text-xs text-gray-500">
                          {new Date(activity.timestamp).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* System Status */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>System Health</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Database</span>
                      <Badge variant="default">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Healthy
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Storage</span>
                      <Badge variant="secondary">
                        {stats.system.storageUsed}% used
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">API Status</span>
                      <Badge variant="default">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Online
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Content Quality</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Species without photos</span>
                      <Badge variant={stats.species.total - stats.species.withPhotos > 0 ? "destructive" : "default"}>
                        {stats.species.total - stats.species.withPhotos}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Unassigned photos</span>
                      <Badge variant={stats.photos.unassigned > 0 ? "destructive" : "default"}>
                        {stats.photos.unassigned}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Draft content</span>
                      <Badge variant="secondary">
                        {stats.species.draft + stats.photos.draft}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="species">
            <SpeciesManager />
          </TabsContent>

          <TabsContent value="photos">
            <EnhancedPhotoManager onPhotoUpdate={loadDashboardData} />
          </TabsContent>

          <TabsContent value="publishing">
            <PublishingWorkflow />
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            {/* Fun Facts Migration Demo */}
            <FunFactsDemo />

            {/* Fun Facts Migration Tool */}
            <FunFactsMigrationTool />

            {/* Data Integrity Dashboard */}
            <DataIntegrityDashboard />

            {/* System Settings */}
            <Card>
              <CardHeader>
                <CardTitle>System Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-gray-600">Additional system configuration and settings.</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Link to="/admin/sync">
                      <Button className="w-full justify-start">
                        <Database className="w-4 h-4 mr-2" />
                        Data Sync
                      </Button>
                    </Link>
                    <Link to="/ai-dashboard">
                      <Button className="w-full justify-start">
                        <Zap className="w-4 h-4 mr-2" />
                        AI Dashboard
                      </Button>
                    </Link>
                    <Link to="/admin/species-enrichment">
                      <Button className="w-full justify-start">
                        <Edit className="w-4 h-4 mr-2" />
                        Species Enrichment
                      </Button>
                    </Link>
                    <Link to="/admin/photo-recovery">
                      <Button className="w-full justify-start">
                        <Shield className="w-4 h-4 mr-2" />
                        Photo Recovery
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Performance Monitor (development only) */}
      <DevPerformanceMonitor />
    </div>
  );
}

export default AdminDashboard;
