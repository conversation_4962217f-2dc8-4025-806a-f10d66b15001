import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { IntelligentUploader } from "@/components/IntelligentUploader";
import { PhotoReviewDashboard } from "@/components/PhotoReviewDashboard";
import { Brain, Upload, Eye, BarChart3 } from "lucide-react";

const AIWorkflow = () => {
  const [activeTab, setActiveTab] = useState("upload");

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Brain className="w-8 h-8 text-blue-600" />
          AI-Powered Wildlife Photo Management
        </h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Upload wildlife photos and let AI automatically identify species, generate metadata, 
          and detect duplicates. Review and approve AI-generated content with confidence.
        </p>
      </div>

      {/* Feature Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Upload className="w-8 h-8 mx-auto mb-2 text-blue-500" />
            <h3 className="font-semibold mb-1">Smart Upload</h3>
            <p className="text-sm text-gray-600">
              AI automatically identifies species, generates titles, and detects duplicates
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Eye className="w-8 h-8 mx-auto mb-2 text-green-500" />
            <h3 className="font-semibold mb-1">Review & Approve</h3>
            <p className="text-sm text-gray-600">
              Review AI-generated metadata and approve or reject with confidence scores
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <BarChart3 className="w-8 h-8 mx-auto mb-2 text-purple-500" />
            <h3 className="font-semibold mb-1">Analytics</h3>
            <p className="text-sm text-gray-600">
              Track AI performance, review statistics, and manage your photo collection
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Workflow */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="upload" className="flex items-center gap-2">
            <Upload className="w-4 h-4" />
            AI Upload
          </TabsTrigger>
          <TabsTrigger value="review" className="flex items-center gap-2">
            <Eye className="w-4 h-4" />
            Review Dashboard
          </TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-5 h-5" />
                Intelligent Photo Upload
              </CardTitle>
            </CardHeader>
            <CardContent>
              <IntelligentUploader 
                onUploadComplete={(photoId) => {
                  console.log('Photo uploaded:', photoId);
                  // Optionally switch to review tab
                  setActiveTab("review");
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="review" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="w-5 h-5" />
                Photo Review Dashboard
              </CardTitle>
            </CardHeader>
            <CardContent>
              <PhotoReviewDashboard />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* AI Agent Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            How the AI Agent Works
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">Species Matching Process</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
                <li>Exact match by scientific name</li>
                <li>Exact match by common name</li>
                <li>Fuzzy match by scientific name (≥80% similarity)</li>
                <li>Fuzzy match by common name (≥70% similarity)</li>
                <li>Partial text match in description fields (≥60% confidence)</li>
                <li>Create new species if confidence ≥50%</li>
              </ol>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Photo Processing Features</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                <li>Automatic duplicate detection using SHA-256 hashing</li>
                <li>AI-generated titles, descriptions, and tags</li>
                <li>Location extraction from image metadata</li>
                <li>Confidence scoring for all AI decisions</li>
                <li>Automatic review flagging for low-confidence results</li>
              </ul>
            </div>
          </div>
          
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-2 text-blue-800">Confidence Thresholds</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <Badge className="bg-green-100 text-green-800">≥75%</Badge>
                <p className="text-gray-600 mt-1">Species Assignment</p>
              </div>
              <div>
                <Badge className="bg-yellow-100 text-yellow-800">≥60%</Badge>
                <p className="text-gray-600 mt-1">Location Extraction</p>
              </div>
              <div>
                <Badge className="bg-blue-100 text-blue-800">≥70%</Badge>
                <p className="text-gray-600 mt-1">Metadata Generation</p>
              </div>
              <div>
                <Badge className="bg-red-100 text-red-800">&lt;50%</Badge>
                <p className="text-gray-600 mt-1">Manual Review Required</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AIWorkflow; 