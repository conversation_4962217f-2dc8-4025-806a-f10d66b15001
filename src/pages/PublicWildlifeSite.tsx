import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { toast } from 'sonner';
import { 
  Search, 
  Filter,
  Grid3X3,
  List,
  Camera,
  MapPin,
  Calendar,
  Eye,
  Heart,
  Share2,
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
  Info,
  Globe,
  Users,
  Shield,
  Leaf,
  Zap,
  Star,
  X,
  ChevronDown,
  Loader2,
  Maximize2,
  Minimize2,
  Settings,
  ArrowUpDown,
  Globe as GlobeIcon,
  Sliders,
  Tag
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import type { Tables } from '@/integrations/supabase/types';

type SpeciesV2 = Tables<"species_v2">;
type Photo = Tables<"photos_v2">;

interface Species extends Omit<SpeciesV2, 'photo_count'> {
  photos?: Photo[];
  photo_count?: number;
}

interface PhotoWithSpecies extends Photo {
  species?: {
    id: string;
    name: string;
    category: string;
    conservation_status?: string;
  };
}

interface Category {
  category: string;
  count: number;
}

interface ConservationStatus {
  conservation_status: string;
  count: number;
}

interface FilterState {
  search: string;
  categories: string[];
  conservationStatuses: string[];
  locations: string[];
  published: boolean;
  featured: boolean;
}

export default function PublicWildlifeSite() {
  // Public-facing wildlife explorer for visitors

  const [species, setSpecies] = useState<Species[]>([]);
  const [photos, setPhotos] = useState<PhotoWithSpecies[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [conservationStatuses, setConservationStatuses] = useState<ConservationStatus[]>([]);
  const [locations, setLocations] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    categories: [],
    conservationStatuses: [],
    locations: [],
    published: true,
    featured: false
  });
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [galleryView, setGalleryView] = useState<'grid' | 'masonry'>('grid');
  const [selectedSpecies, setSelectedSpecies] = useState<Species | null>(null);
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null);
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);
  const [photoGalleryOpen, setPhotoGalleryOpen] = useState(false);
  const [speciesModalOpen, setSpeciesModalOpen] = useState(false);
  const [autoPlay, setAutoPlay] = useState(false);
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'photos'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');


  useEffect(() => {
    loadData();
    loadMetadata();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Fetch species from species_v2
      const { data: speciesData, error: speciesError } = await supabase
        .from("species_v2")
        .select("*")
        .eq("published", true)
        .order("name", { ascending: true });

      if (speciesError) throw speciesError;

      // Fetch photos from photos_v2
      const { data: photosData, error: photosError } = await supabase
        .from("photos_v2")
        .select(`
          id,
          url,
          title,
          description,
          photographer,
          location,
          camera_settings,
          weather_conditions,
          time_of_day,
          tags,
          published,
          created_at,
          species_id
        `)
        .eq("published", true)
        .order("created_at", { ascending: false });

      if (photosError) throw photosError;

      // Combine species with their photos and filter out species without published photos
      const allSpeciesWithPhotos = speciesData?.map(species => {
        const speciesPhotos = photosData?.filter(photo => photo.species_id === species.id) || [];
        return {
          ...species,
          photos: speciesPhotos,
          photo_count: speciesPhotos.length
        };
      }) || [];

      // Filter to only include species that have at least one published photo
      const speciesWithPhotos = allSpeciesWithPhotos.filter(species => {
        const hasPublishedPhotos = species.photos.length > 0;
        if (!hasPublishedPhotos) {
          console.log(`Filtering out species without photos: ${species.name} (${species.id})`);
        }
        return hasPublishedPhotos;
      });

      console.log(`PublicWildlifeSite: Filtered ${allSpeciesWithPhotos.length} -> ${speciesWithPhotos.length} species (removed ${allSpeciesWithPhotos.length - speciesWithPhotos.length} without photos)`);

      setSpecies(speciesWithPhotos);
      setPhotos(photosData || []);

    } catch (error) {
      console.error('Error loading data:', error);
      toast.error("Failed to load wildlife data");
    } finally {
      setLoading(false);
    }
  };

  const loadMetadata = async () => {
    try {
      // Get categories and conservation statuses from species_v2
      const { data: speciesData } = await supabase
        .from('species_v2')
        .select('category, conservation_status, photo_count')
        .eq('published', true);

      // Group by category
      const categoryMap = new Map<string, number>();
      const statusMap = new Map<string, number>();
      
      speciesData?.forEach(species => {
        if (species.category) {
          categoryMap.set(species.category, (categoryMap.get(species.category) || 0) + 1);
        }
        if (species.conservation_status) {
          statusMap.set(species.conservation_status, (statusMap.get(species.conservation_status) || 0) + 1);
        }
      });

      setCategories(Array.from(categoryMap.entries()).map(([category, count]) => ({
        category,
        count
      })));
      
      setConservationStatuses(Array.from(statusMap.entries()).map(([status, count]) => ({
        conservation_status: status,
        count
      })));

      // Get locations from photos_v2
      const { data: photoMetadata } = await supabase
        .from('photos_v2')
        .select('location')
        .eq('published', true)
        .not('location', 'is', null);

      const uniqueLocations = [...new Set(photoMetadata?.map(p => p.location).filter(Boolean))];
      setLocations(uniqueLocations);

    } catch (error) {
      console.error('Error loading metadata:', error);
    }
  };

  const filteredSpecies = useMemo(() => {
    const filtered = species.filter(species => {
      const matchesSearch = !filters.search || 
        species.name?.toLowerCase().includes(filters.search.toLowerCase()) ||
        species.scientific_name?.toLowerCase().includes(filters.search.toLowerCase()) ||
        species.description?.toLowerCase().includes(filters.search.toLowerCase());

      const matchesCategory = filters.categories.length === 0 || 
        (species.category && filters.categories.includes(species.category));
      const matchesStatus = filters.conservationStatuses.length === 0 || 
        (species.conservation_status && filters.conservationStatuses.includes(species.conservation_status));
      
      const matchesLocation = filters.locations.length === 0 || 
        (species.photos && species.photos.some(p => p.location && filters.locations.includes(p.location)));

      return matchesSearch && matchesCategory && matchesStatus && matchesLocation && (!filters.featured || species.featured);
    });

    if (sortBy === 'name') {
      filtered.sort((a, b) => {
        const nameA = a.name || '';
        const nameB = b.name || '';
        return sortOrder === 'asc' ? nameA.localeCompare(nameB) : nameB.localeCompare(nameA);
      });
    } else if (sortBy === 'photos') {
      filtered.sort((a, b) => {
        const countA = a.photo_count || 0;
        const countB = b.photo_count || 0;
        return sortOrder === 'asc' ? countA - countB : countB - countA;
      });
    }

    return filtered;
  }, [species, filters, sortBy, sortOrder]);

  const openPhotoGallery = (photo: Photo, index: number) => {
    setSelectedPhoto(photo);
    setCurrentPhotoIndex(index);
    setPhotoGalleryOpen(true);
  };

  const nextPhoto = () => {
    const nextIndex = (currentPhotoIndex + 1) % photos.length;
    setCurrentPhotoIndex(nextIndex);
    setSelectedPhoto(photos[nextIndex]);
  };

  const prevPhoto = () => {
    const prevIndex = currentPhotoIndex === 0 ? photos.length - 1 : currentPhotoIndex - 1;
    setCurrentPhotoIndex(prevIndex);
    setSelectedPhoto(photos[prevIndex]);
  };

  const getConservationStatusColor = (status: string) => {
    switch (status) {
      case 'Least Concern': return 'bg-green-100 text-green-800';
      case 'Near Threatened': return 'bg-yellow-100 text-yellow-800';
      case 'Vulnerable': return 'bg-orange-100 text-orange-800';
      case 'Endangered': return 'bg-red-100 text-red-800';
      case 'Critically Endangered': return 'bg-red-200 text-red-900';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const updateFilter = (key: keyof FilterState, value: string | boolean | string[]) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      categories: [],
      conservationStatuses: [],
      locations: [],
      published: true,
      featured: false
    });
  };

  const toggleFilter = (key: keyof FilterState, value: string) => {
    setFilters(prev => {
      const currentArray = prev[key] as string[];
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value];
      return { ...prev, [key]: newArray };
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-green-50 via-white to-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Loading wildlife data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-green-50 via-white to-white">
      {/* Sticky Header */}
      <header className="sticky top-0 z-10 backdrop-blur-md bg-white/80 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900 flex items-center gap-2">
                <Leaf className="w-6 h-6 md:w-8 md:h-8 text-green-600" />
                Wildlife Explorer
              </h1>
              <p className="text-sm md:text-base text-gray-600 mt-1">Discover amazing species and their habitats</p>
            </div>
            <div className="flex items-center gap-2 md:gap-4">
              <Button variant="outline" size="sm" className="h-10 px-3 rounded-lg">
                <Share2 className="w-4 h-4 mr-2" />
                <span className="hidden md:inline">Share</span>
              </Button>
            </div>
          </div>

          {/* Sticky Search and Filters */}
          <div className="space-y-4">
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search species, regions, categories, photographers..."
                value={filters.search}
                onChange={(e) => updateFilter('search', e.target.value)}
                className="pl-10 pr-4 h-12 rounded-lg border-gray-200 focus:border-green-500 focus:ring-green-500"
              />
            </div>

            {/* Filter Controls */}
            <div className="flex flex-wrap items-center gap-2 md:gap-4">
              {/* Category Filter */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm" className="h-10 px-3 rounded-lg">
                    <Sliders className="w-4 h-4 mr-2" />
                    Categories
                    {filters.categories.length > 0 && (
                      <Badge variant="secondary" className="ml-2 text-xs">
                        {filters.categories.length}
                      </Badge>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-64 p-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">Categories</h4>
                    {categories.map(cat => (
                      <div key={cat.category} className="flex items-center space-x-2">
                        <Checkbox
                          id={`cat-${cat.category}`}
                          checked={filters.categories.includes(cat.category)}
                          onCheckedChange={() => toggleFilter('categories', cat.category)}
                        />
                        <label htmlFor={`cat-${cat.category}`} className="text-sm">
                          {cat.category} ({cat.count})
                        </label>
                      </div>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>

              {/* Conservation Status Filter */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm" className="h-10 px-3 rounded-lg">
                    <Shield className="w-4 h-4 mr-2" />
                    Status
                    {filters.conservationStatuses.length > 0 && (
                      <Badge variant="secondary" className="ml-2 text-xs">
                        {filters.conservationStatuses.length}
                      </Badge>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-64 p-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">Conservation Status</h4>
                    {conservationStatuses.map(status => (
                      <div key={status.conservation_status} className="flex items-center space-x-2">
                        <Checkbox
                          id={`status-${status.conservation_status}`}
                          checked={filters.conservationStatuses.includes(status.conservation_status)}
                          onCheckedChange={() => toggleFilter('conservationStatuses', status.conservation_status)}
                        />
                        <label htmlFor={`status-${status.conservation_status}`} className="text-sm">
                          {status.conservation_status} ({status.count})
                        </label>
                      </div>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>

              {/* Location Filter */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm" className="h-10 px-3 rounded-lg">
                    <MapPin className="w-4 h-4 mr-2" />
                    Location
                    {filters.locations.length > 0 && (
                      <Badge variant="secondary" className="ml-2 text-xs">
                        {filters.locations.length}
                      </Badge>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-64 p-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">Locations</h4>
                    {locations.map(location => (
                      <div key={location} className="flex items-center space-x-2">
                        <Checkbox
                          id={`loc-${location}`}
                          checked={filters.locations.includes(location)}
                          onCheckedChange={() => toggleFilter('locations', location)}
                        />
                        <label htmlFor={`loc-${location}`} className="text-sm">
                          {location}
                        </label>
                      </div>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>

              {/* Featured Toggle */}
              <Button
                variant={filters.featured ? "default" : "outline"}
                size="sm"
                className="h-10 px-3 rounded-lg"
                onClick={() => updateFilter('featured', !filters.featured)}
              >
                <Star className="w-4 h-4 mr-2" />
                Featured
              </Button>

              {/* Sort */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm" className="h-10 px-3 rounded-lg">
                    <ArrowUpDown className="w-4 h-4 mr-2" />
                    Sort
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">Sort By</h4>
                    <Select value={sortBy} onValueChange={(value: 'name' | 'date' | 'photos') => setSortBy(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="name">Name</SelectItem>
                        <SelectItem value="photos">Photo Count</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select value={sortOrder} onValueChange={(value: 'asc' | 'desc') => setSortOrder(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="asc">Ascending</SelectItem>
                        <SelectItem value="desc">Descending</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </PopoverContent>
              </Popover>

              {/* Clear Filters */}
              {(filters.categories.length > 0 || filters.conservationStatuses.length > 0 || 
                filters.locations.length > 0 || filters.featured) && (
                <Button
                  variant="outline"
                  size="sm"
                  className="h-10 px-3 rounded-lg"
                  onClick={clearFilters}
                >
                  <X className="w-4 h-4 mr-2" />
                  Clear
                </Button>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Stats */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="bg-white rounded-xl border border-gray-200 shadow-md p-4">
            <CardContent className="p-0">
              <div className="flex items-center gap-2">
                <Leaf className="w-5 h-5 text-green-600" />
                <div>
                  <p className="text-xl md:text-2xl font-bold">{filteredSpecies.length}</p>
                  <p className="text-xs md:text-sm text-gray-600">Species</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-white rounded-xl border border-gray-200 shadow-md p-4">
            <CardContent className="p-0">
              <div className="flex items-center gap-2">
                <Camera className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="text-xl md:text-2xl font-bold">{photos.length}</p>
                  <p className="text-xs md:text-sm text-gray-600">Photos</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white rounded-xl border border-gray-200 shadow-md p-4">
            <CardContent className="p-0">
              <div className="flex items-center gap-2">
                <Globe className="w-5 h-5 text-purple-600" />
                <div>
                  <p className="text-xl md:text-2xl font-bold">{categories.length}</p>
                  <p className="text-xs md:text-sm text-gray-600">Categories</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white rounded-xl border border-gray-200 shadow-md p-4">
            <CardContent className="p-0">
              <div className="flex items-center gap-2">
                <Shield className="w-5 h-5 text-orange-600" />
                <div>
                  <p className="text-xl md:text-2xl font-bold">{conservationStatuses.length}</p>
                  <p className="text-xs md:text-sm text-gray-600">Statuses</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
        <Tabs defaultValue="species" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 bg-white rounded-xl border border-gray-200 shadow-sm p-1">
            <TabsTrigger value="species" className="flex items-center gap-2 h-12 rounded-lg">
              <Leaf className="w-4 h-4" />
              <span className="hidden sm:inline">Species</span>
              <Badge variant="secondary" className="ml-1">
                {filteredSpecies.length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="gallery" className="flex items-center gap-2 h-12 rounded-lg">
              <Camera className="w-4 h-4" />
              <span className="hidden sm:inline">Gallery</span>
              <Badge variant="secondary" className="ml-1">
                {photos.length}
              </Badge>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="species" className="space-y-6">
            {/* View Controls */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="h-10 px-3 rounded-lg"
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-10 px-3 rounded-lg"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Empty State */}
            {filteredSpecies.length === 0 && !loading && (
              <div className="flex flex-col items-center justify-center text-gray-600 mt-12 py-12">
                <GlobeIcon className="w-12 h-12 mb-4 text-gray-400" />
                <p className="text-lg font-medium">No species found.</p>
                <p className="text-sm">Try adjusting your filters or syncing the database.</p>
              </div>
            )}

            {/* Species Grid/List */}
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredSpecies.map((species) => (
                  <Card 
                    key={species.id} 
                    className="bg-white rounded-xl border border-gray-200 shadow-md overflow-hidden hover:shadow-lg transition-all duration-200 hover:scale-[1.02] cursor-pointer group"
                  >
                    <div className="relative h-48 bg-gray-200">
                      {species.photos && species.photos.length > 0 ? (
                        <img
                          src={species.photos[0].url}
                          alt={species.name}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = '/placeholder.svg';
                          }}
                        />
                      ) : (
                        <div className="w-full h-full flex flex-col items-center justify-center text-gray-400 bg-gray-100">
                          <Camera className="w-12 h-12" />
                          <p className="mt-2 text-sm">📷 No photo yet</p>
                        </div>
                      )}
                      <div className="absolute top-2 right-2 flex flex-col gap-2">
                        {species.featured && (
                          <Badge variant="default" className="bg-yellow-500">
                            <Star className="w-3 h-3 mr-1" />
                            Featured
                          </Badge>
                        )}
                      </div>
                      <div className="absolute top-2 left-2">
                        <Badge variant="secondary" className="bg-black/50 text-white">
                          <Camera className="w-3 h-3 mr-1" />
                          {species.photo_count || 0}
                        </Badge>
                      </div>
                    </div>
                    <CardContent className="p-4">
                      <h3 className="font-semibold text-lg mb-1 line-clamp-1">{species.name}</h3>
                      {species.common_name && species.common_name !== species.name && (
                        <p className="text-sm text-gray-600 mb-2 line-clamp-1">{species.common_name}</p>
                      )}
                      {species.scientific_name && (
                        <p className="text-sm italic text-gray-500 mb-2 line-clamp-1">{species.scientific_name}</p>
                      )}
                      <div className="flex items-center gap-2 mb-3 flex-wrap">
                        <Badge variant="outline" className="text-xs">{species.category}</Badge>
                        {species.conservation_status && (
                          <Badge className={`text-xs ${getConservationStatusColor(species.conservation_status)}`}>
                            {species.conservation_status}
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center justify-between">
                         <div className="flex items-center gap-1 text-sm text-gray-500">
                          {species.photos && species.photos.length > 0 && species.photos[0].location && (
                            <>
                              <MapPin className="w-4 h-4" />
                              <span className="truncate">{species.photos[0].location.split(',')[0]}</span>
                            </>
                          )}
                        </div>
                        <Button 
                          size="sm"
                          className="h-8 px-3 rounded-lg"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedSpecies(species);
                            setSpeciesModalOpen(true);
                          }}
                        >
                          View Details
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredSpecies.map((species) => (
                  <Card key={species.id} className="bg-white rounded-xl border border-gray-200 shadow-md hover:shadow-lg transition-shadow cursor-pointer p-6">
                    <CardContent className="p-0">
                      <div className="flex items-start gap-4">
                        <div className="w-20 h-20 md:w-24 md:h-24 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                          {species.photos && species.photos.length > 0 ? (
                            <img
                              src={species.photos[0].url}
                              alt={species.name}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = '/placeholder.svg';
                              }}
                            />
                          ) : (
                            <div className="w-full h-full flex flex-col items-center justify-center text-gray-400 bg-gray-100">
                              <Camera className="w-8 h-8" />
                              <p className="mt-1 text-xs">No photo</p>
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div className="min-w-0 flex-1">
                              <h3 className="font-semibold text-lg line-clamp-1">{species.name}</h3>
                              {species.common_name && species.common_name !== species.name && (
                                <p className="text-sm text-gray-600 line-clamp-1">{species.common_name}</p>
                              )}
                              {species.scientific_name && (
                                <p className="text-sm italic text-gray-500 line-clamp-1">{species.scientific_name}</p>
                              )}
                            </div>
                            <div className="flex items-center gap-2 ml-4">
                              {species.featured && (
                                <Badge variant="default" className="bg-yellow-500 text-xs">
                                  <Star className="w-3 h-3 mr-1" />
                                  Featured
                                </Badge>
                              )}
                              <Badge variant="outline" className="text-xs">{species.category}</Badge>
                              {species.conservation_status && (
                                <Badge className={`text-xs ${getConservationStatusColor(species.conservation_status)}`}>
                                  {species.conservation_status}
                                </Badge>
                              )}
                            </div>
                          </div>
                          {species.description && (
                            <p className="text-sm text-gray-600 mt-2 line-clamp-2">{species.description.replace(/AI Facts: \[object Object\]/g, '')}</p>
                          )}
                          <div className="flex items-center gap-4 mt-3">
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                              <Camera className="w-4 h-4" />
                              {species.photo_count || 0} photos
                            </div>
                            <Button 
                              size="sm"
                              className="h-8 px-3 rounded-lg"
                              onClick={() => {
                                setSelectedSpecies(species);
                                setSpeciesModalOpen(true);
                              }}
                            >
                              View Details
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="gallery" className="space-y-6">
            {/* Gallery Controls */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button
                  variant={galleryView === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setGalleryView('grid')}
                  className="h-10 px-3 rounded-lg"
                >
                  <Grid3X3 className="w-4 h-4" />
                  Grid
                </Button>
                <Button
                  variant={galleryView === 'masonry' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setGalleryView('masonry')}
                  className="h-10 px-3 rounded-lg"
                >
                  <List className="w-4 h-4" />
                  Masonry
                </Button>
              </div>
            </div>

            {/* Photo Gallery */}
            <div className={galleryView === 'grid' 
              ? "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4" 
              : "columns-2 sm:columns-3 md:columns-4 lg:columns-6 gap-4"
            }>
              {photos.map((photo, index) => (
                <div
                  key={photo.id}
                  className={`relative group cursor-pointer ${
                    galleryView === 'masonry' ? 'break-inside-avoid mb-4' : ''
                  }`}
                  onClick={() => openPhotoGallery(photo, index)}
                >
                  <img
                    src={photo.url}
                    alt={photo.title || 'Wildlife photo'}
                    className={`w-full object-cover rounded-lg border-2 border-transparent group-hover:border-green-300 transition-colors ${
                      galleryView === 'grid' ? 'h-32' : 'h-auto'
                    }`}
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/placeholder.svg';
                    }}
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all rounded-lg flex flex-col justify-center p-2 text-white text-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                      <p className="text-sm font-semibold">{photo.title || 'Untitled'}</p>
                      <p className="text-xs">{photo.location}</p>
                      <p className="text-xs">{photo.photographer}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Species Detail Modal */}
      <Dialog open={speciesModalOpen} onOpenChange={setSpeciesModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          {selectedSpecies && (
            <div className="space-y-6 h-full flex flex-col">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Leaf className="w-6 h-6 text-green-600" />
                  {selectedSpecies.name}
                </DialogTitle>
              </DialogHeader>
              <div className="overflow-y-auto pr-4 -mr-4 flex-grow">
                <SpeciesDetailModal species={selectedSpecies} />
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Photo Gallery Modal */}
      <Dialog open={photoGalleryOpen} onOpenChange={setPhotoGalleryOpen}>
        <DialogContent className="max-w-5xl max-h-[95vh] overflow-hidden">
          {selectedPhoto && (
            <div className="space-y-4">
              <div className="relative">
                <img
                  src={selectedPhoto.url}
                  alt={selectedPhoto.title || 'Wildlife photo'}
                  className="w-full h-[60vh] object-contain rounded-lg"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/placeholder.svg';
                  }}
                />
                <div className="absolute top-2 right-2 flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setAutoPlay(!autoPlay)}
                  >
                    {autoPlay ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                  </Button>

                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="absolute top-2 left-2"
                  onClick={prevPhoto}
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="absolute top-2 left-12"
                  onClick={nextPhoto}
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
                <div className="absolute bottom-2 left-2 right-2">
                  <div className="bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                    {currentPhotoIndex + 1} of {photos.length}
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <h3 className="font-semibold text-lg">{selectedPhoto.title || 'Untitled'}</h3>
                {selectedPhoto.description && (
                  <p className="text-gray-600">{selectedPhoto.description}</p>
                )}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500">
                  {selectedPhoto.photographer && (
                    <div className="flex items-center gap-1">
                      <Users className="w-4 h-4" />
                      {selectedPhoto.photographer}
                    </div>
                  )}
                  {selectedPhoto.location && (
                    <div className="flex items-center gap-1">
                      <MapPin className="w-4 h-4" />
                      {selectedPhoto.location}
                    </div>
                  )}
                  {selectedPhoto.camera_settings && (
                    <div className="flex items-center gap-1">
                      <Camera className="w-4 h-4" />
                      {selectedPhoto.camera_settings}
                    </div>
                  )}
                  {selectedPhoto.created_at && (
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {new Date(selectedPhoto.created_at).toLocaleDateString()}
                    </div>
                  )}
                </div>
                {selectedPhoto.species && (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{selectedPhoto.species.category}</Badge>
                    <Badge className={getConservationStatusColor(selectedPhoto.species.conservation_status || '')}>
                      {selectedPhoto.species.conservation_status}
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Species Detail Modal Component
function SpeciesDetailModal({ species }: { species: Species }) {
  const formattedFacts = useMemo(() => {
    if (!species.tags) return null;
    if (Array.isArray(species.tags)) {
      return species.tags.join(" • ");
    }
    if (typeof species.tags === 'object' && species.tags !== null) {
      return Object.values(species.tags).join(" • ");
    }
    if (typeof species.tags === 'string') {
      return species.tags;
    }
    return null;
  }, [species.tags]);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h4 className="font-medium mb-2">Basic Information</h4>
          <div className="space-y-2 text-sm">
            <div><strong>Name:</strong> {species.name}</div>
            {species.common_name && <div><strong>Common Name:</strong> {species.common_name}</div>}
            {species.scientific_name && <div><strong>Scientific Name:</strong> <em>{species.scientific_name}</em></div>}
            <div><strong>Category:</strong> {species.category}</div>
            {species.conservation_status && <div><strong>Conservation Status:</strong> {species.conservation_status}</div>}
          </div>
        </div>
        <div>
          <h4 className="font-medium mb-2">Habitat & Behavior</h4>
          <div className="space-y-2 text-sm">
            {species.habitat && <div><strong>Habitat:</strong> {species.habitat}</div>}
            {species.diet && <div><strong>Diet:</strong> {species.diet}</div>}
            {species.behavior && <div><strong>Behavior:</strong> {species.behavior}</div>}
          </div>
        </div>
      </div>
      
      {species.description && (
        <div>
          <h4 className="font-medium mb-2">Description</h4>
          <p className="text-sm text-gray-600">{species.description.replace(/AI Facts: \[object Object\]/g, '')}</p>
        </div>
      )}

      {formattedFacts && (
        <div>
          <h4 className="font-medium mb-2">Fun Facts</h4>
          <p className="text-sm text-gray-600">{formattedFacts}</p>
        </div>
      )}

      {species.photos && species.photos.length > 0 && (
        <div>
          <h4 className="font-medium mb-4">Photos ({species.photos.length})</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {species.photos.map((photo) => (
              <div key={photo.id} className="relative group">
                <img
                  src={photo.url}
                  alt={photo.title || 'Species photo'}
                  className="w-full h-24 object-cover rounded-lg"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/placeholder.svg';
                  }}
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all rounded-lg flex flex-col justify-end p-2 text-white">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity text-xs">
                    {photo.location && (
                      <div className="flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        <span className="truncate">{photo.location}</span>
                      </div>
                    )}
                    {photo.time_of_day && (
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        <span>{new Date(photo.time_of_day).toLocaleDateString()}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 