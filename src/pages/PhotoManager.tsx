import { <PERSON> } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { PhotoManager } from "@/components/PhotoManager";

const PhotoManagerPage = () => {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="relative bg-gradient-to-b from-black/80 to-black/60 py-16">
        <div className="container mx-auto px-6">
          <Link to="/" className="inline-flex items-center text-gray-300 hover:text-white mb-6">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Wildlife Explorer
          </Link>
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-light mb-4 tracking-wide">Photo Manager</h1>
            <p className="text-xl text-gray-300 leading-relaxed">
              Upload, organize, and manage your wildlife photography collection
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-12">
        <PhotoManager mode="gallery" title="Photo Gallery" />
      </div>
    </div>
  );
};

export default PhotoManagerPage;