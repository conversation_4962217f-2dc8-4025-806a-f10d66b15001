import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  MapPin,
  Navigation,
  Clock,
  DollarSign,
  Phone,
  Globe,
  Camera,
  Star,
  Bird,
  TreePine,
  Users,
  Calendar,
  ArrowLeft,
  ExternalLink,
  Info,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { useLocation } from '@/hooks/useLocationData';
import { LoadingSpinner } from '@/components/public/LoadingSpinner';
import { SEOHead } from '@/components/public/SEOHead';

export default function HotspotDetail() {
  const { id } = useParams<{ id: string }>();
  const { location, species, photos, loading, error } = useLocation(id!);
  const [selected<PERSON><PERSON><PERSON>, setSelectedMonth] = useState<number | null>(null);

  if (loading) return <LoadingSpinner />;
  if (error || !location) return (
    <div className="text-center text-red-600 p-8">
      Error: {error || 'Hotspot not found'}
    </div>
  );

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800 border-green-200';
      case 'moderate': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'difficult': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'expert': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getAbundanceColor = (abundance: string) => {
    switch (abundance) {
      case 'very_common': return 'bg-green-500';
      case 'common': return 'bg-green-400';
      case 'uncommon': return 'bg-yellow-400';
      case 'rare': return 'bg-orange-400';
      default: return 'bg-gray-400';
    }
  };

  const monthNames = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
  ];

  const filteredSpecies = selectedMonth 
    ? species.filter(s => s.best_months?.includes(selectedMonth))
    : species;

  // Generate static map URL (using Google Static Maps API)
  const getStaticMapUrl = () => {
    const { latitude, longitude } = location;
    return `https://maps.googleapis.com/maps/api/staticmap?center=${latitude},${longitude}&zoom=14&size=600x300&maptype=terrain&markers=color:red%7C${latitude},${longitude}&key=YOUR_GOOGLE_MAPS_API_KEY`;
  };

  // Generate directions URLs
  const getDirectionsUrls = () => {
    const { latitude, longitude, name } = location;
    const encodedName = encodeURIComponent(name);
    
    return {
      google: `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}&destination_place_id=${encodedName}`,
      apple: `http://maps.apple.com/?daddr=${latitude},${longitude}&dirflg=d`,
      waze: `https://waze.com/ul?ll=${latitude},${longitude}&navigate=yes`
    };
  };

  const directionsUrls = getDirectionsUrls();

  return (
    <>
      <SEOHead 
        title={`${location.name} - Wildlife Hotspot`}
        description={location.description || `Discover wildlife at ${location.name}. View species information, visiting tips, and directions.`}
      />
      
      <div className="container mx-auto p-6 space-y-6">
        {/* Back Button */}
        <Link to="/hotspots">
          <Button variant="outline" className="mb-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Hotspots
          </Button>
        </Link>

        {/* Header */}
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-4xl font-bold text-gray-900">{location.name}</h1>
                {location.featured && (
                  <Badge className="bg-yellow-500 text-white">
                    <Star className="w-4 h-4 mr-1" />
                    Featured
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2 text-gray-600">
                <MapPin className="w-5 h-5" />
                <span className="text-lg">
                  {location.state_province && location.country 
                    ? `${location.state_province}, ${location.country}`
                    : location.country || 'Location not specified'
                  }
                </span>
              </div>
            </div>

            <div className="flex gap-2">
              {location.difficulty_level && (
                <Badge className={getDifficultyColor(location.difficulty_level)}>
                  {location.difficulty_level}
                </Badge>
              )}
              {location.access_type && (
                <Badge variant="outline">
                  {location.access_type.replace('_', ' ')}
                </Badge>
              )}
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{location.total_species || 0}</div>
              <div className="text-sm text-gray-600">Total Species</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{location.common_species || 0}</div>
              <div className="text-sm text-gray-600">Common Species</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{location.rare_species || 0}</div>
              <div className="text-sm text-gray-600">Rare Species</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{location.breeding_species || 0}</div>
              <div className="text-sm text-gray-600">Breeding Species</div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="species">Species ({species.length})</TabsTrigger>
            <TabsTrigger value="directions">Directions</TabsTrigger>
            <TabsTrigger value="photos">Photos ({photos.length})</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Description */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Info className="w-5 h-5" />
                    About This Location
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {location.description && (
                    <p className="text-gray-700">{location.description}</p>
                  )}
                  
                  {location.habitat_types && location.habitat_types.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <TreePine className="w-4 h-4" />
                        Habitat Types
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {location.habitat_types.map((habitat, index) => (
                          <Badge key={index} variant="outline">
                            {habitat.replace('_', ' ')}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {location.target_species && location.target_species.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <Bird className="w-4 h-4" />
                        Target Species
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {location.target_species.map((species, index) => (
                          <Badge key={index} className="bg-green-100 text-green-800">
                            {species}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Practical Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5" />
                    Practical Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {location.best_time_to_visit && (
                    <div className="flex items-start gap-3">
                      <Clock className="w-5 h-5 text-blue-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium">Best Time to Visit</h4>
                        <p className="text-gray-600 text-sm">{location.best_time_to_visit}</p>
                      </div>
                    </div>
                  )}

                  {location.entrance_fee && (
                    <div className="flex items-start gap-3">
                      <DollarSign className="w-5 h-5 text-green-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium">Entrance Fee</h4>
                        <p className="text-gray-600 text-sm">${location.entrance_fee}</p>
                      </div>
                    </div>
                  )}

                  {location.parking_info && (
                    <div className="flex items-start gap-3">
                      <Navigation className="w-5 h-5 text-purple-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium">Parking</h4>
                        <p className="text-gray-600 text-sm">{location.parking_info}</p>
                      </div>
                    </div>
                  )}

                  {location.facilities && location.facilities.length > 0 && (
                    <div className="flex items-start gap-3">
                      <Users className="w-5 h-5 text-orange-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium">Facilities</h4>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {location.facilities.map((facility, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {facility.replace('_', ' ')}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {location.website_url && (
                    <div className="flex items-start gap-3">
                      <Globe className="w-5 h-5 text-blue-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium">Website</h4>
                        <a 
                          href={location.website_url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline text-sm flex items-center gap-1"
                        >
                          Visit Website <ExternalLink className="w-3 h-3" />
                        </a>
                      </div>
                    </div>
                  )}

                  {location.contact_info && (
                    <div className="flex items-start gap-3">
                      <Phone className="w-5 h-5 text-green-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium">Contact</h4>
                        <p className="text-gray-600 text-sm">{location.contact_info}</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Visitor Tips */}
            {location.visitor_tips && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5" />
                    Visitor Tips
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700">{location.visitor_tips}</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Species Tab */}
          <TabsContent value="species" className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-semibold">Species Found at This Location</h3>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Filter by month:</span>
                <select
                  value={selectedMonth || ''}
                  onChange={(e) => setSelectedMonth(e.target.value ? parseInt(e.target.value) : null)}
                  className="border rounded px-2 py-1 text-sm"
                >
                  <option value="">All months</option>
                  {monthNames.map((month, index) => (
                    <option key={index} value={index + 1}>{month}</option>
                  ))}
                </select>
              </div>
            </div>

            {filteredSpecies.length === 0 ? (
              <Card className="p-8 text-center">
                <Bird className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-semibold mb-2">No species data available</h3>
                <p className="text-gray-600">
                  {selectedMonth
                    ? `No species recorded for ${monthNames[selectedMonth - 1]}`
                    : 'No species have been recorded at this location yet.'
                  }
                </p>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredSpecies.map((speciesData) => (
                  <Card key={speciesData.species_id} className="hover:shadow-lg transition-shadow">
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900 line-clamp-1">
                              {speciesData.species_name}
                            </h4>
                            {speciesData.common_name && speciesData.common_name !== speciesData.species_name && (
                              <p className="text-sm text-gray-600 line-clamp-1">
                                {speciesData.common_name}
                              </p>
                            )}
                            {speciesData.scientific_name && (
                              <p className="text-sm italic text-gray-500 line-clamp-1">
                                {speciesData.scientific_name}
                              </p>
                            )}
                          </div>
                          <div className="flex flex-col items-end gap-1">
                            <div
                              className={`w-3 h-3 rounded-full ${getAbundanceColor(speciesData.abundance)}`}
                              title={`Abundance: ${speciesData.abundance.replace('_', ' ')}`}
                            />
                            {speciesData.breeding_status === 'confirmed' && (
                              <Badge variant="secondary" className="text-xs">Breeding</Badge>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>
                            {speciesData.abundance.replace('_', ' ')}
                          </span>
                          {speciesData.observation_count > 0 && (
                            <span>{speciesData.observation_count} observations</span>
                          )}
                        </div>

                        {speciesData.best_months && speciesData.best_months.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {speciesData.best_months.map((month) => (
                              <Badge
                                key={month}
                                variant="outline"
                                className="text-xs"
                              >
                                {monthNames[month - 1]}
                              </Badge>
                            ))}
                          </div>
                        )}

                        {speciesData.notes && (
                          <p className="text-xs text-gray-600 line-clamp-2">
                            {speciesData.notes}
                          </p>
                        )}

                        <Link to={`/wildlife/${speciesData.species_id}`}>
                          <Button size="sm" variant="outline" className="w-full">
                            View Species Details
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          {/* Directions Tab */}
          <TabsContent value="directions" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Static Map */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="w-5 h-5" />
                    Location Map
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Placeholder for static map - replace with actual implementation */}
                    <div className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                      <div className="text-center text-gray-500">
                        <MapPin className="w-12 h-12 mx-auto mb-2" />
                        <p className="text-sm">Static Map</p>
                        <p className="text-xs">
                          {location.latitude.toFixed(6)}, {location.longitude.toFixed(6)}
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="font-medium">Latitude:</span> {location.latitude.toFixed(6)}
                      </div>
                      <div>
                        <span className="font-medium">Longitude:</span> {location.longitude.toFixed(6)}
                      </div>
                      {location.elevation_m && (
                        <div className="col-span-2">
                          <span className="font-medium">Elevation:</span> {location.elevation_m}m
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Directions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Navigation className="w-5 h-5" />
                    Get Directions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <a
                      href={directionsUrls.google}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block"
                    >
                      <Button variant="outline" className="w-full justify-start">
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Google Maps
                      </Button>
                    </a>

                    <a
                      href={directionsUrls.apple}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block"
                    >
                      <Button variant="outline" className="w-full justify-start">
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Apple Maps
                      </Button>
                    </a>

                    <a
                      href={directionsUrls.waze}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block"
                    >
                      <Button variant="outline" className="w-full justify-start">
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Waze
                      </Button>
                    </a>
                  </div>

                  {location.directions && (
                    <div className="pt-4 border-t">
                      <h4 className="font-medium mb-2">Detailed Directions</h4>
                      <p className="text-sm text-gray-700">{location.directions}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Photos Tab */}
          <TabsContent value="photos" className="space-y-6">
            {photos.length === 0 ? (
              <Card className="p-8 text-center">
                <Camera className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-semibold mb-2">No photos available</h3>
                <p className="text-gray-600">
                  No photos have been uploaded for this location yet.
                </p>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {photos.map((photo) => (
                  <Card key={photo.id} className="overflow-hidden">
                    <div className="aspect-video relative">
                      <img
                        src={photo.photo_url}
                        alt={photo.title || 'Location photo'}
                        className="w-full h-full object-cover"
                      />
                      {photo.is_primary && (
                        <Badge className="absolute top-2 left-2 bg-yellow-500 text-white">
                          <Star className="w-3 h-3 mr-1" />
                          Primary
                        </Badge>
                      )}
                    </div>
                    {(photo.title || photo.description || photo.photographer) && (
                      <CardContent className="p-4">
                        {photo.title && (
                          <h4 className="font-medium text-gray-900 mb-1">{photo.title}</h4>
                        )}
                        {photo.description && (
                          <p className="text-sm text-gray-600 mb-2">{photo.description}</p>
                        )}
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          {photo.photographer && (
                            <span>📸 {photo.photographer}</span>
                          )}
                          {photo.taken_date && (
                            <span>{new Date(photo.taken_date).toLocaleDateString()}</span>
                          )}
                        </div>
                      </CardContent>
                    )}
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
