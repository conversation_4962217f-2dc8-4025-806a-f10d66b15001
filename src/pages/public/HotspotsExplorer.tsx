import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  Search, 
  Filter,
  MapPin,
  Star,
  Camera,
  Users,
  Clock,
  Navigation,
  TreePine,
  Bird,
  Eye,
  Grid3X3,
  List,
  X
} from 'lucide-react';
import { useLocations, type LocationWithStats } from '@/hooks/useLocationData';
import { LoadingSpinner } from '@/components/public/LoadingSpinner';
import { SEOHead } from '@/components/public/SEOHead';

interface FilterState {
  search: string;
  difficulty: string;
  access_type: string;
  habitat_types: string[];
  featured_only: boolean;
  has_photos: boolean;
}

export default function HotspotsExplorer() {
  const { locations, loading, error } = useLocations();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    difficulty: '',
    access_type: '',
    habitat_types: [],
    featured_only: false,
    has_photos: false
  });

  // Extract unique values for filters
  const filterOptions = useMemo(() => {
    const difficulties = [...new Set(locations.map(l => l.difficulty_level).filter(Boolean))];
    const accessTypes = [...new Set(locations.map(l => l.access_type).filter(Boolean))];
    const habitatTypes = [...new Set(locations.flatMap(l => l.habitat_types || []))];
    
    return { difficulties, accessTypes, habitatTypes };
  }, [locations]);

  // Filter locations based on current filters
  const filteredLocations = useMemo(() => {
    return locations.filter(location => {
      // Search filter
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        const matchesSearch = 
          location.name.toLowerCase().includes(searchTerm) ||
          location.description?.toLowerCase().includes(searchTerm) ||
          location.state_province?.toLowerCase().includes(searchTerm) ||
          location.country?.toLowerCase().includes(searchTerm) ||
          location.habitat_types?.some(h => h.toLowerCase().includes(searchTerm));
        
        if (!matchesSearch) return false;
      }

      // Difficulty filter
      if (filters.difficulty && location.difficulty_level !== filters.difficulty) {
        return false;
      }

      // Access type filter
      if (filters.access_type && location.access_type !== filters.access_type) {
        return false;
      }

      // Habitat types filter
      if (filters.habitat_types.length > 0) {
        const hasMatchingHabitat = filters.habitat_types.some(habitat =>
          location.habitat_types?.includes(habitat)
        );
        if (!hasMatchingHabitat) return false;
      }

      // Featured only filter
      if (filters.featured_only && !location.featured) {
        return false;
      }

      // Has photos filter
      if (filters.has_photos && !location.primary_photo) {
        return false;
      }

      return true;
    });
  }, [locations, filters]);

  const updateFilters = (updates: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...updates }));
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      difficulty: '',
      access_type: '',
      habitat_types: [],
      featured_only: false,
      has_photos: false
    });
  };

  const hasActiveFilters = filters.search || filters.difficulty || filters.access_type || 
                          filters.habitat_types.length > 0 || filters.featured_only || filters.has_photos;

  if (loading) return <LoadingSpinner />;
  if (error) return <div className="text-center text-red-600 p-8">Error: {error}</div>;

  return (
    <>
      <SEOHead 
        title="Wildlife Hotspots & Locations"
        description="Discover amazing wildlife viewing locations and hotspots. Find the best places to observe and photograph wildlife species."
      />
      
      <div className="min-h-screen bg-black text-white">
        {/* Header */}
        <div className="relative bg-gradient-to-b from-black/80 to-black/60 py-20">
          <div className="container mx-auto px-6 text-center">
            <h1 className="text-5xl md:text-6xl font-light mb-6 tracking-wide">
              Wildlife Hotspots
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Discover the best locations for wildlife viewing and photography.
              Each hotspot features detailed species information, visiting tips, and directions.
            </p>

            {/* Stats */}
            <div className="flex justify-center gap-12 mt-12 text-center">
              <div>
                <div className="text-3xl font-light text-white">{locations.length}</div>
                <div className="text-sm text-gray-400 uppercase tracking-wider">Locations</div>
              </div>
              <div>
                <div className="text-3xl font-light text-white">
                  {locations.reduce((sum, l) => sum + (l.total_species || 0), 0)}
                </div>
                <div className="text-sm text-gray-400 uppercase tracking-wider">Species Recorded</div>
              </div>
              <div>
                <div className="text-3xl font-light text-white">
                  {locations.filter(l => l.featured).length}
                </div>
                <div className="text-sm text-gray-400 uppercase tracking-wider">Featured Hotspots</div>
              </div>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-6 py-12 space-y-8">
          {/* Search and Controls */}
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                placeholder="Search hotspots, locations, habitats..."
                value={filters.search}
                onChange={(e) => updateFilters({ search: e.target.value })}
                className="pl-10 bg-gray-900 border-gray-700 text-white placeholder-gray-400 focus:border-gray-500"
              />
            </div>

            {/* Controls */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 border-gray-700 text-gray-300 hover:bg-gray-800"
              >
                <Filter className="w-4 h-4" />
                Filters
                {hasActiveFilters && (
                  <Badge variant="secondary" className="ml-1 bg-gray-700 text-gray-300">
                    {[filters.difficulty, filters.access_type, ...filters.habitat_types].filter(Boolean).length +
                     (filters.featured_only ? 1 : 0) + (filters.has_photos ? 1 : 0)}
                  </Badge>
                )}
              </Button>

              <div className="flex border border-gray-700 rounded-lg">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className={`rounded-r-none ${viewMode === 'grid' ? 'bg-white text-black' : 'text-gray-300 hover:bg-gray-800'}`}
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className={`rounded-l-none ${viewMode === 'list' ? 'bg-white text-black' : 'text-gray-300 hover:bg-gray-800'}`}
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <Card className="bg-gray-900 border-gray-700">
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Difficulty */}
                <div>
                  <h3 className="font-semibold mb-3 text-white">Difficulty Level</h3>
                  <Select value={filters.difficulty} onValueChange={(value) => updateFilters({ difficulty: value })}>
                    <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                      <SelectValue placeholder="Any difficulty" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-600">
                      <SelectItem value="" className="text-white hover:bg-gray-700">Any difficulty</SelectItem>
                      {filterOptions.difficulties.map(difficulty => (
                        <SelectItem key={difficulty} value={difficulty} className="text-white hover:bg-gray-700">
                          {difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Access Type */}
                <div>
                  <h3 className="font-semibold mb-3 text-white">Access Type</h3>
                  <Select value={filters.access_type} onValueChange={(value) => updateFilters({ access_type: value })}>
                    <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                      <SelectValue placeholder="Any access" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-600">
                      <SelectItem value="" className="text-white hover:bg-gray-700">Any access</SelectItem>
                      {filterOptions.accessTypes.map(accessType => (
                        <SelectItem key={accessType} value={accessType} className="text-white hover:bg-gray-700">
                          {accessType.replace('_', ' ').charAt(0).toUpperCase() + accessType.replace('_', ' ').slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Habitat Types */}
                <div>
                  <h3 className="font-semibold mb-3 text-white">Habitat Types</h3>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-between bg-gray-800 border-gray-600 text-white hover:bg-gray-700">
                        {filters.habitat_types.length > 0
                          ? `${filters.habitat_types.length} selected`
                          : "Select habitats"
                        }
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-64 p-4 bg-gray-800 border-gray-600">
                      <div className="space-y-2 max-h-48 overflow-y-auto">
                        {filterOptions.habitatTypes.map(habitat => (
                          <div key={habitat} className="flex items-center space-x-2">
                            <Checkbox
                              id={`habitat-${habitat}`}
                              checked={filters.habitat_types.includes(habitat)}
                              onCheckedChange={(checked) => {
                                updateFilters({
                                  habitat_types: checked
                                    ? [...filters.habitat_types, habitat]
                                    : filters.habitat_types.filter(h => h !== habitat)
                                });
                              }}
                            />
                            <label htmlFor={`habitat-${habitat}`} className="text-sm">
                              {habitat.replace('_', ' ').charAt(0).toUpperCase() + habitat.replace('_', ' ').slice(1)}
                            </label>
                          </div>
                        ))}
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>

                {/* Options */}
                <div>
                  <h3 className="font-semibold mb-3">Options</h3>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="featured-only"
                        checked={filters.featured_only}
                        onCheckedChange={(checked) => updateFilters({ featured_only: !!checked })}
                      />
                      <label htmlFor="featured-only" className="text-sm">Featured only</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="has-photos"
                        checked={filters.has_photos}
                        onCheckedChange={(checked) => updateFilters({ has_photos: !!checked })}
                      />
                      <label htmlFor="has-photos" className="text-sm">Has photos</label>
                    </div>
                  </div>
                </div>
              </div>

                {hasActiveFilters && (
                  <div className="mt-4 pt-4 border-t">
                    <Button variant="outline" onClick={clearFilters} className="w-full">
                      <X className="w-4 h-4 mr-2" />
                      Clear All Filters
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Results */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <p className="text-gray-400">
                Showing {filteredLocations.length} of {locations.length} hotspots
              </p>
            </div>

            {filteredLocations.length === 0 ? (
              <Card className="bg-gray-900 border-gray-700 p-12 text-center">
                <MapPin className="w-16 h-16 mx-auto mb-4 text-gray-500" />
                <h3 className="text-xl font-semibold mb-2 text-white">No hotspots found</h3>
                <p className="text-gray-400 mb-4">
                  Try adjusting your search terms or filters to find more locations.
                </p>
                <Button onClick={clearFilters} className="bg-white text-black hover:bg-gray-200">Clear Filters</Button>
              </Card>
            ) : (
              <div className={viewMode === 'grid'
                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                : "space-y-4"
              }>
                {filteredLocations.map((location) => (
                  <HotspotCard
                    key={location.id}
                    location={location}
                    viewMode={viewMode}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

// Hotspot Card Component
interface HotspotCardProps {
  location: LocationWithStats;
  viewMode: 'grid' | 'list';
}

function HotspotCard({ location, viewMode }: HotspotCardProps) {
  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-900 text-green-300 border-green-700';
      case 'moderate': return 'bg-yellow-900 text-yellow-300 border-yellow-700';
      case 'difficult': return 'bg-orange-900 text-orange-300 border-orange-700';
      case 'expert': return 'bg-red-900 text-red-300 border-red-700';
      default: return 'bg-gray-800 text-gray-300 border-gray-600';
    }
  };

  const getAccessTypeIcon = (accessType?: string) => {
    switch (accessType) {
      case 'public': return '🌍';
      case 'private': return '🔒';
      case 'permit_required': return '📋';
      case 'restricted': return '⚠️';
      default: return '📍';
    }
  };

  if (viewMode === 'list') {
    return (
      <Card className="bg-gray-900 border-gray-700 hover:bg-gray-800 transition-all duration-200">
        <CardContent className="p-6">
          <div className="flex gap-6">
            {/* Image */}
            <div className="w-32 h-24 flex-shrink-0 rounded-lg overflow-hidden bg-gray-800">
              {location.primary_photo ? (
                <img
                  src={location.primary_photo}
                  alt={location.primary_photo_title || location.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-500">
                  <Camera className="w-8 h-8" />
                </div>
              )}
            </div>

            {/* Content */}
            <div className="flex-1 space-y-3">
              <div className="flex items-start justify-between">
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="text-xl font-semibold text-white">{location.name}</h3>
                    {location.featured && (
                      <Badge className="bg-yellow-600 text-white">
                        <Star className="w-3 h-3 mr-1" />
                        Featured
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2 text-gray-400 text-sm">
                    <MapPin className="w-4 h-4" />
                    {location.state_province && location.country
                      ? `${location.state_province}, ${location.country}`
                      : location.country || 'Location not specified'
                    }
                  </div>
                </div>

                <Link to={`/hotspots/${location.id}`}>
                  <Button size="sm" className="bg-white text-black hover:bg-gray-200">
                    <Eye className="w-4 h-4 mr-2" />
                    View Details
                  </Button>
                </Link>
              </div>

              {location.description && (
                <p className="text-gray-700 line-clamp-2">{location.description}</p>
              )}

              <div className="flex items-center gap-4 text-sm">
                {location.total_species && (
                  <div className="flex items-center gap-1 text-green-600">
                    <Bird className="w-4 h-4" />
                    {location.total_species} species
                  </div>
                )}
                {location.difficulty_level && (
                  <Badge className={getDifficultyColor(location.difficulty_level)}>
                    {location.difficulty_level}
                  </Badge>
                )}
                {location.access_type && (
                  <div className="flex items-center gap-1">
                    <span>{getAccessTypeIcon(location.access_type)}</span>
                    <span className="capitalize">{location.access_type.replace('_', ' ')}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gray-900 border-gray-700 overflow-hidden hover:bg-gray-800 transition-all duration-300 transform hover:-translate-y-1">
      <Link to={`/hotspots/${location.id}`}>
        {/* Image */}
        <div className="relative h-48 overflow-hidden">
          {location.primary_photo ? (
            <img
              src={location.primary_photo}
              alt={location.primary_photo_title || location.name}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-700 flex items-center justify-center">
              <div className="text-center text-gray-400">
                <Camera className="w-12 h-12 mx-auto mb-2" />
                <p className="text-sm font-medium">No photo available</p>
              </div>
            </div>
          )}

          {/* Overlay badges */}
          <div className="absolute top-3 left-3 flex flex-col gap-2">
            {location.featured && (
              <Badge className="bg-yellow-600 text-white">
                <Star className="w-3 h-3 mr-1" />
                Featured
              </Badge>
            )}
            {location.difficulty_level && (
              <Badge className={getDifficultyColor(location.difficulty_level)}>
                {location.difficulty_level}
              </Badge>
            )}
          </div>

          <div className="absolute top-3 right-3">
            {location.access_type && (
              <Badge variant="secondary" className="bg-black/70 text-white border-gray-600">
                <span className="mr-1">{getAccessTypeIcon(location.access_type)}</span>
                {location.access_type.replace('_', ' ')}
              </Badge>
            )}
          </div>
        </div>

        {/* Content */}
        <CardContent className="p-4">
          <div className="space-y-3">
            <div>
              <h3 className="font-bold text-lg text-white line-clamp-1">{location.name}</h3>
              <div className="flex items-center gap-1 text-sm text-gray-400">
                <MapPin className="w-4 h-4" />
                {location.state_province && location.country
                  ? `${location.state_province}, ${location.country}`
                  : location.country || 'Location not specified'
                }
              </div>
            </div>

            {location.description && (
              <p className="text-sm text-gray-400 line-clamp-2">{location.description}</p>
            )}

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm">
                {location.total_species && (
                  <div className="flex items-center gap-1 text-green-400">
                    <Bird className="w-4 h-4" />
                    {location.total_species}
                  </div>
                )}
                {location.habitat_types && location.habitat_types.length > 0 && (
                  <div className="flex items-center gap-1 text-blue-400">
                    <TreePine className="w-4 h-4" />
                    {location.habitat_types.length}
                  </div>
                )}
              </div>

              <Button size="sm" className="h-8 px-3 bg-white text-black hover:bg-gray-200">
                <Eye className="w-3 h-3 mr-1" />
                View
              </Button>
            </div>
          </div>
        </CardContent>
      </Link>
    </Card>
  );
}
