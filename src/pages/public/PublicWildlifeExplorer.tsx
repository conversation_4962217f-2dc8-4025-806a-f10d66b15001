import React, { useState, useMemo, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { SEOHead } from '@/components/public/SEOHead';
import { LoadingSpinner } from '@/components/public/LoadingSpinner';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Filter,
  Grid3X3,
  List,
  Camera,
  Leaf,
  Shield,
  MapPin,
  Star,
  X,
  ArrowUpDown,
  Sliders,
  Home
} from 'lucide-react';
import { usePublicSpecies, usePublicPhotos, usePublicMetadata } from '@/hooks/usePublicWildlifeData';
import { PublicSpeciesCard } from '@/components/public/PublicSpeciesCard';
import { PublicPhotoGallery } from '@/components/public/PublicPhotoGallery';
import { useWildlifeAnalytics, usePageAnalytics } from '@/hooks/useAnalytics';
import { PublicHomepage } from '@/components/public/PublicHomepage';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { supabase } from '@/lib/supabase';

interface FilterState {
  search: string;
  category: string;
  conservation_status: string;
  location: string;
  featured: boolean;
}

export default function PublicWildlifeExplorer() {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const { isAdmin } = useAdminAuth();
  const { trackSpeciesSearch, trackSpeciesFilter, trackNavigation } = useWildlifeAnalytics();

  // Track page analytics
  usePageAnalytics('Wildlife Explorer');
  
  // Get initial state from URL params
  const initialFilters: FilterState = {
    search: searchParams.get('search') || '',
    category: searchParams.get('category') || '',
    conservation_status: searchParams.get('status') || '',
    location: searchParams.get('location') || '',
    featured: searchParams.get('featured') === 'true'
  };

  const [filters, setFilters] = useState<FilterState>(initialFilters);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'name' | 'photos'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [photoSortBy, setPhotoSortBy] = useState<'newest' | 'oldest' | 'species' | 'photographer'>('newest');

  // Get initial tab from URL params or default to home
  // If there are filters but no explicit tab, default to species tab
  const hasFilters = initialFilters.search || initialFilters.category || initialFilters.conservation_status ||
                    initialFilters.location || initialFilters.featured;
  const initialTab = searchParams.get('tab') as 'home' | 'species' | 'gallery' || (hasFilters ? 'species' : 'home');
  const [activeTab, setActiveTab] = useState<'home' | 'species' | 'gallery'>(initialTab);

  // Fetch data
  const { data: species = [], isLoading: speciesLoading, error: speciesError } = usePublicSpecies({
    search: filters.search || undefined,
    category: filters.category || undefined,
    conservation_status: filters.conservation_status || undefined,
    location: filters.location || undefined,
    featured: filters.featured || undefined,
    limit: 500 // Increased from 100 to 500 for better coverage
  });



  const { data: photos = [], isLoading: photosLoading } = usePublicPhotos({
    limit: 1000 // Increased from 200 to 1000 for better photo coverage
  });

  // Filter photos based on current filters
  const filteredPhotos = useMemo(() => {
    let filtered = [...photos];

    // Filter by search term (search in title, description, photographer, location, species name)
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(photo =>
        (photo.title?.toLowerCase().includes(searchTerm)) ||
        (photo.description?.toLowerCase().includes(searchTerm)) ||
        (photo.photographer?.toLowerCase().includes(searchTerm)) ||
        (photo.location?.toLowerCase().includes(searchTerm)) ||
        (photo.species?.name?.toLowerCase().includes(searchTerm))
      );
    }

    // Filter by category (through species)
    if (filters.category) {
      filtered = filtered.filter(photo => photo.species?.category === filters.category);
    }

    // Filter by conservation status (through species)
    if (filters.conservation_status) {
      filtered = filtered.filter(photo => photo.species?.conservation_status === filters.conservation_status);
    }

    // Filter by location
    if (filters.location) {
      filtered = filtered.filter(photo =>
        photo.location?.toLowerCase().includes(filters.location.toLowerCase())
      );
    }

    // Sort photos
    switch (photoSortBy) {
      case 'newest':
        filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
        break;
      case 'species':
        filtered.sort((a, b) => {
          const nameA = a.species?.name || '';
          const nameB = b.species?.name || '';
          return nameA.localeCompare(nameB);
        });
        break;
      case 'photographer':
        filtered.sort((a, b) => {
          const photogA = a.photographer || '';
          const photogB = b.photographer || '';
          return photogA.localeCompare(photogB);
        });
        break;
    }

    return filtered;
  }, [photos, filters, photoSortBy]);

  const { data: metadata } = usePublicMetadata();

  // Sync state with URL parameters when they change
  useEffect(() => {
    const newFilters: FilterState = {
      search: searchParams.get('search') || '',
      category: searchParams.get('category') || '',
      conservation_status: searchParams.get('status') || '',
      location: searchParams.get('location') || '',
      featured: searchParams.get('featured') === 'true'
    };

    const hasNewFilters = newFilters.search || newFilters.category || newFilters.conservation_status ||
                         newFilters.location || newFilters.featured;
    const newTab = searchParams.get('tab') as 'home' | 'species' | 'gallery' || (hasNewFilters ? 'species' : 'home');

    setFilters(newFilters);
    setActiveTab(newTab);
  }, [searchParams]);

  // Filter and sort species
  const filteredSpecies = useMemo(() => {
    let filtered = [...species];

    // Sort
    if (sortBy === 'name') {
      filtered.sort((a, b) => {
        const nameA = a.name || '';
        const nameB = b.name || '';
        return sortOrder === 'asc' ? nameA.localeCompare(nameB) : nameB.localeCompare(nameA);
      });
    } else if (sortBy === 'photos') {
      filtered.sort((a, b) => {
        const countA = a.photo_count || 0;
        const countB = b.photo_count || 0;
        return sortOrder === 'asc' ? countA - countB : countB - countA;
      });
    }

    return filtered;
  }, [species, sortBy, sortOrder]);

  // Update URL when filters change
  const updateFilters = (newFilters: Partial<FilterState>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);

    // Update URL params
    const params = new URLSearchParams();
    if (updatedFilters.search) params.set('search', updatedFilters.search);
    if (updatedFilters.category) params.set('category', updatedFilters.category);
    if (updatedFilters.conservation_status) params.set('status', updatedFilters.conservation_status);
    if (updatedFilters.location) params.set('location', updatedFilters.location);
    if (updatedFilters.featured) params.set('featured', 'true');

    // Preserve current tab in URL
    if (activeTab !== 'home') params.set('tab', activeTab);

    setSearchParams(params);
  };

  const handleTabChange = (newTab: 'home' | 'species' | 'gallery') => {
    setActiveTab(newTab);

    // Track navigation analytics
    trackNavigation(newTab);

    // Update URL with new tab
    const params = new URLSearchParams(searchParams);
    if (newTab === 'home') {
      params.delete('tab');
    } else {
      params.set('tab', newTab);
    }
    setSearchParams(params);
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      category: '',
      conservation_status: '',
      location: '',
      featured: false
    });
    setSearchParams(new URLSearchParams());
  };

  const hasActiveFilters = filters.search || filters.category || filters.conservation_status || 
                          filters.location || filters.featured;

  const handleSearch = (query: string) => {
    updateFilters({ search: query });
    setActiveTab('species');

    // Track search analytics
    if (query.trim()) {
      trackSpeciesSearch(query, filteredSpecies.length);
    }
  };

  const handleViewAllSpecies = () => {
    setActiveTab('species');
  };

  const handleViewAllPhotos = () => {
    setActiveTab('gallery');
  };

  const handleLocationSelect = (location: string) => {
    updateFilters({ location });
    setActiveTab('species');

    // Track location filter analytics
    trackSpeciesFilter('location', location);

    // Smooth scroll to species section after a brief delay to allow tab change
    setTimeout(() => {
      const speciesSection = document.querySelector('[data-tab="species"]');
      if (speciesSection) {
        speciesSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    }, 100);
  };

  const handleSpeciesClick = (speciesId: string) => {
    navigate(`/wildlife/${speciesId}`);
  };

  // Admin action handlers
  const handleEditSpecies = (species: any) => {
    navigate(`/admin/species/${species.id}/edit`);
  };

  const handleDeleteSpecies = async (species: any) => {
    if (!window.confirm(`Are you sure you want to delete "${species.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const { error } = await supabase
        .from('species_v2')
        .delete()
        .eq('id', species.id);

      if (error) throw error;

      // Refresh the species data
      window.location.reload();
    } catch (error) {
      console.error('Error deleting species:', error);
      alert('Failed to delete species. Please try again.');
    }
  };

  // Generate SEO metadata based on current filters
  const seoTitle = filters.search
    ? `Search: ${filters.search} - Wildlife Explorer`
    : filters.category
    ? `${filters.category} - Wildlife Explorer`
    : 'Wildlife Explorer - Discover Amazing Species';

  const seoDescription = filters.search
    ? `Search results for "${filters.search}" in our wildlife database. Discover amazing species and their habitats.`
    : filters.category
    ? `Explore ${filters.category.toLowerCase()} species in our wildlife database. View photos and learn about conservation.`
    : 'Explore our collection of stunning wildlife photography and learn about incredible species from around the world.';

  return (
    <>
      <SEOHead
        title={seoTitle}
        description={seoDescription}
        url={window.location.href}
        type="website"
      />
      <div className="min-h-screen bg-gradient-to-b from-green-50 via-white to-white">
      {/* Header */}
      <header className="sticky top-0 z-10 backdrop-blur-md bg-white/90 shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900 flex items-center gap-2">
                <Leaf className="w-6 h-6 md:w-8 md:h-8 text-green-600" />
                Wildlife Explorer
              </h1>
              <p className="text-sm md:text-base text-gray-600 mt-1">
                Discover amazing species and their habitats
              </p>
            </div>
          </div>

          {/* Navigation Tabs */}
          <Tabs value={activeTab} onValueChange={(value) => handleTabChange(value as any)} className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-white rounded-xl border border-gray-200 shadow-sm p-1 h-14">
              <TabsTrigger value="home" className="flex items-center justify-center gap-2 h-12 rounded-lg">
                <Home className="w-4 h-4" />
                <span className="hidden sm:inline">Home</span>
              </TabsTrigger>
              <TabsTrigger value="species" className="flex items-center justify-center gap-2 h-12 rounded-lg">
                <Leaf className="w-4 h-4" />
                <span className="hidden sm:inline">Species</span>
                <Badge variant="secondary" className="ml-1 text-xs px-2 py-0.5">
                  {filteredSpecies.length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger value="gallery" className="flex items-center justify-center gap-2 h-12 rounded-lg">
                <Camera className="w-4 h-4" />
                <span className="hidden sm:inline">Gallery</span>
                <Badge variant="secondary" className="ml-1 text-xs px-2 py-0.5">
                  {filteredPhotos.length}
                </Badge>
              </TabsTrigger>
            </TabsList>
          </Tabs>

          {/* Search and Filters - Only show on species/gallery tabs */}
          {(activeTab === 'species' || activeTab === 'gallery') && (
            <div className="mt-4 space-y-4">
              {/* Search Bar */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search species, locations, photographers..."
                  value={filters.search}
                  onChange={(e) => updateFilters({ search: e.target.value })}
                  className="pl-10 pr-4 h-12 rounded-lg border-gray-200 focus:border-green-500 focus:ring-green-500"
                />
              </div>

              {/* Filter Controls */}
              <div className="flex flex-wrap items-center gap-2 md:gap-4">
                {/* Category Filter */}
                <Select value={filters.category || "all"} onValueChange={(value) => updateFilters({ category: value === "all" ? "" : value })}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {metadata?.categories.map(cat => (
                      <SelectItem key={cat.category} value={cat.category}>
                        {cat.category} ({cat.count})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Conservation Status Filter */}
                <Select value={filters.conservation_status || "all"} onValueChange={(value) => updateFilters({ conservation_status: value === "all" ? "" : value })}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Conservation Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    {metadata?.conservationStatuses.map(status => (
                      <SelectItem key={status.conservation_status} value={status.conservation_status}>
                        {status.conservation_status} ({status.count})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Geographic Filter */}
                <Select value={filters.location || "all"} onValueChange={(value) => updateFilters({ location: value === "all" ? "" : value })}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Geographic Region" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Regions</SelectItem>

                    {/* Countries Section */}
                    {metadata?.countries && metadata.countries.length > 0 && (
                      <>
                        <div className="px-2 py-1 text-xs font-semibold text-gray-500 bg-gray-50">
                          Countries
                        </div>
                        {metadata.countries.map(country => (
                          <SelectItem key={`country-${country.country}`} value={country.country}>
                            {country.country} ({country.count})
                          </SelectItem>
                        ))}
                      </>
                    )}

                    {/* Regions Section */}
                    {metadata?.regions && metadata.regions.length > 0 && (
                      <>
                        <div className="px-2 py-1 text-xs font-semibold text-gray-500 bg-gray-50">
                          Regions
                        </div>
                        {metadata.regions.map(region => (
                          <SelectItem key={`region-${region.region}`} value={region.region}>
                            {region.region} ({region.count})
                          </SelectItem>
                        ))}
                      </>
                    )}

                    {/* States/Provinces Section (only show if not too many) */}
                    {metadata?.statesProvinces && metadata.statesProvinces.length > 0 && metadata.statesProvinces.length <= 20 && (
                      <>
                        <div className="px-2 py-1 text-xs font-semibold text-gray-500 bg-gray-50">
                          States/Provinces
                        </div>
                        {metadata.statesProvinces.map(state => (
                          <SelectItem key={`state-${state.state}`} value={state.state}>
                            {state.state} ({state.count})
                          </SelectItem>
                        ))}
                      </>
                    )}
                  </SelectContent>
                </Select>

                {/* Featured Toggle */}
                <Button
                  variant={filters.featured ? "default" : "outline"}
                  size="sm"
                  className="h-10 px-3 rounded-lg"
                  onClick={() => updateFilters({ featured: !filters.featured })}
                >
                  <Star className="w-4 h-4 mr-2" />
                  Featured
                </Button>

                {/* Sort */}
                {activeTab === 'species' && (
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" size="sm" className="h-10 px-3 rounded-lg">
                        <ArrowUpDown className="w-4 h-4 mr-2" />
                        Sort
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-48 p-4">
                      <div className="space-y-2">
                        <h4 className="font-medium">Sort By</h4>
                        <Select value={sortBy} onValueChange={(value: 'name' | 'photos') => setSortBy(value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="name">Name</SelectItem>
                            <SelectItem value="photos">Photo Count</SelectItem>
                          </SelectContent>
                        </Select>
                        <Select value={sortOrder} onValueChange={(value: 'asc' | 'desc') => setSortOrder(value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="asc">Ascending</SelectItem>
                            <SelectItem value="desc">Descending</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </PopoverContent>
                  </Popover>
                )}

                {/* Clear Filters */}
                {hasActiveFilters && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-10 px-3 rounded-lg"
                    onClick={clearFilters}
                  >
                    <X className="w-4 h-4 mr-2" />
                    Clear
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
        <Tabs value={activeTab} onValueChange={(value) => handleTabChange(value as any)}>
          {/* Homepage */}
          <TabsContent value="home" className="mt-0">
            <PublicHomepage
              onSearch={handleSearch}
              onViewAllSpecies={handleViewAllSpecies}
              onViewAllPhotos={handleViewAllPhotos}
              onLocationSelect={handleLocationSelect}
            />
          </TabsContent>

          {/* Species Tab */}
          <TabsContent value="species" className="space-y-6 mt-6" data-tab="species">
            {/* View Controls */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="h-10 px-3 rounded-lg"
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-10 px-3 rounded-lg"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
              <p className="text-sm text-gray-600">
                {filteredSpecies.length} species found
              </p>
            </div>

            {/* Species Grid/List */}
            {speciesLoading ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {[...Array(8)].map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <div className="h-48 bg-gray-200" />
                    <CardContent className="p-4">
                      <div className="h-4 bg-gray-200 rounded mb-2" />
                      <div className="h-3 bg-gray-200 rounded w-2/3" />
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : filteredSpecies.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 text-gray-500">
                <Leaf className="w-12 h-12 mb-4" />
                <p className="text-lg font-medium">No species found</p>
                <p className="text-sm">Try adjusting your filters</p>
              </div>
            ) : (
              <div className={viewMode === 'grid' 
                ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                : "space-y-4"
              }>
                {filteredSpecies.map((species) => (
                  <PublicSpeciesCard
                    key={species.id}
                    species={species}
                    onViewDetails={() => handleSpeciesClick(species.id)}
                    showAdminActions={isAdmin}
                    onEdit={handleEditSpecies}
                    onDelete={handleDeleteSpecies}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          {/* Gallery Tab */}
          <TabsContent value="gallery" className="space-y-6 mt-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Photo Gallery</h2>
                <p className="text-sm text-gray-600 mt-1">
                  {filteredPhotos.length} photo{filteredPhotos.length !== 1 ? 's' : ''}
                  {filteredPhotos.length !== photos.length && (
                    <span className="text-gray-400"> of {photos.length} total</span>
                  )}
                </p>
              </div>

              <div className="flex items-center gap-4">
                {/* Photo Sort */}
                <Select value={photoSortBy} onValueChange={(value: 'newest' | 'oldest' | 'species' | 'photographer') => setPhotoSortBy(value)}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">Newest First</SelectItem>
                    <SelectItem value="oldest">Oldest First</SelectItem>
                    <SelectItem value="species">By Species</SelectItem>
                    <SelectItem value="photographer">By Photographer</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {photosLoading ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {[...Array(24)].map((_, i) => (
                  <div key={i} className="aspect-square bg-gray-200 rounded-lg animate-pulse" />
                ))}
              </div>
            ) : (
              <PublicPhotoGallery
                photos={filteredPhotos}
                viewMode="grid"
                showSpeciesInfo={true}
              />
            )}
          </TabsContent>
        </Tabs>
      </main>
    </div>
    </>
  );
}
