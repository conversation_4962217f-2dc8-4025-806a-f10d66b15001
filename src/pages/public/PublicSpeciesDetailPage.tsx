import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { SEOHead } from '@/components/public/SEOHead';
import { LoadingSpinner } from '@/components/public/LoadingSpinner';
import { Button } from '@/components/ui/button';
import { ArrowLeft, AlertTriangle, Loader2 } from 'lucide-react';
import { usePublicSpeciesById } from '@/hooks/usePublicWildlifeData';
import { PublicSpeciesDetail } from '@/components/public/PublicSpeciesDetail';
import { useWildlifeAnalytics, usePageAnalytics } from '@/hooks/useAnalytics';

export default function PublicSpeciesDetailPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const { data: species, isLoading, error } = usePublicSpeciesById(id || '');
  const { trackSpeciesView, trackNavigation } = useWildlifeAnalytics();

  // Track page analytics
  usePageAnalytics('Species Detail');

  // Track species view when data loads
  useEffect(() => {
    if (species && !isLoading) {
      trackSpeciesView(species.common_name || 'Unknown Species', species.id);
    }
  }, [species, isLoading, trackSpeciesView]);

  const handleBack = () => {
    navigate('/wildlife');
  };

  const handleFilterByCategory = (category: string) => {
    navigate(`/wildlife?category=${encodeURIComponent(category)}&tab=species`);
  };

  const handleFilterByConservationStatus = (status: string) => {
    navigate(`/wildlife?status=${encodeURIComponent(status)}&tab=species`);
  };

  if (isLoading) {
    return (
      <>
        <SEOHead
          title="Loading Species Details - Wildlife Explorer"
          description="Loading species information and photos..."
        />
        <div className="min-h-screen bg-gradient-to-b from-green-50 via-white to-white">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="flex items-center justify-center py-20">
              <LoadingSpinner
                size="lg"
                message="Loading species details..."
              />
            </div>
          </div>
        </div>
      </>
    );
  }

  if (error || !species) {
    return (
      <>
        <SEOHead
          title="Species Not Found - Wildlife Explorer"
          description="The requested species could not be found."
        />
        <div className="min-h-screen bg-gradient-to-b from-green-50 via-white to-white">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <Button
              variant="outline"
              onClick={handleBack}
              className="mb-8 flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Wildlife Explorer
            </Button>

            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-8 text-center">
                <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                <h2 className="text-xl font-semibold text-red-800 mb-2">
                  Species Not Found
                </h2>
                <p className="text-red-600 mb-4">
                  The species you're looking for doesn't exist or is not published.
                </p>
                <Button onClick={handleBack} className="bg-red-600 hover:bg-red-700">
                  Return to Wildlife Explorer
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <SEOHead
        title={`${species.name} - Wildlife Explorer`}
        description={`Learn about ${species.name}${species.common_name ? ` (${species.common_name})` : ''}, a ${species.category?.toLowerCase()} species. ${species.description?.slice(0, 150)}...`}
        image={species.photos?.[0]?.url}
        type="article"
        species={species}
      />
      <div className="min-h-screen bg-gradient-to-b from-green-50 via-white to-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <PublicSpeciesDetail
            species={species}
            onBack={handleBack}
            showFavorite={false}
            onFilterByCategory={handleFilterByCategory}
            onFilterByConservationStatus={handleFilterByConservationStatus}
          />
        </div>
      </div>
    </>
  );
}
