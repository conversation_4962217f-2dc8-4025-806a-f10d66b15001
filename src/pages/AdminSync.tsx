import { Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { PhotoRematch } from "@/components/PhotoRematch";
import { PhotoGalleryDebug } from "@/components/PhotoGalleryDebug";
import { PhotoDeduplicator } from "@/components/PhotoDeduplicator";
import { ContentManagementSystem } from "@/components/ContentManagementSystem";
import { DataVerification } from "@/components/DataVerification";
import { SpeciesRegistrySync } from "@/components/SpeciesRegistrySync";
import { FolderPhotoMatcher } from "@/components/FolderPhotoMatcher";
import { PhotoDiagnostic } from "@/components/PhotoDiagnostic";
import { APIDocumentation } from "@/components/APIDocumentation";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { SpeciesDeduplicator } from "@/components/SpeciesDeduplicator";
import DataConsistencyDashboard from "@/components/DataConsistencyDashboard";
import { IntelligentUploader } from "@/components/IntelligentUploader";

const AdminSync = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-green-100">
      <div className="bg-gradient-to-r from-green-800 to-green-600 text-white py-8">
        <div className="container mx-auto px-6">
          <Link to="/" className="inline-flex items-center text-green-100 hover:text-white mb-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Wildlife Explorer
          </Link>
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-2">Admin Dashboard</h1>
            <p className="text-xl text-green-100">
              Manage content, sync data, and verify database integrity
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-8">
        <Tabs defaultValue="photo-diagnostic" className="w-full">
          <TabsList className="grid w-full grid-cols-11 mb-8">
            <TabsTrigger value="uploader">Intelligent Uploader</TabsTrigger>
            <TabsTrigger value="photo-diagnostic">Photo Diagnostic</TabsTrigger>
            <TabsTrigger value="folder-matcher">Folder Matcher</TabsTrigger>
            <TabsTrigger value="verification">Data Verification</TabsTrigger>
            <TabsTrigger value="registry">Species Registry</TabsTrigger>
            <TabsTrigger value="cms">Content Management</TabsTrigger>
            <TabsTrigger value="sync">Data Sync</TabsTrigger>
            <TabsTrigger value="cleanup">Data Cleanup</TabsTrigger>
            <TabsTrigger value="consistency">Data Consistency</TabsTrigger>
            <TabsTrigger value="debug">Debug Tools</TabsTrigger>
            <TabsTrigger value="api-docs">API Docs</TabsTrigger>
          </TabsList>

          <TabsContent value="uploader">
            <IntelligentUploader />
          </TabsContent>

          <TabsContent value="photo-diagnostic">
            <PhotoDiagnostic />
          </TabsContent>

          <TabsContent value="folder-matcher">
            <FolderPhotoMatcher />
          </TabsContent>

          <TabsContent value="verification">
            <DataVerification />
          </TabsContent>

          <TabsContent value="registry">
            <SpeciesRegistrySync />
          </TabsContent>

          <TabsContent value="cms">
            <ContentManagementSystem />
          </TabsContent>

          <TabsContent value="sync" className="space-y-8">
            <PhotoRematch />
          </TabsContent>

          <TabsContent value="cleanup" className="space-y-8">
            <SpeciesDeduplicator />
            <PhotoDeduplicator />
          </TabsContent>

          <TabsContent value="consistency">
            <DataConsistencyDashboard />
          </TabsContent>

          <TabsContent value="debug">
            <PhotoGalleryDebug />
          </TabsContent>

          <TabsContent value="api-docs">
            <APIDocumentation />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AdminSync;