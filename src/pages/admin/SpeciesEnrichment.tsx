import React, { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Textarea } from '../../components/ui/textarea';
import { Badge } from '../../components/ui/badge';
import { Switch } from '../../components/ui/switch';
import { Label } from '../../components/ui/label';
import { Loader2, Sparkles, Save, RefreshCw } from 'lucide-react';
import { extractAllFunFacts } from '../../lib/textUtils';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'http://127.0.0.1:54321';
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

const supabase = createClient(supabaseUrl, supabaseKey);

interface Species {
  id: string;
  common_name: string;
  scientific_name: string;
  description: string | null;
  habitat?: string | null;
  diet?: string | null;
  behavior?: string | null;
  regions?: string | null;
}

interface EnrichmentState {
  [speciesId: string]: {
    isEnriching: boolean;
    isSaving: boolean;
    draftDescription: string;
    hasChanges: boolean;
  };
}

export default function SpeciesEnrichment() {
  const [species, setSpecies] = useState<Species[]>([]);
  const [loading, setLoading] = useState(true);
  const [showOnlyMissing, setShowOnlyMissing] = useState(true);
  const [enrichmentStates, setEnrichmentStates] = useState<EnrichmentState>({});
  const [autoRunCleanup, setAutoRunCleanup] = useState(true);

  // Load species data
  useEffect(() => {
    loadSpecies();
  }, [showOnlyMissing]);

  const loadSpecies = async () => {
    setLoading(true);
    try {
      let query = supabase
        .from('species_v2')
        .select('id, common_name, scientific_name, description, habitat, diet, behavior, regions')
        .order('common_name');

      if (showOnlyMissing) {
        query = query.or('description.is.null,description.eq.');
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error loading species:', error);
        return;
      }

      setSpecies(data || []);
    } catch (error) {
      console.error('Error loading species:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEnrich = async (species: Species) => {
    const speciesId = species.id;
    
    setEnrichmentStates(prev => ({
      ...prev,
      [speciesId]: {
        ...prev[speciesId],
        isEnriching: true
      }
    }));

    try {
      const { data: result, error } = await supabase.functions.invoke('enrich-species', {
        body: {
          commonName: species.common_name,
          scientificName: species.scientific_name,
          currentDescription: species.description || undefined
        }
      });

      if (error) {
        throw error;
      }

      if (result && result.description) {
        setEnrichmentStates(prev => ({
          ...prev,
          [speciesId]: {
            ...prev[speciesId],
            isEnriching: false,
            draftDescription: result.description!,
            hasChanges: true
          }
        }));
      } else {
        console.error('Enrichment failed:', result.error);
        alert(`Failed to enrich ${species.common_name}: ${result.error}`);
      }
    } catch (error) {
      console.error('Enrichment error:', error);
      alert(`Error enriching ${species.common_name}: ${error.message}`);
    } finally {
      setEnrichmentStates(prev => ({
        ...prev,
        [speciesId]: {
          ...prev[speciesId],
          isEnriching: false
        }
      }));
    }
  };

  const handleSave = async (species: Species) => {
    const speciesId = species.id;
    const state = enrichmentStates[speciesId];
    
    if (!state || !state.hasChanges) return;

    setEnrichmentStates(prev => ({
      ...prev,
      [speciesId]: {
        ...prev[speciesId],
        isSaving: true
      }
    }));

    try {
      // Update the species description
      const { error: updateError } = await supabase
        .from('species_v2')
        .update({ description: state.draftDescription })
        .eq('id', speciesId);

      if (updateError) {
        throw updateError;
      }

      // Run cleanup if enabled
      if (autoRunCleanup) {
        await runSpeciesCleanup(speciesId, state.draftDescription);
      }

      // Update local state
      setSpecies(prev => prev.map(s => 
        s.id === speciesId 
          ? { ...s, description: state.draftDescription }
          : s
      ));

      setEnrichmentStates(prev => ({
        ...prev,
        [speciesId]: {
          ...prev[speciesId],
          isSaving: false,
          hasChanges: false
        }
      }));

      console.log(`✅ Saved description for ${species.common_name}`);
    } catch (error) {
      console.error('Save error:', error);
      alert(`Failed to save ${species.common_name}: ${error}`);
    } finally {
      setEnrichmentStates(prev => ({
        ...prev,
        [speciesId]: {
          ...prev[speciesId],
          isSaving: false
        }
      }));
    }
  };

  const runSpeciesCleanup = async (speciesId: string, description: string) => {
    try {
      // For now, just update the description
      // The structured field extraction can be done later with a proper function
      console.log(`✅ Description saved for ${speciesId}`);
      
      // TODO: Add structured field extraction when we have a proper function
      // that can extract habitat, diet, behavior, regions from description text
      
    } catch (error) {
      console.error('Cleanup error:', error);
    }
  };

  const handleDraftChange = (speciesId: string, value: string) => {
    setEnrichmentStates(prev => ({
      ...prev,
      [speciesId]: {
        ...prev[speciesId],
        draftDescription: value,
        hasChanges: true
      }
    }));
  };

  const getCurrentDescription = (species: Species) => {
    const state = enrichmentStates[species.id];
    if (state && state.draftDescription) {
      return state.draftDescription;
    }
    return species.description || 'No description available';
  };

  const hasDescription = (species: Species) => {
    return species.description && species.description.trim() !== '';
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading species...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Species Enrichment</h1>
        <p className="text-muted-foreground">
          Generate and edit AI-powered descriptions for species
        </p>
      </div>

      {/* Controls */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="show-only-missing"
                  checked={showOnlyMissing}
                  onCheckedChange={setShowOnlyMissing}
                />
                <Label htmlFor="show-only-missing">Show only species missing descriptions</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="auto-cleanup"
                  checked={autoRunCleanup}
                  onCheckedChange={setAutoRunCleanup}
                />
                <Label htmlFor="auto-cleanup">Auto-run cleanup after save</Label>
              </div>
            </div>

            <Button onClick={loadSpecies} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">{species.length}</div>
            <p className="text-xs text-muted-foreground">Total Species</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">
              {species.filter(s => hasDescription(s)).length}
            </div>
            <p className="text-xs text-muted-foreground">With Descriptions</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="text-2xl font-bold">
              {species.filter(s => !hasDescription(s)).length}
            </div>
            <p className="text-xs text-muted-foreground">Missing Descriptions</p>
          </CardContent>
        </Card>
      </div>

      {/* Species List */}
      <div className="space-y-4">
        {species.map((speciesItem) => {
          const state = enrichmentStates[speciesItem.id] || {
            isEnriching: false,
            isSaving: false,
            draftDescription: '',
            hasChanges: false
          };

          return (
            <Card key={speciesItem.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">
                      {speciesItem.common_name}
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                      {speciesItem.scientific_name}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    {hasDescription(speciesItem) && (
                      <Badge variant="secondary">Has Description</Badge>
                    )}
                    {!hasDescription(speciesItem) && (
                      <Badge variant="destructive">Missing Description</Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Current Description */}
                  {hasDescription(speciesItem) && (
                    <div>
                      <Label className="text-sm font-medium">Current Description:</Label>
                      <p className="text-sm text-muted-foreground mt-1">
                        {speciesItem.description}
                      </p>
                    </div>
                  )}

                  {/* Draft Description */}
                  <div>
                    <Label className="text-sm font-medium">Description:</Label>
                    <Textarea
                      value={getCurrentDescription(speciesItem)}
                      onChange={(e) => handleDraftChange(speciesItem.id, e.target.value)}
                      placeholder="Click 'Enrich' to generate a description, or edit manually..."
                      className="mt-1 min-h-[100px]"
                    />
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    <Button
                      onClick={() => handleEnrich(speciesItem)}
                      disabled={state.isEnriching}
                      size="sm"
                    >
                      {state.isEnriching ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Sparkles className="h-4 w-4 mr-2" />
                      )}
                      Enrich
                    </Button>

                    <Button
                      onClick={() => handleSave(speciesItem)}
                      disabled={!state.hasChanges || state.isSaving}
                      size="sm"
                      variant="default"
                    >
                      {state.isSaving ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4 mr-2" />
                      )}
                      Save
                    </Button>

                    {state.hasChanges && (
                      <Badge variant="outline" className="text-xs">
                        Unsaved Changes
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {species.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-muted-foreground">
              {showOnlyMissing 
                ? 'No species found missing descriptions.'
                : 'No species found.'
              }
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 