import { useAdminAuth } from '@/hooks/useAdminAuth';
import { PhotoReviewGrid } from '@/components/PhotoReviewGrid';
import { usePhotoReviewData, PhotoReviewItem } from '@/hooks/usePhotoReviewData';
import React from 'react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { Info } from 'lucide-react';

export default function PhotoReviewPage() {
  const { isAdmin, ready, session } = useAdminAuth();
  const { data = [], isLoading, error, refetch } = usePhotoReviewData();
  const [actionLoading, setActionLoading] = React.useState<number | null>(null);

  const handleAccept = async (photo: PhotoReviewItem) => {
    setActionLoading(photo.id);
    try {
      await supabase.from('photos_v2').update({
        species_id: photo.ai_suggested_id,
        ai_reviewed: true
      }).eq('id', photo.id);
      
      toast.success(`Accepted suggestion for ${photo.ai_suggested_name}.`);
      refetch(); // Let react-query handle optimistic updates via cache invalidation
    } catch (err) {
      toast.error('Failed to accept photo.');
    } finally {
      setActionLoading(null);
    }
  };

  const handleReject = async (photo: PhotoReviewItem) => {
    setActionLoading(photo.id);
    try {
      const userId = session?.user?.id || null;
      await supabase.from('photos').update({
        species_id: null,
        ai_reviewed: true,
        ai_suggested_id: null,
        ai_confidence: null,
      }).eq('id', photo.id);
      
      await supabase.from('ai_override_log').insert({
        table_name: 'photos',
        record_id: photo.id.toString(),
        field_name: 'species_id',
        old_value: photo.ai_suggested_id,
        new_value: null,
        override_type: 'species_assignment_rejection',
        user_id: userId,
        override_reason: 'Admin rejected AI suggestion from review UI.',
      });

      toast.success(`Rejected suggestion for ${photo.ai_suggested_name}.`);
      refetch();
    } catch (err) {
      toast.error('Failed to reject photo.');
    } finally {
      setActionLoading(null);
    }
  };

  if (!ready) return null;
  if (!isAdmin) return <div className="p-8 text-center text-red-600 font-bold">Admin access required.</div>;

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-2">AI Photo Review</h1>
      <p className="text-gray-600 mb-6">Review photos that have been automatically tagged by the AI.</p>
      
      <div className="bg-gray-100 p-4 rounded-lg mb-8 flex items-start text-sm text-gray-700">
        <Info className="w-5 h-5 mr-3 mt-1 flex-shrink-0" />
        <div>
          <h4 className="font-semibold">How to Review</h4>
          <p>Click "Accept" to confirm the AI's suggested species ID or "Reject" to clear it. Rejected photos will be available for manual assignment later.</p>
        </div>
      </div>
      
      <PhotoReviewGrid 
        data={data} 
        isLoading={isLoading || !!actionLoading}
        error={error} 
        onAccept={handleAccept}
        onReject={handleReject}
      />
    </div>
  );
} 