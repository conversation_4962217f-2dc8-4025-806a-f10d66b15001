import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Download, 
  Upload, 
  MapPin, 
  Calendar, 
  Bird, 
  Leaf, 
  Settings,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react';
import { ObservationImportService } from '@/services/observationImportService';

interface ImportStatus {
  status: 'idle' | 'importing' | 'success' | 'error';
  message: string;
  progress?: number;
}

export default function ObservationImport() {
  const [importStatus, setImportStatus] = useState<ImportStatus>({ status: 'idle', message: '' });
  const [importDays, setImportDays] = useState(30);
  const [ebirdApiKey, setEbirdApiKey] = useState('');
  const [ebirdUserId, setEbirdUserId] = useState('');
  const [inatUserId, setInatUserId] = useState('');
  const [importService] = useState(() => new ObservationImportService());

  const handleImportData = async () => {
    setImportStatus({ status: 'importing', message: 'Starting data import...' });
    
    try {
      const result = await importService.importAllData(importDays);
      
      if (result.success) {
        setImportStatus({ 
          status: 'success', 
          message: result.message 
        });
      } else {
        setImportStatus({ 
          status: 'error', 
          message: result.message 
        });
      }
    } catch (error) {
      setImportStatus({ 
        status: 'error', 
        message: `Import failed: ${error}` 
      });
    }
  };

  const handleTestConnection = async (service: 'ebird' | 'inaturalist') => {
    setImportStatus({ status: 'importing', message: `Testing ${service} connection...` });
    
    try {
      if (service === 'ebird') {
        await importService.fetchPersonalEBirdObservations(1);
        setImportStatus({ status: 'success', message: 'eBird connection successful!' });
      } else {
        await importService.fetchiNatObservations(undefined, 1, 1);
        setImportStatus({ status: 'success', message: 'iNaturalist connection successful!' });
      }
    } catch (error) {
      setImportStatus({ status: 'error', message: `${service} connection failed: ${error}` });
    }
  };

  const getStatusIcon = () => {
    switch (importStatus.status) {
      case 'importing': return <Clock className="w-4 h-4 animate-spin" />;
      case 'success': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'error': return <AlertCircle className="w-4 h-4 text-red-600" />;
      default: return null;
    }
  };

  const getStatusColor = () => {
    switch (importStatus.status) {
      case 'importing': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'success': return 'bg-green-100 text-green-800 border-green-200';
      case 'error': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Observation Data Import</h1>
          <p className="text-gray-600 mt-2">
            Import your personal wildlife observations from eBird and iNaturalist
          </p>
        </div>
        <Badge variant="outline" className="px-3 py-1">
          <Settings className="w-4 h-4 mr-2" />
          Admin Tool
        </Badge>
      </div>

      {/* Status Display */}
      {importStatus.message && (
        <Card>
          <CardContent className="p-4">
            <div className={`flex items-center gap-2 p-3 rounded-lg border ${getStatusColor()}`}>
              {getStatusIcon()}
              <span className="font-medium">{importStatus.message}</span>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="import" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="import">Import Data</TabsTrigger>
          <TabsTrigger value="config">Configuration</TabsTrigger>
          <TabsTrigger value="stats">Statistics</TabsTrigger>
        </TabsList>

        {/* Import Tab */}
        <TabsContent value="import" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* eBird Import */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bird className="w-5 h-5 text-blue-600" />
                  eBird Import
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="import-days">Import last N days</Label>
                  <Input
                    id="import-days"
                    type="number"
                    value={importDays}
                    onChange={(e) => setImportDays(parseInt(e.target.value))}
                    min="1"
                    max="365"
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button 
                    onClick={() => handleTestConnection('ebird')}
                    variant="outline"
                    className="flex-1"
                    disabled={importStatus.status === 'importing'}
                  >
                    Test Connection
                  </Button>
                  <Button 
                    onClick={handleImportData}
                    className="flex-1"
                    disabled={importStatus.status === 'importing'}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Import eBird
                  </Button>
                </div>

                <div className="text-sm text-gray-600 space-y-1">
                  <p>• Imports your personal eBird checklists</p>
                  <p>• Creates species records for new birds</p>
                  <p>• Links observations to locations</p>
                </div>
              </CardContent>
            </Card>

            {/* iNaturalist Import */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Leaf className="w-5 h-5 text-green-600" />
                  iNaturalist Import
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="inat-user">iNaturalist User ID</Label>
                  <Input
                    id="inat-user"
                    value={inatUserId}
                    onChange={(e) => setInatUserId(e.target.value)}
                    placeholder="Your iNaturalist username or ID"
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button 
                    onClick={() => handleTestConnection('inaturalist')}
                    variant="outline"
                    className="flex-1"
                    disabled={importStatus.status === 'importing'}
                  >
                    Test Connection
                  </Button>
                  <Button 
                    onClick={handleImportData}
                    className="flex-1"
                    disabled={importStatus.status === 'importing'}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Import iNat
                  </Button>
                </div>

                <div className="text-sm text-gray-600 space-y-1">
                  <p>• Imports all your observations</p>
                  <p>• Includes photos when available</p>
                  <p>• Supports all taxa (not just birds)</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Bulk Import */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="w-5 h-5 text-purple-600" />
                Bulk Import
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={handleImportData}
                size="lg"
                className="w-full"
                disabled={importStatus.status === 'importing'}
              >
                <Download className="w-5 h-5 mr-2" />
                Import All Data Sources
              </Button>
              
              <div className="text-sm text-gray-600 text-center">
                This will import data from both eBird and iNaturalist for the last {importDays} days
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Configuration Tab */}
        <TabsContent value="config" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>API Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="ebird-api-key">eBird API Key</Label>
                <Input
                  id="ebird-api-key"
                  type="password"
                  value={ebirdApiKey}
                  onChange={(e) => setEbirdApiKey(e.target.value)}
                  placeholder="Your eBird API key"
                />
                <p className="text-sm text-gray-600">
                  Get your free API key from <a href="https://ebird.org/api/keygen" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">eBird API Key Generator</a>
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="ebird-user-id">eBird User ID</Label>
                <Input
                  id="ebird-user-id"
                  value={ebirdUserId}
                  onChange={(e) => setEbirdUserId(e.target.value)}
                  placeholder="Your eBird username"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="inat-user-config">iNaturalist User ID</Label>
                <Input
                  id="inat-user-config"
                  value={inatUserId}
                  onChange={(e) => setInatUserId(e.target.value)}
                  placeholder="Your iNaturalist username"
                />
              </div>

              <Button className="w-full">
                Save Configuration
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Statistics Tab */}
        <TabsContent value="stats" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Observations</p>
                    <p className="text-2xl font-bold">0</p>
                  </div>
                  <MapPin className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Unique Locations</p>
                    <p className="text-2xl font-bold">0</p>
                  </div>
                  <MapPin className="w-8 h-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Last Import</p>
                    <p className="text-2xl font-bold">Never</p>
                  </div>
                  <Calendar className="w-8 h-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
