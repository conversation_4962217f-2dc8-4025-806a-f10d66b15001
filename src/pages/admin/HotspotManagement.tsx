import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  MapPin,
  Plus,
  Edit,
  Trash2,
  Star,
  Bird,
  Camera,
  Settings,
  Save,
  X,
  ExternalLink,
  Upload
} from 'lucide-react';
import { useLocations, useLocationManagement, type LocationWithStats } from '@/hooks/useLocationData';
import { LoadingSpinner } from '@/components/public/LoadingSpinner';
import SpeciesLocationMapping from '@/components/admin/SpeciesLocationMapping';

interface LocationFormData {
  name: string;
  latitude: number;
  longitude: number;
  country: string;
  state_province: string;
  county: string;
  locality: string;
  description: string;
  directions: string;
  facilities: string[];
  best_time_to_visit: string;
  difficulty_level: 'easy' | 'moderate' | 'difficult' | 'expert' | '';
  access_type: 'public' | 'private' | 'permit_required' | 'restricted' | '';
  parking_info: string;
  entrance_fee: number | '';
  website_url: string;
  contact_info: string;
  featured: boolean;
  photo_url: string;
  habitat_types: string[];
  target_species: string[];
  visitor_tips: string;
  published: boolean;
  google_maps_url: string;
  apple_maps_url: string;
}

const initialFormData: LocationFormData = {
  name: '',
  latitude: 0,
  longitude: 0,
  country: '',
  state_province: '',
  county: '',
  locality: '',
  description: '',
  directions: '',
  facilities: [],
  best_time_to_visit: '',
  difficulty_level: '',
  access_type: '',
  parking_info: '',
  entrance_fee: '',
  website_url: '',
  contact_info: '',
  featured: false,
  photo_url: '',
  habitat_types: [],
  target_species: [],
  visitor_tips: '',
  published: true,
  google_maps_url: '',
  apple_maps_url: ''
};

export default function HotspotManagement() {
  const { locations, loading, error, refetch } = useLocations();
  const { createLocation, updateLocation, deleteLocation, loading: actionLoading } = useLocationManagement();
  const [showForm, setShowForm] = useState(false);
  const [editingLocation, setEditingLocation] = useState<LocationWithStats | null>(null);
  const [formData, setFormData] = useState<LocationFormData>(initialFormData);
  const [searchTerm, setSearchTerm] = useState('');

  const facilityOptions = [
    'restrooms', 'parking', 'trails', 'visitor_center', 'benches', 
    'picnic_tables', 'water_fountains', 'gift_shop', 'cafe', 'guided_tours'
  ];

  const habitatOptions = [
    'deciduous_forest', 'coniferous_forest', 'mixed_forest', 'grassland', 
    'wetland', 'lake', 'river', 'coastal', 'mountain', 'desert', 'urban_park'
  ];

  const filteredLocations = locations.filter(location =>
    location.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    location.state_province?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    location.country?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleEdit = (location: LocationWithStats) => {
    setEditingLocation(location);
    setFormData({
      name: location.name,
      latitude: location.latitude,
      longitude: location.longitude,
      country: location.country || '',
      state_province: location.state_province || '',
      county: location.county || '',
      locality: location.locality || '',
      description: location.description || '',
      directions: location.directions || '',
      facilities: location.facilities || [],
      best_time_to_visit: location.best_time_to_visit || '',
      difficulty_level: location.difficulty_level || '',
      access_type: location.access_type || '',
      parking_info: location.parking_info || '',
      entrance_fee: location.entrance_fee || '',
      website_url: location.website_url || '',
      contact_info: location.contact_info || '',
      featured: location.featured,
      photo_url: location.photo_url || '',
      habitat_types: location.habitat_types || [],
      target_species: location.target_species || [],
      visitor_tips: location.visitor_tips || '',
      published: location.published,
      google_maps_url: location.google_maps_url || '',
      apple_maps_url: location.apple_maps_url || ''
    });
    setShowForm(true);
  };

  const handleDelete = async (location: LocationWithStats) => {
    if (!window.confirm(`Are you sure you want to delete "${location.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteLocation(location.id);
      await refetch();
    } catch (error) {
      console.error('Error deleting location:', error);
      alert('Failed to delete location. Please try again.');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const locationData = {
        ...formData,
        entrance_fee: formData.entrance_fee === '' ? null : Number(formData.entrance_fee)
      };

      if (editingLocation) {
        await updateLocation(editingLocation.id, locationData);
      } else {
        await createLocation(locationData);
      }
      
      await refetch();
      setShowForm(false);
      setEditingLocation(null);
      setFormData(initialFormData);
    } catch (error) {
      console.error('Error saving location:', error);
      alert('Failed to save location. Please try again.');
    }
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditingLocation(null);
    setFormData(initialFormData);
  };

  const updateFormData = (field: keyof LocationFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const toggleArrayItem = (field: 'facilities' | 'habitat_types' | 'target_species', item: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].includes(item)
        ? prev[field].filter(i => i !== item)
        : [...prev[field], item]
    }));
  };

  if (loading) return <LoadingSpinner />;
  if (error) return <div className="text-center text-red-600 p-8">Error: {error}</div>;

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Hotspot Management</h1>
          <p className="text-gray-600 mt-2">
            Manage wildlife viewing locations and their species relationships
          </p>
        </div>
        <Badge variant="outline" className="px-3 py-1">
          <Settings className="w-4 h-4 mr-2" />
          Admin Tool
        </Badge>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{locations.length}</div>
            <div className="text-sm text-gray-600">Total Locations</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {locations.filter(l => l.published).length}
            </div>
            <div className="text-sm text-gray-600">Published</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {locations.filter(l => l.featured).length}
            </div>
            <div className="text-sm text-gray-600">Featured</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">
              {locations.reduce((sum, l) => sum + (l.total_species || 0), 0)}
            </div>
            <div className="text-sm text-gray-600">Total Species</div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="locations" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="locations">Locations ({locations.length})</TabsTrigger>
          <TabsTrigger value="species-mapping">Species Mapping</TabsTrigger>
          <TabsTrigger value="bulk-import">Bulk Import</TabsTrigger>
        </TabsList>

        {/* Locations Tab */}
        <TabsContent value="locations" className="space-y-6">
          {/* Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Input
                placeholder="Search locations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64"
              />
            </div>
            <Button onClick={() => setShowForm(true)} disabled={showForm}>
              <Plus className="w-4 h-4 mr-2" />
              Add Location
            </Button>
          </div>

          {/* Location Form */}
          {showForm && (
            <Card>
              <CardHeader>
                <CardTitle>
                  {editingLocation ? 'Edit Location' : 'Add New Location'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Basic Information */}
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="name">Location Name *</Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => updateFormData('name', e.target.value)}
                          required
                        />
                      </div>
                      
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label htmlFor="latitude">Latitude *</Label>
                          <Input
                            id="latitude"
                            type="number"
                            step="any"
                            value={formData.latitude}
                            onChange={(e) => updateFormData('latitude', parseFloat(e.target.value))}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="longitude">Longitude *</Label>
                          <Input
                            id="longitude"
                            type="number"
                            step="any"
                            value={formData.longitude}
                            onChange={(e) => updateFormData('longitude', parseFloat(e.target.value))}
                            required
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label htmlFor="country">Country</Label>
                          <Input
                            id="country"
                            value={formData.country}
                            onChange={(e) => updateFormData('country', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor="state_province">State/Province</Label>
                          <Input
                            id="state_province"
                            value={formData.state_province}
                            onChange={(e) => updateFormData('state_province', e.target.value)}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Additional Details */}
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="description">Description</Label>
                        <Textarea
                          id="description"
                          value={formData.description}
                          onChange={(e) => updateFormData('description', e.target.value)}
                          rows={3}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label htmlFor="difficulty_level">Difficulty</Label>
                          <Select 
                            value={formData.difficulty_level} 
                            onValueChange={(value) => updateFormData('difficulty_level', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select difficulty" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="easy">Easy</SelectItem>
                              <SelectItem value="moderate">Moderate</SelectItem>
                              <SelectItem value="difficult">Difficult</SelectItem>
                              <SelectItem value="expert">Expert</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label htmlFor="access_type">Access Type</Label>
                          <Select 
                            value={formData.access_type} 
                            onValueChange={(value) => updateFormData('access_type', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select access type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="public">Public</SelectItem>
                              <SelectItem value="private">Private</SelectItem>
                              <SelectItem value="permit_required">Permit Required</SelectItem>
                              <SelectItem value="restricted">Restricted</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="featured"
                            checked={formData.featured}
                            onCheckedChange={(checked) => updateFormData('featured', !!checked)}
                          />
                          <Label htmlFor="featured">Featured Location</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="published"
                            checked={formData.published}
                            onCheckedChange={(checked) => updateFormData('published', !!checked)}
                          />
                          <Label htmlFor="published">Published</Label>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Form Actions */}
                  <div className="flex items-center gap-2 pt-4 border-t">
                    <Button type="submit" disabled={actionLoading}>
                      <Save className="w-4 h-4 mr-2" />
                      {editingLocation ? 'Update' : 'Create'} Location
                    </Button>
                    <Button type="button" variant="outline" onClick={handleCancel}>
                      <X className="w-4 h-4 mr-2" />
                      Cancel
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          {/* Locations List */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredLocations.map((location) => (
              <Card key={location.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-semibold text-gray-900 line-clamp-1">
                            {location.name}
                          </h3>
                          {location.featured && (
                            <Badge className="bg-yellow-500 text-white">
                              <Star className="w-3 h-3" />
                            </Badge>
                          )}
                          {!location.published && (
                            <Badge variant="secondary">Draft</Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-1 text-sm text-gray-600">
                          <MapPin className="w-4 h-4" />
                          {location.state_province && location.country 
                            ? `${location.state_province}, ${location.country}`
                            : location.country || 'Location not specified'
                          }
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4 text-sm">
                      {location.total_species && (
                        <div className="flex items-center gap-1 text-green-600">
                          <Bird className="w-4 h-4" />
                          {location.total_species}
                        </div>
                      )}
                      {location.primary_photo && (
                        <div className="flex items-center gap-1 text-blue-600">
                          <Camera className="w-4 h-4" />
                          Photo
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEdit(location)}
                        className="flex-1"
                      >
                        <Edit className="w-3 h-3 mr-1" />
                        Edit
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDelete(location)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        asChild
                      >
                        <a href={`/hotspots/${location.id}`} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="w-3 h-3" />
                        </a>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Species Mapping Tab */}
        <TabsContent value="species-mapping" className="space-y-6">
          <SpeciesLocationMapping />
        </TabsContent>

        {/* Bulk Import Tab */}
        <TabsContent value="bulk-import" className="space-y-6">
          <Card className="p-8 text-center">
            <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-semibold mb-2">Bulk Import Locations</h3>
            <p className="text-gray-600 mb-4">
              Import locations from eBird hotspots, iNaturalist places, or CSV files.
            </p>
            <Button variant="outline">
              <Upload className="w-4 h-4 mr-2" />
              Coming Soon
            </Button>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
