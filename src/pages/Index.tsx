import { useState, useMemo, useEffect } from "react";
import { Link } from "react-router-dom";
import { useSpeciesWithPhotos, SpeciesWithPhotos } from "@/hooks/useSpeciesData";
import { SafeSpeciesCard } from "@/components/SafeSpeciesCard";
import { MultiSelectFilter } from "@/components/MultiSelectFilter";
import { But<PERSON> } from "@/components/ui/button";
import { Search, Camera, Upload, Brain, BarChart3 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { SkipLink } from "@/components/ui/skip-link";
import { useAdminAuth } from "@/hooks/useAdminAuth";
import { EditSpeciesModal } from "@/components/EditSpeciesModal";
import { usePerformanceMonitor } from "@/hooks/usePerformanceMonitor";
import type { Tables } from "@/integrations/supabase/types";

type SpeciesV2 = Tables<"species_v2">;

const Index = () => {
  // Deprecation warning for legacy route
  console.warn(
    '⚠️ DEPRECATED: This page is deprecated. Please use the unified Wildlife Explorer at "/" instead. ' +
    'This legacy route will be removed in a future version.'
  );

  const { data: speciesData = [], isLoading, error } = useSpeciesWithPhotos();
  const performanceMonitor = usePerformanceMonitor({
    componentName: 'Index Page',
    threshold: 50 // Log if operations take more than 50ms
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const { isAdmin } = useAdminAuth();

  // Modal state for species editing
  const [selectedSpecies, setSelectedSpecies] = useState<SpeciesWithPhotos | null>(null);
  const [modalOpen, setModalOpen] = useState(false);

  // Handler to trigger modal
  const handleEditSpecies = (species: SpeciesWithPhotos) => {
    setSelectedSpecies(species);
    setModalOpen(true);
  };

  // Handler for successful species update
  const handleSpeciesUpdate = (updatedSpecies: { id: string; name: string; category: string; conservation_status: string }) => {
    // For now, we'll just close the modal since the data will be refetched
    // In a more sophisticated implementation, we could update the local state
    setModalOpen(false);
    setSelectedSpecies(null);
    
    // Optionally trigger a refetch of the species data
    // This would require exposing a refetch function from useSpeciesWithPhotos
  };

  // Get unique categories and conservation statuses
  const categories = useMemo(() => {
    const uniqueCategories = [...new Set(speciesData.map(species => species.category).filter(Boolean))];
    return uniqueCategories.sort();
  }, [speciesData]);

  const conservationStatuses = useMemo(() => {
    const statuses = speciesData
      .map(species => species.conservation_status)
      .filter(Boolean) as string[];
    return [...new Set(statuses)].sort();
  }, [speciesData]);

  // Filter species based on search term, categories, and conservation status
  const filteredSpecies = useMemo(() => {
    performanceMonitor.startMeasurement();
    const result = speciesData.filter(species => {
      const matchesSearch = !searchTerm ||
        species.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        species.scientific_name?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCategory = selectedCategories.length === 0 ||
        (species.category && selectedCategories.includes(species.category));

      const matchesStatus = selectedStatuses.length === 0 ||
        (species.conservation_status && selectedStatuses.includes(species.conservation_status));

      return matchesSearch && matchesCategory && matchesStatus;
    });

    performanceMonitor.endMeasurement('render');
    return result;
  }, [speciesData, searchTerm, selectedCategories, selectedStatuses]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-wildlife-secondary to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-wildlife-primary mx-auto mb-4" role="status" aria-label="Loading wildlife data"></div>
          <p className="text-wildlife-primary-dark font-medium">Loading wildlife data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    console.error('Error loading species data:', error);
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-wildlife-secondary via-blue-50 to-wildlife-secondary">
      {/* Skip Links for Accessibility */}
      <SkipLink href="#main-content">Skip to main content</SkipLink>
      <SkipLink href="#filters">Skip to filters</SkipLink>

      {/* Hero Section */}
      <header className="bg-gradient-to-r from-wildlife-primary-darker to-wildlife-primary text-white py-16">
        <div className="container mx-auto px-6">
          <div className="text-center">
            <h1 className="text-5xl font-bold mb-4">Wildlife Explorer</h1>
            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
              Discover and explore the magnificent wildlife species around the world, 
              their habitats, and conservation status through stunning photography.
            </p>
            
            {/* Navigation Links */}
            <nav aria-label="Main navigation" className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 mb-8">
              <Link to="/gallery" className="w-full sm:w-auto">
                <Button variant="secondary" className="w-full sm:w-auto bg-white/10 hover:bg-white/20 border-white/20">
                  <Camera className="w-4 h-4 mr-2" aria-hidden="true" />
                  Photo Gallery
                </Button>
              </Link>
              <Link to="/photos" className="w-full sm:w-auto">
                <Button variant="secondary" className="w-full sm:w-auto bg-white/10 hover:bg-white/20 border-white/20">
                  <Upload className="w-4 h-4 mr-2" aria-hidden="true" />
                  Manage Photos
                </Button>
              </Link>
              <Link to="/ai-workflow" className="w-full sm:w-auto">
                <Button variant="secondary" className="w-full sm:w-auto bg-white/10 hover:bg-white/20 border-white/20">
                  <Brain className="w-4 h-4 mr-2" aria-hidden="true" />
                  AI Workflow
                </Button>
              </Link>
              <Link to="/ai-dashboard" className="w-full sm:w-auto">
                <Button variant="secondary" className="w-full sm:w-auto bg-white/10 hover:bg-white/20 border-white/20">
                  <BarChart3 className="w-4 h-4 mr-2" aria-hidden="true" />
                  AI Dashboard
                </Button>
              </Link>
              <Link to="/admin/sync" className="w-full sm:w-auto">
                <Button variant="secondary" className="w-full sm:w-auto bg-white/10 hover:bg-white/20 border-white/20">
                  Sync Data
                </Button>
              </Link>
            </nav>
            
            {/* Search Bar */}
            <div className="relative max-w-md mx-auto">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" aria-hidden="true" />
              <Input
                type="text"
                placeholder="Search species..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white/90 border-white/30 focus:border-white focus:ring-white/50"
                aria-label="Search species by name or scientific name"
              />
            </div>
          </div>
        </div>
      </header>

      <main id="main-content" className="container mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Filters */}
          <aside id="filters" className="lg:col-span-1 space-y-6" aria-label="Filter options">
            <MultiSelectFilter
              options={categories}
              selected={selectedCategories}
              onChange={setSelectedCategories}
              label="Filter by Category"
              colorScheme="wildlife"
            />

            <MultiSelectFilter
              options={conservationStatuses}
              selected={selectedStatuses}
              onChange={setSelectedStatuses}
              label="Filter by Conservation Status"
              colorScheme="conservation" // Automatic conservation status colors
            />
          </aside>

          {/* Main Content */}
          <section className="lg:col-span-3" aria-label="Species listing">
            {/* Results Summary */}
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-foreground mb-2">
                Wildlife Species
              </h2>
              <p className="text-muted-foreground" aria-live="polite">
                Showing {filteredSpecies.length} of {speciesData.length} species
                {searchTerm && ` matching "${searchTerm}"`}
              </p>
            </div>

            {/* Species Grid */}
            {filteredSpecies.length > 0 ? (
              <div 
                className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
                role="list"
                aria-label="Species cards"
              >
                {filteredSpecies.map((species) => (
                  <div key={species.id} role="listitem">
                    <SafeSpeciesCard
                      species={species}
                      isAdmin={isAdmin}
                      onEdit={handleEditSpecies}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12" role="status" aria-live="polite">
                <div className="text-6xl mb-4" aria-hidden="true">🔍</div>
                <h3 className="text-xl font-semibold text-gray-700 mb-2">
                  No species found
                </h3>
                <p className="text-gray-600">
                  Try adjusting your search criteria or filters
                </p>
              </div>
            )}
          </section>
        </div>
      </main>

      {/* Data Source Note */}
      <footer className="bg-wildlife-primary-darker text-white py-4">
        <div className="container mx-auto px-6 text-center">
          <p className="text-white/90">
            Displaying {speciesData.length} species from the species_v2 database.
          </p>
        </div>
      </footer>

      {/* Edit Species Modal */}
      {selectedSpecies && (
        <EditSpeciesModal
          open={modalOpen}
          species={{
            id: selectedSpecies.id,
            name: selectedSpecies.name || '',
            scientific_name: selectedSpecies.scientific_name || '',
            category: selectedSpecies.category || '',
            conservation_status: selectedSpecies.conservation_status || 'Least Concern',
            description: selectedSpecies.description || '',
            featured: selectedSpecies.featured || false,
            published: selectedSpecies.published !== false,
            // Add other available fields from SpeciesWithPhotos
            size_description: selectedSpecies.size_description || '',
            ai_fun_facts: selectedSpecies.ai_fun_facts || '',
            fun_facts_field: selectedSpecies.fun_facts_field || '',
          }}
          onOpenChange={setModalOpen}
          onSave={handleSpeciesUpdate}
        />
      )}
    </div>
  );
};

export default Index;