import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, Bug } from "lucide-react";
import { Link } from "react-router-dom";
import { isValidUUID } from "@/utils/validate";

// Define the type for a single species object from species_v2
interface Species {
  id: string;
  name: string;
  common_name: string | null;
  scientific_name: string | null;
  category: string | null;
  conservation_status: string | null;
  published: boolean;
}

export default function SpeciesPage() {
  // Deprecation warning for legacy route
  console.warn(
    '⚠️ DEPRECATED: This page is deprecated. Please use the unified Wildlife Explorer at "/" instead. ' +
    'This legacy route will be removed in a future version.'
  );

  const [species, setSpecies] = useState<Species[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadSpecies() {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from("species_v2")
          .select("*")
          .order("name", { ascending: true });

        if (error) {
          throw error;
        }

        // Filter out any species with invalid UUIDs and log them
        const validSpecies = (data || []).filter(species => {
          if (!isValidUUID(species.id)) {
            console.warn("Skipping species with invalid UUID:", species);
            return false;
          }
          return true;
        });

        console.log("Species data fetched successfully:", validSpecies);
        setSpecies(validSpecies);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "An unknown error occurred.";
        console.error("Error loading species:", errorMessage);
        setError(`Failed to fetch species. Please try again later. Error: ${errorMessage}`);
      } finally {
        setLoading(false);
      }
    }
    loadSpecies();
  }, []);

  const getConservationStatusColor = (status: string | null) => {
    if (!status) return "secondary";
    const lowerStatus = status.toLowerCase();
    if (lowerStatus.includes("endangered")) return "destructive";
    if (lowerStatus.includes("vulnerable")) return "default";
    return "secondary";
  };

  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center py-10">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <p className="ml-2">Loading species...</p>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex flex-col items-center text-red-600 py-10">
          <Bug className="w-10 h-10 mb-2" />
          <p className="font-semibold">An Error Occurred</p>
          <p>{error}</p>
        </div>
      );
    }

    if (species.length === 0) {
      return (
        <div className="text-center text-muted-foreground py-10">
          <p>No species found in the database.</p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {species.map((sp) => (
          <Link to={`/species/${sp.id}`} key={sp.id} className="block hover:no-underline">
            <Card className="h-full transition-all hover:shadow-md hover:border-primary">
              <CardHeader>
                <CardTitle>{sp.common_name || sp.name}</CardTitle>
                <p className="text-sm text-muted-foreground italic">{sp.scientific_name}</p>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <Badge variant="outline">{sp.category || "Uncategorized"}</Badge>
                  {sp.conservation_status && (
                    <Badge variant={getConservationStatusColor(sp.conservation_status)}>
                      {sp.conservation_status}
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Species Registry</h1>
      </div>
      {renderContent()}
    </div>
  );
} 