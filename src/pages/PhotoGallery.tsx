import { <PERSON> } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { PhotoManager } from "@/components/PhotoManager";

const PhotoGallery = () => {

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <div className="relative bg-gradient-to-b from-black/80 to-black/60 py-20">
        <div className="container mx-auto px-6">
          <Link to="/" className="inline-flex items-center text-gray-300 hover:text-white mb-6">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Wildlife Explorer
          </Link>
          <div className="text-center">
            <h1 className="text-5xl md:text-6xl font-light mb-6 tracking-wide">Photo Gallery</h1>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed">
              Explore stunning wildlife photography from around the world
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-12">
        <PhotoManager mode="gallery" title="Wildlife Photography" />
      </div>
    </div>
  );
};

export default PhotoGallery;