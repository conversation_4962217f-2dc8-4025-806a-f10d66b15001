import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { FunFacts } from "@/components/ui/FunFacts";
import { 
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { ArrowLeft, Camera, MapPin, Info, Shield, Compass, Book, ExternalLink, Eye, EyeOff, AlertTriangle } from "lucide-react";
import { extractCleanDescription } from "@/lib/textUtils";
import { useSpeciesDetail } from "@/hooks/useSpeciesData";

const getConservationStatusColor = (status: string | null) => {
  switch (status?.toLowerCase()) {
    case 'critically endangered': return 'bg-red-600';
    case 'endangered': return 'bg-red-500';
    case 'vulnerable': return 'bg-orange-500';
    case 'near threatened': return 'bg-yellow-500 text-black';
    case 'least concern': return 'bg-green-500';
    default: return 'bg-gray-500';
  }
};

const SpeciesDetail = () => {
  const { id } = useParams<{ id: string }>();
  
  const { data: species, isLoading, error } = useSpeciesDetail(id);

  if (isLoading) return <div className="text-center p-8">Loading...</div>;
  if (error) return <div className="text-center p-8 text-red-500">Error: {(error as Error).message}</div>;
  if (!species) return <div className="text-center p-8">Species not found.</div>;

  const description = extractCleanDescription(species.description || '');
  const photos = species.photos || [];
  const publishedPhotos = photos.filter(p => p.published);
  const unpublishedPhotos = photos.filter(p => !p.published);

  return (
    <div className="container mx-auto py-8">
      <Button asChild variant="outline" className="mb-6">
        <Link to="/species">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Species List
        </Link>
      </Button>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-4xl font-bold">{species.name || 'Unnamed Species'}</CardTitle>
              <p className="text-lg text-muted-foreground italic">{species.scientific_name || 'Scientific name not available'}</p>
              <div className="mt-2 flex items-center gap-2">
                <Badge variant="secondary">{species.category || 'Unknown Category'}</Badge>
                <Badge className={getConservationStatusColor(species.conservation_status)}>
                  {species.conservation_status || 'Status Unknown'}
                </Badge>
              </div>
            </div>
            {photos.length > 0 && (
              <div className="w-1/2">
                <div className="relative">
                  <img
                    src={photos[0]?.url || ''}
                    alt={photos[0]?.title || species.name || 'Species photo'}
                    className="w-full h-64 object-cover rounded-lg shadow-lg"
                  />
                  {!photos[0]?.published && (
                    <div className="absolute top-3 right-3 bg-yellow-500 text-white px-3 py-1 rounded-full text-sm flex items-center gap-1">
                      <EyeOff className="w-4 h-4" />
                      Draft
                    </div>
                  )}
                  {photos[0]?.title && (
                    <div className="absolute bottom-3 left-3 bg-black/70 text-white px-3 py-1 rounded text-sm">
                      {photos[0].title}
                    </div>
                  )}
                  {photos.length > 1 && (
                    <div className="absolute bottom-3 right-3 bg-black/70 text-white px-3 py-1 rounded text-sm">
                      +{photos.length - 1} more
                    </div>
                  )}
                </div>

                {/* Photo status summary */}
                <div className="mt-3 text-sm text-muted-foreground flex items-center gap-4">
                  {publishedPhotos.length > 0 && (
                    <span className="inline-flex items-center gap-1">
                      <Eye className="w-4 h-4" />
                      {publishedPhotos.length} published
                    </span>
                  )}
                  {unpublishedPhotos.length > 0 && (
                    <span className="inline-flex items-center gap-1">
                      <EyeOff className="w-4 h-4" />
                      {unpublishedPhotos.length} draft
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <Separator className="my-6" />
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="md:col-span-2 space-y-6">
              <div>
                <h3 className="text-xl font-semibold mb-2 flex items-center"><Book className="w-5 h-5 mr-2" /> Description</h3>
                <p className="text-muted-foreground">
                  {description || 'No description available for this species.'}
                </p>
              </div>
              
              <FunFacts facts={Array.isArray(species.fun_facts)
                ? species.fun_facts.filter(fact => typeof fact === 'string' && fact.trim().length > 0)
                : []
              } />
              
            </div>
            <div className="space-y-6">
              <Card className="bg-gray-50/50">
                <CardHeader><CardTitle className="text-lg flex items-center"><Info className="w-4 h-4 mr-2"/> Details</CardTitle></CardHeader>
                <CardContent className="text-sm space-y-2">
                  <p><strong>Habitat:</strong> {species.habitat || 'Information not available'}</p>
                  {/* Geographic Information */}
                  {(species.countries || species.states_provinces || species.primary_region || species.regions) && (
                    <div>
                      <strong>Geographic Distribution:</strong>
                      <div className="mt-1 space-y-1">
                        {/* Countries */}
                        {species.countries && species.countries.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            <span className="text-xs text-muted-foreground">Countries:</span>
                            {species.countries.map((country: string, index: number) => (
                              <Link
                                key={index}
                                to={`/wildlife?location=${encodeURIComponent(country)}&tab=species`}
                                className="inline-block"
                              >
                                <Badge
                                  variant="default"
                                  className="text-xs hover:bg-blue-600 transition-colors cursor-pointer"
                                >
                                  <MapPin className="h-3 w-3 mr-1" />
                                  {country}
                                </Badge>
                              </Link>
                            ))}
                          </div>
                        )}

                        {/* States/Provinces */}
                        {species.states_provinces && species.states_provinces.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            <span className="text-xs text-muted-foreground">Regions:</span>
                            {species.states_provinces.map((region: string, index: number) => (
                              <Link
                                key={index}
                                to={`/wildlife?location=${encodeURIComponent(region)}&tab=species`}
                                className="inline-block"
                              >
                                <Badge
                                  variant="secondary"
                                  className="text-xs hover:bg-gray-300 transition-colors cursor-pointer"
                                >
                                  {region}
                                </Badge>
                              </Link>
                            ))}
                          </div>
                        )}

                        {/* Primary Region */}
                        {species.primary_region && (
                          <div className="flex flex-wrap gap-1">
                            <span className="text-xs text-muted-foreground">Primary:</span>
                            <Link
                              to={`/wildlife?location=${encodeURIComponent(species.primary_region)}&tab=species`}
                              className="inline-block"
                            >
                              <Badge
                                variant="outline"
                                className="text-xs hover:bg-gray-100 transition-colors cursor-pointer"
                              >
                                {species.primary_region}
                              </Badge>
                            </Link>
                          </div>
                        )}

                        {/* Legacy regions field as fallback */}
                        {!species.countries && !species.states_provinces && !species.primary_region && species.regions && (
                          <p className="text-sm">{species.regions}</p>
                        )}
                      </div>
                    </div>
                  )}
                  <p><strong>Diet:</strong> {species.diet || 'Information not available'}</p>
                  <p><strong>Behavior:</strong> {species.behavior || 'Information not available'}</p>
                  {species.family && <p><strong>Family:</strong> {species.family}</p>}
                  {species.conservation_actions && <p><strong>Conservation Actions:</strong> {species.conservation_actions}</p>}
                  {species.population_trend && <p><strong>Population Trend:</strong> {species.population_trend}</p>}
                  {species.threat_level && <p><strong>Threat Level:</strong> {species.threat_level}</p>}
                  {species.size_description && <p><strong>Size:</strong> {species.size_description}</p>}
                  {species.weight_g && <p><strong>Weight:</strong> {species.weight_g}g</p>}
                  {species.lifespan_years && <p><strong>Lifespan:</strong> {species.lifespan_years} years</p>}
                </CardContent>
              </Card>

              {species.inat_id && (
                <Button asChild variant="outline" className="w-full">
                  <a href={`https://www.inaturalist.org/taxa/${species.inat_id}`} target="_blank" rel="noopener noreferrer">
                    View on iNaturalist <ExternalLink className="w-4 h-4 ml-2" />
                  </a>
                </Button>
              )}

              {photos.length === 0 && (
                <Card className="bg-yellow-50/50 border-yellow-200">
                  <CardContent className="pt-6">
                    <p className="text-sm text-yellow-800">
                      <Camera className="w-4 h-4 inline mr-2" />
                      No photos available for this species yet.
                    </p>
                  </CardContent>
                </Card>
              )}

              {photos.length > 0 && unpublishedPhotos.length > 0 && (
                <Card className="bg-blue-50/50 border-blue-200">
                  <CardContent className="pt-6">
                    <p className="text-sm text-blue-800">
                      <EyeOff className="w-4 h-4 inline mr-2" />
                      {unpublishedPhotos.length} photo{unpublishedPhotos.length !== 1 ? 's' : ''} pending review.
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Photo Gallery Section */}
      {photos.length > 0 && (
        <Card className="mt-8">
          <CardHeader>
            <div className="flex items-center gap-3">
              <Camera className="w-6 h-6 text-blue-600" />
              <CardTitle className="text-2xl">Photo Gallery</CardTitle>
              <Badge variant="secondary">{photos.length} photo{photos.length !== 1 ? 's' : ''}</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {photos.map((photo, index) => (
                <div key={photo.id} className="relative group overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
                  <img
                    src={photo.url || ''}
                    alt={photo.title || `${species.name} photo ${index + 1}`}
                    className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-200"
                  />
                  {!photo.published && (
                    <div className="absolute top-3 right-3 bg-yellow-500 text-white px-2 py-1 rounded text-xs flex items-center gap-1">
                      <EyeOff className="w-3 h-3" />
                      Draft
                    </div>
                  )}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200" />

                  {(photo.title || photo.description || photo.location) && (
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                      {photo.title && (
                        <h3 className="font-semibold text-white mb-1">{photo.title}</h3>
                      )}
                      {photo.description && (
                        <p className="text-sm text-gray-200 mb-2">{photo.description}</p>
                      )}
                      {photo.location && (
                        <div className="flex items-center gap-1 text-xs text-gray-300">
                          <MapPin className="w-3 h-3" />
                          {photo.location}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SpeciesDetail;