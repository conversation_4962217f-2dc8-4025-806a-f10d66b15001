import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { Shield, LogIn } from 'lucide-react';

function AdminLogin() {
  const { login } = useAdminAuth();

  const handleLogin = () => {
    // The simplified hook now handles navigation
    login();
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
            <Shield className="h-6 w-6 text-blue-600" />
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Admin Dashboard
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Access the wildlife database administration panel
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-center">Admin Access</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-sm text-gray-600 mb-6">
              You are using simplified local authentication.
            </p>
            <Button
              onClick={handleLogin}
              className="w-full"
            >
              <LogIn className="w-4 h-4 mr-2" />
              Enter Admin Dashboard
            </Button>
          </CardContent>
        </Card>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            To re-enable Supabase authentication, edit the hook at:<br />
            <code className="text-xs font-mono bg-gray-200 p-1 rounded">src/hooks/useAdminAuth.tsx</code>
          </p>
        </div>
      </div>
    </div>
  );
}

export default AdminLogin;