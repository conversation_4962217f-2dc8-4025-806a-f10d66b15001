import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface OverrideLog {
  id: number;
  table_name: string;
  record_id: string;
  field_name: string;
  old_value: string | null;
  new_value: string | null;
  override_type: string;
  override_reason: string | null;
  created_at: string;
  record_title: string | null;
  photo_url: string | null;
}

interface RealtimeOverridesOptions {
  enabled?: boolean;
  showNotifications?: boolean;
  maxNotifications?: number;
}

export function useRealtimeOverrides(options: RealtimeOverridesOptions & { ready?: boolean } = {}) {
  const {
    enabled = true,
    showNotifications = true,
    maxNotifications = 10,
    ready = true
  } = options;

  const [overrides, setOverrides] = useState<OverrideLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadRecentOverrides = useCallback(async () => {
    try {
      setLoading(true);

      // Try the view first, fallback to direct table query
      let data, error;

      try {
        const result = await supabase
          .from('recent_ai_overrides')
          .select('*')
          .limit(maxNotifications)
          .order('created_at', { ascending: false });
        data = result.data;
        error = result.error;
      } catch (viewError) {
        // Fallback: query ai_override_log table directly if view doesn't exist
        const result = await supabase
          .from('ai_override_log')
          .select('*')
          .limit(maxNotifications)
          .order('created_at', { ascending: false });

        data = result.data?.map(log => ({
          ...log,
          record_title: 'Unknown',
          photo_url: null
        })) || [];
        error = result.error;
      }

      if (error) throw error;
      setOverrides(data || []);
      setError(null);
    } catch (err) {
      console.error('Error loading recent overrides:', err);
      setError('Failed to load override history');
      setOverrides([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  }, [maxNotifications]);

  const handleNewOverride = useCallback((newOverride: OverrideLog) => {
    setOverrides(prev => {
      const updated = [newOverride, ...prev.slice(0, maxNotifications - 1)];
      return updated;
    });

    if (showNotifications) {
      const overrideTypeIcon = getOverrideTypeIcon(newOverride.override_type);
      const recordName = newOverride.record_title || `Record ${newOverride.record_id}`;
      
      toast.success(`${overrideTypeIcon} Manual override: ${newOverride.override_type.replace('_', ' ')} on ${recordName}`);
    }
  }, [maxNotifications, showNotifications]);

  const handleNewReviewItem = useCallback((type: 'photo' | 'species') => {
    if (showNotifications) {
      const icon = type === 'photo' ? '📸' : '🦋';
      const itemType = type === 'photo' ? 'photo' : 'species';
      
      toast.info(`${icon} New AI-generated ${itemType} needs review`);
    }
  }, [showNotifications]);

  const getOverrideTypeIcon = (type: string): string => {
    switch (type) {
      case 'species_assignment':
        return '🦋';
      case 'metadata_edit':
        return '✏️';
      case 'review_status':
        return '👁️';
      case 'confidence_override':
        return '🎯';
      default:
        return '📝';
    }
  };

  useEffect(() => {
    if (!ready) return;
    loadRecentOverrides();

    // Disable real-time subscriptions due to WebSocket connection issues
    // Use periodic polling instead
    const interval = setInterval(() => {
      loadRecentOverrides();
    }, 30000); // Refresh every 30 seconds

    return () => {
      clearInterval(interval);
    };
  }, [ready, loadRecentOverrides]);

  const clearOverrides = () => {
    setOverrides([]);
  };

  const refreshOverrides = () => {
    loadRecentOverrides();
  };

  return {
    overrides,
    loading,
    error,
    clearOverrides,
    refreshOverrides,
    hasNewOverrides: overrides.length > 0
  };
}

// Hook for real-time review queue monitoring
export function useRealtimeReviewQueue(ready: boolean = true) {
  const [reviewCounts, setReviewCounts] = useState({
    photos: 0,
    species: 0,
    total: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!ready) return;
    loadReviewCounts();

    // Disable real-time subscriptions due to WebSocket connection issues
    // Use periodic polling instead
    const interval = setInterval(() => {
      loadReviewCounts();
    }, 30000); // Refresh every 30 seconds

    return () => {
      clearInterval(interval);
    };
  }, [ready]);

  const loadReviewCounts = async () => {
    try {
      setLoading(true);

      // Try the view first, fallback to direct table queries
      try {
        const { data, error } = await supabase
          .from('review_queue_summary')
          .select('*')
          .single();

        if (error) throw error;

        setReviewCounts({
          photos: data.photos_needing_review || 0,
          species: data.species_needing_review || 0,
          total: data.total_items_needing_review || 0
        });
      } catch (viewError) {
        // Fallback: calculate counts directly from v2 tables
        const [photosResult, speciesResult] = await Promise.all([
          supabase
            .from('photos_v2')
            .select('*', { count: 'exact', head: true })
            .eq('published', false),
          supabase
            .from('species_v2')
            .select('*', { count: 'exact', head: true })
            .eq('published', false)
        ]);

        const photosCount = photosResult.count || 0;
        const speciesCount = speciesResult.count || 0;

        setReviewCounts({
          photos: photosCount,
          species: speciesCount,
          total: photosCount + speciesCount
        });
      }
    } catch (err) {
      console.error('Error loading review counts:', err);
      // Set default values on error
      setReviewCounts({
        photos: 0,
        species: 0,
        total: 0
      });
    } finally {
      setLoading(false);
    }
  };

  return {
    reviewCounts,
    loading,
    refresh: loadReviewCounts
  };
}

// Hook for real-time AI performance metrics
export function useRealtimeAIMetrics(ready: boolean = true) {
  const [metrics, setMetrics] = useState({
    total_photos: 0,
    ai_tagged: 0,
    flagged_for_review: 0,
    avg_confidence: 0,
    published_photos: 0,
    unpublished_photos: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!ready) return;
    loadMetrics();

    // Disable real-time subscriptions due to WebSocket connection issues
    // Use periodic polling instead
    const interval = setInterval(() => {
      loadMetrics();
    }, 30000); // Refresh every 30 seconds

    return () => {
      clearInterval(interval);
    };
  }, [ready]);

  const loadMetrics = async () => {
    try {
      setLoading(true);

      // Try the view first, fallback to direct table calculation
      try {
        const { data, error } = await supabase
          .from('ai_assignment_stats')
          .select('*')
          .single();

        if (error) throw error;
        setMetrics(data || {
          total_photos: 0,
          ai_tagged: 0,
          flagged_for_review: 0,
          avg_confidence: 0,
          published_photos: 0,
          unpublished_photos: 0
        });
      } catch (viewError) {
        // Fallback: calculate metrics directly from photos_v2 table
        const [totalResult, publishedResult, unpublishedResult] = await Promise.all([
          supabase
            .from('photos_v2')
            .select('*', { count: 'exact', head: true }),
          supabase
            .from('photos_v2')
            .select('*', { count: 'exact', head: true })
            .eq('published', true),
          supabase
            .from('photos_v2')
            .select('*', { count: 'exact', head: true })
            .eq('published', false)
        ]);

        setMetrics({
          total_photos: totalResult.count || 0,
          ai_tagged: 0, // Placeholder
          flagged_for_review: 0, // Placeholder
          avg_confidence: 0, // Placeholder
          published_photos: publishedResult.count || 0,
          unpublished_photos: unpublishedResult.count || 0
        });
      }
    } catch (err) {
      console.error('Error loading AI metrics:', err);
      // Set default values on error
      setMetrics({
        total_photos: 0,
        ai_tagged: 0,
        flagged_for_review: 0,
        avg_confidence: 0,
        published_photos: 0,
        unpublished_photos: 0
      });
    } finally {
      setLoading(false);
    }
  };

  return {
    metrics,
    loading,
    refresh: loadMetrics
  };
} 