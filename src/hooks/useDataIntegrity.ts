import { useState, useCallback } from 'react';
import { DataIntegrityService, ValidationResult } from '@/utils/dataIntegrity';
import { toast } from 'sonner';

interface UseDataIntegrityReturn {
  // Validation functions
  validateSpecies: (data: any) => ValidationResult;
  validatePhoto: (data: any) => ValidationResult;
  
  // Safe operations
  createSpecies: (data: any) => Promise<{ success: boolean; id?: string; errors?: string[] }>;
  assignPhotoToSpecies: (photoId: string, speciesId: string) => Promise<{ success: boolean; errors?: string[] }>;
  
  // Integrity checks
  performIntegrityCheck: () => Promise<void>;
  fixOrphanedPhotos: () => Promise<void>;
  recalculatePhotoCounts: () => Promise<void>;
  
  // State
  loading: boolean;
  lastCheck?: Date;
}

/**
 * Hook for data integrity operations
 * Provides validation and safe operations for species and photos
 */
export const useDataIntegrity = (): UseDataIntegrityReturn => {
  const [loading, setLoading] = useState(false);
  const [lastCheck, setLastCheck] = useState<Date>();

  const validateSpecies = useCallback((data: any): ValidationResult => {
    return DataIntegrityService.validateSpecies(data);
  }, []);

  const validatePhoto = useCallback((data: any): ValidationResult => {
    return DataIntegrityService.validatePhoto(data);
  }, []);

  const createSpecies = useCallback(async (data: any) => {
    const validation = validateSpecies(data);
    
    if (!validation.isValid) {
      toast.error(`Validation failed: ${validation.errors.join(', ')}`);
      return { success: false, errors: validation.errors };
    }

    if (validation.warnings.length > 0) {
      validation.warnings.forEach(warning => toast.warning(warning));
    }

    setLoading(true);
    try {
      const result = await DataIntegrityService.createSpecies(data);
      
      if (result.success) {
        toast.success('Species created successfully');
      } else {
        toast.error(`Failed to create species: ${result.errors?.join(', ')}`);
      }
      
      return result;
    } catch (error) {
      console.error('Error creating species:', error);
      toast.error('Failed to create species');
      return { success: false, errors: ['Failed to create species'] };
    } finally {
      setLoading(false);
    }
  }, [validateSpecies]);

  const assignPhotoToSpecies = useCallback(async (photoId: string, speciesId: string) => {
    setLoading(true);
    try {
      const result = await DataIntegrityService.assignPhotoToSpecies(photoId, speciesId);
      
      if (result.success) {
        toast.success('Photo assigned successfully');
      } else {
        toast.error(`Failed to assign photo: ${result.errors?.join(', ')}`);
      }
      
      return result;
    } catch (error) {
      console.error('Error assigning photo:', error);
      toast.error('Failed to assign photo');
      return { success: false, errors: ['Failed to assign photo'] };
    } finally {
      setLoading(false);
    }
  }, []);

  const performIntegrityCheck = useCallback(async () => {
    setLoading(true);
    try {
      const result = await DataIntegrityService.performIntegrityCheck();
      setLastCheck(new Date());
      
      if (result.issues.length === 0) {
        toast.success('Data integrity check completed - no issues found');
      } else {
        toast.warning(`Found ${result.issues.length} data integrity issues`);
      }
    } catch (error) {
      console.error('Error performing integrity check:', error);
      toast.error('Failed to perform integrity check');
    } finally {
      setLoading(false);
    }
  }, []);

  const fixOrphanedPhotos = useCallback(async () => {
    setLoading(true);
    try {
      const orphanedPhotos = await DataIntegrityService.findOrphanedPhotos();
      
      if (orphanedPhotos.length === 0) {
        toast.info('No orphaned photos found');
        return;
      }

      const success = await DataIntegrityService.fixOrphanedPhotos(orphanedPhotos);
      
      if (success) {
        toast.success(`Fixed ${orphanedPhotos.length} orphaned photos`);
      } else {
        toast.error('Failed to fix orphaned photos');
      }
    } catch (error) {
      console.error('Error fixing orphaned photos:', error);
      toast.error('Failed to fix orphaned photos');
    } finally {
      setLoading(false);
    }
  }, []);

  const recalculatePhotoCounts = useCallback(async () => {
    setLoading(true);
    try {
      const result = await DataIntegrityService.recalculateAllPhotoCounts();
      
      if (result.errors === 0) {
        toast.success(`Updated photo counts for ${result.updated} species`);
      } else {
        toast.warning(`Updated ${result.updated} species, ${result.errors} errors`);
      }
    } catch (error) {
      console.error('Error recalculating photo counts:', error);
      toast.error('Failed to recalculate photo counts');
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    validateSpecies,
    validatePhoto,
    createSpecies,
    assignPhotoToSpecies,
    performIntegrityCheck,
    fixOrphanedPhotos,
    recalculatePhotoCounts,
    loading,
    lastCheck
  };
};

/**
 * Hook for form validation with real-time feedback
 */
export const useFormValidation = (type: 'species' | 'photo') => {
  const [errors, setErrors] = useState<string[]>([]);
  const [warnings, setWarnings] = useState<string[]>([]);
  const [isValid, setIsValid] = useState(true);

  const validate = useCallback((data: any) => {
    let validation: ValidationResult;
    
    if (type === 'species') {
      validation = DataIntegrityService.validateSpecies(data);
    } else {
      validation = DataIntegrityService.validatePhoto(data);
    }

    setErrors(validation.errors);
    setWarnings(validation.warnings);
    setIsValid(validation.isValid);

    return validation;
  }, [type]);

  const clearValidation = useCallback(() => {
    setErrors([]);
    setWarnings([]);
    setIsValid(true);
  }, []);

  return {
    validate,
    clearValidation,
    errors,
    warnings,
    isValid
  };
};

/**
 * Hook for monitoring data integrity status
 */
export const useIntegrityMonitor = () => {
  const [status, setStatus] = useState({
    orphanedPhotos: 0,
    photoCountErrors: 0,
    lastCheck: null as Date | null,
    isHealthy: true
  });

  const checkStatus = useCallback(async () => {
    try {
      const result = await DataIntegrityService.performIntegrityCheck();
      
      setStatus({
        orphanedPhotos: result.orphanedPhotos.length,
        photoCountErrors: result.photoCountUpdates.errors,
        lastCheck: new Date(),
        isHealthy: result.issues.length === 0
      });
    } catch (error) {
      console.error('Error checking integrity status:', error);
    }
  }, []);

  return {
    status,
    checkStatus
  };
};
