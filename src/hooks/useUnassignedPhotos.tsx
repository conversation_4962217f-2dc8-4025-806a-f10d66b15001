import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export function useUnassignedPhotos() {
  const [count, setCount] = useState<number>(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchUnassignedCount() {
      try {
        const { count, error } = await supabase
          .from('photos_v2')
          .select('*', { count: 'exact', head: true })
          .is('species_id', null);

        if (error) {
          console.error('Error fetching unassigned photos count:', error);
          setCount(0);
        } else {
          setCount(count || 0);
        }
      } catch (error) {
        console.error('Error fetching unassigned photos count:', error);
        setCount(0);
      } finally {
        setLoading(false);
      }
    }

    fetchUnassignedCount();
  }, []);

  return { count, loading };
}
