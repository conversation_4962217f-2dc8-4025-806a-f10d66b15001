import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';

export interface Location {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  country?: string;
  state_province?: string;
  county?: string;
  locality?: string;
  ebird_location_id?: string;
  inat_place_id?: number;
  elevation_m?: number;
  habitat_type?: string;
  created_at: string;
  updated_at: string;
}

export interface Observation {
  id: string;
  species_id: string;
  location_id: string;
  observer_id: string;
  source: 'ebird' | 'inaturalist' | 'manual';
  external_id: string;
  observation_date: string;
  observation_time?: string;
  count: number;
  breeding_code?: string;
  behavior?: string;
  notes?: string;
  confidence_level?: string;
  photo_url?: string;
  audio_url?: string;
  weather_conditions?: string;
  temperature_c?: number;
  wind_speed_kmh?: number;
  visibility_km?: number;
  created_at: string;
  updated_at: string;
}

export interface SpeciesOccurrence {
  id: string;
  species_id: string;
  location_id: string;
  month: number;
  frequency: number;
  abundance_category: string;
  first_observed: string;
  last_observed: string;
  total_observations: number;
  breeding_evidence: boolean;
  migration_status: string;
  created_at: string;
  updated_at: string;
}

export function useObservationData() {
  const [observations, setObservations] = useState<Observation[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  const [speciesOccurrence, setSpeciesOccurrence] = useState<SpeciesOccurrence[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check if tables exist and create them if needed
  const initializeTables = async () => {
    try {
      // Check if tables exist by trying to query them
      const { error: locationsError } = await supabase
        .from('locations')
        .select('id')
        .limit(1);

      if (locationsError && locationsError.code === '42P01') {
        // Table doesn't exist, create it
        console.log('Creating observation tables...');
        await createObservationTables();
      }
    } catch (error) {
      console.error('Error initializing tables:', error);
      setError('Failed to initialize database tables');
    }
  };

  const createObservationTables = async () => {
    const createTablesSQL = `
      -- 1. Locations table for standardized location data
      CREATE TABLE IF NOT EXISTS locations (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name TEXT NOT NULL,
        latitude DECIMAL(10, 8) NOT NULL,
        longitude DECIMAL(11, 8) NOT NULL,
        country TEXT,
        state_province TEXT,
        county TEXT,
        locality TEXT,
        ebird_location_id TEXT,
        inat_place_id INTEGER,
        elevation_m INTEGER,
        habitat_type TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(latitude, longitude, name)
      );

      -- 2. Observations table for tracking personal sightings
      CREATE TABLE IF NOT EXISTS observations (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        species_id UUID REFERENCES species_v2(id) ON DELETE CASCADE,
        location_id UUID REFERENCES locations(id) ON DELETE SET NULL,
        observer_id TEXT,
        source TEXT NOT NULL CHECK (source IN ('ebird', 'inaturalist', 'manual')),
        external_id TEXT,
        observation_date DATE NOT NULL,
        observation_time TIME,
        count INTEGER DEFAULT 1,
        breeding_code TEXT,
        behavior TEXT,
        notes TEXT,
        confidence_level TEXT,
        photo_url TEXT,
        audio_url TEXT,
        weather_conditions TEXT,
        temperature_c INTEGER,
        wind_speed_kmh INTEGER,
        visibility_km DECIMAL(4,2),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(external_id, source)
      );

      -- 3. Species occurrence patterns
      CREATE TABLE IF NOT EXISTS species_occurrence (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        species_id UUID REFERENCES species_v2(id) ON DELETE CASCADE,
        location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
        month INTEGER CHECK (month >= 1 AND month <= 12),
        frequency DECIMAL(5,4),
        abundance_category TEXT,
        first_observed DATE,
        last_observed DATE,
        total_observations INTEGER DEFAULT 0,
        breeding_evidence BOOLEAN DEFAULT FALSE,
        migration_status TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(species_id, location_id, month)
      );

      -- Add indexes
      CREATE INDEX IF NOT EXISTS idx_observations_species_id ON observations(species_id);
      CREATE INDEX IF NOT EXISTS idx_observations_location_id ON observations(location_id);
      CREATE INDEX IF NOT EXISTS idx_observations_date ON observations(observation_date);
      CREATE INDEX IF NOT EXISTS idx_observations_source ON observations(source);
      CREATE INDEX IF NOT EXISTS idx_locations_coords ON locations(latitude, longitude);
      CREATE INDEX IF NOT EXISTS idx_species_occurrence_species ON species_occurrence(species_id);
      CREATE INDEX IF NOT EXISTS idx_species_occurrence_location ON species_occurrence(location_id);

      -- Enable RLS
      ALTER TABLE locations ENABLE ROW LEVEL SECURITY;
      ALTER TABLE observations ENABLE ROW LEVEL SECURITY;
      ALTER TABLE species_occurrence ENABLE ROW LEVEL SECURITY;

      -- Add policies
      CREATE POLICY IF NOT EXISTS "Public read access for locations" ON locations FOR SELECT USING (true);
      CREATE POLICY IF NOT EXISTS "Public read access for species occurrence" ON species_occurrence FOR SELECT USING (true);
      CREATE POLICY IF NOT EXISTS "Admin full access for locations" ON locations FOR ALL USING (auth.email() = '<EMAIL>');
      CREATE POLICY IF NOT EXISTS "Admin full access for observations" ON observations FOR ALL USING (auth.email() = '<EMAIL>');
      CREATE POLICY IF NOT EXISTS "Admin full access for species occurrence" ON species_occurrence FOR ALL USING (auth.email() = '<EMAIL>');
    `;

    const { error } = await supabase.rpc('exec_sql', { sql: createTablesSQL });
    if (error) {
      throw error;
    }
  };

  const fetchObservations = async () => {
    try {
      const { data, error } = await supabase
        .from('observations')
        .select(`
          *,
          species:species_v2(name, common_name, scientific_name),
          location:locations(name, state_province, country)
        `)
        .order('observation_date', { ascending: false });

      if (error) throw error;
      setObservations(data || []);
    } catch (error) {
      console.error('Error fetching observations:', error);
      setError('Failed to fetch observations');
    }
  };

  const fetchLocations = async () => {
    try {
      const { data, error } = await supabase
        .from('locations')
        .select('*')
        .order('name');

      if (error) throw error;
      setLocations(data || []);
    } catch (error) {
      console.error('Error fetching locations:', error);
      setError('Failed to fetch locations');
    }
  };

  const fetchSpeciesOccurrence = async () => {
    try {
      const { data, error } = await supabase
        .from('species_occurrence')
        .select(`
          *,
          species:species_v2(name, common_name, scientific_name),
          location:locations(name, state_province, country)
        `)
        .order('frequency', { ascending: false });

      if (error) throw error;
      setSpeciesOccurrence(data || []);
    } catch (error) {
      console.error('Error fetching species occurrence:', error);
      setError('Failed to fetch species occurrence data');
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        await initializeTables();
        await Promise.all([
          fetchObservations(),
          fetchLocations(),
          fetchSpeciesOccurrence()
        ]);
      } catch (error) {
        console.error('Error loading observation data:', error);
        setError('Failed to load observation data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const addObservation = async (observation: Partial<Observation>) => {
    try {
      const { data, error } = await supabase
        .from('observations')
        .insert(observation)
        .select()
        .single();

      if (error) throw error;
      
      // Refresh observations
      await fetchObservations();
      return data;
    } catch (error) {
      console.error('Error adding observation:', error);
      throw error;
    }
  };

  const addLocation = async (location: Partial<Location>) => {
    try {
      const { data, error } = await supabase
        .from('locations')
        .insert(location)
        .select()
        .single();

      if (error) throw error;
      
      // Refresh locations
      await fetchLocations();
      return data;
    } catch (error) {
      console.error('Error adding location:', error);
      throw error;
    }
  };

  return {
    observations,
    locations,
    speciesOccurrence,
    loading,
    error,
    addObservation,
    addLocation,
    refetch: async () => {
      await Promise.all([
        fetchObservations(),
        fetchLocations(),
        fetchSpeciesOccurrence()
      ]);
    }
  };
}
