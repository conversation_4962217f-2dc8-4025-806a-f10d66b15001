import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export type PhotoReviewItem = {
  id: number;
  url: string;
  species_id: string | null;
  species_name: string | null;
  ai_suggested_id: string | null;
  ai_suggested_name: string | null;
  ai_confidence: number | null;
  ai_reviewed: boolean;
  location: string | null;
  dateTaken: string | null;
  published: boolean;
  needs_recovery?: boolean;
  reason?: string;
};

export function usePhotoReviewData() {
  return useQuery<PhotoReviewItem[]>({
    queryKey: ['photos-requiring-review'],
    queryFn: async () => {
      console.log('🔍 Fetching photos for review...');
      try {
        const { data, error } = await supabase
          .from('photos_requiring_review')
          .select('*')
          .order('ai_confidence', { ascending: false });
        
        // If there's a Supabase error, throw it
        if (error) {
          console.error('❌ Supabase error in photos_requiring_review:', error);
          throw error;
        }
        
        // If data is null (shouldn't happen with Supabase), return empty array
        if (data === null) {
          console.warn('⚠️ photos_requiring_review returned null data');
          return [];
        }
        
        // Return the data array (could be empty if no photos match criteria)
        console.log(`✅ photos_requiring_review returned ${data.length} photos`);
        return data as PhotoReviewItem[];
      } catch (err) {
        console.error('❌ Error fetching photos for review:', err);
        throw err;
      }
    },
    staleTime: 1000 * 60, // 1 minute
    refetchOnWindowFocus: false, // Disable this to prevent excessive refetching
    retry: 1, // Reduce retries
    refetchInterval: false, // Disable automatic refetching
  });
} 