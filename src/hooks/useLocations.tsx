import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export interface Location {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  state_province?: string;
  country?: string;
  description?: string;
  habitat_types?: string[];
  visitor_tips?: string;
  best_times?: string;
  website_url?: string;
  contact_info?: string;
  photo_url?: string;
  featured?: boolean;
  published?: boolean;
  photo_count?: number;
  total_species?: number;
}

export interface LocationFilters {
  search?: string;
  country?: string;
  state_province?: string;
  featured?: boolean;
  published?: boolean;
  limit?: number;
  offset?: number;
}

// Hook to fetch locations with optional filtering
export const useLocations = (filters: LocationFilters = {}) => {
  return useQuery({
    queryKey: ['locations', filters],
    queryFn: async (): Promise<Location[]> => {
      let query = supabase
        .from('locations')
        .select(`
          id,
          name,
          latitude,
          longitude,
          state_province,
          country,
          description,
          habitat_types,
          visitor_tips,
          best_times,
          website_url,
          contact_info,
          photo_url,
          featured,
          published
        `);

      // Apply filters
      if (filters.search) {
        query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%,state_province.ilike.%${filters.search}%,country.ilike.%${filters.search}%`);
      }
      
      if (filters.country) {
        query = query.eq('country', filters.country);
      }
      
      if (filters.state_province) {
        query = query.eq('state_province', filters.state_province);
      }
      
      if (filters.featured !== undefined) {
        query = query.eq('featured', filters.featured);
      }
      
      if (filters.published !== undefined) {
        query = query.eq('published', filters.published);
      }

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }
      
      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
      }

      // Order by featured first, then by name
      query = query.order('featured', { ascending: false }).order('name', { ascending: true });

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching locations:', error);
        throw error;
      }

      return data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
};

// Hook to fetch a single location by ID
export const useLocationById = (id: string) => {
  return useQuery({
    queryKey: ['location', id],
    queryFn: async (): Promise<Location | null> => {
      if (!id) return null;

      const { data, error } = await supabase
        .from('locations')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        throw error;
      }

      return data;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
};

// Hook to fetch locations with enhanced data (from hotspot_highlights view)
export const useHotspotLocations = (filters: LocationFilters = {}) => {
  return useQuery({
    queryKey: ['hotspot-locations', filters],
    queryFn: async (): Promise<Location[]> => {
      let query = supabase
        .from('hotspot_highlights')
        .select('*');

      // Apply filters
      if (filters.search) {
        query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%,state_province.ilike.%${filters.search}%,country.ilike.%${filters.search}%`);
      }
      
      if (filters.country) {
        query = query.eq('country', filters.country);
      }
      
      if (filters.state_province) {
        query = query.eq('state_province', filters.state_province);
      }
      
      if (filters.featured !== undefined) {
        query = query.eq('featured', filters.featured);
      }

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }
      
      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
      }

      // Order by featured first, then by total species count
      query = query.order('featured', { ascending: false }).order('total_species', { ascending: false });

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching hotspot locations:', error);
        throw error;
      }

      return data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
};
