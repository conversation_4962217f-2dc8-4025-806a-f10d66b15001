import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';

// Types for location data
export interface Location {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  country?: string;
  state_province?: string;
  county?: string;
  locality?: string;
  description?: string;
  directions?: string;
  facilities?: string[];
  best_time_to_visit?: string;
  difficulty_level?: 'easy' | 'moderate' | 'difficult' | 'expert';
  access_type?: 'public' | 'private' | 'permit_required' | 'restricted';
  parking_info?: string;
  entrance_fee?: number;
  website_url?: string;
  contact_info?: string;
  featured: boolean;
  photo_url?: string;
  habitat_types?: string[];
  target_species?: string[];
  seasonal_highlights?: any;
  visitor_tips?: string;
  published: boolean;
  google_maps_url?: string;
  apple_maps_url?: string;
  ebird_location_id?: string;
  inat_place_id?: number;
  elevation_m?: number;
  habitat_type?: string;
  created_at: string;
  updated_at: string;
}

export interface SpeciesLocation {
  id: string;
  species_id: string;
  location_id: string;
  abundance: 'rare' | 'uncommon' | 'common' | 'abundant' | 'very_common';
  seasonal_presence: string[];
  breeding_status: 'non_breeding' | 'possible' | 'probable' | 'confirmed';
  best_months: number[];
  notes?: string;
  confidence_level: 'low' | 'medium' | 'high' | 'confirmed';
  last_observed?: string;
  observation_count: number;
  created_at: string;
  updated_at: string;
}

export interface LocationPhoto {
  id: string;
  location_id: string;
  photo_url: string;
  title?: string;
  description?: string;
  photographer?: string;
  taken_date?: string;
  is_primary: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface LocationWithStats extends Location {
  total_species?: number;
  common_species?: number;
  rare_species?: number;
  breeding_species?: number;
  categories_present?: string[];
  total_observations?: number;
  primary_photo?: string;
  primary_photo_title?: string;
}

export interface SpeciesLocationDetail {
  species_id: string;
  species_name: string;
  common_name?: string;
  scientific_name?: string;
  category?: string;
  conservation_status?: string;
  location_id: string;
  location_name: string;
  state_province?: string;
  country?: string;
  latitude: number;
  longitude: number;
  abundance: string;
  seasonal_presence: string[];
  breeding_status: string;
  best_months: number[];
  notes?: string;
  confidence_level: string;
  last_observed?: string;
  observation_count: number;
  photo_count: number;
}

export function useLocations() {
  const [locations, setLocations] = useState<LocationWithStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchLocations = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('hotspot_highlights')
        .select('*')
        .order('featured', { ascending: false })
        .order('total_species', { ascending: false });

      if (error) throw error;
      setLocations(data || []);
    } catch (error) {
      console.error('Error fetching locations:', error);
      setError('Failed to fetch locations');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLocations();
  }, []);

  return {
    locations,
    loading,
    error,
    refetch: fetchLocations
  };
}

export function useLocation(locationId: string) {
  const [location, setLocation] = useState<LocationWithStats | null>(null);
  const [species, setSpecies] = useState<SpeciesLocationDetail[]>([]);
  const [photos, setPhotos] = useState<LocationPhoto[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchLocationDetails = async () => {
    if (!locationId) return;

    try {
      setLoading(true);
      
      // Fetch location details
      const { data: locationData, error: locationError } = await supabase
        .from('hotspot_highlights')
        .select('*')
        .eq('id', locationId)
        .single();

      if (locationError) throw locationError;
      setLocation(locationData);

      // Fetch species at this location
      const { data: speciesData, error: speciesError } = await supabase
        .from('species_location_details')
        .select('*')
        .eq('location_id', locationId)
        .order('abundance', { ascending: false })
        .order('species_name');

      if (speciesError) throw speciesError;
      setSpecies(speciesData || []);

      // Fetch location photos
      const { data: photosData, error: photosError } = await supabase
        .from('location_photos')
        .select('*')
        .eq('location_id', locationId)
        .order('is_primary', { ascending: false })
        .order('sort_order');

      if (photosError) throw photosError;
      setPhotos(photosData || []);

    } catch (error) {
      console.error('Error fetching location details:', error);
      setError('Failed to fetch location details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLocationDetails();
  }, [locationId]);

  return {
    location,
    species,
    photos,
    loading,
    error,
    refetch: fetchLocationDetails
  };
}

export function useSpeciesLocations(speciesId: string) {
  const [locations, setLocations] = useState<SpeciesLocationDetail[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSpeciesLocations = async () => {
    if (!speciesId) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('species_location_details')
        .select('*')
        .eq('species_id', speciesId)
        .order('abundance', { ascending: false })
        .order('location_name');

      if (error) throw error;
      setLocations(data || []);
    } catch (error) {
      console.error('Error fetching species locations:', error);
      setError('Failed to fetch species locations');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSpeciesLocations();
  }, [speciesId]);

  return {
    locations,
    loading,
    error,
    refetch: fetchSpeciesLocations
  };
}

export function useLocationManagement() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createLocation = async (locationData: Partial<Location>) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('locations')
        .insert(locationData)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating location:', error);
      setError('Failed to create location');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateLocation = async (locationId: string, updates: Partial<Location>) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('locations')
        .update(updates)
        .eq('id', locationId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating location:', error);
      setError('Failed to update location');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const deleteLocation = async (locationId: string) => {
    try {
      setLoading(true);
      setError(null);

      const { error } = await supabase
        .from('locations')
        .delete()
        .eq('id', locationId);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting location:', error);
      setError('Failed to delete location');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const addSpeciesLocation = async (speciesLocationData: Partial<SpeciesLocation>) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('species_locations')
        .insert(speciesLocationData)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error adding species to location:', error);
      setError('Failed to add species to location');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const removeSpeciesLocation = async (speciesId: string, locationId: string) => {
    try {
      setLoading(true);
      setError(null);

      const { error } = await supabase
        .from('species_locations')
        .delete()
        .eq('species_id', speciesId)
        .eq('location_id', locationId);

      if (error) throw error;
    } catch (error) {
      console.error('Error removing species from location:', error);
      setError('Failed to remove species from location');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const addLocationPhoto = async (photoData: Partial<LocationPhoto>) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('location_photos')
        .insert(photoData)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error adding location photo:', error);
      setError('Failed to add location photo');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    createLocation,
    updateLocation,
    deleteLocation,
    addSpeciesLocation,
    removeSpeciesLocation,
    addLocationPhoto
  };
}
