import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import type { Tables } from "@/integrations/supabase/types";
import { fetchSpeciesFunFacts } from "./useSpeciesData";

// Type definitions for public data
type SpeciesV2 = Tables<"species_v2">;
type Photo = Tables<"photos_v2">;

export interface PublicSpecies extends SpeciesV2 {
  photos: Photo[];
  photo_count: number;
}

export interface PublicPhoto extends Photo {
  species?: {
    id: string;
    name: string;
    category: string;
    conservation_status?: string;
  };
}

export interface PublicSpeciesFilters {
  search?: string;
  category?: string;
  conservation_status?: string;
  location?: string;
  featured?: boolean;
  limit?: number;
  offset?: number;
}

export interface PublicPhotoFilters {
  species_id?: string;
  photographer?: string;
  location?: string;
  limit?: number;
  offset?: number;
}

// Hook to fetch published species with their photos
export const usePublicSpecies = (filters: PublicSpeciesFilters = {}) => {
  return useQuery({
    queryKey: ['public-species', filters],
    queryFn: async (): Promise<PublicSpecies[]> => {


      // Build species query with only essential fields for performance
      let speciesQuery = supabase
        .from('species_v2')
        .select(`
          id,
          name,
          scientific_name,
          common_name,
          category,
          conservation_status,
          description,
          habitat,
          diet,
          behavior,
          size_cm,
          weight_g,
          lifespan_years,
          migration_pattern,
          breeding_season,
          threat_level,
          population_trend,
          featured,
          photo_count,
          tags,
          size_description,
          ai_fun_facts,
          fun_facts_field,
          regions,
          created_at,
          updated_at
        `)
        .eq('published', true);

      // Apply filters
      if (filters.category) {
        speciesQuery = speciesQuery.eq('category', filters.category);
      }
      if (filters.conservation_status) {
        speciesQuery = speciesQuery.eq('conservation_status', filters.conservation_status);
      }
      if (filters.featured !== undefined) {
        speciesQuery = speciesQuery.eq('featured', filters.featured);
      }
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        speciesQuery = speciesQuery.or(`name.ilike.%${searchTerm}%,common_name.ilike.%${searchTerm}%,scientific_name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
      }

      // Apply pagination
      if (filters.limit) {
        speciesQuery = speciesQuery.limit(filters.limit);
      }
      if (filters.offset) {
        speciesQuery = speciesQuery.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
      }

      speciesQuery = speciesQuery.order('name', { ascending: true });

      const { data: speciesData, error: speciesError } = await speciesQuery;



      if (speciesError) {
        console.error('Error fetching public species:', speciesError);
        throw speciesError;
      }
      if (!speciesData || speciesData.length === 0) {
        return [];
      }

      // Fetch photos for all species in one optimized query
      const speciesIds = speciesData.map(s => s.id);
      let photosQuery = supabase
        .from('photos_v2')
        .select(`
          id,
          url,
          title,
          description,
          photographer,
          location,
          camera_settings,
          weather_conditions,
          time_of_day,
          tags,
          created_at,
          species_id
        `)
        .in('species_id', speciesIds)
        .eq('published', true);

      // Note: Location filtering is now handled at species level, not photo level
      // So we don't filter photos by location here anymore

      photosQuery = photosQuery
        .order('created_at', { ascending: false })
        .limit(3000); // Increased limit for better photo coverage

      const { data: photosData, error: photosError } = await photosQuery;

      if (photosError) {
        console.error('Error fetching photos for public species:', photosError);
        // Don't throw error for photos, just log it
      }

      // Combine species with their photos
      const speciesWithPhotos = speciesData.map(species => {
        const speciesPhotos = photosData?.filter(photo => photo.species_id === species.id) || [];
        return {
          ...species,
          photos: speciesPhotos,
          photo_count: speciesPhotos.length
        };
      });

      // Apply geographic filtering at species level
      if (filters.location) {
        return speciesWithPhotos.filter(species => {
          // Check if location matches countries
          if (species.countries && Array.isArray(species.countries)) {
            if (species.countries.some(country =>
              country && country.toLowerCase().includes(filters.location!.toLowerCase())
            )) {
              return true;
            }
          }

          // Check if location matches states/provinces
          if (species.states_provinces && Array.isArray(species.states_provinces)) {
            if (species.states_provinces.some(state =>
              state && state.toLowerCase().includes(filters.location!.toLowerCase())
            )) {
              return true;
            }
          }

          // Check if location matches primary region
          if (species.primary_region &&
              species.primary_region.toLowerCase().includes(filters.location!.toLowerCase())) {
            return true;
          }

          // Fallback to legacy regions field
          if (species.regions) {
            const regions = Array.isArray(species.regions) ? species.regions : [species.regions];
            if (regions.some(region =>
              region && region.toLowerCase().includes(filters.location!.toLowerCase())
            )) {
              return true;
            }
          }

          return false;
        });
      }

      return speciesWithPhotos;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
};

// Hook to fetch a single published species by ID
export const usePublicSpeciesById = (id: string) => {
  return useQuery({
    queryKey: ['public-species', id],
    queryFn: async (): Promise<PublicSpecies | null> => {
      if (!id) return null;

      const { data: speciesData, error: speciesError } = await supabase
        .from('species_v2')
        .select('*')
        .eq('id', id)
        .eq('published', true)
        .single();

      if (speciesError) {
        if (speciesError.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        throw speciesError;
      }

      // Fetch photos for this species
      const { data: photosData, error: photosError } = await supabase
        .from('photos_v2')
        .select(`
          id,
          url,
          title,
          description,
          photographer,
          location,
          camera_settings,
          weather_conditions,
          time_of_day,
          tags,
          created_at,
          species_id
        `)
        .eq('species_id', id)
        .eq('published', true)
        .order('created_at', { ascending: false });

      if (photosError) {
        console.error('Error fetching photos for species:', photosError);
      }

      // Fetch fun facts from the dedicated table
      const funFacts = await fetchSpeciesFunFacts(id);

      return {
        ...speciesData,
        photos: photosData || [],
        photo_count: photosData?.length || 0,
        fun_facts: funFacts
      };
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
};

// Hook to fetch published photos with species information
export const usePublicPhotos = (filters: PublicPhotoFilters = {}) => {
  return useQuery({
    queryKey: ['public-photos', filters],
    queryFn: async (): Promise<PublicPhoto[]> => {
      let query = supabase
        .from('photos_v2')
        .select(`
          id,
          url,
          title,
          description,
          photographer,
          location,
          camera_settings,
          weather_conditions,
          time_of_day,
          tags,
          created_at,
          species_id
        `)
        .eq('published', true);

      // Apply filters
      if (filters.species_id) {
        query = query.eq('species_id', filters.species_id);
      }
      if (filters.photographer) {
        query = query.ilike('photographer', `%${filters.photographer}%`);
      }
      if (filters.location) {
        query = query.ilike('location', `%${filters.location}%`);
      }

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }
      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
      }

      // Order by created_at descending to get newest photos first
      query = query.order('created_at', { ascending: false });

      const { data: photosData, error } = await query;

      if (error) {
        console.error('Error fetching public photos:', error);
        throw error;
      }

      if (!photosData || photosData.length === 0) {
        return [];
      }

      // Fetch species data for all photos
      const speciesIds = [...new Set(photosData.map(p => p.species_id).filter(Boolean))];

      if (speciesIds.length === 0) {
        return photosData.map(photo => ({ ...photo, species: null }));
      }

      const { data: speciesData, error: speciesError } = await supabase
        .from('species_v2')
        .select('id, name, category, conservation_status')
        .in('id', speciesIds)
        .eq('published', true);

      if (speciesError) {
        console.error('Error fetching species for photos:', speciesError);
        // Return photos without species data rather than failing completely
        return photosData.map(photo => ({ ...photo, species: null }));
      }

      // Create a map for quick species lookup
      const speciesMap = new Map(speciesData?.map(s => [s.id, s]) || []);

      // Join photos with species data
      return photosData.map(photo => ({
        ...photo,
        species: photo.species_id ? speciesMap.get(photo.species_id) || null : null
      }));
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
};

// Hook to fetch metadata for filters (categories, conservation statuses, locations)
export const usePublicMetadata = () => {
  return useQuery({
    queryKey: ['public-metadata'],
    queryFn: async () => {
      // Get categories, conservation statuses, and geographic data from published species
      const { data: speciesData } = await supabase
        .from('species_v2')
        .select('category, conservation_status, countries, states_provinces, primary_region, regions')
        .eq('published', true);

      // Process categories
      const categoryMap = new Map<string, number>();
      const statusMap = new Map<string, number>();
      const countryMap = new Map<string, number>();
      const regionMap = new Map<string, number>();
      const stateProvinceMap = new Map<string, number>();

      speciesData?.forEach(species => {
        if (species.category) {
          categoryMap.set(species.category, (categoryMap.get(species.category) || 0) + 1);
        }
        if (species.conservation_status) {
          statusMap.set(species.conservation_status, (statusMap.get(species.conservation_status) || 0) + 1);
        }

        // Process countries array
        if (species.countries && Array.isArray(species.countries)) {
          species.countries.forEach(country => {
            if (country) {
              countryMap.set(country, (countryMap.get(country) || 0) + 1);
            }
          });
        }

        // Process states/provinces array
        if (species.states_provinces && Array.isArray(species.states_provinces)) {
          species.states_provinces.forEach(state => {
            if (state) {
              stateProvinceMap.set(state, (stateProvinceMap.get(state) || 0) + 1);
            }
          });
        }

        // Process primary region
        if (species.primary_region) {
          regionMap.set(species.primary_region, (regionMap.get(species.primary_region) || 0) + 1);
        }

        // Fallback to legacy regions field if new fields are empty
        if (species.regions && (!species.countries || species.countries.length === 0)) {
          // Parse legacy regions field (assuming it might be a string or array)
          const regions = Array.isArray(species.regions) ? species.regions : [species.regions];
          regions.forEach(region => {
            if (region) {
              countryMap.set(region, (countryMap.get(region) || 0) + 1);
            }
          });
        }
      });

      // Add some sample data for testing if no geographic data is found
      const sampleCountries = countryMap.size === 0 ? [
        { country: 'Australia', count: 15 },
        { country: 'United States', count: 25 },
        { country: 'Canada', count: 18 },
        { country: 'United Kingdom', count: 12 },
        { country: 'Kenya', count: 8 },
        { country: 'India', count: 10 }
      ] : Array.from(countryMap.entries()).map(([country, count]) => ({
        country,
        count
      })).sort((a, b) => a.country.localeCompare(b.country));

      const sampleRegions = regionMap.size === 0 ? [
        { region: 'North America', count: 43 },
        { region: 'Australia', count: 15 },
        { region: 'Europe', count: 12 },
        { region: 'Africa', count: 8 },
        { region: 'Asia', count: 10 }
      ] : Array.from(regionMap.entries()).map(([region, count]) => ({
        region,
        count
      })).sort((a, b) => a.region.localeCompare(b.region));

      return {
        categories: Array.from(categoryMap.entries()).map(([category, count]) => ({
          category,
          count
        })),
        conservationStatuses: Array.from(statusMap.entries()).map(([status, count]) => ({
          conservation_status: status,
          count
        })),
        countries: sampleCountries,
        regions: sampleRegions,
        statesProvinces: Array.from(stateProvinceMap.entries()).map(([state, count]) => ({
          state,
          count
        })).sort((a, b) => a.state.localeCompare(b.state))
      };
    },
    staleTime: 10 * 60 * 1000, // 10 minutes (metadata changes less frequently)
    refetchOnWindowFocus: false,
    retry: 1,
  });
};
