import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { trackPageView, trackWildlifeEvent } from '@/services/analytics';

// Hook for automatic page view tracking
export const usePageTracking = () => {
  const location = useLocation();
  const previousPath = useRef<string>('');

  useEffect(() => {
    const currentPath = location.pathname + location.search;
    
    // Only track if the path has actually changed
    if (currentPath !== previousPath.current) {
      trackPageView(currentPath);
      previousPath.current = currentPath;
    }
  }, [location]);
};

// Hook for tracking time spent on page
export const useTimeTracking = (pageName: string) => {
  const startTime = useRef<number>(Date.now());

  useEffect(() => {
    startTime.current = Date.now();

    return () => {
      const timeSpent = Math.round((Date.now() - startTime.current) / 1000);
      if (timeSpent > 5) { // Only track if user spent more than 5 seconds
        trackWildlifeEvent.timeOnPage(pageName, timeSpent);
      }
    };
  }, [pageName]);
};

// Hook for tracking scroll depth
export const useScrollTracking = (pageName: string) => {
  const maxScrollDepth = useRef<number>(0);
  const hasTracked25 = useRef<boolean>(false);
  const hasTracked50 = useRef<boolean>(false);
  const hasTracked75 = useRef<boolean>(false);
  const hasTracked100 = useRef<boolean>(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercentage = Math.round((scrollTop / documentHeight) * 100);

      if (scrollPercentage > maxScrollDepth.current) {
        maxScrollDepth.current = scrollPercentage;
      }

      // Track milestone percentages
      if (scrollPercentage >= 25 && !hasTracked25.current) {
        trackWildlifeEvent.scrollDepth(pageName, 25);
        hasTracked25.current = true;
      } else if (scrollPercentage >= 50 && !hasTracked50.current) {
        trackWildlifeEvent.scrollDepth(pageName, 50);
        hasTracked50.current = true;
      } else if (scrollPercentage >= 75 && !hasTracked75.current) {
        trackWildlifeEvent.scrollDepth(pageName, 75);
        hasTracked75.current = true;
      } else if (scrollPercentage >= 100 && !hasTracked100.current) {
        trackWildlifeEvent.scrollDepth(pageName, 100);
        hasTracked100.current = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      
      // Track final scroll depth on unmount
      if (maxScrollDepth.current > 0) {
        trackWildlifeEvent.scrollDepth(pageName, maxScrollDepth.current);
      }
    };
  }, [pageName]);
};

// Combined hook for comprehensive page analytics
export const usePageAnalytics = (pageName: string) => {
  usePageTracking();
  useTimeTracking(pageName);
  useScrollTracking(pageName);
};

// Hook for wildlife-specific analytics
export const useWildlifeAnalytics = () => {
  return {
    trackSpeciesView: trackWildlifeEvent.viewSpecies,
    trackSpeciesSearch: trackWildlifeEvent.searchSpecies,
    trackSpeciesFilter: trackWildlifeEvent.filterSpecies,
    trackPhotoView: trackWildlifeEvent.viewPhoto,
    trackPhotoShare: trackWildlifeEvent.sharePhoto,
    trackHotspotView: trackWildlifeEvent.viewHotspot,
    trackHotspotSearch: trackWildlifeEvent.searchHotspots,
    trackNavigation: trackWildlifeEvent.navigateToSection,
  };
};

export default {
  usePageTracking,
  useTimeTracking,
  useScrollTracking,
  usePageAnalytics,
  useWildlifeAnalytics,
};
