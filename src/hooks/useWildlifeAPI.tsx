import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api, type Species, type Photo, type SearchResult, type Category, type ConservationStatus } from "@/lib/api";
import { toast } from "sonner";

// Species hooks
export const useSpecies = (params?: {
  page?: number;
  limit?: number;
  category?: string;
  conservation_status?: string;
  featured?: boolean;
  published?: boolean;
}) => {
  return useQuery({
    queryKey: ['species', params],
    queryFn: () => api.getSpecies(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useSpeciesById = (id: string) => {
  return useQuery({
    queryKey: ['species', id],
    queryFn: () => api.getSpeciesById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreateSpecies = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (species: Partial<Species>) => api.createSpecies(species),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['species'] });
      toast.success('Species created successfully');
    },
    onError: (error) => {
      toast.error(`Failed to create species: ${error.message}`);
    },
  });
};

export const useUpdateSpecies = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, species }: { id: string; species: Partial<Species> }) => 
      api.updateSpecies(id, species),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['species'] });
      queryClient.invalidateQueries({ queryKey: ['species', id] });
      toast.success('Species updated successfully');
    },
    onError: (error) => {
      toast.error(`Failed to update species: ${error.message}`);
    },
  });
};

export const useDeleteSpecies = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => api.deleteSpecies(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['species'] });
      toast.success('Species deleted successfully');
    },
    onError: (error) => {
      toast.error(`Failed to delete species: ${error.message}`);
    },
  });
};

// Photo hooks
export const usePhotos = (params?: {
  page?: number;
  limit?: number;
  species_id?: string;
  photographer?: string;
  location?: string;
  published?: boolean;
}) => {
  return useQuery({
    queryKey: ['photos', params],
    queryFn: () => api.getPhotos(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const usePhotoById = (id: number) => {
  return useQuery({
    queryKey: ['photos', id],
    queryFn: () => api.getPhotoById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCreatePhoto = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (photo: Partial<Photo>) => api.createPhoto(photo),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['photos'] });
      toast.success('Photo created successfully');
    },
    onError: (error) => {
      toast.error(`Failed to create photo: ${error.message}`);
    },
  });
};

export const useUpdatePhoto = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, photo }: { id: number; photo: Partial<Photo> }) => 
      api.updatePhoto(id, photo),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['photos'] });
      queryClient.invalidateQueries({ queryKey: ['photos', id] });
      toast.success('Photo updated successfully');
    },
    onError: (error) => {
      toast.error(`Failed to update photo: ${error.message}`);
    },
  });
};

export const useDeletePhoto = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: number) => api.deletePhoto(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['photos'] });
      toast.success('Photo deleted successfully');
    },
    onError: (error) => {
      toast.error(`Failed to delete photo: ${error.message}`);
    },
  });
};

// Search hook
export const useSearch = (params: {
  q: string;
  type?: 'species' | 'photos' | 'all';
  page?: number;
  limit?: number;
  category?: string;
  conservation_status?: string;
}) => {
  return useQuery({
    queryKey: ['search', params],
    queryFn: () => api.search(params),
    enabled: !!params.q.trim(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Metadata hooks
export const useCategories = () => {
  return useQuery({
    queryKey: ['categories'],
    queryFn: () => api.getCategories(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useConservationStatuses = () => {
  return useQuery({
    queryKey: ['conservation-statuses'],
    queryFn: () => api.getConservationStatuses(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}; 