import { useEffect, useRef } from 'react';

interface PerformanceMetrics {
  queryTime: number;
  renderTime: number;
  componentName: string;
  timestamp: number;
}

interface UsePerformanceMonitorOptions {
  componentName: string;
  enabled?: boolean;
  threshold?: number; // Log only if query takes longer than this (ms)
}

export const usePerformanceMonitor = (options: UsePerformanceMonitorOptions) => {
  const { componentName, enabled = process.env.NODE_ENV === 'development', threshold = 100 } = options;
  const startTimeRef = useRef<number>();
  const metricsRef = useRef<PerformanceMetrics[]>([]);

  const startMeasurement = () => {
    if (!enabled) return;
    startTimeRef.current = performance.now();
  };

  const endMeasurement = (operationType: 'query' | 'render' = 'query') => {
    if (!enabled || !startTimeRef.current) return;
    
    const endTime = performance.now();
    const duration = endTime - startTimeRef.current;
    
    if (duration > threshold) {
      const metric: PerformanceMetrics = {
        queryTime: operationType === 'query' ? duration : 0,
        renderTime: operationType === 'render' ? duration : 0,
        componentName,
        timestamp: Date.now()
      };
      
      metricsRef.current.push(metric);
      
      // Log performance warning
      console.warn(`🐌 Performance Warning: ${componentName} ${operationType} took ${duration.toFixed(2)}ms`);
      
      // Keep only last 100 metrics to prevent memory leaks
      if (metricsRef.current.length > 100) {
        metricsRef.current = metricsRef.current.slice(-100);
      }
    }
  };

  const getMetrics = () => metricsRef.current;

  const clearMetrics = () => {
    metricsRef.current = [];
  };

  // Log metrics summary on component unmount
  useEffect(() => {
    return () => {
      if (enabled && metricsRef.current.length > 0) {
        const avgQueryTime = metricsRef.current.reduce((sum, m) => sum + m.queryTime, 0) / metricsRef.current.length;
        const avgRenderTime = metricsRef.current.reduce((sum, m) => sum + m.renderTime, 0) / metricsRef.current.length;
        
        console.log(`📊 Performance Summary for ${componentName}:`, {
          totalMeasurements: metricsRef.current.length,
          avgQueryTime: avgQueryTime.toFixed(2) + 'ms',
          avgRenderTime: avgRenderTime.toFixed(2) + 'ms',
          slowestQuery: Math.max(...metricsRef.current.map(m => m.queryTime)).toFixed(2) + 'ms',
          slowestRender: Math.max(...metricsRef.current.map(m => m.renderTime)).toFixed(2) + 'ms'
        });
      }
    };
  }, [componentName, enabled]);

  return {
    startMeasurement,
    endMeasurement,
    getMetrics,
    clearMetrics
  };
};

// Hook for monitoring React Query performance
export const useQueryPerformanceMonitor = (queryKey: string[], enabled = true) => {
  const monitor = usePerformanceMonitor({ 
    componentName: `Query: ${queryKey.join('-')}`, 
    enabled 
  });

  return {
    onQueryStart: monitor.startMeasurement,
    onQueryEnd: monitor.endMeasurement,
    getMetrics: monitor.getMetrics
  };
};

// Performance utilities
export const performanceUtils = {
  // Measure function execution time
  measureFunction: async <T>(fn: () => Promise<T>, name: string): Promise<T> => {
    const start = performance.now();
    try {
      const result = await fn();
      const end = performance.now();
      const duration = end - start;
      
      if (duration > 100) {
        console.warn(`🐌 Slow function: ${name} took ${duration.toFixed(2)}ms`);
      }
      
      return result;
    } catch (error) {
      const end = performance.now();
      const duration = end - start;
      console.error(`❌ Function failed: ${name} took ${duration.toFixed(2)}ms before failing`, error);
      throw error;
    }
  },

  // Get current performance metrics
  getCurrentMetrics: () => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const paint = performance.getEntriesByType('paint');
      
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
        firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
        memoryUsage: (performance as any).memory ? {
          used: (performance as any).memory.usedJSHeapSize,
          total: (performance as any).memory.totalJSHeapSize,
          limit: (performance as any).memory.jsHeapSizeLimit
        } : null
      };
    }
    return null;
  },

  // Log performance summary
  logPerformanceSummary: () => {
    const metrics = performanceUtils.getCurrentMetrics();
    if (metrics) {
      console.log('📊 Performance Summary:', metrics);
    }
  }
};
