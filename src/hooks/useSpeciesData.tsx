import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import type { Tables } from "@/integrations/supabase/types";
import { isValidUUID } from "@/utils/validate";

// Type definitions
type SpeciesV2 = Tables<"species_v2">;
type Photo = Tables<"photos_v2">;

// Unified interfaces
export interface SpeciesWithPhotos extends SpeciesV2 {
  photos: Photo[];
  photo_count: number;
}

export interface SpeciesDetails extends SpeciesV2 {
  photos: Photo[];
  fun_facts: string[];
}

export interface SpeciesListItem extends SpeciesV2 {
  photos?: Photo[];
  photo_count: number;
}

// Query parameters interface
export interface SpeciesQueryParams {
  // Filtering
  category?: string;
  conservation_status?: string;
  featured?: boolean;
  published?: boolean;
  
  // Pagination
  page?: number;
  limit?: number;
  
  // Search
  search?: string;
  
  // Data inclusion
  includePhotos?: boolean;
  includeUnpublishedPhotos?: boolean;
  onlyWithPhotos?: boolean; // Filter to only show species with published photos
  
  // Sorting
  sortBy?: 'name' | 'created_at' | 'updated_at' | 'photo_count';
  sortOrder?: 'asc' | 'desc';
}

// Query modes
export type SpeciesQueryMode =
  | 'list'           // Get list of species with optional photos
  | 'detail'         // Get single species with full details
  | 'with-photos'    // Get species with photos (replaces useSpeciesDataV2)
  | 'needing-review' // Get species needing review (admin)
  | 'by-id'          // Get single species by ID
  | 'all-for-analysis'; // Get ALL species without pagination for data quality analysis

// Main hook interface
export interface UseSpeciesDataOptions {
  mode: SpeciesQueryMode;
  id?: string;
  params?: SpeciesQueryParams;
  enabled?: boolean;
}

// Utility functions
export const extractFunFacts = (species: SpeciesV2): string[] => {
  const facts: string[] = [];

  if (species.ai_fun_facts) {
    try {
      const parsed = JSON.parse(species.ai_fun_facts);
      if (Array.isArray(parsed)) {
        facts.push(...parsed);
      }
    } catch (e) {
      // If parsing fails, treat as single fact
      facts.push(species.ai_fun_facts);
    }
  }

  if (species.fun_facts_field) {
    facts.push(species.fun_facts_field);
  }

  return facts;
};

// New function to fetch fun facts from the dedicated table
export const fetchSpeciesFunFacts = async (speciesId: string): Promise<string[]> => {
  const { data, error } = await supabase
    .from('fun_facts')
    .select('fact')
    .eq('species_id', speciesId)
    .order('created_at', { ascending: true });

  if (error) {
    console.error('Error fetching fun facts:', error);
    return [];
  }

  return data?.map(item => item.fact) || [];
};

export const parseSize = (sizeStr: string | null): { value: number; unit: string } | null => {
  if (!sizeStr) return null;
  
  const match = sizeStr.match(/(\d+(?:\.\d+)?)\s*(\w+)/);
  if (match) {
    return {
      value: parseFloat(match[1]),
      unit: match[2]
    };
  }
  return null;
};

export const parseWeight = (weightStr: string | null): { value: number; unit: string } | null => {
  if (!weightStr) return null;

  const match = weightStr.match(/(\d+(?:\.\d+)?)\s*(\w+)/);
  if (match) {
    return {
      value: parseFloat(match[1]),
      unit: match[2]
    };
  }
  return null;
};

// Query functions for different modes
const fetchSpeciesList = async (params: SpeciesQueryParams = {}): Promise<SpeciesListItem[]> => {

  let query = supabase
    .from('species_v2')
    .select(`
      id,
      name,
      scientific_name,
      family,
      category,
      conservation_status,
      description,
      habitat,
      diet,
      behavior,
      size_cm,
      weight_g,
      lifespan_years,
      migration_pattern,
      breeding_season,
      threat_level,
      population_trend,
      featured,
      photo_count,
      tags,
      published,
      size_description,
      notes,
      ai_fun_facts,
      fun_facts_field,
      related_groups,
      conservation_actions,
      regions,
      common_group,
      taxonomy_order,
      taxonomy_subfamily,
      taxonomy_genus,
      created_at,
      updated_at
    `);

  // Apply filters
  if (params.published !== undefined) {
    query = query.eq('published', params.published);
  }

  if (params.featured !== undefined) {
    query = query.eq('featured', params.featured);
  }

  if (params.category) {
    query = query.eq('category', params.category);
  }

  if (params.conservation_status) {
    query = query.eq('conservation_status', params.conservation_status);
  }

  if (params.search) {
    query = query.or(`name.ilike.%${params.search}%,scientific_name.ilike.%${params.search}%`);
  }

  // Apply sorting
  const sortBy = params.sortBy || 'name';
  const sortOrder = params.sortOrder || 'asc';
  query = query.order(sortBy, { ascending: sortOrder === 'asc' });

  // Apply pagination (default to prevent loading all data)
  const page = params.page || 1;
  const limit = params.limit || 20; // Default limit to prevent performance issues
  const from = (page - 1) * limit;
  const to = from + limit - 1;
  query = query.range(from, to);

  const { data: speciesData, error: speciesError } = await query;

  if (speciesError) {
    console.error('Error fetching species list:', speciesError);
    throw speciesError;
  }

  if (!speciesData) {
    return [];
  }

  // If photos are requested, use optimized join query instead of N+1
  if (params.includePhotos && speciesData.length > 0) {
    const speciesIds = speciesData.map(s => s.id);

    // Optimized query: fetch only essential photo fields and limit per species
    let photosQuery = supabase
      .from('photos_v2')
      .select(`
        id,
        url,
        title,
        published,
        created_at,
        species_id
      `)
      .in('species_id', speciesIds);

    if (!params.includeUnpublishedPhotos) {
      photosQuery = photosQuery.eq('published', true);
    }

    // Limit photos per species to prevent memory issues
    photosQuery = photosQuery
      .order('created_at', { ascending: false })
      .limit(2000); // Increased limit for better photo coverage

    const { data: allPhotos, error: photosError } = await photosQuery;

    if (photosError) {
      console.error('Error fetching photos:', photosError);
    }

    // Group photos by species_id with performance optimization
    const photosBySpecies = new Map<string, Photo[]>();
    (allPhotos || []).forEach(photo => {
      if (photo.species_id) {
        if (!photosBySpecies.has(photo.species_id)) {
          photosBySpecies.set(photo.species_id, []);
        }
        photosBySpecies.get(photo.species_id)!.push(photo);
      }
    });

    // Attach photos to species and filter out species without photos if requested
    const allSpeciesWithPhotos = speciesData.map(species => ({
      ...species,
      photos: photosBySpecies.get(species.id) || [],
      photo_count: species.photo_count || 0
    }));

    // If we're including photos, filter to only show species with published photos
    if (params.onlyWithPhotos !== false) { // Default to filtering unless explicitly disabled
      return allSpeciesWithPhotos.filter(species => species.photos.length > 0);
    }

    return allSpeciesWithPhotos;
  }

  return speciesData.map(species => ({
    ...species,
    photo_count: species.photo_count || 0
  }));
};

const fetchSpeciesWithPhotos = async (): Promise<SpeciesWithPhotos[]> => {

  // Fetch only essential fields for performance - reduced from 30+ fields to 12 essential ones
  const { data: speciesData, error: speciesError } = await supabase
    .from('species_v2')
    .select(`
      id,
      name,
      scientific_name,
      category,
      conservation_status,
      description,
      featured,
      photo_count,
      published,
      size_description,
      ai_fun_facts,
      fun_facts_field
    `)
    .eq('published', true)
    .order('name')
    .limit(1000); // Increased limit to load more species

  if (speciesError) {
    console.error('Error fetching species from species_v2:', speciesError);
    throw speciesError;
  }

  if (!speciesData || speciesData.length === 0) {
    return [];
  }

  // Fetch photos with optimized query - only essential fields and limited count
  const speciesIds = speciesData.map(s => s.id);
  const { data: allPhotos, error: photosError } = await supabase
    .from('photos_v2')
    .select(`
      id,
      url,
      title,
      published,
      created_at,
      species_id
    `)
    .in('species_id', speciesIds)
    .eq('published', true)
    .order('created_at', { ascending: false })
    .limit(5000); // Increased limit to load more photos

  if (photosError) {
    console.error('Error fetching photos:', photosError);
  }

  // Group photos by species_id using Map for better performance
  const photosBySpecies = new Map<string, Photo[]>();
  (allPhotos || []).forEach(photo => {
    if (photo.species_id) {
      if (!photosBySpecies.has(photo.species_id)) {
        photosBySpecies.set(photo.species_id, []);
      }
      photosBySpecies.get(photo.species_id)!.push(photo);
    }
  });

  // Combine species with their photos using Map lookup and filter out species without published photos
  const allSpeciesWithPhotos: SpeciesWithPhotos[] = speciesData.map(species => ({
    ...species,
    photos: photosBySpecies.get(species.id) || [],
    photo_count: species.photo_count || 0
  }));

  // Filter to only include species that have at least one published photo
  const speciesWithPhotos = allSpeciesWithPhotos.filter(species => {
    return species.photos.length > 0;
  });

  return speciesWithPhotos;
};

const fetchSpeciesDetail = async (speciesId: string): Promise<SpeciesDetails> => {
  console.log(`🔍 Fetching species details for id: ${speciesId}`);

  // Query species_v2 table
  const { data: speciesData, error: speciesError } = await supabase
    .from('species_v2')
    .select('*')
    .eq('id', speciesId)
    .single();

  if (speciesError) {
    console.error('❌ Error fetching species:', speciesError);
    throw new Error(`Failed to fetch species: ${speciesError.message}`);
  }

  if (!speciesData) {
    throw new Error('Species not found.');
  }

  console.log('✅ Species data fetched:', {
    id: speciesData.id,
    name: speciesData.name,
    published: speciesData.published
  });

  // Query photos table for this species
  const { data: photosData, error: photosError } = await supabase
    .from('photos_v2')
    .select('*')
    .eq('species_id', speciesId)
    .order('created_at', { ascending: false });

  if (photosError) {
    console.error('❌ Error fetching photos:', photosError);
  } else {
    console.log(`✅ Found ${photosData?.length || 0} photos for species ${speciesData.id}`);
    if (photosData && photosData.length > 0) {
      const publishedCount = photosData.filter(p => p.published).length;
      const unpublishedCount = photosData.filter(p => !p.published).length;
      console.log(`  - Published: ${publishedCount}`);
      console.log(`  - Unpublished: ${unpublishedCount}`);
    }
  }

  // Fetch fun facts from the dedicated table
  const funFactsData = await fetchSpeciesFunFacts(speciesId);

  return {
    ...speciesData,
    photos: photosData || [],
    fun_facts: funFactsData,
  };
};

const fetchSpeciesNeedingReview = async () => {
  const { data, error } = await supabase
    .from('species_needing_review')
    .select('*');

  if (error) {
    console.error('Error fetching species needing review:', error);
    throw new Error(error.message);
  }
  return data || [];
};

// Fetch ALL species for data quality analysis (no pagination)
const fetchAllSpeciesForAnalysis = async (): Promise<SpeciesListItem[]> => {

  const { data: speciesData, error: speciesError } = await supabase
    .from('species_v2')
    .select(`
      id,
      name,
      scientific_name,
      family,
      category,
      conservation_status,
      description,
      habitat,
      diet,
      behavior,
      size_cm,
      weight_g,
      lifespan_years,
      migration_pattern,
      breeding_season,
      threat_level,
      population_trend,
      featured,
      photo_count,
      tags,
      published,
      size_description,
      notes,
      ai_fun_facts,
      fun_facts_field,
      related_groups,
      conservation_actions,
      regions,
      common_group,
      taxonomy_order,
      taxonomy_subfamily,
      taxonomy_genus,
      created_at,
      updated_at
    `)
    .order('name'); // No pagination, no limits - get ALL species

  if (speciesError) {
    console.error('Error fetching all species for analysis:', speciesError);
    throw speciesError;
  }



  return (speciesData || []).map(species => ({
    ...species,
    photos: [], // We don't need photos for data quality analysis
    photo_count: species.photo_count || 0
  }));
};

// Main consolidated hook
export const useSpeciesData = (options: UseSpeciesDataOptions) => {
  const { mode, id, params = {}, enabled = true } = options;

  return useQuery({
    queryKey: ['species-data-v2', mode, id, params], // Updated key to force cache invalidation
    queryFn: async () => {
      switch (mode) {
        case 'list':
          return fetchSpeciesList(params);

        case 'with-photos':
          return fetchSpeciesWithPhotos();

        case 'detail':
        case 'by-id':
          if (!id) {
            throw new Error('ID is required for detail/by-id mode');
          }
          return fetchSpeciesDetail(id);

        case 'needing-review':
          return fetchSpeciesNeedingReview();

        case 'all-for-analysis':
          return fetchAllSpeciesForAnalysis();

        default:
          throw new Error(`Unknown mode: ${mode}`);
      }
    },
    enabled: enabled && (mode !== 'detail' && mode !== 'by-id' ? true : isValidUUID(id)),
    staleTime: 0, // Force fresh data after table fix
    gcTime: 5 * 60 * 1000, // 5 minutes - shorter cache for testing
    retry: 1, // Reduce retries for better performance
    refetchOnWindowFocus: true, // Enable refetch to get fresh data
    refetchOnMount: true, // Enable refetch on mount to get fresh data
    refetchInterval: false, // Disable automatic refetching
  });
};

// Convenience hooks for backward compatibility and ease of use
export const useSpeciesList = (params?: SpeciesQueryParams) => {
  return useSpeciesData({ mode: 'list', params });
};

export const useSpeciesWithPhotos = () => {
  return useSpeciesData({ mode: 'with-photos' });
};

export const useSpeciesDetail = (id: string | undefined) => {
  return useSpeciesData({
    mode: 'detail',
    id,
    enabled: !!id
  });
};

export const useSpeciesById = (id: string) => {
  return useSpeciesData({
    mode: 'by-id',
    id,
    enabled: !!id
  });
};

export const useSpeciesNeedingReview = () => {
  return useSpeciesData({ mode: 'needing-review' });
};
