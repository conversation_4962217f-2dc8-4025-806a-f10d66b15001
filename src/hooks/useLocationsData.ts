import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';

interface Location {
  id: string;
  name: string;
  description?: string;
  state_province?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
  published?: boolean;
  created_at?: string;
  updated_at?: string;
}

interface UseLocationsDataOptions {
  mode?: 'all-for-analysis' | 'published-only' | 'unpublished-only';
  limit?: number;
  page?: number;
}

export const useLocationsData = (options: UseLocationsDataOptions = {}) => {
  const { mode = 'published-only', limit, page } = options;

  return useQuery({
    queryKey: ['locations', mode, limit, page],
    queryFn: async () => {
      let query = supabase
        .from('locations')
        .select('*');

      // Apply filters based on mode
      if (mode === 'published-only') {
        query = query.eq('published', true);
      } else if (mode === 'unpublished-only') {
        query = query.eq('published', false);
      }
      // 'all-for-analysis' mode doesn't filter by published status

      // Apply pagination if specified
      if (limit && page) {
        const from = (page - 1) * limit;
        const to = from + limit - 1;
        query = query.range(from, to);
      }

      // Order by name
      query = query.order('name');

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching locations:', error);
        throw error;
      }

      return data as Location[];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

export type { Location };
