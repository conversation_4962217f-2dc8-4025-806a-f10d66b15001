import { useCallback, useMemo, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import type { AdminUser } from '@/utils/auth';
import { trackWildlifeEvent } from '@/services/analytics';

// Global state for admin authentication (for development)
let globalAdminState = false; // Start as public user - NEVER default to admin in production!
const listeners = new Set<() => void>();

function setGlobalAdminState(newState: boolean) {
  globalAdminState = newState;
  listeners.forEach(listener => listener());
}

function useGlobalAdminState() {
  const [state, setState] = useState(globalAdminState);

  useEffect(() => {
    const listener = () => setState(globalAdminState);
    listeners.add(listener);
    return () => listeners.delete(listener);
  }, []);

  return [state, setGlobalAdminState] as const;
}

/**
 * Provides a simplified authentication hook for development and testing.
 *
 * This hook allows toggling between admin and non-admin states to test
 * the UI behavior for both user types. Uses global state to sync across components.
 *
 * To re-enable full Supabase authentication:
 * 1. Locate the original `useAdminAuth` hook logic (e.g., in git history).
 * 2. Replace the content of this file with the original code.
 */
export function useAdminAuth() {
  const navigate = useNavigate();
  const [isAdminState, setIsAdminState] = useGlobalAdminState();

  // Create a mock admin user for local development
  const FAKE_ADMIN_USER: AdminUser = useMemo(() => ({
    id: 'local-admin',
    email: import.meta.env.VITE_ADMIN_EMAIL || '<EMAIL>',
    role: 'admin',
  }), []);

  const login = useCallback(() => {
    setIsAdminState(true);
    trackWildlifeEvent.adminLogin();
    navigate('/admin/dashboard');
  }, [navigate, setIsAdminState]);

  const logout = useCallback(() => {
    setIsAdminState(false);
    trackWildlifeEvent.adminLogout();
    navigate('/');
  }, [navigate, setIsAdminState]);

  // Return admin state based on current authentication status
  return useMemo(() => ({
    isAdmin: isAdminState,
    adminUser: isAdminState ? FAKE_ADMIN_USER : null,
    session: null,
    loading: false,
    ready: true,
    login,
    loginWithMagicLink: login,
    logout,
  }), [isAdminState, login, logout, FAKE_ADMIN_USER]);
}