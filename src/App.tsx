import { Toaster as Sonner } from "@/components/ui/sonner";
import { Toolt<PERSON>Provider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { HelmetProvider } from 'react-helmet-async';
import { BrowserRouter, Routes, Route, Link, useLocation } from "react-router-dom";
import { Suspense, lazy, useEffect } from "react";
import { Loader2, Shield, LogOut, Database, Camera, MapPin, Brain, BarChart3, Leaf, Users, Settings } from "lucide-react";
import { ActionIcons, CategoryIcons } from "@/components/ui/wildlife-icons";
import { ErrorBoundary } from "@/components/error-boundary";
import { RequireAdmin } from "@/components/RequireAdmin";
import { useAdminAuth } from "@/hooks/useAdminAuth";
import { Badge } from "@/components/ui/badge";
import { NavigationLink } from "@/components/ui/navigation-link";
import { MobileNavigation } from "@/components/ui/mobile-navigation";
import { AdminToggle } from "@/components/AdminToggle";
import { initializeAnalytics } from "@/services/analytics";
import { usePageTracking } from "@/hooks/useAnalytics";
import SpeciesDetail from "./pages/SpeciesDetail";
import SpeciesImport from "./pages/SpeciesImport";
import SpeciesPage from "./pages/SpeciesPage";
import SpeciesPhotoMatrix from "./pages/SpeciesPhotoMatrix";
import UUIDRouteGuard from "./components/UUIDRouteGuard";



// Lazy load components for code splitting
const Index = lazy(() => import("./pages/Index"));
const WildlifeExplorer = lazy(() => import("./pages/WildlifeExplorer"));
const PhotoGallery = lazy(() => import("./pages/PhotoGallery"));
const PhotoManager = lazy(() => import("./pages/PhotoManager"));
const NotFound = lazy(() => import("./pages/NotFound"));
const AdminSync = lazy(() => import("./pages/AdminSync"));
const AIWorkflow = lazy(() => import("./pages/AIWorkflow"));
const AIDashboard = lazy(() => import("./pages/AIDashboard"));
const PhotoAssignmentPage = lazy(() => import("./pages/PhotoAssignmentPage"));

const AdminLogin = lazy(() => import('./pages/AdminLogin'));
const AdminDashboard = lazy(() => import('./pages/AdminDashboard'));
const UnifiedCMS = lazy(() => import('./pages/UnifiedCMS'));
const PublicWildlifeSite = lazy(() => import('./pages/PublicWildlifeSite'));
const PublicWildlifeExplorer = lazy(() => import('./pages/public/PublicWildlifeExplorer'));
const PublicSpeciesDetailPage = lazy(() => import('./pages/public/PublicSpeciesDetailPage'));
const HotspotsExplorer = lazy(() => import('./pages/public/HotspotsExplorer'));
const HotspotDetail = lazy(() => import('./pages/public/HotspotDetail'));
const EnhancedLocationDetail = lazy(() => import('./components/hotspots/EnhancedLocationDetail'));

// Import SpeciesDataQualityDashboard for direct route
import { SpeciesDataQualityDashboard } from './components/SpeciesDataQualityDashboard';
const PhotoReviewPage = lazy(() => import('./pages/admin/PhotoReviewPage'));
const SpeciesEnrichment = lazy(() => import('./pages/admin/SpeciesEnrichment'));
const ObservationImport = lazy(() => import('./pages/admin/ObservationImport'));
const HotspotManagement = lazy(() => import('./pages/admin/HotspotManagement'));

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

// Loading component for Suspense fallback
const LoadingSpinner = () => (
  <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 flex items-center justify-center">
    <div className="text-center space-y-4">
      <div className="relative">
        <div className="w-16 h-16 border-4 border-green-200 rounded-full animate-spin border-t-green-600 mx-auto"></div>
        <CategoryIcons.Bird className="absolute inset-0 m-auto w-6 h-6 text-green-600" />
      </div>
      <div className="space-y-2">
        <p className="text-green-700 font-semibold text-lg">Loading Wildlife Explorer</p>
        <p className="text-green-600 text-sm">Discovering amazing species...</p>
      </div>
    </div>
  </div>
);

// Navigation component that uses useLocation
function Navigation() {
  const location = useLocation();
  const { isAdmin, adminUser, logout } = useAdminAuth();
  
  const handleLogout = async () => {
    await logout();
  };

  return (
    <nav className="flex items-center space-x-2 lg:space-x-4">
      <NavigationLink to="/" exactMatch className="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-green-50 transition-colors">
        <CategoryIcons.Bird size={18} />
        <span className="hidden sm:inline">Explore Wildlife</span>
      </NavigationLink>
      <NavigationLink to="/photos" className="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-blue-50 transition-colors">
        <ActionIcons.Camera size={18} />
        <span className="hidden sm:inline">Photos</span>
      </NavigationLink>
      <NavigationLink to="/hotspots" className="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-red-50 transition-colors">
        <ActionIcons.Location size={18} />
        <span className="hidden sm:inline">Hotspots</span>
      </NavigationLink>

      {/* Admin Links - Only show if user is admin */}
      {isAdmin && (
        <>
          <div className="h-6 w-px bg-gray-300 mx-2" />
          <NavigationLink to="/admin/cms" className="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors">
            <Database className="w-4 h-4" />
            <span className="hidden sm:inline">CMS Hub</span>
          </NavigationLink>
          <NavigationLink to="/admin/observation-import" className="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-blue-50 transition-colors">
            <Database className="w-4 h-4" />
            <span className="hidden sm:inline">Data Import</span>
          </NavigationLink>
          <NavigationLink to="/admin/dashboard" className="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-green-50 transition-colors">
            <BarChart3 className="w-4 h-4" />
            <span className="hidden sm:inline">System Monitor</span>
          </NavigationLink>
        </>
      )}
    </nav>
  );
}

// Layout component that includes navigation
function Layout({ children }: { children: React.ReactNode }) {
  const { isAdmin, adminUser, ready, loading, logout } = useAdminAuth();
  
  const handleLogout = async () => {
    await logout();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50/30 via-blue-50/30 to-purple-50/30">
      <header className="sticky top-0 z-50 w-full border-b border-green-200/50 bg-white/80 backdrop-blur-lg supports-[backdrop-filter]:bg-white/60 shadow-sm">
        <div className="container flex h-16 items-center">
          {/* Mobile Navigation */}
          <MobileNavigation />

          {/* Desktop Navigation */}
          <div className="mr-4 hidden md:flex">
            <a className="mr-6 flex items-center space-x-3" href="/">
              <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg">
                <CategoryIcons.Bird size={20} className="text-white" />
              </div>
              <span className="hidden font-bold text-xl bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent sm:inline-block">
                Fauna Focus
              </span>
            </a>
            <Navigation />
          </div>

          {/* Mobile Logo */}
          <div className="flex md:hidden flex-1 justify-center">
            <a className="flex items-center space-x-2" href="/">
              <div className="flex items-center justify-center w-7 h-7 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg">
                <CategoryIcons.Bird size={16} className="text-white" />
              </div>
              <span className="font-bold text-lg bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">Fauna Focus</span>
            </a>
          </div>
          <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
            <div className="w-full flex-1 md:w-auto md:flex-none">
            </div>
            <nav className="flex items-center space-x-3">
              {/* Status Indicators */}
              <div className="flex items-center gap-2">
                <Badge
                  variant={ready ? "default" : "secondary"}
                  className={`text-xs px-2 py-1 rounded-full ${ready ? 'bg-green-100 text-green-700 border-green-200' : 'bg-gray-100 text-gray-600'}`}
                >
                  {loading ? "Loading..." : ready ? "✓ Ready" : "Initializing"}
                </Badge>
                {isAdmin && (
                  <Badge variant="outline" className="text-xs px-2 py-1 rounded-full bg-blue-50 text-blue-700 border-blue-200">
                    <Shield className="w-3 h-3 mr-1" />
                    Admin
                  </Badge>
                )}
              </div>

              {isAdmin && adminUser && (
                <div className="flex items-center space-x-3 text-sm">
                  <span className="text-gray-600 hidden sm:inline">{adminUser.email}</span>
                  <button
                    onClick={handleLogout}
                    className="flex items-center space-x-1 px-3 py-1.5 text-xs bg-red-50 text-red-700 hover:bg-red-100 rounded-lg transition-colors border border-red-200"
                  >
                    <LogOut className="w-3 h-3" />
                    <span>Logout</span>
                  </button>
                </div>
              )}
            </nav>
          </div>
        </div>
      </header>
      <main className="container py-6">
        {children}
      </main>

      {/* Development Admin Toggle - Remove in production */}
      {import.meta.env.DEV && <AdminToggle />}
    </div>
  );
}

// Analytics wrapper component
const AnalyticsWrapper = ({ children }: { children: React.ReactNode }) => {
  usePageTracking();

  useEffect(() => {
    initializeAnalytics();
  }, []);

  return <>{children}</>;
};

const App = () => (
  <ErrorBoundary>
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Sonner />
          <BrowserRouter>
            <AnalyticsWrapper>
              <Layout>
            <Suspense fallback={<LoadingSpinner />}>
              <Routes>
                {/* Public Wildlife Routes */}
                <Route path="/" element={<PublicWildlifeExplorer />} />
                <Route path="/wildlife" element={<PublicWildlifeExplorer />} />
                <Route path="/wildlife/:id" element={<PublicSpeciesDetailPage />} />

                {/* Hotspots Routes */}
                <Route path="/hotspots" element={<HotspotsExplorer />} />
                <Route path="/hotspots/:id" element={<EnhancedLocationDetail />} />

                {/* Legacy Public Routes - Redirect to new structure */}
                <Route path="/explore" element={<WildlifeExplorer />} />
                <Route path="/species" element={<WildlifeExplorer />} />
                <Route path="/species/:id" element={<UUIDRouteGuard component={SpeciesDetail} />} />
                <Route path="/gallery" element={<PhotoGallery />} />
                <Route path="/photos" element={<PhotoManager />} />

                {/* Legacy Routes - Redirect to unified explorer */}
                <Route path="/legacy/home" element={<Index />} />
                <Route path="/legacy/explore" element={<PublicWildlifeSite />} />
                <Route path="/legacy/species" element={<SpeciesPage />} />
                <Route path="/ai-workflow" element={<AIWorkflow />} />
                <Route
                  path="/ai-dashboard"
                  element={
                    <RequireAdmin>
                      <AIDashboard />
                    </RequireAdmin>
                  }
                />
                
                {/* Admin Routes */}
                <Route path="/admin/login" element={<AdminLogin />} />
                <Route 
                  path="/admin/dashboard" 
                  element={
                    <RequireAdmin>
                      <AdminDashboard />
                    </RequireAdmin>
                  } 
                />
                <Route
                  path="/admin/species-import"
                  element={
                    <RequireAdmin>
                      <SpeciesImport />
                    </RequireAdmin>
                  }
                />
                <Route
                  path="/admin/observation-import"
                  element={
                    <RequireAdmin>
                      <ObservationImport />
                    </RequireAdmin>
                  }
                />
                <Route
                  path="/admin/hotspot-management"
                  element={
                    <RequireAdmin>
                      <HotspotManagement />
                    </RequireAdmin>
                  }
                />
                <Route 
                  path="/admin/photo-assignment" 
                  element={
                    <RequireAdmin>
                      <PhotoAssignmentPage />
                    </RequireAdmin>
                  } 
                />
                <Route 
                  path="/admin/photo-review" 
                  element={
                    <RequireAdmin>
                      <PhotoReviewPage />
                    </RequireAdmin>
                  } 
                />

                <Route
                  path="/admin/species-photo-matrix"
                  element={
                    <RequireAdmin>
                      <UnifiedCMS />
                    </RequireAdmin>
                  }
                />
                <Route
                  path="/admin/cms"
                  element={
                    <RequireAdmin>
                      <UnifiedCMS />
                    </RequireAdmin>
                  }
                />
                <Route 
                  path="/admin/sync" 
                  element={
                    <RequireAdmin>
                      <AdminSync />
                    </RequireAdmin>
                  } 
                />
                <Route
                  path="/admin/species-enrichment"
                  element={
                    <RequireAdmin>
                      <SpeciesEnrichment />
                    </RequireAdmin>
                  }
                />
                <Route
                  path="/admin/data-quality"
                  element={
                    <RequireAdmin>
                      <div className="container mx-auto">
                        <SpeciesDataQualityDashboard />
                      </div>
                    </RequireAdmin>
                  }
                />
                
                {/* Legacy Routes - Redirect to admin versions */}
                <Route path="/species-import" element={<SpeciesImport />} />
                <Route path="/photo-assignment" element={<PhotoAssignmentPage />} />

                <Route path="/species-photo-matrix" element={<SpeciesPhotoMatrix />} />
                <Route path="/admin-sync" element={<AdminSync />} />
                
                {/* Catch-all route */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </Suspense>
            </Layout>
            </AnalyticsWrapper>
          </BrowserRouter>
        </TooltipProvider>
      </QueryClientProvider>
    </HelmetProvider>
  </ErrorBoundary>
);

export default App;