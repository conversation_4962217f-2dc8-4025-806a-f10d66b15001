/**
 * Utility functions for detecting and managing duplicate photos
 */

export interface PhotoRecord {
  id: string;
  url: string;
  title?: string;
  created_at: string;
  file_size?: number;
  hash?: string;
}

/**
 * Extract filename from URL
 */
export function extractFilename(url: string): string | null {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const filename = pathname.split('/').pop();
    return filename && filename.length > 3 ? filename : null;
  } catch {
    return null;
  }
}

/**
 * Generate a simple hash from a string
 */
export function simpleHash(str: string): string {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}

/**
 * Detect potential duplicates by URL similarity
 */
export function findUrlDuplicates(photos: PhotoRecord[]): Map<string, PhotoRecord[]> {
  const groups = new Map<string, PhotoRecord[]>();
  
  photos.forEach(photo => {
    if (!photo.url) return;
    
    // Group by exact URL
    if (!groups.has(photo.url)) {
      groups.set(photo.url, []);
    }
    groups.get(photo.url)!.push(photo);
  });
  
  // Only return groups with duplicates
  const duplicates = new Map<string, PhotoRecord[]>();
  groups.forEach((photoList, url) => {
    if (photoList.length > 1) {
      duplicates.set(url, photoList);
    }
  });
  
  return duplicates;
}

/**
 * Detect potential duplicates by filename similarity
 */
export function findFilenameDuplicates(photos: PhotoRecord[]): Map<string, PhotoRecord[]> {
  const groups = new Map<string, PhotoRecord[]>();
  
  photos.forEach(photo => {
    if (!photo.url) return;
    
    const filename = extractFilename(photo.url);
    if (!filename) return;
    
    if (!groups.has(filename)) {
      groups.set(filename, []);
    }
    groups.get(filename)!.push(photo);
  });
  
  // Only return groups with duplicates
  const duplicates = new Map<string, PhotoRecord[]>();
  groups.forEach((photoList, filename) => {
    if (photoList.length > 1) {
      duplicates.set(filename, photoList);
    }
  });
  
  return duplicates;
}

/**
 * Detect potential duplicates by hash
 */
export function findHashDuplicates(photos: PhotoRecord[]): Map<string, PhotoRecord[]> {
  const groups = new Map<string, PhotoRecord[]>();
  
  photos.forEach(photo => {
    if (!photo.hash) return;
    
    if (!groups.has(photo.hash)) {
      groups.set(photo.hash, []);
    }
    groups.get(photo.hash)!.push(photo);
  });
  
  // Only return groups with duplicates
  const duplicates = new Map<string, PhotoRecord[]>();
  groups.forEach((photoList, hash) => {
    if (photoList.length > 1) {
      duplicates.set(hash, photoList);
    }
  });
  
  return duplicates;
}

/**
 * Score a photo based on metadata completeness
 */
export function scorePhotoQuality(photo: any): number {
  let score = 0;
  
  // Basic metadata
  if (photo.title && photo.title.trim().length > 0) score += 2;
  if (photo.description && photo.description.trim().length > 10) score += 2;
  if (photo.photographer && photo.photographer.trim().length > 0) score += 1;
  if (photo.location && photo.location.trim().length > 0) score += 1;
  
  // Species assignment
  if (photo.species_id) score += 3;
  
  // Publication status
  if (photo.published) score += 1;
  
  // File quality indicators
  if (photo.file_size && photo.file_size > 100000) score += 1; // Larger files often better quality
  
  // Date information
  if (photo.created_at) score += 1;
  
  return score;
}

/**
 * Recommend which photo to keep from a duplicate group
 */
export function recommendPhotoToKeep(photos: PhotoRecord[]): PhotoRecord {
  if (photos.length === 0) throw new Error('No photos provided');
  if (photos.length === 1) return photos[0];
  
  // Score each photo
  const scoredPhotos = photos.map(photo => ({
    photo,
    score: scorePhotoQuality(photo)
  }));
  
  // Sort by score (highest first), then by creation date (oldest first)
  scoredPhotos.sort((a, b) => {
    if (a.score !== b.score) {
      return b.score - a.score; // Higher score first
    }
    // If scores are equal, prefer older photo
    return new Date(a.photo.created_at).getTime() - new Date(b.photo.created_at).getTime();
  });
  
  return scoredPhotos[0].photo;
}

/**
 * Get photos to delete from a duplicate group
 */
export function getPhotosToDelete(photos: PhotoRecord[]): PhotoRecord[] {
  if (photos.length <= 1) return [];
  
  const photoToKeep = recommendPhotoToKeep(photos);
  return photos.filter(photo => photo.id !== photoToKeep.id);
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Calculate potential storage savings from removing duplicates
 */
export function calculateStorageSavings(duplicateGroups: Map<string, PhotoRecord[]>): {
  totalDuplicates: number;
  estimatedSavings: number;
  averageFileSize: number;
} {
  let totalDuplicates = 0;
  let totalSize = 0;
  let totalFiles = 0;
  
  duplicateGroups.forEach(photos => {
    const duplicatesInGroup = photos.length - 1; // Keep one, remove others
    totalDuplicates += duplicatesInGroup;
    
    photos.forEach(photo => {
      if (photo.file_size) {
        totalSize += photo.file_size;
        totalFiles++;
      }
    });
  });
  
  const averageFileSize = totalFiles > 0 ? totalSize / totalFiles : 0;
  const estimatedSavings = totalDuplicates * averageFileSize;
  
  return {
    totalDuplicates,
    estimatedSavings,
    averageFileSize
  };
}

/**
 * Validate that a photo URL is accessible
 */
export async function validatePhotoUrl(url: string): Promise<boolean> {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
}

/**
 * Find broken photo URLs
 */
export async function findBrokenPhotos(photos: PhotoRecord[]): Promise<PhotoRecord[]> {
  const brokenPhotos: PhotoRecord[] = [];
  
  // Check URLs in batches to avoid overwhelming the server
  const batchSize = 10;
  for (let i = 0; i < photos.length; i += batchSize) {
    const batch = photos.slice(i, i + batchSize);
    
    const promises = batch.map(async (photo) => {
      if (!photo.url) return { photo, isBroken: true };
      
      const isValid = await validatePhotoUrl(photo.url);
      return { photo, isBroken: !isValid };
    });
    
    const results = await Promise.all(promises);
    results.forEach(({ photo, isBroken }) => {
      if (isBroken) {
        brokenPhotos.push(photo);
      }
    });
    
    // Small delay between batches
    if (i + batchSize < photos.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  return brokenPhotos;
}

/**
 * Generate a report of duplicate analysis
 */
export function generateDuplicateReport(
  urlDuplicates: Map<string, PhotoRecord[]>,
  filenameDuplicates: Map<string, PhotoRecord[]>,
  hashDuplicates: Map<string, PhotoRecord[]>
): {
  summary: string;
  details: {
    urlDuplicates: number;
    filenameDuplicates: number;
    hashDuplicates: number;
    totalUniqueGroups: number;
    recommendedDeletions: number;
  };
} {
  const urlGroups = urlDuplicates.size;
  const filenameGroups = filenameDuplicates.size;
  const hashGroups = hashDuplicates.size;
  
  // Calculate total unique groups (avoiding double counting)
  const allPhotoIds = new Set<string>();
  [urlDuplicates, filenameDuplicates, hashDuplicates].forEach(duplicateMap => {
    duplicateMap.forEach(photos => {
      photos.forEach(photo => allPhotoIds.add(photo.id));
    });
  });
  
  let recommendedDeletions = 0;
  [urlDuplicates, filenameDuplicates, hashDuplicates].forEach(duplicateMap => {
    duplicateMap.forEach(photos => {
      recommendedDeletions += photos.length - 1; // Keep one, delete others
    });
  });
  
  const totalUniqueGroups = urlGroups + filenameGroups + hashGroups;
  
  const summary = `Found ${totalUniqueGroups} duplicate groups affecting ${allPhotoIds.size} photos. ` +
    `Recommended to delete ${recommendedDeletions} duplicate photos to clean up the database.`;
  
  return {
    summary,
    details: {
      urlDuplicates: urlGroups,
      filenameDuplicates: filenameGroups,
      hashDuplicates: hashGroups,
      totalUniqueGroups,
      recommendedDeletions
    }
  };
}
