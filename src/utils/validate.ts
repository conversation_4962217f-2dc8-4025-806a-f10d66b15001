/**
 * Validates if a given string is a UUID (versions 1-5).
 * @param str The string to validate.
 * @returns True if the string is a valid UUID, false otherwise.
 */
export const isValidUUID = (str: string | undefined | null): boolean => {
  if (!str) return false;
  // This regex validates UUID versions 1-5 and is case-insensitive
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
} 