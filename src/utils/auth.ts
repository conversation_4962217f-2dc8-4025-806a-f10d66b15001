import { supabase } from '@/integrations/supabase/client';
import type { Session } from '@supabase/supabase-js';

export interface AdminUser {
  id: string;
  email: string;
  role: 'admin';
}

/**
 * Check if the current user is an admin
 */
export async function isAdminUser(): Promise<boolean> {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    const user = session?.user;
    
    if (!user?.email) {
      console.log('No user email found in session');
      return false;
    }

    // Check against admin email from environment
    const adminEmail = import.meta.env.VITE_ADMIN_EMAIL;
    console.log('Checking admin status:', { userEmail: user.email, adminEmail, isMatch: user.email === adminEmail });
    return user.email === adminEmail;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

/**
 * Get current admin user info
 */
export async function getCurrentAdminUser(): Promise<AdminUser | null> {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    const user = session?.user;
    
    if (!user?.email) {
      console.log('No user email found in session');
      return null;
    }

    const adminEmail = import.meta.env.VITE_ADMIN_EMAIL;
    console.log('Getting admin user:', { userEmail: user.email, adminEmail, isMatch: user.email === adminEmail });
    if (user.email !== adminEmail) {
      return null;
    }

    return {
      id: user.id,
      email: user.email,
      role: 'admin' as const
    };
  } catch (error) {
    console.error('Error getting admin user:', error);
    return null;
  }
}

/**
 * Sign in with email and password
 */
export async function signInWithPassword(email: string, password: string) {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  });

  if (error) {
    throw error;
  }

  return data;
}

/**
 * Sign in with magic link
 */
export async function signInWithMagicLink(email: string) {
  const { data, error } = await supabase.auth.signInWithOtp({
    email,
    options: {
      emailRedirectTo: `${window.location.origin}/admin/dashboard`
    }
  });

  if (error) {
    console.error('Magic link error:', error);
    throw error;
  }

  console.log('Magic link sent successfully');
  return data;
}

/**
 * Sign out current user
 */
export async function signOut() {
  const { error } = await supabase.auth.signOut();
  
  if (error) {
    throw error;
  }
}

/**
 * Get current session
 */
export async function getCurrentSession() {
  const { data: { session } } = await supabase.auth.getSession();
  return session;
}

/**
 * Subscribe to auth state changes
 */
export function onAuthStateChange(callback: (session: Session | null) => void) {
  return supabase.auth.onAuthStateChange((event, session) => {
    callback(session);
  });
} 