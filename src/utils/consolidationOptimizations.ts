/**
 * Performance optimizations for consolidated navigation components
 */

import { useMemo, useCallback, useRef, useEffect } from 'react';

// Cache for frequently accessed data
const dataCache = new Map<string, { data: any; timestamp: number; ttl: number }>();

/**
 * Enhanced caching utility with TTL support
 */
export function useDataCache<T>(
  key: string, 
  fetcher: () => Promise<T>, 
  ttl: number = 5 * 60 * 1000 // 5 minutes default
) {
  const getCachedData = useCallback(() => {
    const cached = dataCache.get(key);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }
    return null;
  }, [key]);

  const setCachedData = useCallback((data: T) => {
    dataCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }, [key, ttl]);

  return { getCachedData, setCachedData };
}

/**
 * Optimized filter hook for large datasets
 */
export function useOptimizedFilter<T>(
  data: T[],
  filterFn: (item: T) => boolean,
  dependencies: any[] = []
) {
  return useMemo(() => {
    if (!data || data.length === 0) return [];
    
    // Use requestIdleCallback for large datasets
    if (data.length > 1000) {
      return data.filter(filterFn);
    }
    
    return data.filter(filterFn);
  }, [data, ...dependencies]);
}

/**
 * Debounced search with performance monitoring
 */
export function usePerformantSearch(
  searchTerm: string,
  delay: number = 300
) {
  const [debouncedTerm, setDebouncedTerm] = useState(searchTerm);
  const searchStartTime = useRef<number>(0);

  useEffect(() => {
    searchStartTime.current = performance.now();
    
    const timer = setTimeout(() => {
      setDebouncedTerm(searchTerm);
      
      // Log search performance
      const searchTime = performance.now() - searchStartTime.current;
      if (searchTime > 100) {
        console.warn(`Slow search detected: ${searchTime}ms for "${searchTerm}"`);
      }
    }, delay);

    return () => clearTimeout(timer);
  }, [searchTerm, delay]);

  return debouncedTerm;
}

/**
 * Virtual scrolling for large lists
 */
export function useVirtualScrolling<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number
) {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );
    
    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight
    };
  }, [items, itemHeight, containerHeight, scrollTop]);

  return {
    visibleItems,
    setScrollTop,
    totalHeight: visibleItems.totalHeight
  };
}

/**
 * Intersection Observer for lazy loading
 */
export function useLazyLoading(
  threshold: number = 0.1
) {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          // Disconnect after first intersection for one-time loading
          observer.disconnect();
        }
      },
      { threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [threshold]);

  return { ref, isVisible };
}

/**
 * Memory usage monitoring
 */
export function useMemoryMonitor(componentName: string) {
  useEffect(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const memoryInfo = {
        used: Math.round(memory.usedJSHeapSize / 1048576), // MB
        total: Math.round(memory.totalJSHeapSize / 1048576), // MB
        limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
      };
      
      console.log(`[${componentName}] Memory usage:`, memoryInfo);
      
      // Warn if memory usage is high
      if (memoryInfo.used > memoryInfo.limit * 0.8) {
        console.warn(`[${componentName}] High memory usage detected: ${memoryInfo.used}MB`);
      }
    }
  }, [componentName]);
}

/**
 * Bundle size optimization utilities
 */
export const optimizationUtils = {
  // Lazy load heavy components
  lazyLoad: <T extends React.ComponentType<any>>(
    importFn: () => Promise<{ default: T }>
  ) => {
    return React.lazy(importFn);
  },

  // Preload critical resources
  preloadResource: (url: string, type: 'script' | 'style' | 'image' = 'script') => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = url;
    link.as = type;
    document.head.appendChild(link);
  },

  // Optimize images
  optimizeImageUrl: (url: string, width?: number, height?: number, quality: number = 80) => {
    if (!url) return url;
    
    // Add optimization parameters if supported
    const params = new URLSearchParams();
    if (width) params.set('w', width.toString());
    if (height) params.set('h', height.toString());
    params.set('q', quality.toString());
    
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}${params.toString()}`;
  },

  // Debounce expensive operations
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let timeoutId: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  },

  // Throttle high-frequency events
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
};

/**
 * Performance metrics collection
 */
export class PerformanceCollector {
  private static metrics: Map<string, number[]> = new Map();

  static recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name)!.push(value);
  }

  static getAverageMetric(name: string): number {
    const values = this.metrics.get(name) || [];
    return values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0;
  }

  static getMetricSummary(): Record<string, { avg: number; count: number; latest: number }> {
    const summary: Record<string, { avg: number; count: number; latest: number }> = {};
    
    this.metrics.forEach((values, name) => {
      summary[name] = {
        avg: this.getAverageMetric(name),
        count: values.length,
        latest: values[values.length - 1] || 0
      };
    });
    
    return summary;
  }

  static clearMetrics() {
    this.metrics.clear();
  }
}

/**
 * Component performance wrapper
 */
export function withPerformanceTracking<P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
) {
  return React.memo((props: P) => {
    const renderStart = useRef<number>(0);
    
    useEffect(() => {
      renderStart.current = performance.now();
    });

    useEffect(() => {
      const renderTime = performance.now() - renderStart.current;
      PerformanceCollector.recordMetric(`${componentName}_render_time`, renderTime);
      
      if (renderTime > 16) { // 60fps threshold
        console.warn(`[${componentName}] Slow render: ${renderTime.toFixed(2)}ms`);
      }
    });

    return <Component {...props} />;
  });
}

// Export React import for the lazy loading utility
import React, { useState, useEffect } from 'react';
