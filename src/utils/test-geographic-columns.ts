import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseAnonKey);

export async function testGeographicColumns() {
  console.log('🔍 Testing if geographic columns exist...');

  try {
    // Try to select geographic columns from species_v2
    const { data, error } = await supabase
      .from('species_v2')
      .select('id, name, countries, states_provinces, primary_region, geographic_scope')
      .limit(1);

    if (error) {
      console.error('❌ Geographic columns do not exist:', error.message);
      return { exists: false, error: error.message };
    }

    console.log('✅ Geographic columns exist! Sample data:', data);
    return { exists: true, data };

  } catch (error) {
    console.error('❌ Error testing geographic columns:', error);
    return { exists: false, error: error.message };
  }
}

// Add this to window for easy testing
if (typeof window !== 'undefined') {
  (window as any).testGeographicColumns = testGeographicColumns;
}
