import { supabase } from "@/integrations/supabase/client";

export interface DataCleanupResult {
  success: boolean;
  message: string;
  affectedRecords: number;
  errors?: string[];
}

/**
 * Fix malformed conservation_actions JSON data
 */
export const fixConservationActionsData = async (): Promise<DataCleanupResult> => {
  try {
    // Get all species with malformed conservation_actions
    const { data: species, error: fetchError } = await supabase
      .from('species_v2')
      .select('id, name, conservation_actions')
      .not('conservation_actions', 'is', null);

    if (fetchError) {
      return {
        success: false,
        message: 'Failed to fetch species data',
        affectedRecords: 0,
        errors: [fetchError.message]
      };
    }

    if (!species || species.length === 0) {
      return {
        success: true,
        message: 'No species with conservation actions found',
        affectedRecords: 0
      };
    }

    const updates = [];
    const errors = [];

    for (const s of species) {
      try {
        // Check if conservation_actions contains malformed JSON strings
        if (typeof s.conservation_actions === 'string' && 
            s.conservation_actions.includes('{"state"')) {
          // This is malformed data, set to null
          updates.push({
            id: s.id,
            conservation_actions: null
          });
        } else if (typeof s.conservation_actions === 'object' && 
                   s.conservation_actions !== null &&
                   'state' in s.conservation_actions &&
                   s.conservation_actions.state === 'empty') {
          // This is also malformed data from the reactive state
          updates.push({
            id: s.id,
            conservation_actions: null
          });
        }
      } catch (error) {
        errors.push(`Error processing species ${s.name}: ${error}`);
      }
    }

    if (updates.length === 0) {
      return {
        success: true,
        message: 'No malformed conservation actions found',
        affectedRecords: 0
      };
    }

    // Batch update the records
    const { error: updateError } = await supabase
      .from('species_v2')
      .upsert(updates);

    if (updateError) {
      return {
        success: false,
        message: 'Failed to update conservation actions',
        affectedRecords: 0,
        errors: [updateError.message, ...errors]
      };
    }

    return {
      success: true,
      message: `Successfully cleaned conservation actions for ${updates.length} species`,
      affectedRecords: updates.length,
      errors: errors.length > 0 ? errors : undefined
    };

  } catch (error) {
    return {
      success: false,
      message: 'Unexpected error during cleanup',
      affectedRecords: 0,
      errors: [String(error)]
    };
  }
};

/**
 * Standardize habitat data to predefined categories
 */
export const standardizeHabitatData = async (): Promise<DataCleanupResult> => {
  try {
    const { data: species, error: fetchError } = await supabase
      .from('species_v2')
      .select('id, name, habitat')
      .not('habitat', 'is', null);

    if (fetchError) {
      return {
        success: false,
        message: 'Failed to fetch species data',
        affectedRecords: 0,
        errors: [fetchError.message]
      };
    }

    if (!species || species.length === 0) {
      return {
        success: true,
        message: 'No species with habitat data found',
        affectedRecords: 0
      };
    }

    const habitatMapping: Record<string, string> = {
      'varied': 'Multiple habitat types',
      'information not available': null,
      'varied, regions: north america': 'North American varied habitats',
      'varied diet': null, // This seems to be in wrong field
    };

    const updates = [];
    let affectedCount = 0;

    for (const s of species) {
      if (s.habitat && typeof s.habitat === 'string') {
        const lowerHabitat = s.habitat.toLowerCase();
        
        if (habitatMapping.hasOwnProperty(lowerHabitat)) {
          updates.push({
            id: s.id,
            habitat: habitatMapping[lowerHabitat]
          });
          affectedCount++;
        }
      }
    }

    if (updates.length > 0) {
      const { error: updateError } = await supabase
        .from('species_v2')
        .upsert(updates);

      if (updateError) {
        return {
          success: false,
          message: 'Failed to update habitat data',
          affectedRecords: 0,
          errors: [updateError.message]
        };
      }
    }

    return {
      success: true,
      message: `Successfully standardized habitat data for ${affectedCount} species`,
      affectedRecords: affectedCount
    };

  } catch (error) {
    return {
      success: false,
      message: 'Unexpected error during habitat standardization',
      affectedRecords: 0,
      errors: [String(error)]
    };
  }
};

/**
 * Clean up diet data - remove generic entries
 */
export const cleanDietData = async (): Promise<DataCleanupResult> => {
  try {
    const { data: species, error: fetchError } = await supabase
      .from('species_v2')
      .select('id, name, diet')
      .not('diet', 'is', null);

    if (fetchError) {
      return {
        success: false,
        message: 'Failed to fetch species data',
        affectedRecords: 0,
        errors: [fetchError.message]
      };
    }

    if (!species || species.length === 0) {
      return {
        success: true,
        message: 'No species with diet data found',
        affectedRecords: 0
      };
    }

    const genericDietTerms = [
      'varied diet',
      'information not available',
      'varied'
    ];

    const updates = [];
    let affectedCount = 0;

    for (const s of species) {
      if (s.diet && typeof s.diet === 'string') {
        const lowerDiet = s.diet.toLowerCase();
        
        if (genericDietTerms.includes(lowerDiet)) {
          updates.push({
            id: s.id,
            diet: null
          });
          affectedCount++;
        }
      }
    }

    if (updates.length > 0) {
      const { error: updateError } = await supabase
        .from('species_v2')
        .upsert(updates);

      if (updateError) {
        return {
          success: false,
          message: 'Failed to update diet data',
          affectedRecords: 0,
          errors: [updateError.message]
        };
      }
    }

    return {
      success: true,
      message: `Successfully cleaned diet data for ${affectedCount} species`,
      affectedRecords: affectedCount
    };

  } catch (error) {
    return {
      success: false,
      message: 'Unexpected error during diet cleanup',
      affectedRecords: 0,
      errors: [String(error)]
    };
  }
};

/**
 * Remove "Information not available" entries from regions
 */
export const cleanRegionsData = async (): Promise<DataCleanupResult> => {
  try {
    const { data: species, error: fetchError } = await supabase
      .from('species_v2')
      .select('id, name, regions')
      .eq('regions', 'Information not available');

    if (fetchError) {
      return {
        success: false,
        message: 'Failed to fetch species data',
        affectedRecords: 0,
        errors: [fetchError.message]
      };
    }

    if (!species || species.length === 0) {
      return {
        success: true,
        message: 'No species with "Information not available" regions found',
        affectedRecords: 0
      };
    }

    const updates = species.map(s => ({
      id: s.id,
      regions: null
    }));

    const { error: updateError } = await supabase
      .from('species_v2')
      .upsert(updates);

    if (updateError) {
      return {
        success: false,
        message: 'Failed to update regions data',
        affectedRecords: 0,
        errors: [updateError.message]
      };
    }

    return {
      success: true,
      message: `Successfully cleaned regions data for ${species.length} species`,
      affectedRecords: species.length
    };

  } catch (error) {
    return {
      success: false,
      message: 'Unexpected error during regions cleanup',
      affectedRecords: 0,
      errors: [String(error)]
    };
  }
};

/**
 * Run all data cleanup operations
 */
export const runFullDataCleanup = async (): Promise<DataCleanupResult[]> => {
  const results = [];
  
  results.push(await fixConservationActionsData());
  results.push(await standardizeHabitatData());
  results.push(await cleanDietData());
  results.push(await cleanRegionsData());
  
  return results;
};
