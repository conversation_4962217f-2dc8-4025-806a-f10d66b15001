/**
 * Data validation utilities for the Species Photo Matrix
 */

import { SpeciesPhotoMatrixData, SpeciesWithPhotos, Species, Photo } from './speciesPhotoMatrix';

/**
 * Validates that the species photo matrix data has the correct structure
 */
export function validateSpeciesPhotoMatrixData(data: any): data is SpeciesPhotoMatrixData {
  if (!data || typeof data !== 'object') {
    console.error('Data validation failed: data is not an object', data);
    return false;
  }

  if (!Array.isArray(data.species)) {
    console.error('Data validation failed: species is not an array', data.species);
    return false;
  }

  if (!Array.isArray(data.unassigned_photos)) {
    console.error('Data validation failed: unassigned_photos is not an array', data.unassigned_photos);
    return false;
  }

  // Validate each species entry
  for (let i = 0; i < data.species.length; i++) {
    const speciesWithPhotos = data.species[i];
    if (!validateSpeciesWithPhotos(speciesWithPhotos)) {
      console.error(`Data validation failed: species[${i}] is invalid`, speciesWithPhotos);
      return false;
    }
  }

  // Validate each unassigned photo
  for (let i = 0; i < data.unassigned_photos.length; i++) {
    const photo = data.unassigned_photos[i];
    if (!validatePhoto(photo)) {
      console.error(`Data validation failed: unassigned_photos[${i}] is invalid`, photo);
      return false;
    }
  }

  return true;
}

/**
 * Validates a SpeciesWithPhotos object
 */
export function validateSpeciesWithPhotos(data: any): data is SpeciesWithPhotos {
  if (!data || typeof data !== 'object') {
    return false;
  }

  if (!validateSpecies(data.species)) {
    return false;
  }

  if (!Array.isArray(data.photos)) {
    return false;
  }

  // Validate each photo
  for (const photo of data.photos) {
    if (!validatePhoto(photo)) {
      return false;
    }
  }

  return true;
}

/**
 * Validates a Species object
 */
export function validateSpecies(data: any): data is Species {
  if (!data || typeof data !== 'object') {
    return false;
  }

  // Required fields
  if (typeof data.id !== 'string' || !data.id) {
    return false;
  }

  if (typeof data.name !== 'string' || !data.name) {
    return false;
  }

  // Optional fields should have correct types if present
  if (data.scientific_name !== null && typeof data.scientific_name !== 'string') {
    return false;
  }

  if (data.common_name !== null && typeof data.common_name !== 'string') {
    return false;
  }

  if (data.category !== null && typeof data.category !== 'string') {
    return false;
  }

  if (typeof data.published !== 'boolean') {
    return false;
  }

  return true;
}

/**
 * Validates a Photo object
 */
export function validatePhoto(data: any): data is Photo {
  if (!data || typeof data !== 'object') {
    return false;
  }

  // Required fields
  if (typeof data.id !== 'number' && typeof data.id !== 'string') {
    return false;
  }

  // Optional fields should have correct types if present
  if (data.species_id !== null && typeof data.species_id !== 'string') {
    return false;
  }

  if (data.url !== null && typeof data.url !== 'string') {
    return false;
  }

  if (typeof data.published !== 'boolean') {
    return false;
  }

  return true;
}

/**
 * Sanitizes and repairs species photo matrix data
 */
export function sanitizeSpeciesPhotoMatrixData(data: any): SpeciesPhotoMatrixData {
  const sanitized: SpeciesPhotoMatrixData = {
    species: [],
    unassigned_photos: [],
    totalPhotos: 0,
    publishedPhotos: 0,
    unpublishedPhotos: 0
  };

  // Sanitize species array
  if (Array.isArray(data?.species)) {
    sanitized.species = data.species
      .filter((item: any) => item && typeof item === 'object')
      .map((item: any) => sanitizeSpeciesWithPhotos(item))
      .filter((item: SpeciesWithPhotos | null) => item !== null) as SpeciesWithPhotos[];
  }

  // Sanitize unassigned photos array
  if (Array.isArray(data?.unassigned_photos)) {
    sanitized.unassigned_photos = data.unassigned_photos
      .filter((item: any) => item && typeof item === 'object')
      .map((item: any) => sanitizePhoto(item))
      .filter((item: Photo | null) => item !== null) as Photo[];
  }

  // Calculate totals
  const allPhotos = [
    ...sanitized.species.flatMap(s => s.photos),
    ...sanitized.unassigned_photos
  ];
  
  sanitized.totalPhotos = allPhotos.length;
  sanitized.publishedPhotos = allPhotos.filter(p => p.published).length;
  sanitized.unpublishedPhotos = sanitized.totalPhotos - sanitized.publishedPhotos;

  return sanitized;
}

/**
 * Sanitizes a SpeciesWithPhotos object
 */
export function sanitizeSpeciesWithPhotos(data: any): SpeciesWithPhotos | null {
  if (!data || typeof data !== 'object') {
    return null;
  }

  const species = sanitizeSpecies(data.species);
  if (!species) {
    return null;
  }

  const photos = Array.isArray(data.photos) 
    ? data.photos
        .map((photo: any) => sanitizePhoto(photo))
        .filter((photo: Photo | null) => photo !== null) as Photo[]
    : [];

  return {
    species,
    photos
  };
}

/**
 * Sanitizes a Species object
 */
export function sanitizeSpecies(data: any): Species | null {
  if (!data || typeof data !== 'object') {
    return null;
  }

  // Required fields
  if (typeof data.id !== 'string' || !data.id) {
    return null;
  }

  if (typeof data.name !== 'string' || !data.name) {
    return null;
  }

  return {
    id: data.id,
    name: data.name,
    scientific_name: typeof data.scientific_name === 'string' ? data.scientific_name : null,
    common_name: typeof data.common_name === 'string' ? data.common_name : null,
    category: typeof data.category === 'string' ? data.category : null,
    conservation_status: typeof data.conservation_status === 'string' ? data.conservation_status : null,
    description: typeof data.description === 'string' ? data.description : null,
    habitat: typeof data.habitat === 'string' ? data.habitat : null,
    diet: typeof data.diet === 'string' ? data.diet : null,
    behavior: typeof data.behavior === 'string' ? data.behavior : null,
    published: typeof data.published === 'boolean' ? data.published : false,
    photo_count: typeof data.photo_count === 'number' ? data.photo_count : 0,
    created_at: typeof data.created_at === 'string' ? data.created_at : null,
    updated_at: typeof data.updated_at === 'string' ? data.updated_at : null
  };
}

/**
 * Sanitizes a Photo object
 */
export function sanitizePhoto(data: any): Photo | null {
  if (!data || typeof data !== 'object') {
    return null;
  }

  // Required fields
  if (typeof data.id !== 'number' && typeof data.id !== 'string') {
    return null;
  }

  return {
    id: data.id,
    species_id: typeof data.species_id === 'string' ? data.species_id : null,
    url: typeof data.url === 'string' ? data.url : null,
    title: typeof data.title === 'string' ? data.title : null,
    description: typeof data.description === 'string' ? data.description : null,
    location: typeof data.location === 'string' ? data.location : null,
    date_taken: typeof data.date_taken === 'string' ? data.date_taken : null,
    photographer: typeof data.photographer === 'string' ? data.photographer : null,
    published: typeof data.published === 'boolean' ? data.published : false,
    ai_generated: typeof data.ai_generated === 'boolean' ? data.ai_generated : false,
    original_filename: typeof data.original_filename === 'string' ? data.original_filename : null,
    camera_make: typeof data.camera_make === 'string' ? data.camera_make : null,
    camera_model: typeof data.camera_model === 'string' ? data.camera_model : null,
    lens: typeof data.lens === 'string' ? data.lens : null,
    focal_length: typeof data.focal_length === 'string' ? data.focal_length : null,
    aperture: typeof data.aperture === 'string' ? data.aperture : null,
    shutter_speed: typeof data.shutter_speed === 'string' ? data.shutter_speed : null,
    iso: typeof data.iso === 'string' ? data.iso : null,
    exposure_mode: typeof data.exposure_mode === 'string' ? data.exposure_mode : null,
    white_balance: typeof data.white_balance === 'string' ? data.white_balance : null,
    metering_mode: typeof data.metering_mode === 'string' ? data.metering_mode : null,
    flash_used: typeof data.flash_used === 'boolean' ? data.flash_used : false,
    color_profile: typeof data.color_profile === 'string' ? data.color_profile : null,
    ai_assigned_species_id: typeof data.ai_assigned_species_id === 'string' ? data.ai_assigned_species_id : null,
    ai_confidence: typeof data.ai_confidence === 'number' ? data.ai_confidence : null,
    ai_model: typeof data.ai_model === 'string' ? data.ai_model : null,
    is_orphaned: typeof data.is_orphaned === 'boolean' ? data.is_orphaned : false,
    is_duplicate: typeof data.is_duplicate === 'boolean' ? data.is_duplicate : false,
    image_hash: typeof data.image_hash === 'string' ? data.image_hash : null,
    metadata: data.metadata && typeof data.metadata === 'object' ? data.metadata : null,
    created_at: typeof data.created_at === 'string' ? data.created_at : null,
    updated_at: typeof data.updated_at === 'string' ? data.updated_at : null
  };
}
