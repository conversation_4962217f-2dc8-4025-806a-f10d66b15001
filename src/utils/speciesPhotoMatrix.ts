import { supabase } from '@/integrations/supabase/client';
import type { Json, Tables } from '@/integrations/supabase/types';

export type Species = Tables<'species_v2'>;
export type Photo = Tables<'photos_v2'> & {
  // Add all possible extra fields for advanced view
  camera_make?: string | null;
  camera_model?: string | null;
  lens?: string | null;
  focal_length?: string | null;
  aperture?: string | null;
  shutter_speed?: string | null;
  iso?: string | null;
  exposure_mode?: string | null;
  white_balance?: string | null;
  metering_mode?: string | null;
  flash_used?: boolean | null;
  color_profile?: string | null;
  ai_assigned_species_id?: string | null;
  ai_confidence?: number | null;
  ai_model?: string | null;
  is_orphaned?: boolean | null;
  is_duplicate?: boolean | null;
  image_hash?: string | null;
  metadata?: Record<string, unknown> | null;
  original_filename?: string | null;
  date_taken?: string | null;
};

export interface SpeciesWithPhotos {
  species: Species;
  photos: Photo[];
}

export interface SpeciesPhotoMatrixData {
  species: SpeciesWithPhotos[];
  unassigned_photos: Photo[];
  totalSpecies: number;
  totalPhotos: number;
  publishedPhotos: number;
  unpublishedPhotos: number;
}

/**
 * Fetch all species with their related photos using an RPC call
 */
export async function getSpeciesWithAllPhotoData(): Promise<SpeciesPhotoMatrixData> {
  try {
    // @ts-ignore - The RPC function is not yet in the generated types
    const { data, error } = await supabase.rpc('get_species_photo_matrix');

    if (error) {
      console.error('Error calling get_species_photo_matrix RPC:', error);
      throw error;
    }
    
    // The RPC function returns a single JSONB object.
    const matrixData = data as { species: SpeciesWithPhotos[], unassigned_photos: Photo[] };

    const speciesWithPhotos: SpeciesWithPhotos[] = matrixData.species || [];
    const unassignedPhotos: Photo[] = matrixData.unassigned_photos || [];
    
    // Recalculate stats from the returned data
    const allPhotos = [...speciesWithPhotos.flatMap(s => s.photos), ...unassignedPhotos];
    const totalPhotos = allPhotos.length;
    const publishedPhotos = allPhotos.filter(p => p.published).length;
    const unpublishedPhotos = totalPhotos - publishedPhotos;

    return {
      species: speciesWithPhotos,
      unassigned_photos: unassignedPhotos,
      totalSpecies: speciesWithPhotos.length,
      totalPhotos,
      publishedPhotos,
      unpublishedPhotos,
    };
  } catch (error) {
    console.error('Error fetching species with photo data:', error);
    throw error;
  }
}

/**
 * Filter species and photos based on search criteria
 */
export function filterSpeciesPhotoMatrix(
  data: SpeciesPhotoMatrixData,
  searchTerm: string,
  filters: {
    photoPublished?: boolean | null;
    speciesPublished?: boolean | null;
    category?: string;
  }
): SpeciesPhotoMatrixData {
  const lowercasedTerm = searchTerm.toLowerCase();

  const filteredSpecies = data.species.filter(speciesWithPhotos => {
    const { species, photos } = speciesWithPhotos;

    // Filter by species criteria
    if (filters.speciesPublished !== null && filters.speciesPublished !== undefined) {
      if (species.published !== filters.speciesPublished) return false;
    }

    if (filters.category && species.category !== filters.category) {
      return false;
    }

    // Filter by search term (species fields)
    const speciesMatchesSearch = !searchTerm || 
      species.name.toLowerCase().includes(lowercasedTerm) ||
      (species.common_name && species.common_name.toLowerCase().includes(lowercasedTerm)) ||
      (species.scientific_name && species.scientific_name.toLowerCase().includes(lowercasedTerm)) ||
      (species.description && species.description.toLowerCase().includes(lowercasedTerm)) ||
      (species.habitat && species.habitat.toLowerCase().includes(lowercasedTerm));

    // Filter photos by criteria
    const filteredPhotos = photos.filter(photo => {
      if (filters.photoPublished !== null && filters.photoPublished !== undefined) {
        if (photo.published !== filters.photoPublished) return false;
      }

      // Filter by search term (photo fields)
      const photoMatchesSearch = !searchTerm ||
        (photo.location && photo.location.toLowerCase().includes(lowercasedTerm)) ||
        (photo.title && photo.title.toLowerCase().includes(lowercasedTerm)) ||
        (photo.description && photo.description.toLowerCase().includes(lowercasedTerm)) ||
        photo.id.toString().includes(lowercasedTerm);

      return photoMatchesSearch;
    });

    // Include species if it matches search OR has photos that match search
    return speciesMatchesSearch || filteredPhotos.length > 0;
  }).map(speciesWithPhotos => {
    const { species, photos } = speciesWithPhotos;

    // Filter photos for this species
    const filteredPhotos = photos.filter(photo => {
      if (filters.photoPublished !== null && filters.photoPublished !== undefined) {
        if (photo.published !== filters.photoPublished) return false;
      }

      // Filter by search term (photo fields)
      const photoMatchesSearch = !searchTerm ||
        (photo.location && photo.location.toLowerCase().includes(lowercasedTerm)) ||
        (photo.title && photo.title.toLowerCase().includes(lowercasedTerm)) ||
        (photo.description && photo.description.toLowerCase().includes(lowercasedTerm)) ||
        photo.id.toString().includes(lowercasedTerm);

      return photoMatchesSearch;
    });

    return {
      species,
      photos: filteredPhotos
    };
  });

  // Recalculate statistics for filtered data
  const totalPhotos = filteredSpecies.reduce((sum, sp) => sum + sp.photos.length, 0);
  const publishedPhotos = filteredSpecies.reduce((sum, sp) => 
    sum + sp.photos.filter(p => p.published).length, 0);

  const filteredUnassigned = data.unassigned_photos.filter(p => {
    const fileName = p.url?.split('/').pop()?.toLowerCase() || '';
    return fileName.includes(lowercasedTerm);
  });

  return {
    species: filteredSpecies,
    unassigned_photos: lowercasedTerm ? filteredUnassigned : data.unassigned_photos,
    totalSpecies: filteredSpecies.length,
    totalPhotos,
    publishedPhotos,
    unpublishedPhotos: totalPhotos - publishedPhotos
  };
}

/**
 * Get unique categories from species data
 */
export function getUniqueCategories(species: Species[]): string[] {
  const categories = new Set<string>();

  // Validate input
  if (!Array.isArray(species)) {
    console.warn('getUniqueCategories: species is not an array:', species);
    return [];
  }

  species.forEach(s => {
    if (s && s.category) {
      categories.add(s.category);
    }
  });
  return Array.from(categories).sort();
}

/**
 * Format date for display
 */
export function formatDate(dateString: string | null): string {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString();
}

/**
 * Format boolean for display
 */
export function formatBoolean(value: boolean | null): string {
  if (value === null) return 'N/A';
  return value ? 'Yes' : 'No';
}

/**
 * Parse camera settings JSON string
 */
export function parseCameraSettings(cameraSettings: string | null): Record<string, string | number | boolean> | null {
  if (!cameraSettings) return null;
  try {
    return JSON.parse(cameraSettings);
  } catch {
    return null;
  }
}

/**
 * Export species and photos data to CSV format
 */
export function exportToCSV(data: SpeciesPhotoMatrixData, speciesId?: string): string {
  const csvRows: string[] = [];

  // Validate input data
  if (!data || !Array.isArray(data.species)) {
    throw new Error('Invalid data structure: species array is required');
  }

  // Full exhaustive header row
  const headers = [
    'Species ID', 'Species Name', 'Scientific Name', 'Common Name', 'Category',
    'Conservation Status', 'Published', 'Photo ID', 'Species ID (Photo)', 'Photo URL', 'Location', 'Date Taken', 'Published (Photo)', 'Original Filename',
    'Camera Make', 'Camera Model', 'Lens', 'Focal Length', 'Aperture', 'Shutter Speed', 'ISO',
    'Exposure Mode', 'White Balance', 'Metering Mode', 'Flash Used', 'Color Profile',
    'AI Assigned Species ID', 'AI Confidence', 'AI Model',
    'Is Orphaned', 'Is Duplicate', 'Image Hash', 'Metadata',
    'Created At', 'Updated At', 'Title', 'Description'
  ];
  csvRows.push(headers.join(','));

  // Data rows
  data.species.forEach(speciesWithPhotos => {
    const { species, photos } = speciesWithPhotos;
    
    // If filtering by specific species
    if (speciesId && species.id !== speciesId) return;

    if (photos.length === 0) {
      // Species with no photos
      const row = [
        species.id,
        species.name,
        species.scientific_name || '',
        species.common_name || '',
        species.category || '',
        species.conservation_status || '',
        species.published ? 'Yes' : 'No',
        '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''
      ];
      csvRows.push(row.join(','));
    } else {
      // Species with photos
      photos.forEach(photo => {
        const row = [
          species.id,
          species.name,
          species.scientific_name || '',
          species.common_name || '',
          species.category || '',
          species.conservation_status || '',
          species.published ? 'Yes' : 'No',
          photo.id,
          photo.species_id || '',
          photo.url || '',
          photo.location || '',
          photo.date_taken || '',
          photo.published ? 'Yes' : 'No',
          photo.original_filename || '',
          photo.camera_make || '',
          photo.camera_model || '',
          photo.lens || '',
          photo.focal_length || '',
          photo.aperture || '',
          photo.shutter_speed || '',
          photo.iso || '',
          photo.exposure_mode || '',
          photo.white_balance || '',
          photo.metering_mode || '',
          photo.flash_used ? 'Yes' : 'No',
          photo.color_profile || '',
          photo.ai_assigned_species_id || '',
          photo.ai_confidence?.toString() || '',
          photo.ai_model || '',
          photo.is_orphaned ? 'Yes' : 'No',
          photo.is_duplicate ? 'Yes' : 'No',
          photo.image_hash || '',
          photo.metadata ? JSON.stringify(photo.metadata) : '',
          photo.created_at || '',
          photo.updated_at || '',
          photo.title || '',
          photo.description || ''
        ];
        csvRows.push(row.join(','));
      });
    }
  });

  return csvRows.join('\n');
}

/**
 * Download CSV data as a file
 */
export function downloadCSV(csvData: string, filename: string): void {
  const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
} 