import { supabase } from '@/integrations/supabase/client';

export interface AuditLogEntry {
  id?: string;
  action: 'create' | 'update' | 'delete' | 'publish' | 'unpublish' | 'assign' | 'bulk_operation';
  entity_type: 'species' | 'photo' | 'system';
  entity_id: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  user_id?: string;
  metadata?: Record<string, any>;
  timestamp: string;
}

export class AuditLogger {
  private static instance: AuditLogger;
  private logQueue: AuditLogEntry[] = [];
  private isProcessing = false;

  static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger();
    }
    return AuditLogger.instance;
  }

  async log(entry: Omit<AuditLogEntry, 'timestamp'>): Promise<void> {
    const logEntry: AuditLogEntry = {
      ...entry,
      timestamp: new Date().toISOString()
    };

    this.logQueue.push(logEntry);
    
    if (!this.isProcessing) {
      this.processQueue();
    }
  }

  private async processQueue(): Promise<void> {
    if (this.logQueue.length === 0 || this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      const batch = this.logQueue.splice(0, 10); // Process in batches of 10
      
      // In a real implementation, you would save to a dedicated audit_logs table
      // For now, we'll just log to console and optionally save to a simple table
      console.group('Audit Log Batch');
      batch.forEach(entry => {
        console.log(`[${entry.timestamp}] ${entry.action.toUpperCase()} ${entry.entity_type}:${entry.entity_id}`, entry);
      });
      console.groupEnd();

      // Optionally save to database (would need audit_logs table)
      // await supabase.from('audit_logs').insert(batch);
      
    } catch (error) {
      console.error('Error processing audit log queue:', error);
      // Re-add failed entries to queue
      this.logQueue.unshift(...batch);
    } finally {
      this.isProcessing = false;
      
      // Process remaining items
      if (this.logQueue.length > 0) {
        setTimeout(() => this.processQueue(), 1000);
      }
    }
  }

  async logSpeciesCreate(speciesId: string, speciesData: any, userId?: string): Promise<void> {
    await this.log({
      action: 'create',
      entity_type: 'species',
      entity_id: speciesId,
      new_values: speciesData,
      user_id: userId,
      metadata: { source: 'cms' }
    });
  }

  async logSpeciesUpdate(speciesId: string, oldData: any, newData: any, userId?: string): Promise<void> {
    await this.log({
      action: 'update',
      entity_type: 'species',
      entity_id: speciesId,
      old_values: oldData,
      new_values: newData,
      user_id: userId,
      metadata: { source: 'cms' }
    });
  }

  async logSpeciesPublish(speciesId: string, published: boolean, userId?: string): Promise<void> {
    await this.log({
      action: published ? 'publish' : 'unpublish',
      entity_type: 'species',
      entity_id: speciesId,
      new_values: { published },
      user_id: userId,
      metadata: { source: 'cms' }
    });
  }

  async logPhotoUpload(photoId: string, photoData: any, userId?: string): Promise<void> {
    await this.log({
      action: 'create',
      entity_type: 'photo',
      entity_id: photoId,
      new_values: photoData,
      user_id: userId,
      metadata: { source: 'upload' }
    });
  }

  async logPhotoAssignment(photoId: string, oldSpeciesId: string | null, newSpeciesId: string | null, userId?: string): Promise<void> {
    await this.log({
      action: 'assign',
      entity_type: 'photo',
      entity_id: photoId,
      old_values: { species_id: oldSpeciesId },
      new_values: { species_id: newSpeciesId },
      user_id: userId,
      metadata: { source: 'cms', type: 'species_assignment' }
    });
  }

  async logBulkOperation(operation: string, entityType: 'species' | 'photo', entityIds: string[], metadata?: any, userId?: string): Promise<void> {
    await this.log({
      action: 'bulk_operation',
      entity_type: entityType,
      entity_id: entityIds.join(','),
      metadata: {
        operation,
        count: entityIds.length,
        entity_ids: entityIds,
        ...metadata
      },
      user_id: userId
    });
  }

  async logAIOperation(operation: string, entityId: string, entityType: 'species' | 'photo', aiData: any, userId?: string): Promise<void> {
    await this.log({
      action: 'update',
      entity_type: entityType,
      entity_id: entityId,
      new_values: aiData,
      user_id: userId,
      metadata: {
        source: 'ai',
        operation,
        ai_generated: true
      }
    });
  }
}

// Version control utilities
export interface VersionEntry {
  id: string;
  entity_type: 'species' | 'photo';
  entity_id: string;
  version: number;
  data: Record<string, any>;
  created_at: string;
  created_by?: string;
  change_summary?: string;
}

export class VersionControl {
  private static instance: VersionControl;

  static getInstance(): VersionControl {
    if (!VersionControl.instance) {
      VersionControl.instance = new VersionControl();
    }
    return VersionControl.instance;
  }

  async createVersion(
    entityType: 'species' | 'photo',
    entityId: string,
    data: Record<string, any>,
    changeSummary?: string,
    userId?: string
  ): Promise<VersionEntry> {
    // Get current version number
    const currentVersion = await this.getCurrentVersion(entityType, entityId);
    
    const versionEntry: VersionEntry = {
      id: `${entityType}_${entityId}_v${currentVersion + 1}`,
      entity_type: entityType,
      entity_id: entityId,
      version: currentVersion + 1,
      data,
      created_at: new Date().toISOString(),
      created_by: userId,
      change_summary: changeSummary
    };

    // In a real implementation, save to versions table
    console.log('Version created:', versionEntry);
    
    return versionEntry;
  }

  async getCurrentVersion(entityType: 'species' | 'photo', entityId: string): Promise<number> {
    // In a real implementation, query versions table
    // For now, return a mock version
    return 0;
  }

  async getVersionHistory(entityType: 'species' | 'photo', entityId: string): Promise<VersionEntry[]> {
    // In a real implementation, query versions table
    // For now, return empty array
    return [];
  }

  async revertToVersion(entityType: 'species' | 'photo', entityId: string, version: number): Promise<boolean> {
    try {
      const versionHistory = await this.getVersionHistory(entityType, entityId);
      const targetVersion = versionHistory.find(v => v.version === version);
      
      if (!targetVersion) {
        throw new Error(`Version ${version} not found`);
      }

      // Restore the data
      const table = entityType === 'species' ? 'species_v2' : 'photos_v2';
      const { error } = await supabase
        .from(table)
        .update(targetVersion.data)
        .eq('id', entityId);

      if (error) throw error;

      // Log the revert operation
      await AuditLogger.getInstance().log({
        action: 'update',
        entity_type: entityType,
        entity_id: entityId,
        new_values: targetVersion.data,
        metadata: {
          source: 'version_control',
          reverted_to_version: version,
          operation: 'revert'
        }
      });

      return true;
    } catch (error) {
      console.error('Error reverting to version:', error);
      return false;
    }
  }
}

// Performance tracking
export class PerformanceTracker {
  private static metrics: Map<string, number[]> = new Map();

  static trackOperation(operationName: string, duration: number): void {
    if (!this.metrics.has(operationName)) {
      this.metrics.set(operationName, []);
    }
    
    const operations = this.metrics.get(operationName)!;
    operations.push(duration);
    
    // Keep only last 100 measurements
    if (operations.length > 100) {
      operations.shift();
    }
  }

  static getMetrics(operationName: string): { avg: number; min: number; max: number; count: number } | null {
    const operations = this.metrics.get(operationName);
    if (!operations || operations.length === 0) {
      return null;
    }

    const avg = operations.reduce((sum, val) => sum + val, 0) / operations.length;
    const min = Math.min(...operations);
    const max = Math.max(...operations);

    return { avg, min, max, count: operations.length };
  }

  static getAllMetrics(): Record<string, { avg: number; min: number; max: number; count: number }> {
    const result: Record<string, any> = {};
    
    for (const [operationName] of this.metrics) {
      const metrics = this.getMetrics(operationName);
      if (metrics) {
        result[operationName] = metrics;
      }
    }
    
    return result;
  }
}

// Utility function to wrap operations with performance tracking
export function withPerformanceTracking<T extends (...args: any[]) => any>(
  operationName: string,
  fn: T
): T {
  return ((...args: any[]) => {
    const startTime = performance.now();
    
    try {
      const result = fn(...args);
      
      // Handle both sync and async functions
      if (result instanceof Promise) {
        return result.finally(() => {
          const duration = performance.now() - startTime;
          PerformanceTracker.trackOperation(operationName, duration);
        });
      } else {
        const duration = performance.now() - startTime;
        PerformanceTracker.trackOperation(operationName, duration);
        return result;
      }
    } catch (error) {
      const duration = performance.now() - startTime;
      PerformanceTracker.trackOperation(`${operationName}_error`, duration);
      throw error;
    }
  }) as T;
}
