import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface SpeciesValidation {
  name: string;
  scientific_name?: string;
  category?: string;
  conservation_status?: string;
  published?: boolean;
}

export interface PhotoValidation {
  title?: string;
  url: string;
  species_id?: string;
  published?: boolean;
}

/**
 * Data Integrity Service
 * Ensures consistency and validation across species_v2 and photos tables
 */
export class DataIntegrityService {
  
  /**
   * Validate species data before creation or update
   */
  static validateSpecies(data: SpeciesValidation): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required field validation
    if (!data.name || data.name.trim().length === 0) {
      errors.push('Species name is required');
    }

    if (data.name && data.name.trim().length < 2) {
      errors.push('Species name must be at least 2 characters long');
    }

    if (data.name && data.name.trim().length > 255) {
      errors.push('Species name must be less than 255 characters');
    }

    // Scientific name validation
    if (data.scientific_name) {
      const scientificNamePattern = /^[A-Z][a-z]+ [a-z]+$/;
      if (!scientificNamePattern.test(data.scientific_name.trim())) {
        warnings.push('Scientific name should follow binomial nomenclature (e.g., "Homo sapiens")');
      }
    }

    // Category validation
    const validCategories = ['Mammals', 'Birds', 'Reptiles', 'Amphibians', 'Fish', 'Invertebrates', 'Plants', 'Other'];
    if (data.category && !validCategories.includes(data.category)) {
      errors.push(`Category must be one of: ${validCategories.join(', ')}`);
    }

    // Conservation status validation
    const validStatuses = ['Least Concern', 'Near Threatened', 'Vulnerable', 'Endangered', 'Critically Endangered', 'Extinct in the Wild', 'Extinct'];
    if (data.conservation_status && !validStatuses.includes(data.conservation_status)) {
      errors.push(`Conservation status must be one of: ${validStatuses.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate photo data before creation or update
   */
  static validatePhoto(data: PhotoValidation): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // URL validation
    if (!data.url || data.url.trim().length === 0) {
      errors.push('Photo URL is required');
    }

    if (data.url) {
      try {
        new URL(data.url);
      } catch {
        errors.push('Photo URL must be a valid URL');
      }
    }

    // Title validation
    if (data.title && data.title.trim().length > 255) {
      errors.push('Photo title must be less than 255 characters');
    }

    // Species ID validation (if provided)
    if (data.species_id && !this.isValidUUID(data.species_id)) {
      errors.push('Species ID must be a valid UUID');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Check if species exists before assigning photos
   */
  static async validateSpeciesExists(speciesId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('species_v2')
        .select('id')
        .eq('id', speciesId)
        .single();

      if (error) {
        console.error('Error checking species existence:', error);
        return false;
      }

      return !!data;
    } catch (error) {
      console.error('Error validating species existence:', error);
      return false;
    }
  }

  /**
   * Update photo count for a species
   */
  static async updateSpeciesPhotoCount(speciesId: string): Promise<boolean> {
    try {
      // Count published photos for this species
      const { count, error: countError } = await supabase
        .from('photos_v2')
        .select('*', { count: 'exact', head: true })
        .eq('species_id', speciesId)
        .eq('published', true);

      if (countError) {
        console.error('Error counting photos:', countError);
        return false;
      }

      // Update species photo count
      const { error: updateError } = await supabase
        .from('species_v2')
        .update({ photo_count: count || 0 })
        .eq('id', speciesId);

      if (updateError) {
        console.error('Error updating photo count:', updateError);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error updating species photo count:', error);
      return false;
    }
  }

  /**
   * Validate and clean orphaned photos (photos without valid species)
   */
  static async findOrphanedPhotos(): Promise<string[]> {
    try {
      const { data: photos, error: photosError } = await supabase
        .from('photos_v2')
        .select('id, species_id')
        .not('species_id', 'is', null);

      if (photosError) {
        console.error('Error fetching photos:', photosError);
        return [];
      }

      const orphanedPhotoIds: string[] = [];

      for (const photo of photos || []) {
        if (photo.species_id) {
          const exists = await this.validateSpeciesExists(photo.species_id);
          if (!exists) {
            orphanedPhotoIds.push(photo.id.toString());
          }
        }
      }

      return orphanedPhotoIds;
    } catch (error) {
      console.error('Error finding orphaned photos:', error);
      return [];
    }
  }

  /**
   * Fix orphaned photos by setting species_id to null
   */
  static async fixOrphanedPhotos(photoIds: string[]): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('photos_v2')
        .update({ species_id: null })
        .in('id', photoIds);

      if (error) {
        console.error('Error fixing orphaned photos:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error fixing orphaned photos:', error);
      return false;
    }
  }

  /**
   * Validate and update all species photo counts
   */
  static async recalculateAllPhotoCounts(): Promise<{ updated: number; errors: number }> {
    try {
      const { data: species, error: speciesError } = await supabase
        .from('species_v2')
        .select('id');

      if (speciesError) {
        console.error('Error fetching species:', speciesError);
        return { updated: 0, errors: 1 };
      }

      let updated = 0;
      let errors = 0;

      for (const sp of species || []) {
        const success = await this.updateSpeciesPhotoCount(sp.id);
        if (success) {
          updated++;
        } else {
          errors++;
        }
      }

      return { updated, errors };
    } catch (error) {
      console.error('Error recalculating photo counts:', error);
      return { updated: 0, errors: 1 };
    }
  }

  /**
   * Comprehensive data integrity check
   */
  static async performIntegrityCheck(): Promise<{
    orphanedPhotos: string[];
    photoCountUpdates: { updated: number; errors: number };
    issues: string[];
  }> {
    const issues: string[] = [];

    try {
      // Find orphaned photos
      const orphanedPhotos = await this.findOrphanedPhotos();
      if (orphanedPhotos.length > 0) {
        issues.push(`Found ${orphanedPhotos.length} orphaned photos`);
      }

      // Recalculate photo counts
      const photoCountUpdates = await this.recalculateAllPhotoCounts();
      if (photoCountUpdates.errors > 0) {
        issues.push(`${photoCountUpdates.errors} errors while updating photo counts`);
      }

      return {
        orphanedPhotos,
        photoCountUpdates,
        issues
      };
    } catch (error) {
      console.error('Error performing integrity check:', error);
      return {
        orphanedPhotos: [],
        photoCountUpdates: { updated: 0, errors: 1 },
        issues: ['Failed to perform integrity check']
      };
    }
  }

  /**
   * Safe species creation with validation
   */
  static async createSpecies(data: SpeciesValidation): Promise<{ success: boolean; id?: string; errors?: string[] }> {
    const validation = this.validateSpecies(data);
    
    if (!validation.isValid) {
      return { success: false, errors: validation.errors };
    }

    try {
      const { data: newSpecies, error } = await supabase
        .from('species_v2')
        .insert({
          ...data,
          name: data.name.trim(),
          scientific_name: data.scientific_name?.trim() || null,
          photo_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (error) {
        console.error('Error creating species:', error);
        return { success: false, errors: [error.message] };
      }

      if (validation.warnings.length > 0) {
        validation.warnings.forEach(warning => toast.warning(warning));
      }

      return { success: true, id: newSpecies.id };
    } catch (error) {
      console.error('Error creating species:', error);
      return { success: false, errors: ['Failed to create species'] };
    }
  }

  /**
   * Safe photo assignment with validation
   */
  static async assignPhotoToSpecies(photoId: string, speciesId: string): Promise<{ success: boolean; errors?: string[] }> {
    try {
      // Validate species exists
      const speciesExists = await this.validateSpeciesExists(speciesId);
      if (!speciesExists) {
        return { success: false, errors: ['Species does not exist'] };
      }

      // Update photo
      const { error: photoError } = await supabase
        .from('photos_v2')
        .update({ species_id: speciesId })
        .eq('id', photoId);

      if (photoError) {
        console.error('Error assigning photo:', photoError);
        return { success: false, errors: [photoError.message] };
      }

      // Update species photo count
      await this.updateSpeciesPhotoCount(speciesId);

      return { success: true };
    } catch (error) {
      console.error('Error assigning photo to species:', error);
      return { success: false, errors: ['Failed to assign photo'] };
    }
  }

  /**
   * Utility function to validate UUID format
   */
  private static isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }
}
