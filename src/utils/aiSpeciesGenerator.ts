import { supabase } from '@/integrations/supabase/client';

// Species name validation and disambiguation
const SPECIES_DISAMBIGUATION = {
  'bare-faced ibis': {
    scientific: 'Phimosus infuscatus',
    common: 'Bare-faced Ibis',
    notes: 'NOT White-faced Ibis (<PERSON><PERSON><PERSON><PERSON> chihi)'
  },
  'white-faced ibis': {
    scientific: 'Plegadis chihi',
    common: 'White-faced Ibis',
    notes: 'NOT Bare-faced Ibis (Phimosus infuscatus)'
  },
  'american robin': {
    scientific: 'Turdus migratorius',
    common: 'American Robin',
    notes: 'NOT European Robin (Erithacus rubecula)'
  },
  'sand goanna': {
    scientific: 'Varanus gouldii',
    common: 'Sand goanna (Gould\'s monitor)',
    notes: 'Found across arid and semi-arid regions of Australia. Geographic range: New South Wales, Queensland, Victoria, South Australia, Western Australia. Specific habitats include Great Western Woodlands, Pilbara region, Great Victoria Desert, Nullarbor Plain, Murray-Darling Basin.'
  }
};

function getSpeciesDisambiguation(speciesName: string): string {
  const key = speciesName.toLowerCase().trim();
  const disambiguation = SPECIES_DISAMBIGUATION[key as keyof typeof SPECIES_DISAMBIGUATION];

  if (disambiguation) {
    return `\n\nSPECIES DISAMBIGUATION: For "${speciesName}", use scientific name ${disambiguation.scientific} (${disambiguation.common}). ${disambiguation.notes}`;
  }

  return '';
}

export interface SpeciesMetadata {
  // Basic identification
  name: string;
  common_name?: string;
  scientific_name: string;
  family?: string;

  // Physical characteristics
  description: string;
  size_cm?: string;
  weight_g?: string;
  size_description?: string;

  // Biological information
  habitat: string;
  diet: string;
  behavior?: string;
  lifespan_years?: number;
  migration_pattern?: string;
  breeding_season?: string;

  // Conservation and status
  conservation_status: string;
  threat_level?: string;
  population_trend?: string;
  conservation_actions?: string;

  // Classification
  category: string;
  taxonomy_order?: string;
  taxonomy_subfamily?: string;
  taxonomy_genus?: string;
  common_group?: string;
  related_groups?: string[];

  // External identifiers
  itis_tsn?: string;
  gbif_id?: string;
  ebird_code?: string;
  inat_id?: string;
  avibase_id?: string;

  // Additional metadata
  tags?: string[];
  related_locations: string[];
  regions?: string;
  notes?: string;

  // Enhanced geographic data
  countries?: string[];
  states_provinces?: string[];
  geographic_scope?: 'global' | 'continental' | 'national' | 'regional' | 'local';
  primary_region?: string;
  habitat_specificity?: 'endemic' | 'specialized' | 'general' | 'widespread';

  // Fun facts and AI metadata
  fun_facts?: string[];
  ai_fun_facts?: string[];
  ai_generated?: boolean;
  ai_confidence?: number;
}

export interface EnrichedSpecies extends SpeciesMetadata {
  id?: string;
  ai_generated: boolean;
  published: boolean;
  created_at?: string;
  updated_at?: string;
}

// Comprehensive species database with real scientific names
const dummySpeciesData: Record<string, SpeciesMetadata> = {
  // Birds
  'Bare-faced Ibis': {
    name: 'Bare-faced Ibis',
    scientific_name: 'Phimosus infuscatus',
    description: 'A medium-sized wading bird found in South America. Distinguished by its bare, dark facial skin and brownish-black plumage with a metallic green sheen. Unlike the White-faced Ibis, it lacks white facial markings.',
    habitat: 'Wetlands, marshes, flooded grasslands, and shallow water bodies in tropical and subtropical regions',
    diet: 'Aquatic invertebrates, small fish, frogs, insects, and crustaceans found by probing in mud and shallow water',
    behavior: 'Forages by probing in soft substrates with its curved bill. Often feeds in flocks and roosts communally in trees near water.',
    conservation_status: 'Least Concern',
    category: 'Birds',
    family: 'Threskiornithidae',
    size_cm: 47,
    weight_g: 650,
    lifespan_years: 15,
    migration_pattern: 'Partial migrant, some populations move seasonally',
    breeding_season: 'October to February (Southern Hemisphere summer)',
    threat_level: 'Low',
    population_trend: 'Stable',
    related_locations: ['South America', 'Argentina', 'Brazil', 'Paraguay', 'Uruguay'],
    countries: ['Argentina', 'Brazil', 'Paraguay', 'Uruguay', 'Bolivia'],
    states_provinces: ['Buenos Aires', 'Santa Fe', 'Rio Grande do Sul', 'Mato Grosso do Sul'],
    geographic_scope: 'regional',
    primary_region: 'South America',
    habitat_specificity: 'specialized'
  },
  'Barred Antshrike': {
    name: 'Barred Antshrike',
    scientific_name: 'Thamnophilus doliatus',
    description: 'A medium-sized passerine bird found in tropical forests. Males have distinctive black and white barred plumage, while females are rufous-brown. Known for their loud, antiphonal duets between mated pairs.',
    habitat: 'Tropical and subtropical forests, forest edges, secondary growth, and woodland clearings',
    diet: 'Primarily insects and small arthropods including beetles, ants, caterpillars, and spiders',
    behavior: 'Forages in pairs or small groups, often joining mixed-species flocks. Males and females perform coordinated duets to maintain territory.',
    conservation_status: 'Least Concern',
    category: 'Birds',
    family: 'Thamnophilidae',
    size_cm: 16,
    weight_g: 25,
    lifespan_years: 8,
    migration_pattern: 'Non-migratory resident',
    breeding_season: 'March to August',
    threat_level: 'Low',
    population_trend: 'Stable',
    related_locations: ['Central America', 'South America', 'Southern Mexico']
  },
  'Tolima blossomcrown': {
    name: 'Tolima blossomcrown',
    scientific_name: 'Anthocephala berlepschi',
    description: 'A small hummingbird endemic to Colombia, known for its iridescent plumage and specialized nectar-feeding behavior.',
    habitat: 'Cloud forests and forest edges in the Andes',
    diet: 'Nectar from flowers, small insects and spiders',
    conservation_status: 'Near Threatened',
    category: 'Birds',
    related_locations: ['Colombia']
  },
  'White-breasted robin': {
    name: 'White-breasted robin',
    scientific_name: 'Turdus torquatus',
    description: 'A medium-sized thrush with distinctive white breast markings, found in mountainous regions.',
    habitat: 'Mountain forests, alpine meadows, and rocky areas',
    diet: 'Insects, worms, berries, and fruits',
    conservation_status: 'Least Concern',
    category: 'Birds',
    related_locations: ['Europe', 'Asia']
  },
  'Australian kestrel': {
    name: 'Australian kestrel',
    scientific_name: 'Falco cenchroides',
    description: 'A small falcon native to Australia and New Guinea, known for its hovering hunting behavior.',
    habitat: 'Open woodlands, grasslands, and urban areas',
    diet: 'Small mammals, insects, and small birds',
    conservation_status: 'Least Concern',
    category: 'Birds',
    related_locations: ['Australia', 'New Guinea']
  },
  'Western Rosella': {
    name: 'Western Rosella',
    scientific_name: 'Platycercus icterotis',
    description: 'A colorful parrot endemic to southwestern Australia, known for its vibrant red and yellow plumage.',
    habitat: 'Eucalyptus forests, woodlands, and parks',
    diet: 'Seeds, fruits, nectar, and insects',
    conservation_status: 'Least Concern',
    category: 'Birds',
    related_locations: ['Western Australia']
  },
  'Ring-necked Pheasant': {
    name: 'Ring-necked Pheasant',
    scientific_name: 'Phasianus colchicus',
    description: 'A large game bird originally from Asia, now widely introduced across the world.',
    habitat: 'Agricultural areas, grasslands, and woodland edges',
    diet: 'Seeds, grains, insects, and small animals',
    conservation_status: 'Least Concern',
    category: 'Birds',
    related_locations: ['Asia', 'North America', 'Europe']
  },
  'Bald Eagle': {
    name: 'Bald Eagle',
    scientific_name: 'Haliaeetus leucocephalus',
    description: 'A large bird of prey and the national bird of the United States, known for its distinctive white head and tail feathers that develop at maturity.',
    habitat: 'Near large bodies of water, forests, coastal areas, and increasingly urban environments',
    diet: 'Primarily fish, but also waterfowl, small mammals, and carrion',
    behavior: 'Soars on thermals, builds large stick nests in tall trees, often reuses nests for many years.',
    conservation_status: 'Least Concern',
    category: 'Birds',
    family: 'Accipitridae',
    size_cm: 96,
    weight_g: 4500,
    lifespan_years: 28,
    migration_pattern: 'Partial migrant',
    breeding_season: 'December to August',
    threat_level: 'Low',
    population_trend: 'Increasing',
    related_locations: ['North America']
  },
  'Red-winged Blackbird': {
    name: 'Red-winged Blackbird',
    scientific_name: 'Agelaius phoeniceus',
    description: 'A passerine bird found in wetlands throughout North America. Males are distinguished by bright red shoulder patches with yellow borders.',
    habitat: 'Marshes, wetlands, agricultural areas, and roadside ditches',
    diet: 'Seeds, insects, and small aquatic animals including dragonflies and midges',
    behavior: 'Highly territorial during breeding season, males defend territories from prominent perches.',
    conservation_status: 'Least Concern',
    category: 'Birds',
    family: 'Icteridae',
    size_cm: 22,
    weight_g: 65,
    lifespan_years: 15,
    migration_pattern: 'Partial migrant',
    breeding_season: 'March to July',
    threat_level: 'Low',
    population_trend: 'Stable',
    related_locations: ['North America']
  },
  'Buff-banded Rail': {
    name: 'Buff-banded Rail',
    scientific_name: 'Gallirallus philippensis',
    description: 'A medium-sized rail with distinctive buff and black banding on the upperparts. A secretive waterbird found in wetlands across the Pacific region.',
    habitat: 'Freshwater and saltwater wetlands, mangroves, swamps, and dense vegetation near water',
    diet: 'Omnivorous - insects, crustaceans, mollusks, small fish, seeds, and aquatic vegetation',
    behavior: 'Secretive and elusive, more often heard than seen. Runs quickly through dense vegetation, rarely flies.',
    conservation_status: 'Least Concern',
    category: 'Birds',
    family: 'Rallidae',
    size_cm: 30,
    weight_g: 180,
    lifespan_years: 8,
    migration_pattern: 'Sedentary with some local movements',
    breeding_season: 'September to February',
    threat_level: 'Low',
    population_trend: 'Stable',
    related_locations: ['Australia', 'New Zealand', 'Pacific Islands', 'Philippines']
  },
  // Mammals
  'African Elephant': {
    name: 'African Elephant',
    scientific_name: 'Loxodonta africana',
    description: 'The African elephant is the largest living terrestrial animal, known for its long trunk, large ears, and tusks.',
    habitat: 'Savannas, forests, and grasslands of sub-Saharan Africa',
    diet: 'Herbivorous - grasses, leaves, bark, fruits, and roots',
    conservation_status: 'Vulnerable',
    category: 'Mammals',
    related_locations: ['Kenya', 'Tanzania', 'Botswana', 'South Africa']
  },
  'Red Fox': {
    name: 'Red Fox',
    scientific_name: 'Vulpes vulpes',
    description: 'A small to medium-sized omnivorous mammal with a distinctive red coat and bushy tail.',
    habitat: 'Forests, grasslands, mountains, and urban areas',
    diet: 'Small mammals, birds, insects, fruits, and vegetables',
    conservation_status: 'Least Concern',
    category: 'Mammals',
    related_locations: ['North America', 'Europe', 'Asia']
  },
  'Gray Wolf': {
    name: 'Gray Wolf',
    scientific_name: 'Canis lupus',
    description: 'The largest wild member of the Canidae family and the ancestor of the domestic dog.',
    habitat: 'Forests, tundra, grasslands, and mountains',
    diet: 'Large ungulates, small mammals, and fish',
    conservation_status: 'Least Concern',
    category: 'Mammals',
    related_locations: ['North America', 'Europe', 'Asia']
  },
  'White-tailed Deer': {
    name: 'White-tailed Deer',
    scientific_name: 'Odocoileus virginianus',
    description: 'A medium-sized deer native to North America, known for the white underside of its tail.',
    habitat: 'Forests, grasslands, and suburban areas',
    diet: 'Leaves, twigs, fruits, and nuts',
    conservation_status: 'Least Concern',
    category: 'Mammals',
    related_locations: ['North America', 'Central America', 'South America']
  },
  // Reptiles
  'Green Tree Python': {
    name: 'Green Tree Python',
    scientific_name: 'Morelia viridis',
    description: 'A non-venomous python species found in New Guinea and Australia, known for its bright green coloration.',
    habitat: 'Rainforests and woodland areas',
    diet: 'Birds and small mammals',
    conservation_status: 'Least Concern',
    category: 'Reptiles',
    related_locations: ['Australia', 'New Guinea']
  },
  'American Alligator': {
    name: 'American Alligator',
    scientific_name: 'Alligator mississippiensis',
    description: 'A large crocodilian endemic to the southeastern United States.',
    habitat: 'Freshwater environments including swamps, marshes, and rivers',
    diet: 'Fish, birds, mammals, and other reptiles',
    conservation_status: 'Least Concern',
    category: 'Reptiles',
    related_locations: ['Southeastern United States']
  },
  'Bengal Tiger': {
    name: 'Bengal Tiger',
    scientific_name: 'Panthera tigris tigris',
    description: 'The Bengal tiger is the most numerous tiger subspecies, known for its distinctive orange coat with black stripes.',
    habitat: 'Tropical and subtropical forests, grasslands, and mangrove swamps',
    diet: 'Carnivorous - deer, wild boar, water buffalo, and other large mammals',
    conservation_status: 'Endangered',
    category: 'Mammals',
    related_locations: ['India', 'Bangladesh', 'Nepal', 'Bhutan']
  },
  'Green Sea Turtle': {
    name: 'Green Sea Turtle',
    scientific_name: 'Chelonia mydas',
    description: 'The green sea turtle is a large sea turtle found in tropical and subtropical oceans worldwide.',
    habitat: 'Coastal waters, coral reefs, and seagrass beds',
    diet: 'Herbivorous - seagrasses, algae, and marine plants',
    conservation_status: 'Endangered',
    category: 'Reptiles',
    related_locations: ['Hawaii', 'Florida', 'Australia', 'Costa Rica']
  },
  'Giant Panda': {
    name: 'Giant Panda',
    scientific_name: 'Ailuropoda melanoleuca',
    description: 'The giant panda is a bear native to China, known for its distinctive black and white coat.',
    habitat: 'Bamboo forests in mountainous regions of central China',
    diet: 'Herbivorous - primarily bamboo, occasionally small animals',
    conservation_status: 'Vulnerable',
    category: 'Mammals',
    related_locations: ['China', 'Sichuan Province', 'Shaanxi Province']
  }
};

// Categories for random assignment
const categories = ['Mammals', 'Birds', 'Reptiles', 'Amphibians', 'Fish', 'Invertebrates', 'Plants'];

// Conservation statuses
const conservationStatuses = ['Least Concern', 'Near Threatened', 'Vulnerable', 'Endangered', 'Critically Endangered', 'Extinct in the Wild', 'Extinct'];

// Improved species data generator with better species-specific logic
export function generateDummySpeciesData(speciesName: string): SpeciesMetadata {
  // Check if we have predefined data (case-insensitive)
  const exactMatch = Object.keys(dummySpeciesData).find(
    key => key.toLowerCase() === speciesName.toLowerCase()
  );

  if (exactMatch) {
    return dummySpeciesData[exactMatch];
  }

  // Improved species-specific logic based on common name patterns
  const lowerName = speciesName.toLowerCase();
  let category = 'Unknown';
  let conservationStatus = 'Least Concern'; // Most species are least concern
  let habitat = '';
  let diet = '';
  let scientificName = '';
  let description = '';

  // Bird indicators - comprehensive list
  if (lowerName.includes('bird') || lowerName.includes('eagle') || lowerName.includes('hawk') ||
      lowerName.includes('owl') || lowerName.includes('sparrow') || lowerName.includes('robin') ||
      lowerName.includes('cardinal') || lowerName.includes('jay') || lowerName.includes('crow') ||
      lowerName.includes('duck') || lowerName.includes('goose') || lowerName.includes('swan') ||
      lowerName.includes('heron') || lowerName.includes('crane') || lowerName.includes('woodpecker') ||
      lowerName.includes('warbler') || lowerName.includes('finch') || lowerName.includes('thrush') ||
      lowerName.includes('flycatcher') || lowerName.includes('shrike') || lowerName.includes('antshrike') ||
      lowerName.includes('blossomcrown') || lowerName.includes('hummingbird') || lowerName.includes('kestrel') ||
      lowerName.includes('ibis') || lowerName.includes('stork') || lowerName.includes('flamingo') ||
      lowerName.includes('falcon') || lowerName.includes('rosella') || lowerName.includes('parrot') ||
      lowerName.includes('pheasant') || lowerName.includes('blackbird') || lowerName.includes('dove') ||
      lowerName.includes('pigeon') || lowerName.includes('wren') || lowerName.includes('chickadee') ||
      lowerName.includes('nuthatch') || lowerName.includes('kingfisher') || lowerName.includes('swallow') ||
      lowerName.includes('martin') || lowerName.includes('swift') || lowerName.includes('tern') ||
      lowerName.includes('gull') || lowerName.includes('pelican') || lowerName.includes('cormorant') ||
      lowerName.includes('rail') || lowerName.includes('crake') || lowerName.includes('gallinule') ||
      lowerName.includes('emu') || lowerName.includes('ostrich') || lowerName.includes('cassowary')) {
    category = 'Birds';
    habitat = 'Forests, woodlands, and open areas';
    diet = 'Insects, seeds, and small animals';

    // Special cases for specific birds
    if (lowerName.includes('antshrike')) {
      scientificName = 'Thamnophilus doliatus';
      description = 'A medium-sized passerine bird found in tropical forests. Known for its distinctive barred plumage pattern and insectivorous diet.';
      habitat = 'Tropical and subtropical forests, forest edges, and secondary growth';
      diet = 'Primarily insects and small arthropods';
    } else if (lowerName.includes('buff-banded rail') || lowerName.includes('buff banded rail')) {
      scientificName = 'Gallirallus philippensis';
      description = 'A medium-sized rail with distinctive buff and black banding on the upperparts. A secretive waterbird found in wetlands across the Pacific region.';
      habitat = 'Freshwater and saltwater wetlands, mangroves, swamps, and dense vegetation near water';
      diet = 'Omnivorous - insects, crustaceans, mollusks, small fish, seeds, and aquatic vegetation';
    } else if (lowerName.includes('emu')) {
      scientificName = 'Dromaius novaehollandiae';
      description = 'The second-largest living bird in the world by height. Emus are flightless birds native to Australia, known for their long legs, distinctive feathers, and ability to run at high speeds.';
      habitat = 'Open woodlands, scrublands, grasslands, and semi-arid regions across Australia';
      diet = 'Omnivorous - fruits, seeds, flowers, insects, and small vertebrates';
    } else {
      scientificName = generateScientificName(speciesName);
      description = `A bird species known as ${speciesName}. This avian species is adapted to its specific habitat and plays an important role in the ecosystem.`;
    }
  }
  // Mammal indicators
  else if (lowerName.includes('bear') || lowerName.includes('wolf') || lowerName.includes('fox') ||
           lowerName.includes('deer') || lowerName.includes('elk') || lowerName.includes('moose') ||
           lowerName.includes('rabbit') || lowerName.includes('squirrel') || lowerName.includes('mouse') ||
           lowerName.includes('cat') || lowerName.includes('dog') || lowerName.includes('lion') ||
           lowerName.includes('tiger') || lowerName.includes('elephant') || lowerName.includes('monkey') ||
           lowerName.includes('bat') || lowerName.includes('flying fox')) {
    category = 'Mammals';

    // Special case for Grey-headed flying fox
    if (lowerName.includes('grey-headed flying fox') || lowerName.includes('gray-headed flying fox')) {
      scientificName = 'Pteropus poliocephalus';
      description = 'The grey-headed flying fox is the largest bat in Australia. A megabat native to the eastern coast, it plays a crucial role in pollination and seed dispersal.';
      habitat = 'Coastal forests, woodlands, and urban areas along eastern Australia';
      diet = 'Nectar, pollen, and fruit from native trees';
      conservationStatus = 'Vulnerable';
    } else {
      habitat = 'Varied habitats including forests, grasslands, and mountains';
      diet = 'Omnivorous, feeding on plants and animals';
      scientificName = generateScientificName(speciesName);
      description = `A mammalian species known as ${speciesName}. This species is well-adapted to its environment and exhibits typical mammalian characteristics.`;
    }
  }
  // Reptile indicators
  else if (lowerName.includes('snake') || lowerName.includes('lizard') || lowerName.includes('turtle') ||
           lowerName.includes('gecko') || lowerName.includes('iguana') || lowerName.includes('alligator') ||
           lowerName.includes('crocodile')) {
    category = 'Reptiles';
    habitat = 'Warm climates including deserts, forests, and wetlands';
    diet = 'Carnivorous, feeding on various prey';
    scientificName = generateScientificName(speciesName);
    description = `A reptilian species known as ${speciesName}. This cold-blooded vertebrate is adapted to warm environments.`;
  }
  // Amphibian indicators
  else if (lowerName.includes('frog') || lowerName.includes('toad') || lowerName.includes('salamander') ||
           lowerName.includes('newt') || lowerName.includes('caecilian')) {
    category = 'Amphibians';

    // Special case for Peron's tree frog
    if (lowerName.includes("peron") && lowerName.includes("tree") && lowerName.includes("frog")) {
      scientificName = 'Litoria peronii';
      description = "Peron's tree frog is a common Australian tree frog found along the eastern coast. Known for its distinctive call and ability to change color from brown to bright green.";
      habitat = 'Coastal forests, woodlands, gardens, and urban areas with water sources';
      diet = 'Insects, spiders, and other small arthropods caught at night';
      conservationStatus = 'Least Concern';
    } else {
      habitat = 'Aquatic and terrestrial environments, often near water sources';
      diet = 'Insects, small invertebrates, and aquatic organisms';
      scientificName = generateScientificName(speciesName);
      description = `An amphibian species known as ${speciesName}. This species exhibits typical amphibian characteristics including metamorphosis and skin-based respiration.`;
    }
  }
  // Default fallback
  else {
    category = categories[Math.floor(Math.random() * categories.length)];
    habitat = 'Various habitats depending on species requirements';
    diet = 'Diet varies by species and ecological niche';
    scientificName = generateScientificName(speciesName);
    description = `A species known as ${speciesName}. This organism is adapted to its specific ecological niche.`;
  }

  // Generate additional comprehensive fields
  const family = category === 'Birds' ? 'Avian family' :
                category === 'Mammals' ? 'Mammalian family' :
                `${category} family`;

  const sizeDescription = category === 'Birds' ? 'Medium-sized bird' :
                         category === 'Mammals' ? 'Medium-sized mammal' :
                         'Medium-sized organism';

  return {
    // Basic identification
    name: speciesName,
    common_name: speciesName,
    scientific_name: scientificName,
    family: family,

    // Physical characteristics
    description: description,
    size_cm: category === 'Birds' ? '15-25' : category === 'Mammals' ? '30-50' : '10-30',
    weight_g: category === 'Birds' ? '50-150' : category === 'Mammals' ? '500-2000' : '100-500',
    size_description: sizeDescription,

    // Biological information
    habitat: habitat,
    diet: diet,
    behavior: 'Typical behaviors for this species group',
    lifespan_years: category === 'Birds' ? 8 : category === 'Mammals' ? 12 : 5,
    migration_pattern: category === 'Birds' ? 'Seasonal migration patterns' : null,
    breeding_season: 'Spring to summer',

    // Conservation
    conservation_status: conservationStatus,
    threat_level: conservationStatus === 'Least Concern' ? null : 'Habitat loss',
    population_trend: 'Stable',
    conservation_actions: conservationStatus === 'Least Concern' ? null : 'Habitat protection efforts',

    // Classification
    category: category,
    taxonomy_order: category === 'Birds' ? 'Passeriformes' :
                   category === 'Mammals' ? 'Carnivora' :
                   `${category} order`,
    taxonomy_subfamily: null,
    taxonomy_genus: scientificName.split(' ')[0],
    common_group: category === 'Birds' ? 'Songbirds' :
                 category === 'Mammals' ? 'Small mammals' :
                 category,
    related_groups: `Other ${category.toLowerCase()}`,

    // External identifiers (null for generated data)
    itis_tsn: null,
    gbif_id: null,
    ebird_code: category === 'Birds' ? null : null,
    inat_id: null,
    avibase_id: category === 'Birds' ? null : null,

    // Enhanced geographic data with more specificity
    countries: generateCountriesForSpecies(speciesName, category),
    states_provinces: generateStatesProvincesForSpecies(speciesName, category),
    geographic_scope: 'regional',
    primary_region: generatePrimaryRegionForSpecies(speciesName, category),
    habitat_specificity: 'general',

    // Additional metadata
    tags: [category.toLowerCase(), 'wildlife', 'nature'],
    related_locations: generateRelatedLocationsForSpecies(speciesName, category),
    regions: generatePrimaryRegionForSpecies(speciesName, category),
    notes: `Generated species data for ${speciesName}`,

    // Fun facts and AI metadata
    fun_facts: [
      `${speciesName} is an important part of its ecosystem`,
      `This species has adapted well to its natural habitat`,
      `${speciesName} plays a crucial role in maintaining biodiversity`,
      `Conservation efforts help protect ${speciesName} populations`
    ],
    ai_fun_facts: [
      `${speciesName} is an important part of its ecosystem`,
      `This species has adapted well to its natural habitat`,
      `${speciesName} plays a crucial role in maintaining biodiversity`,
      `Conservation efforts help protect ${speciesName} populations`
    ],
    ai_generated: true,
    ai_confidence: 0.5
  };
}

// Helper function to generate more realistic scientific names
function generateScientificName(commonName: string): string {
  const words = commonName.toLowerCase().split(' ');

  // Common genus patterns for different bird types
  const birdGenera: Record<string, string> = {
    'eagle': 'Aquila',
    'hawk': 'Accipiter',
    'owl': 'Strix',
    'robin': 'Turdus',
    'sparrow': 'Passer',
    'warbler': 'Setophaga',
    'finch': 'Spinus',
    'jay': 'Cyanocitta',
    'cardinal': 'Cardinalis',
    'woodpecker': 'Picoides',
    'hummingbird': 'Trochilus',
    'falcon': 'Falco',
    'kestrel': 'Falco',
    'dove': 'Columba',
    'pigeon': 'Columba',
    'crow': 'Corvus',
    'blackbird': 'Agelaius',
    'pheasant': 'Phasianus',
    'duck': 'Anas',
    'goose': 'Anser',
    'swan': 'Cygnus',
    'heron': 'Ardea',
    'crane': 'Grus'
  };

  // Mammal genera
  const mammalGenera: Record<string, string> = {
    'fox': 'Vulpes',
    'wolf': 'Canis',
    'bear': 'Ursus',
    'deer': 'Odocoileus',
    'elk': 'Cervus',
    'moose': 'Alces',
    'rabbit': 'Lepus',
    'squirrel': 'Sciurus',
    'mouse': 'Mus',
    'cat': 'Felis',
    'lion': 'Panthera',
    'tiger': 'Panthera'
  };

  // Reptile genera
  const reptileGenera: Record<string, string> = {
    'snake': 'Serpentes',
    'python': 'Python',
    'lizard': 'Lacerta',
    'gecko': 'Gekko',
    'iguana': 'Iguana',
    'turtle': 'Testudo',
    'alligator': 'Alligator',
    'crocodile': 'Crocodylus'
  };

  // Find appropriate genus based on common name
  let genus = '';
  let species = '';

  for (const word of words) {
    if (birdGenera[word]) {
      genus = birdGenera[word];
      break;
    } else if (mammalGenera[word]) {
      genus = mammalGenera[word];
      break;
    } else if (reptileGenera[word]) {
      genus = reptileGenera[word];
      break;
    }
  }

  if (!genus && words.length >= 2) {
    // Use first word as genus if no match found
    genus = words[0].charAt(0).toUpperCase() + words[0].slice(1);
    species = words[1].toLowerCase();
  } else if (!genus) {
    // Single word - create a genus
    genus = commonName.charAt(0).toUpperCase() + commonName.slice(1).toLowerCase();
    species = 'sp.';
  } else {
    // Use last word as species
    species = words[words.length - 1].toLowerCase();
  }

  return `${genus} ${species}`;
}

// Helper functions for generating more specific geographic data
function generateCountriesForSpecies(speciesName: string, category: string): string[] {
  const lowerName = speciesName.toLowerCase();

  // Australian species detection
  if (lowerName.includes('goanna') || lowerName.includes('wombat') || lowerName.includes('kangaroo') ||
      lowerName.includes('koala') || lowerName.includes('echidna') || lowerName.includes('platypus') ||
      lowerName.includes('kookaburra') || lowerName.includes('cockatoo') || lowerName.includes('lorikeet')) {
    return ['Australia'];
  }

  // North American species detection
  if (lowerName.includes('raccoon') || lowerName.includes('opossum') || lowerName.includes('cardinal') ||
      lowerName.includes('blue jay') || lowerName.includes('chipmunk') || lowerName.includes('squirrel')) {
    return ['United States', 'Canada'];
  }

  // European species detection
  if (lowerName.includes('robin') || lowerName.includes('hedgehog') || lowerName.includes('badger') ||
      lowerName.includes('fox') && !lowerName.includes('flying')) {
    return ['United Kingdom', 'France', 'Germany', 'Netherlands'];
  }

  // South American species detection
  if (lowerName.includes('tanager') || lowerName.includes('toucan') || lowerName.includes('macaw') ||
      lowerName.includes('condor') || lowerName.includes('llama') || lowerName.includes('vicuña') ||
      lowerName.includes('mountain') || lowerName.includes('andean') || lowerName.includes('scarlet')) {
    return ['Peru', 'Ecuador', 'Colombia', 'Bolivia', 'Venezuela'];
  }

  // Default to global distribution
  return ['Multiple countries'];
}

function generateStatesProvincesForSpecies(speciesName: string, category: string): string[] {
  const lowerName = speciesName.toLowerCase();

  // Australian species - provide specific states
  if (lowerName.includes('goanna') || lowerName.includes('wombat') || lowerName.includes('kangaroo') ||
      lowerName.includes('koala') || lowerName.includes('echidna') || lowerName.includes('platypus') ||
      lowerName.includes('kookaburra') || lowerName.includes('cockatoo') || lowerName.includes('lorikeet')) {

    // Sand goanna specifically
    if (lowerName.includes('sand') && lowerName.includes('goanna')) {
      return ['New South Wales', 'Queensland', 'Victoria', 'South Australia', 'Western Australia'];
    }

    // Other Australian species
    return ['New South Wales', 'Queensland', 'Victoria', 'Tasmania', 'South Australia', 'Western Australia', 'Northern Territory'];
  }

  // North American species
  if (lowerName.includes('raccoon') || lowerName.includes('opossum') || lowerName.includes('cardinal') ||
      lowerName.includes('blue jay') || lowerName.includes('chipmunk') || lowerName.includes('squirrel')) {
    return ['California', 'Texas', 'Florida', 'New York', 'Ontario', 'British Columbia'];
  }

  // European species
  if (lowerName.includes('robin') || lowerName.includes('hedgehog') || lowerName.includes('badger') ||
      lowerName.includes('fox') && !lowerName.includes('flying')) {
    return ['England', 'Scotland', 'Wales', 'Normandy', 'Bavaria', 'Tuscany'];
  }

  // South American species
  if (lowerName.includes('tanager') || lowerName.includes('toucan') || lowerName.includes('macaw') ||
      lowerName.includes('condor') || lowerName.includes('llama') || lowerName.includes('vicuña') ||
      lowerName.includes('mountain') || lowerName.includes('andean') || lowerName.includes('scarlet')) {
    return ['Cusco Peru', 'Quito Ecuador', 'Bogotá Colombia', 'La Paz Bolivia', 'Caracas Venezuela'];
  }

  // Default
  return ['Various regions'];
}

function generatePrimaryRegionForSpecies(speciesName: string, category: string): string {
  const lowerName = speciesName.toLowerCase();

  // Australian species
  if (lowerName.includes('goanna') || lowerName.includes('wombat') || lowerName.includes('kangaroo') ||
      lowerName.includes('koala') || lowerName.includes('echidna') || lowerName.includes('platypus') ||
      lowerName.includes('kookaburra') || lowerName.includes('cockatoo') || lowerName.includes('lorikeet')) {

    // Sand goanna specifically - found in arid regions
    if (lowerName.includes('sand') && lowerName.includes('goanna')) {
      return 'Arid and semi-arid Australia';
    }

    return 'Eastern Australian woodlands and forests';
  }

  // North American species
  if (lowerName.includes('raccoon') || lowerName.includes('opossum') || lowerName.includes('cardinal') ||
      lowerName.includes('blue jay') || lowerName.includes('chipmunk') || lowerName.includes('squirrel')) {
    return 'North American deciduous and mixed forests';
  }

  // European species
  if (lowerName.includes('robin') || lowerName.includes('hedgehog') || lowerName.includes('badger') ||
      lowerName.includes('fox') && !lowerName.includes('flying')) {
    return 'European temperate woodlands and countryside';
  }

  // South American species
  if (lowerName.includes('tanager') || lowerName.includes('toucan') || lowerName.includes('macaw') ||
      lowerName.includes('condor') || lowerName.includes('llama') || lowerName.includes('vicuña') ||
      lowerName.includes('mountain') || lowerName.includes('andean') || lowerName.includes('scarlet')) {
    return 'Andean highlands and cloud forests of Peru and Ecuador';
  }

  return 'Temperate forest and woodland regions';
}

function generateRelatedLocationsForSpecies(speciesName: string, category: string): string[] {
  const lowerName = speciesName.toLowerCase();

  // Australian species
  if (lowerName.includes('goanna') || lowerName.includes('wombat') || lowerName.includes('kangaroo') ||
      lowerName.includes('koala') || lowerName.includes('echidna') || lowerName.includes('platypus') ||
      lowerName.includes('kookaburra') || lowerName.includes('cockatoo') || lowerName.includes('lorikeet')) {

    // Sand goanna specifically
    if (lowerName.includes('sand') && lowerName.includes('goanna')) {
      return ['Great Western Woodlands', 'Pilbara region', 'Great Victoria Desert', 'Nullarbor Plain', 'Murray-Darling Basin'];
    }

    return ['Blue Mountains', 'Kakadu National Park', 'Great Barrier Reef region', 'Daintree Rainforest', 'Grampians National Park'];
  }

  // North American species
  if (lowerName.includes('raccoon') || lowerName.includes('opossum') || lowerName.includes('cardinal') ||
      lowerName.includes('blue jay') || lowerName.includes('chipmunk') || lowerName.includes('squirrel')) {
    return ['Yellowstone National Park', 'Great Smoky Mountains', 'Rocky Mountains', 'Appalachian Mountains', 'Pacific Northwest'];
  }

  // European species
  if (lowerName.includes('robin') || lowerName.includes('hedgehog') || lowerName.includes('badger') ||
      lowerName.includes('fox') && !lowerName.includes('flying')) {
    return ['Scottish Highlands', 'English countryside', 'Black Forest', 'Alps region', 'Scandinavian forests'];
  }

  return ['Various habitats', 'Multiple ecosystems'];
}

// AI-powered species metadata generation with multiple providers
export async function generateAISpeciesMetadata(speciesNames: string[], photoUrl?: string): Promise<SpeciesMetadata[]> {
  console.log('🤖 Starting generateAISpeciesMetadata for:', speciesNames);
  console.log('📸 Photo URL provided:', !!photoUrl);

  try {
    // Try OpenAI first
    const openaiApiKey = import.meta.env.VITE_OPENAI_API_KEY;
    console.log('🔑 OpenAI API key available:', !!openaiApiKey);

    if (openaiApiKey) {
      try {
        console.log('🚀 Trying OpenAI first...');
        return await generateWithOpenAI(speciesNames, openaiApiKey, photoUrl);
      } catch (error) {
        console.warn('❌ OpenAI failed, trying Gemini...', error);
      }
    } else {
      console.log('⚠️ No OpenAI API key, skipping to Gemini...');
    }

    // Try Gemini as fallback
    const geminiApiKey = import.meta.env.VITE_GEMINI_API_KEY;
    console.log('🔑 Gemini API key available:', !!geminiApiKey);
    console.log('🔑 Gemini API key value:', geminiApiKey ? 'PRESENT' : 'MISSING');

    if (geminiApiKey) {
      try {
        console.log('🚀 Trying Gemini...');
        const geminiResult = await generateWithGemini(speciesNames, geminiApiKey, photoUrl);
        console.log('✅ Gemini succeeded!', geminiResult);
        return geminiResult;
      } catch (error) {
        console.warn('❌ Gemini failed, using fallback data...', error);
      }
    } else {
      console.log('⚠️ No Gemini API key, skipping to fallback...');
    }

    console.warn('❌ No AI API keys available, using dummy data');
    return speciesNames.map(generateDummySpeciesData);

  } catch (error) {
    console.error('❌ Error generating AI metadata:', error);
    console.log('🔄 Falling back to dummy data for:', speciesNames);
    const fallbackData = speciesNames.map(generateDummySpeciesData);
    console.log('📋 Fallback data generated:', fallbackData);
    return fallbackData;
  }
}

// OpenAI generation function
async function generateWithOpenAI(speciesNames: string[], apiKey: string, photoUrl?: string): Promise<SpeciesMetadata[]> {
  let prompt = `Given the following list of species names, return comprehensive metadata for each as a JSON array.

IMPORTANT: Use the EXACT species name provided. If the name is ambiguous or similar to other species, prioritize the exact match. For example:
- "Bare-faced Ibis" should return data for Phimosus infuscatus (Bare-faced Ibis), NOT White-faced Ibis
- "American Robin" should return Turdus migratorius, NOT European Robin

For each species, provide ALL of the following fields:

BASIC IDENTIFICATION:
- name: The common name
- common_name: Alternative common name (if different from name)
- scientific_name: The scientific name (genus and species)
- family: Taxonomic family

PHYSICAL CHARACTERISTICS:
- description: A detailed description (3-4 sentences)
- size_cm: Size range in centimeters (e.g., "15-20" for length/height)
- weight_g: Weight range in grams (e.g., "50-100")
- size_description: Descriptive size (e.g., "Small songbird", "Large mammal")

BIOLOGICAL INFORMATION:
- habitat: Where this species lives
- diet: What this species eats
- behavior: Key behaviors and characteristics
- lifespan_years: Typical lifespan as integer
- migration_pattern: Migration behavior (if applicable)
- breeding_season: When they breed

CONSERVATION:
- conservation_status: IUCN status (Least Concern, Near Threatened, Vulnerable, Endangered, Critically Endangered)
- threat_level: Main threats (if any)
- population_trend: Increasing, Stable, Decreasing, or Unknown
- conservation_actions: Conservation efforts (if any)

CLASSIFICATION:
- category: Animal kingdom category (Mammals, Birds, Reptiles, Amphibians, Fish, Invertebrates, Plants)
- taxonomy_order: Taxonomic order
- taxonomy_subfamily: Taxonomic subfamily (if applicable)
- taxonomy_genus: Genus name
- common_group: Common grouping (e.g., "Songbirds", "Big Cats")
- related_groups: Related species groups

EXTERNAL IDs (provide if known, otherwise null):
- itis_tsn: ITIS Taxonomic Serial Number
- gbif_id: GBIF species ID
- ebird_code: eBird species code (for birds)
- inat_id: iNaturalist taxon ID
- avibase_id: Avibase ID (for birds)

ADDITIONAL:
- tags: Array of relevant tags/keywords
- related_locations: Array of SPECIFIC countries/regions where found (e.g., ["Peru", "Ecuador", "Colombia"])
- regions: Geographic regions as SPECIFIC text (e.g., "Andean highlands of Peru and Ecuador" NOT "South America")
- notes: Additional interesting notes about the species
- fun_facts: Array of 4-6 interesting fun facts about the species
- ai_fun_facts: Same as fun_facts (duplicate for database compatibility)

ENHANCED GEOGRAPHIC DATA (BE VERY SPECIFIC - PROVIDE STATE/PROVINCE LEVEL DETAIL):
- countries: Array of specific countries where found (e.g., ["United States", "Canada", "Mexico"])
- states_provinces: Array of states/provinces for granular location. ALWAYS provide specific states/provinces, not just countries. Examples:
  * Australian species: ["New South Wales", "Queensland", "Victoria", "Western Australia", "South Australia"]
  * US species: ["California", "Oregon", "Washington", "Nevada", "Arizona"]
  * Canadian species: ["British Columbia", "Alberta", "Ontario", "Quebec"]
  * European species: ["England", "Scotland", "Bavaria", "Tuscany", "Provence"]
- geographic_scope: One of "global", "continental", "national", "regional", "local"
- primary_region: Main geographic region with state-level specificity (e.g., "Eastern Australian states", "Southwestern United States", "Northern European countries")
- habitat_specificity: One of "endemic", "specialized", "general", "widespread"
- related_locations: Array of specific regions/habitats (e.g., ["Great Barrier Reef", "Blue Mountains", "Kakadu National Park"] for Australian species)

Species names: ${speciesNames.join(', ')}

CRITICAL GEOGRAPHIC REQUIREMENT: For geographic data, NEVER use broad regions. Always provide specific states, provinces, or regions within countries. Examples:
- Instead of "Australia", use ["New South Wales", "Queensland", "Victoria", "Western Australia", "South Australia"]
- Instead of "United States", use ["California", "Texas", "Florida", "New York", "Oregon"]
- Instead of "Europe", use ["England", "Scotland", "France", "Germany", "Italy"]
- Instead of "Canada", use ["British Columbia", "Alberta", "Ontario", "Quebec"]
- Instead of "South America", use ["Peru", "Ecuador", "Colombia", "Bolivia", "Venezuela"]
- Instead of "Andean region", use ["Cusco Peru", "Quito Ecuador", "Bogotá Colombia", "La Paz Bolivia"]
- For primary_region field, use specific areas like "Andean highlands of Peru and Ecuador" NOT "Andean region of South America"

SPECIFIC EXAMPLE FOR TANAGERS:
For "Scarlet-bellied Mountain Tanager":
- countries: ["Peru", "Ecuador", "Colombia"]
- states_provinces: ["Cusco Peru", "Quito Ecuador", "Bogotá Colombia"]
- primary_region: "Andean cloud forests of Peru and Ecuador"
- regions: "Andean cloud forests of Peru and Ecuador"
- related_locations: ["Peru", "Ecuador", "Colombia"]

${speciesNames.map(name => getSpeciesDisambiguation(name)).join('')}

${photoUrl ? `\n\nIMAGE ANALYSIS: A photo is provided at ${photoUrl}. Use this image to verify the species identification and ensure accuracy. The image should help confirm the exact species being requested.` : ''}

Return only valid JSON array with ALL fields populated (use null for unknown external IDs). Make sure fun_facts contains interesting, engaging facts that would fascinate wildlife enthusiasts.

CRITICAL: Ensure the species name in the response exactly matches the requested species name, not a similar species.`;

  console.log('📡 Making OpenAI API request...');

  // Temporarily disable vision to test text-only generation
  let adjustedPrompt = prompt;
  if (photoUrl) {
    console.log('📸 Image analysis requested but temporarily disabled to avoid rate limits');
    adjustedPrompt += `\n\nNote: Image analysis requested for photo at: ${photoUrl} (analysis temporarily disabled to conserve API quota)`;
  }

  const userMessage = {
    role: 'user',
    content: adjustedPrompt
  };

  const model = 'gpt-4o-mini'; // Use text-only model
  console.log('🤖 Using model:', model, '(text only - vision temporarily disabled)');

  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: model,
      messages: [
        {
          role: 'system',
          content: 'You are a wildlife expert. Provide accurate, scientific information about species. Return only valid JSON. Pay careful attention to exact species names and avoid confusion between similar species.'
        },
        userMessage
      ],
      temperature: 0.3,
      max_tokens: 1500
    })
  });

    console.log('📊 OpenAI Response status:', response.status, response.statusText);

    if (!response.ok) {
      let errorMessage = `OpenAI API error: ${response.status}`;

      if (response.status === 429) {
        errorMessage += ' (Rate limit exceeded - please wait or check your OpenAI billing)';
        console.error('❌ Rate limit exceeded! You may need to wait or check your OpenAI billing.');
      } else if (response.status === 401) {
        errorMessage += ' (Invalid API key)';
        console.error('❌ Invalid OpenAI API key!');
      } else if (response.status === 403) {
        errorMessage += ' (Insufficient permissions)';
        console.error('❌ Insufficient OpenAI permissions!');
      }

      console.error('❌ OpenAI API failed:', errorMessage);
      throw new Error(errorMessage);
    }

    console.log('✅ OpenAI API response received successfully');
    const data = await response.json() as { choices: Array<{ message: { content: string } }> };
    console.log('📋 OpenAI response data:', data);

    const content = data.choices[0]?.message?.content;
    console.log('📝 OpenAI content:', content);

    if (!content) {
      console.error('❌ No content received from OpenAI');
      throw new Error('No content received from OpenAI');
    }

    // Parse the JSON response
    console.log('🔄 Parsing JSON content...');
    const parsedData = JSON.parse(content) as Array<Partial<SpeciesMetadata>>;
    console.log('✅ Parsed species data:', parsedData);

    if (!Array.isArray(parsedData)) {
      console.error('❌ Invalid response format from OpenAI - not an array');
      throw new Error('Invalid response format from OpenAI');
    }

    const validatedData = parsedData.map((item: Partial<SpeciesMetadata>) => ({
      // Basic identification
      name: item.name || '',
      common_name: item.common_name || item.name || '',
      scientific_name: item.scientific_name || '',
      family: item.family || null,

      // Physical characteristics
      description: item.description || '',
      size_cm: item.size_cm || null,
      weight_g: item.weight_g || null,
      size_description: item.size_description || null,

      // Biological information
      habitat: item.habitat || '',
      diet: item.diet || '',
      behavior: item.behavior || null,
      lifespan_years: item.lifespan_years || null,
      migration_pattern: item.migration_pattern || null,
      breeding_season: item.breeding_season || null,

      // Conservation
      conservation_status: item.conservation_status || 'Unknown',
      threat_level: item.threat_level || null,
      population_trend: item.population_trend || null,
      conservation_actions: item.conservation_actions || null,

      // Classification
      category: item.category || 'Unknown',
      taxonomy_order: item.taxonomy_order || null,
      taxonomy_subfamily: item.taxonomy_subfamily || null,
      taxonomy_genus: item.taxonomy_genus || null,
      common_group: item.common_group || null,
      related_groups: item.related_groups || null,

      // External identifiers
      itis_tsn: item.itis_tsn || null,
      gbif_id: item.gbif_id || null,
      ebird_code: item.ebird_code || null,
      inat_id: item.inat_id || null,
      avibase_id: item.avibase_id || null,

      // Enhanced geographic data
      countries: Array.isArray(item.countries) ? item.countries : ['Multiple countries'],
      states_provinces: Array.isArray(item.states_provinces) ? item.states_provinces : ['Various regions'],
      geographic_scope: item.geographic_scope || 'regional',
      primary_region: item.primary_region || 'Global distribution',
      habitat_specificity: item.habitat_specificity || 'general',

      // Additional metadata
      tags: Array.isArray(item.tags) ? item.tags : [],
      related_locations: Array.isArray(item.related_locations) ? item.related_locations : ['Various habitats'],
      regions: item.regions || item.primary_region || 'Global distribution',
      notes: item.notes || null,

      // Fun facts and AI metadata
      fun_facts: Array.isArray(item.fun_facts) ? item.fun_facts : [],
      ai_fun_facts: Array.isArray(item.ai_fun_facts) ? item.ai_fun_facts : (Array.isArray(item.fun_facts) ? item.fun_facts : []),
      ai_generated: true,
      ai_confidence: 0.8
    }));

  console.log('🎯 Final validated OpenAI data:', validatedData);
  return validatedData;
}

// Gemini generation function
async function generateWithGemini(speciesNames: string[], apiKey: string, photoUrl?: string): Promise<SpeciesMetadata[]> {
  let prompt = `Given the following list of species names, return comprehensive metadata for each as a JSON array.

IMPORTANT: Use the EXACT species name provided. If the name is ambiguous or similar to other species, prioritize the exact match. For example:
- "Bare-faced Ibis" should return data for Phimosus infuscatus (Bare-faced Ibis), NOT White-faced Ibis
- "American Robin" should return Turdus migratorius, NOT European Robin

For each species, provide ALL of the following fields:

BASIC IDENTIFICATION:
- name: The common name
- common_name: Alternative common name (if different from name)
- scientific_name: The scientific name (genus and species)
- family: Taxonomic family

PHYSICAL CHARACTERISTICS:
- description: A detailed description (3-4 sentences)
- size_cm: Size range in centimeters (e.g., "15-20" for length/height)
- weight_g: Weight range in grams (e.g., "50-100")
- size_description: Descriptive size (e.g., "Small songbird", "Large mammal")

BIOLOGICAL INFORMATION:
- habitat: Where this species lives
- diet: What this species eats
- behavior: Key behaviors and characteristics
- lifespan_years: Typical lifespan as integer
- migration_pattern: Migration behavior (if applicable)
- breeding_season: When they breed

CONSERVATION:
- conservation_status: IUCN status (Least Concern, Near Threatened, Vulnerable, Endangered, Critically Endangered)
- threat_level: Main threats (if any)
- population_trend: Increasing, Stable, Decreasing, or Unknown
- conservation_actions: Conservation efforts (if any)

CLASSIFICATION:
- category: Animal kingdom category (Mammals, Birds, Reptiles, Amphibians, Fish, Invertebrates, Plants)
- taxonomy_order: Taxonomic order
- taxonomy_subfamily: Taxonomic subfamily (if applicable)
- taxonomy_genus: Genus name
- common_group: Common grouping (e.g., "Songbirds", "Big Cats")
- related_groups: Related species groups

EXTERNAL IDs (provide if known, otherwise null):
- itis_tsn: ITIS Taxonomic Serial Number
- gbif_id: GBIF species ID
- ebird_code: eBird species code (for birds)
- inat_id: iNaturalist taxon ID
- avibase_id: Avibase ID (for birds)

ADDITIONAL:
- tags: Array of relevant tags/keywords
- related_locations: Array of SPECIFIC countries/regions where found (e.g., ["Peru", "Ecuador", "Colombia"])
- regions: Geographic regions as SPECIFIC text (e.g., "Andean highlands of Peru and Ecuador" NOT "South America")
- notes: Additional interesting notes about the species
- fun_facts: Array of 4-6 interesting fun facts about the species
- ai_fun_facts: Same as fun_facts (duplicate for database compatibility)

ENHANCED GEOGRAPHIC DATA (BE VERY SPECIFIC - PROVIDE STATE/PROVINCE LEVEL DETAIL):
- countries: Array of specific countries where found (e.g., ["United States", "Canada", "Mexico"])
- states_provinces: Array of states/provinces for granular location. ALWAYS provide specific states/provinces, not just countries. Examples:
  * Australian species: ["New South Wales", "Queensland", "Victoria", "Western Australia", "South Australia"]
  * US species: ["California", "Oregon", "Washington", "Nevada", "Arizona"]
  * Canadian species: ["British Columbia", "Alberta", "Ontario", "Quebec"]
  * European species: ["England", "Scotland", "Bavaria", "Tuscany", "Provence"]
- geographic_scope: One of "global", "continental", "national", "regional", "local"
- primary_region: Main geographic region with state-level specificity (e.g., "Eastern Australian states", "Southwestern United States", "Northern European countries")
- habitat_specificity: One of "endemic", "specialized", "general", "widespread"
- related_locations: Array of specific regions/habitats (e.g., ["Great Barrier Reef", "Blue Mountains", "Kakadu National Park"] for Australian species)

Species names: ${speciesNames.join(', ')}

CRITICAL GEOGRAPHIC REQUIREMENT: For geographic data, NEVER use broad regions. Always provide specific states, provinces, or regions within countries. Examples:
- Instead of "Australia", use ["New South Wales", "Queensland", "Victoria", "Western Australia", "South Australia"]
- Instead of "United States", use ["California", "Texas", "Florida", "New York", "Oregon"]
- Instead of "Europe", use ["England", "Scotland", "France", "Germany", "Italy"]
- Instead of "Canada", use ["British Columbia", "Alberta", "Ontario", "Quebec"]
- Instead of "South America", use ["Peru", "Ecuador", "Colombia", "Bolivia", "Venezuela"]
- Instead of "Andean region", use ["Cusco Peru", "Quito Ecuador", "Bogotá Colombia", "La Paz Bolivia"]
- For primary_region field, use specific areas like "Andean highlands of Peru and Ecuador" NOT "Andean region of South America"

SPECIFIC EXAMPLE FOR TANAGERS:
For "Scarlet-bellied Mountain Tanager":
- countries: ["Peru", "Ecuador", "Colombia"]
- states_provinces: ["Cusco Peru", "Quito Ecuador", "Bogotá Colombia"]
- primary_region: "Andean cloud forests of Peru and Ecuador"
- regions: "Andean cloud forests of Peru and Ecuador"
- related_locations: ["Peru", "Ecuador", "Colombia"]

${speciesNames.map(name => getSpeciesDisambiguation(name)).join('')}

${photoUrl ? `\n\nIMAGE ANALYSIS: A photo is provided. Use this image to verify the species identification and ensure accuracy. The image should help confirm the exact species being requested.` : ''}

Return only valid JSON array with ALL fields populated (use null for unknown external IDs). Make sure fun_facts contains interesting, engaging facts that would fascinate wildlife enthusiasts.

CRITICAL: Ensure the species name in the response exactly matches the requested species name, not a similar species.`;

  console.log('📡 Making Gemini API request...');

  // Prepare content parts - include image if provided
  const parts = [{
    text: prompt
  }];

  if (photoUrl) {
    console.log('📸 Image analysis requested but temporarily disabled to avoid rate limits');
    // Temporarily disable image processing to avoid rate limits
    parts[0].text += `\n\nNote: Image analysis requested for photo at: ${photoUrl} (analysis temporarily disabled to conserve API quota)`;
  }

  const model = 'gemini-1.5-flash-latest'; // Use faster, cheaper model for text-only
  console.log('🤖 Using Gemini model:', model, '(text only - vision temporarily disabled)');

  const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      contents: [{
        parts: parts
      }],
      generationConfig: {
        temperature: 0.3,
        maxOutputTokens: 1500,
        responseMimeType: "application/json"
      }
    })
  });

  console.log('📊 Gemini Response status:', response.status, response.statusText);

  if (!response.ok) {
    let errorMessage = `Gemini API error: ${response.status}`;

    if (response.status === 429) {
      errorMessage += ' (Rate limit exceeded)';
      console.error('❌ Gemini rate limit exceeded!');
    } else if (response.status === 401) {
      errorMessage += ' (Invalid API key)';
      console.error('❌ Invalid Gemini API key!');
    } else if (response.status === 403) {
      errorMessage += ' (Insufficient permissions)';
      console.error('❌ Insufficient Gemini permissions!');
    }

    console.error('❌ Gemini API failed:', errorMessage);
    throw new Error(errorMessage);
  }

  console.log('✅ Gemini API response received successfully');
  const data = await response.json();
  console.log('📋 Gemini response data:', data);

  const content = data.candidates?.[0]?.content?.parts?.[0]?.text;
  console.log('📝 Gemini content:', content);

  if (!content) {
    console.error('❌ No content received from Gemini');
    throw new Error('No content received from Gemini');
  }

  // Parse the JSON response
  console.log('🔄 Parsing Gemini JSON content...');
  const rawParsedData = JSON.parse(content) as Array<any>;
  console.log('✅ Parsed Gemini species data:', rawParsedData);

  if (!Array.isArray(rawParsedData)) {
    console.error('❌ Invalid response format from Gemini - not an array');
    throw new Error('Invalid response format from Gemini');
  }

  // Flatten nested structure if Gemini returned grouped data
  const parsedData = rawParsedData.map((item: any) => {
    // Check if this is a nested structure with sections
    if (item['BASIC IDENTIFICATION'] || item['PHYSICAL CHARACTERISTICS']) {
      console.log('🔄 Flattening nested Gemini response structure...');
      return {
        // Basic identification
        name: item['BASIC IDENTIFICATION']?.name || item.name || '',
        common_name: item['BASIC IDENTIFICATION']?.common_name || item.common_name || '',
        scientific_name: item['BASIC IDENTIFICATION']?.scientific_name || item.scientific_name || '',
        family: item['BASIC IDENTIFICATION']?.family || item.family || null,

        // Physical characteristics
        description: item['PHYSICAL CHARACTERISTICS']?.description || item.description || '',
        size_cm: item['PHYSICAL CHARACTERISTICS']?.size_cm || item.size_cm || null,
        weight_g: item['PHYSICAL CHARACTERISTICS']?.weight_g || item.weight_g || null,
        size_description: item['PHYSICAL CHARACTERISTICS']?.size_description || item.size_description || null,

        // Biological information
        habitat: item['BIOLOGICAL INFORMATION']?.habitat || item.habitat || '',
        diet: item['BIOLOGICAL INFORMATION']?.diet || item.diet || '',
        behavior: item['BIOLOGICAL INFORMATION']?.behavior || item.behavior || null,
        lifespan_years: item['BIOLOGICAL INFORMATION']?.lifespan_years || item.lifespan_years || null,
        migration_pattern: item['BIOLOGICAL INFORMATION']?.migration_pattern || item.migration_pattern || null,
        breeding_season: item['BIOLOGICAL INFORMATION']?.breeding_season || item.breeding_season || null,

        // Conservation
        conservation_status: item['CONSERVATION']?.conservation_status || item.conservation_status || 'Unknown',
        threat_level: item['CONSERVATION']?.threat_level || item.threat_level || null,
        population_trend: item['CONSERVATION']?.population_trend || item.population_trend || null,
        conservation_actions: item['CONSERVATION']?.conservation_actions || item.conservation_actions || null,

        // Classification
        category: item['CLASSIFICATION']?.category || item.category || 'Unknown',
        taxonomy_order: item['CLASSIFICATION']?.taxonomy_order || item.taxonomy_order || null,
        taxonomy_subfamily: item['CLASSIFICATION']?.taxonomy_subfamily || item.taxonomy_subfamily || null,
        taxonomy_genus: item['CLASSIFICATION']?.taxonomy_genus || item.taxonomy_genus || null,
        common_group: item['CLASSIFICATION']?.common_group || item.common_group || null,
        related_groups: item['CLASSIFICATION']?.related_groups || item.related_groups || null,

        // External identifiers
        itis_tsn: item['EXTERNAL IDs']?.itis_tsn || item.itis_tsn || null,
        gbif_id: item['EXTERNAL IDs']?.gbif_id || item.gbif_id || null,
        ebird_code: item['EXTERNAL IDs']?.ebird_code || item.ebird_code || null,
        inat_id: item['EXTERNAL IDs']?.inat_id || item.inat_id || null,
        avibase_id: item['EXTERNAL IDs']?.avibase_id || item.avibase_id || null,

        // Additional metadata
        tags: item['ADDITIONAL']?.tags || item.tags || [],
        related_locations: item['ADDITIONAL']?.related_locations || item.related_locations || [],
        regions: item['ADDITIONAL']?.regions || item.regions || null,
        notes: item['ADDITIONAL']?.notes || item.notes || null,

        // Fun facts and AI metadata
        fun_facts: item['ADDITIONAL']?.fun_facts || item.fun_facts || [],
        ai_fun_facts: item['ADDITIONAL']?.ai_fun_facts || item.ai_fun_facts || item['ADDITIONAL']?.fun_facts || item.fun_facts || [],
        ai_generated: true,
        ai_confidence: 0.8
      };
    }

    // Return as-is if already flat
    return item;
  });

  const validatedData = parsedData.map((item: Partial<SpeciesMetadata>) => ({
    // Basic identification
    name: item.name || '',
    common_name: item.common_name || item.name || '',
    scientific_name: item.scientific_name || '',
    family: item.family || null,

    // Physical characteristics
    description: item.description || '',
    size_cm: item.size_cm || null,
    weight_g: item.weight_g || null,
    size_description: item.size_description || null,

    // Biological information
    habitat: item.habitat || '',
    diet: item.diet || '',
    behavior: item.behavior || null,
    lifespan_years: item.lifespan_years || null,
    migration_pattern: item.migration_pattern || null,
    breeding_season: item.breeding_season || null,

    // Conservation
    conservation_status: item.conservation_status || 'Unknown',
    threat_level: item.threat_level || null,
    population_trend: item.population_trend || null,
    conservation_actions: item.conservation_actions || null,

    // Classification
    category: item.category || 'Unknown',
    taxonomy_order: item.taxonomy_order || null,
    taxonomy_subfamily: item.taxonomy_subfamily || null,
    taxonomy_genus: item.taxonomy_genus || null,
    common_group: item.common_group || null,
    related_groups: item.related_groups || null,

    // External identifiers
    itis_tsn: item.itis_tsn || null,
    gbif_id: item.gbif_id || null,
    ebird_code: item.ebird_code || null,
    inat_id: item.inat_id || null,
    avibase_id: item.avibase_id || null,

    // Enhanced geographic data
    countries: Array.isArray(item.countries) ? item.countries : ['Multiple countries'],
    states_provinces: Array.isArray(item.states_provinces) ? item.states_provinces : ['Various regions'],
    geographic_scope: item.geographic_scope || 'regional',
    primary_region: item.primary_region || 'Global distribution',
    habitat_specificity: item.habitat_specificity || 'general',

    // Additional metadata
    tags: Array.isArray(item.tags) ? item.tags : [],
    related_locations: Array.isArray(item.related_locations) ? item.related_locations : ['Various habitats'],
    regions: item.regions || item.primary_region || 'Global distribution',
    notes: item.notes || null,

    // Fun facts and AI metadata
    fun_facts: Array.isArray(item.fun_facts) ? item.fun_facts : [],
    ai_fun_facts: Array.isArray(item.ai_fun_facts) ? item.ai_fun_facts : (Array.isArray(item.fun_facts) ? item.fun_facts : []),
    ai_generated: true,
    ai_confidence: 0.8
  }));

  console.log('🎯 Final validated Gemini data:', validatedData);
  return validatedData;
}

// Convert enriched species to Supabase insert format
export function prepareSpeciesForInsert(enrichedSpecies: EnrichedSpecies[]): Record<string, unknown>[] {
  return enrichedSpecies.map(species => ({
    // Basic identification
    name: species.name,
    common_name: species.common_name,
    scientific_name: species.scientific_name,
    family: species.family,

    // Physical characteristics
    description: species.description,
    size_cm: species.size_cm,
    weight_g: species.weight_g,
    size_description: species.size_description,

    // Biological information
    habitat: species.habitat,
    diet: species.diet,
    behavior: species.behavior,
    lifespan_years: species.lifespan_years,
    migration_pattern: species.migration_pattern,
    breeding_season: species.breeding_season,

    // Conservation
    conservation_status: species.conservation_status,
    threat_level: species.threat_level,
    population_trend: species.population_trend,
    conservation_actions: species.conservation_actions,

    // Classification
    category: species.category,
    taxonomy_order: species.taxonomy_order,
    taxonomy_subfamily: species.taxonomy_subfamily,
    taxonomy_genus: species.taxonomy_genus,
    common_group: species.common_group,
    related_groups: species.related_groups,

    // External identifiers
    itis_tsn: species.itis_tsn,
    gbif_id: species.gbif_id,
    ebird_code: species.ebird_code,
    inat_id: species.inat_id,
    avibase_id: species.avibase_id,

    // Additional metadata
    tags: species.tags,
    regions: species.regions,
    notes: species.notes,

    // Fun facts and AI metadata
    ai_fun_facts: species.ai_fun_facts,
    fun_facts_field: species.fun_facts,
    ai_generated: species.ai_generated || true,
    ai_confidence: species.ai_confidence || 0.8,

    // System fields
    published: species.published,
    featured: false,
    photo_count: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }));
}

// Insert fun facts into the fun_facts table
export async function insertFunFacts(speciesId: string, funFacts: string[]): Promise<{ success: boolean; inserted: number; errors: string[] }> {
  const errors: string[] = [];
  let inserted = 0;

  try {
    if (!funFacts || funFacts.length === 0) {
      return { success: true, inserted: 0, errors: [] };
    }

    const factsToInsert = funFacts
      .filter(fact => fact && fact.trim().length > 10) // Only meaningful facts
      .map(fact => ({
        species_id: speciesId,
        fact: fact.trim()
      }));

    if (factsToInsert.length === 0) {
      return { success: true, inserted: 0, errors: [] };
    }

    const { data, error } = await supabase
      .from('fun_facts')
      .insert(factsToInsert)
      .select();

    if (error) {
      errors.push(`Fun facts database error: ${error.message}`);
      return { success: false, inserted: 0, errors };
    }

    inserted = data?.length || 0;
    return { success: true, inserted, errors };

  } catch (error) {
    errors.push(`Unexpected fun facts error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return { success: false, inserted: 0, errors };
  }
}

// Insert species into Supabase
export async function insertSpeciesToSupabase(species: EnrichedSpecies[]): Promise<{ success: boolean; inserted: number; errors: string[] }> {
  const errors: string[] = [];
  let inserted = 0;

  try {
    const speciesToInsert = prepareSpeciesForInsert(species);

    const { data, error } = await supabase
      .from('species_v2')
      .insert(speciesToInsert)
      .select();

    if (error) {
      errors.push(`Database error: ${error.message}`);
      return { success: false, inserted: 0, errors };
    }

    inserted = data?.length || 0;

    // Insert fun facts for each species
    if (data && data.length > 0) {
      for (let i = 0; i < data.length; i++) {
        const insertedSpecies = data[i];
        const originalSpecies = species[i];

        if (originalSpecies.fun_facts && originalSpecies.fun_facts.length > 0) {
          const funFactsResult = await insertFunFacts(insertedSpecies.id, originalSpecies.fun_facts);
          if (!funFactsResult.success) {
            errors.push(...funFactsResult.errors);
          }
        }
      }
    }

    return { success: true, inserted, errors };

  } catch (error) {
    errors.push(`Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return { success: false, inserted: 0, errors };
  }
}

// Parse CSV content
export function parseCSVContent(content: string): string[] {
  const lines = content.trim().split('\n');
  return lines
    .map(line => line.trim())
    .filter(line => line.length > 0)
    .map(line => {
      // Handle CSV format - take first column if comma-separated
      const parts = line.split(',');
      return parts[0].trim().replace(/"/g, '');
    });
}

// Parse JSON content
export function parseJSONContent(content: string): string[] {
  try {
    const data = JSON.parse(content) as unknown;
    
    if (Array.isArray(data)) {
      // If it's an array of strings
      if (typeof data[0] === 'string') {
        return data as string[];
      }
      // If it's an array of objects with name field
      if (typeof data[0] === 'object' && data[0] && typeof (data[0] as Record<string, unknown>).name === 'string') {
        return (data as Array<Record<string, unknown>>).map((item: Record<string, unknown>) => item.name as string);
      }
    }
    
    // If it's an object with a species array
    if (data && typeof data === 'object' && 'species' in data && Array.isArray((data as Record<string, unknown>).species)) {
      const speciesArray = (data as Record<string, unknown>).species as unknown[];
      return speciesArray.map((item: unknown) => typeof item === 'string' ? item : (item as Record<string, unknown>).name as string);
    }
    
    throw new Error('Invalid JSON format');
  } catch (error) {
    throw new Error(`Failed to parse JSON: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
} 