{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "build:analyze": "vite build --mode production && npx vite-bundle-analyzer dist", "build:preview": "vite build && vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --fix", "preview": "vite preview", "type-check": "tsc --noEmit", "audit": "npm audit", "audit:fix": "npm audit fix", "update-deps": "npm update", "clean": "rm -rf dist node_modules/.vite", "prebuild": "npm run clean", "validate-urls": "tsx scripts/url-validator.ts", "cleanup-data": "tsx scripts/data-cleanup.ts", "gen-types": "npx supabase gen types typescript --project-id $(grep VITE_SUPABASE_URL .env | cut -d'=' -f2 | cut -d'.' -f1) > src/integrations/supabase/types.ts", "script:find-broken-photos": "ts-node src/scripts/manualPhotoRecovery.ts", "script:update-photo-urls": "ts-node src/scripts/updatePhotoUrls.ts"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@supabase/supabase-js": "^2.50.1", "@tanstack/react-query": "^5.51.15", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "commander": "^12.1.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "gtag": "^1.0.1", "input-otp": "^1.2.4", "lucide-react": "^0.417.0", "next-themes": "^0.3.0", "openai": "^5.6.0", "pg": "^8.16.2", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-device-detect": "^2.2.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-touch-backend": "^16.0.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss": "^3.4.7", "tailwindcss-animate": "^1.0.7", "typescript": "^5.5.4", "uuid": "^11.1.0", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/pg": "^8.15.4", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "dotenv": "^16.5.0", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "terser": "^5.43.1", "ts-node": "^10.9.2", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}