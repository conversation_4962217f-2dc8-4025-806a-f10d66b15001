-- DIAGNOSTIC SCRIPT - Check what tables and columns actually exist
-- Run this first to see what we're working with

-- 1. Check if species_locations table exists and its structure
SELECT 'species_locations table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'species_locations' 
ORDER BY ordinal_position;

-- 2. Check if the table exists at all
SELECT 'Does species_locations table exist?' as question;
SELECT EXISTS (
  SELECT FROM information_schema.tables 
  WHERE table_name = 'species_locations'
) as table_exists;

-- 3. Check what hotspot-related tables exist
SELECT 'Hotspot-related tables that exist:' as info;
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('locations', 'species_locations', 'location_photos', 'observations', 'checklists', 'species_occurrence');

-- 4. Check locations table structure
SELECT 'locations table structure:' as info;
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'locations' 
ORDER BY ordinal_position;

-- 5. Check if we have any data in locations
SELECT 'Sample locations data:' as info;
SELECT id, name, country, state_province, published 
FROM locations 
LIMIT 5;

-- 6. Check what views exist
SELECT 'Existing views:' as info;
SELECT table_name 
FROM information_schema.views 
WHERE table_schema = 'public' 
AND table_name IN ('species_location_details', 'location_species_summary', 'hotspot_highlights', 'unassigned_species', 'locations_without_species');

-- 7. If species_locations exists, check if it has any data
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'species_locations') THEN
    RAISE NOTICE 'species_locations table exists, checking data...';
    PERFORM 1; -- We'll add a data check in the next query
  ELSE
    RAISE NOTICE 'species_locations table does NOT exist!';
  END IF;
END $$;

-- 8. Check species_locations data if table exists
SELECT 'species_locations data count:' as info;
SELECT 
  CASE 
    WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'species_locations') 
    THEN (SELECT COUNT(*)::text FROM species_locations)
    ELSE 'Table does not exist'
  END as row_count;

-- 9. Check species_v2 table (needed for foreign keys)
SELECT 'species_v2 table exists?' as question;
SELECT EXISTS (
  SELECT FROM information_schema.tables 
  WHERE table_name = 'species_v2'
) as table_exists;

-- 10. Show any existing policies on species_locations
SELECT 'RLS policies on species_locations:' as info;
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE tablename = 'species_locations';
