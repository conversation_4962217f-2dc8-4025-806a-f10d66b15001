-- =====================================================
-- FUN FACTS STATUS CHECK
-- =====================================================
-- Run these queries to check the current state of fun facts

-- 1. Check if fun_facts table exists
SELECT 
  table_name, 
  table_type 
FROM information_schema.tables 
WHERE table_name = 'fun_facts' AND table_schema = 'public';

-- 2. Check species with JSONB fun facts data
SELECT 
  COUNT(*) as species_with_ai_fun_facts
FROM species_v2 
WHERE ai_fun_facts IS NOT NULL;

SELECT 
  COUNT(*) as species_with_fun_facts_field
FROM species_v2 
WHERE fun_facts_field IS NOT NULL;

-- 3. Sample of current JSONB fun facts (to see the problem)
SELECT 
  name,
  ai_fun_facts,
  fun_facts_field
FROM species_v2 
WHERE ai_fun_facts IS NOT NULL OR fun_facts_field IS NOT NULL
LIMIT 3;

-- 4. If fun_facts table exists, check its contents
SELECT COUNT(*) as total_fun_facts FROM fun_facts;

-- 5. Sample of migrated fun facts (if any)
SELECT 
  s.name as species_name,
  ff.fact
FROM fun_facts ff
JOIN species_v2 s ON ff.species_id = s.id
LIMIT 5;
