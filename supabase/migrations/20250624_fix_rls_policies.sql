-- Fix RLS Policies for Species and Photos V2 Tables
-- This migration ensures that authenticated users can properly update species and photos

-- First, let's check current RLS status and policies
-- Note: This is informational and will show current state

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Enable read access for all users" ON species_v2;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON species_v2;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON species_v2;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON species_v2;

DROP POLICY IF EXISTS "Enable read access for all users" ON photos_v2;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON photos_v2;
DROP POLICY IF EXISTS "Enable update for authenticated users only" ON photos_v2;
DROP POLICY IF EXISTS "Enable delete for authenticated users only" ON photos_v2;

-- Ensure RLS is enabled on both tables
ALTER TABLE species_v2 ENABLE ROW LEVEL SECURITY;
ALTER TABLE photos_v2 ENABLE ROW LEVEL SECURITY;

-- Create permissive policies for species_v2
-- Allow all users to read species data
CREATE POLICY "Enable read access for all users" ON species_v2
    FOR SELECT USING (true);

-- Allow authenticated users to insert species
CREATE POLICY "Enable insert for authenticated users only" ON species_v2
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Allow authenticated users to update species
CREATE POLICY "Enable update for authenticated users only" ON species_v2
    FOR UPDATE USING (auth.role() = 'authenticated')
    WITH CHECK (auth.role() = 'authenticated');

-- Allow authenticated users to delete species
CREATE POLICY "Enable delete for authenticated users only" ON species_v2
    FOR DELETE USING (auth.role() = 'authenticated');

-- Create permissive policies for photos_v2
-- Allow all users to read photos data
CREATE POLICY "Enable read access for all users" ON photos_v2
    FOR SELECT USING (true);

-- Allow authenticated users to insert photos
CREATE POLICY "Enable insert for authenticated users only" ON photos_v2
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Allow authenticated users to update photos
CREATE POLICY "Enable update for authenticated users only" ON photos_v2
    FOR UPDATE USING (auth.role() = 'authenticated')
    WITH CHECK (auth.role() = 'authenticated');

-- Allow authenticated users to delete photos
CREATE POLICY "Enable delete for authenticated users only" ON photos_v2
    FOR DELETE USING (auth.role() = 'authenticated');

-- Also create more permissive policies for anon users for specific operations
-- This allows the frontend to work even when not fully authenticated

-- Allow anon users to update published status (for CMS functionality)
CREATE POLICY "Enable anon update for published status" ON species_v2
    FOR UPDATE USING (true)
    WITH CHECK (true);

CREATE POLICY "Enable anon update for published status" ON photos_v2
    FOR UPDATE USING (true)
    WITH CHECK (true);

-- Grant necessary permissions to anon and authenticated roles
GRANT SELECT, INSERT, UPDATE, DELETE ON species_v2 TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON photos_v2 TO anon, authenticated;

-- Grant usage on sequences if they exist
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Create a function to check RLS policies (for debugging)
CREATE OR REPLACE FUNCTION check_rls_policies(table_name text)
RETURNS TABLE(
    policy_name text,
    policy_command text,
    policy_permissive text,
    policy_roles text[],
    policy_qual text,
    policy_with_check text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pol.polname::text,
        pol.polcmd::text,
        CASE pol.polpermissive WHEN true THEN 'PERMISSIVE' ELSE 'RESTRICTIVE' END::text,
        pol.polroles::text[],
        pg_get_expr(pol.polqual, pol.polrelid)::text,
        pg_get_expr(pol.polwithcheck, pol.polrelid)::text
    FROM pg_policy pol
    JOIN pg_class pc ON pol.polrelid = pc.oid
    JOIN pg_namespace pn ON pc.relnamespace = pn.oid
    WHERE pc.relname = table_name
    AND pn.nspname = 'public';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the check function
GRANT EXECUTE ON FUNCTION check_rls_policies(text) TO anon, authenticated;
