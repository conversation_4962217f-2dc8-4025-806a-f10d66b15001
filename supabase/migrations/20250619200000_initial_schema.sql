-- Create the species table
CREATE TABLE public.species (
    id uuid DEFAULT gen_random_uuid() NOT NULL PRIMARY KEY,
    airtable_id text,
    name text NOT NULL UNIQUE,
    common_name text,
    scientific_name text,
    description text,
    category text,
    conservation_status text,
    habitat text,
    diet text,
    behavior text,
    family text,
    size_cm numeric,
    weight_g numeric,
    lifespan_years integer,
    migration_pattern text,
    breeding_season text,
    threat_level text,
    population_trend text,
    published boolean DEFAULT false,
    featured boolean DEFAULT false,
    photo_count integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Create the photos table
CREATE TABLE public.photos (
    id bigint GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
    species_id uuid,
    airtable_id text,
    url text,
    title text,
    description text,
    photographer text,
    location text,
    camera_settings text,
    weather_conditions text,
    time_of_day text,
    notes text,
    published boolean DEFAULT false,
    hash text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT photos_species_id_fkey FOREIGN KEY (species_id) REFERENCES public.species(id) ON DELETE SET NULL
);

-- Create policies and triggers
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = now();
   RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_species_updated_at BEFORE UPDATE ON public.species FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();
CREATE TRIGGER update_photos_updated_at BEFORE UPDATE ON public.photos FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();

-- Enable RLS
ALTER TABLE public.species ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.photos ENABLE ROW LEVEL SECURITY;

-- Policies for public access
CREATE POLICY "Allow public read access to published species" ON public.species FOR SELECT USING (published = true);
CREATE POLICY "Allow public read access to published photos" ON public.photos FOR SELECT USING (published = true);

-- Policies for admin access (assuming an admin role or specific user IDs)
-- This is a placeholder; you should define your own admin role or user check
CREATE POLICY "Allow full access for admins" ON public.species FOR ALL USING (true);
CREATE POLICY "Allow full access for admins" on public.photos FOR ALL USING (true); 