-- Enable pgcrypto for UUID generation
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Convert species.id to uuid
ALTER TABLE species
  ALTER COLUMN id SET DATA TYPE uuid USING gen_random_uuid(),
  ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- Convert photos.id and photos.species_id to uuid
ALTER TABLE photos
  ALTER COLUMN id SET DATA TYPE uuid USING gen_random_uuid(),
  ALTER COLUMN id SET DEFAULT gen_random_uuid(),
  ALTER COLUMN species_id SET DATA TYPE uuid USING gen_random_uuid();

-- Convert fun_facts.id and fun_facts.species_id to uuid
ALTER TABLE fun_facts
  ALTER COLUMN id SET DATA TYPE uuid USING gen_random_uuid(),
  ALTER COLUMN id SET DEFAULT gen_random_uuid(),
  ALTER COLUMN species_id SET DATA TYPE uuid USING gen_random_uuid(); 