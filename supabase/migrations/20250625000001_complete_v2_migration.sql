-- Complete V2 Table Migration
-- This migration finalizes the transition from legacy tables to v2 tables

-- Step 1: Update foreign key constraint from photos to species_v2
-- First, drop the existing constraint that points to the legacy species table
ALTER TABLE public.photos 
DROP CONSTRAINT IF EXISTS photos_species_id_fkey;

ALTER TABLE public.photos 
DROP CONSTRAINT IF EXISTS photos_species_id_fkey_v2;

-- Add new foreign key constraint pointing to species_v2
ALTER TABLE public.photos 
ADD CONSTRAINT photos_species_id_fkey_v2 
FOREIGN KEY (species_id) REFERENCES public.species_v2(id) ON DELETE SET NULL;

-- Step 2: Update any remaining views that reference the legacy species table
-- Update species_photo_counts view to use species_v2
DROP VIEW IF EXISTS species_photo_counts CASCADE;
CREATE VIEW species_photo_counts AS
SELECT 
    s.id,
    s.name,
    s.common_name,
    s.scientific_name,
    s.category,
    s.conservation_status,
    count(p.id) AS total_photos,
    sum(CASE WHEN p.published THEN 1 ELSE 0 END) AS published_photos,
    sum(CASE WHEN NOT p.published THEN 1 ELSE 0 END) AS unpublished_photos,
    0 AS ai_tagged_photos,
    0 AS photos_needing_review,
    s.created_at,
    s.updated_at
FROM species_v2 s
LEFT JOIN photos p ON s.id = p.species_id
GROUP BY 
    s.id, s.name, s.common_name, s.scientific_name, 
    s.category, s.conservation_status, s.created_at, s.updated_at
ORDER BY count(p.id) DESC;

-- Update orphaned_photos view to use species_v2
DROP VIEW IF EXISTS orphaned_photos CASCADE;
CREATE OR REPLACE VIEW orphaned_photos AS
SELECT 
  p.id, 
  p.url, 
  p.title, 
  p.species_id, 
  p.created_at,
  p.hash,
  'orphaned' as issue_type
FROM photos p
LEFT JOIN species_v2 s ON p.species_id = s.id
WHERE p.species_id IS NULL OR s.id IS NULL;

-- Update photos_with_missing_species view to use species_v2
DROP VIEW IF EXISTS photos_with_missing_species CASCADE;
CREATE VIEW photos_with_missing_species AS
SELECT 
    count(*) AS count,
    'photos_with_missing_species_id' AS issue_type
FROM photos 
WHERE species_id IS NULL
UNION ALL
SELECT 
    count(*) AS count,
    'photos_with_invalid_species_id' AS issue_type
FROM photos p
WHERE p.species_id IS NOT NULL 
    AND NOT EXISTS (SELECT 1 FROM species_v2 s WHERE s.id = p.species_id);

-- Step 3: Create a view to show species that have no photos (for data quality)
CREATE OR REPLACE VIEW species_with_no_photos AS
SELECT
  s.id,
  s.name,
  s.scientific_name,
  s.category,
  s.conservation_status,
  s.published,
  s.created_at,
  'no_photos' as issue_type
FROM species_v2 s
LEFT JOIN photos_v2 p ON s.id = p.species_id
WHERE p.id IS NULL
  AND s.published = true
ORDER BY s.created_at DESC;

-- Step 4: Add helpful indexes for performance
CREATE INDEX IF NOT EXISTS idx_photos_species_id_v2 ON photos_v2(species_id);
CREATE INDEX IF NOT EXISTS idx_species_v2_published ON species_v2(published);
CREATE INDEX IF NOT EXISTS idx_species_v2_category ON species_v2(category);
CREATE INDEX IF NOT EXISTS idx_species_v2_conservation_status ON species_v2(conservation_status);

-- Step 5: Add comments for documentation
COMMENT ON VIEW species_photo_counts IS 'Species with photo counts using species_v2 table';
COMMENT ON VIEW orphaned_photos IS 'Photos with missing or invalid species_id references to species_v2';
COMMENT ON VIEW photos_with_missing_species IS 'Photos with missing or invalid species_id references to species_v2';
COMMENT ON VIEW species_with_no_photos IS 'Published species that have no associated photos';

-- Step 6: Grant necessary permissions
GRANT SELECT ON species_photo_counts TO authenticated;
GRANT SELECT ON orphaned_photos TO authenticated;
GRANT SELECT ON photos_with_missing_species TO authenticated;
GRANT SELECT ON species_with_no_photos TO authenticated;

GRANT SELECT ON species_photo_counts TO anon;
GRANT SELECT ON orphaned_photos TO anon;
GRANT SELECT ON photos_with_missing_species TO anon;
GRANT SELECT ON species_with_no_photos TO anon;
