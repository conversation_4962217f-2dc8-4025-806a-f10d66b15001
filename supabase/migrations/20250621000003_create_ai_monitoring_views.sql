-- AI-Tagged Wildlife Photo Monitoring Dashboard Views
-- This migration creates views for monitoring AI-generated content and review workflow
-- Note: These views are designed to work with the current schema and will be enhanced when AI fields are added

-- 1. Photos needing review - Photos that require human review (placeholder for AI fields)
CREATE OR REPLACE VIEW photos_needing_review AS
SELECT
  p.id,
  p.url,
  p.title,
  p.description,
  p.location,
  p.species_id,
  p.published,
  p.created_at,
  s.name as species_name,
  s.scientific_name,
  s.common_name,
  -- Placeholder for AI fields that will be added later
  false as ai_generated_metadata,
  false as needs_review
FROM photos p
LEFT JOIN species s ON p.species_id = s.id
WHERE p.published = false  -- For now, show unpublished photos as needing review
ORDER BY p.created_at DESC;

-- 2. AI-created species - Species records created by the AI agent (placeholder)
CREATE OR REPLACE VIEW ai_created_species AS
SELECT
  id,
  name,
  common_name,
  scientific_name,
  published,
  created_at,
  updated_at,
  -- Placeholder for AI fields that will be added later
  false as created_by_ai,
  0.0 as ai_confidence
FROM species
WHERE published = false  -- For now, show unpublished species as AI-created
ORDER BY created_at DESC;

-- 3. AI assignment stats - Statistical overview of AI activity (placeholder)
CREATE OR REPLACE VIEW ai_assignment_stats AS
SELECT
  COUNT(*) as total_photos,
  0 as ai_tagged,  -- Placeholder for AI fields
  0 as flagged_for_review,  -- Placeholder for AI fields
  0.0 as avg_confidence,  -- Placeholder for AI fields
  SUM(CASE WHEN published THEN 1 ELSE 0 END) as published_photos,
  SUM(CASE WHEN NOT published THEN 1 ELSE 0 END) as unpublished_photos
FROM photos;

-- 4. Species photo counts - Ranks species by photo count
CREATE OR REPLACE VIEW species_photo_counts AS
SELECT
  s.id,
  s.name,
  s.common_name,
  s.scientific_name,
  s.category,
  s.conservation_status,
  COUNT(p.id) as total_photos,
  SUM(CASE WHEN p.published THEN 1 ELSE 0 END) as published_photos,
  SUM(CASE WHEN NOT p.published THEN 1 ELSE 0 END) as unpublished_photos,
  0 as ai_tagged_photos,  -- Placeholder for AI fields
  0 as photos_needing_review,  -- Placeholder for AI fields
  s.created_at,
  s.updated_at
FROM species s
LEFT JOIN photos p ON s.id = p.species_id
GROUP BY s.id, s.name, s.common_name, s.scientific_name, s.category, s.conservation_status, s.created_at, s.updated_at
ORDER BY total_photos DESC;

-- 5. AI confidence distribution - Shows confidence score distribution (placeholder)
CREATE OR REPLACE VIEW ai_confidence_distribution AS
SELECT
  'Not Implemented Yet' as confidence_range,
  0 as photo_count,
  0.0 as percentage
WHERE false;  -- This view will be populated when AI fields are added

-- 6. Recent AI activity - Shows recent AI-generated content (placeholder)
CREATE OR REPLACE VIEW recent_ai_activity AS
SELECT
  'photo' as content_type,
  p.id::text as content_id,
  p.title as content_title,
  p.species_id::text as species_id,
  s.name as species_name,
  false as ai_generated_metadata,  -- Placeholder for AI fields
  false as needs_review,  -- Placeholder for AI fields
  p.created_at,
  'Photo uploaded (AI processing not yet implemented)' as activity_description
FROM photos p
LEFT JOIN species s ON p.species_id = s.id
WHERE p.created_at >= NOW() - INTERVAL '7 days'

UNION ALL

SELECT
  'species' as content_type,
  s.id::text as content_id,
  s.name as content_title,
  s.id::text as species_id,
  s.name as species_name,
  false as ai_generated_metadata,  -- Placeholder for AI fields
  false as needs_review,  -- Placeholder for AI fields
  s.created_at,
  'New species created (AI processing not yet implemented)' as activity_description
FROM species s
WHERE s.created_at >= NOW() - INTERVAL '7 days'

ORDER BY created_at DESC;

-- 7. Review queue summary - Summary of items needing review (placeholder)
CREATE OR REPLACE VIEW review_queue_summary AS
SELECT
  COUNT(*) as total_items_needing_review,
  SUM(CASE WHEN content_type = 'photo' THEN 1 ELSE 0 END) as photos_needing_review,
  SUM(CASE WHEN content_type = 'species' THEN 1 ELSE 0 END) as species_needing_review,
  0.0 as avg_confidence,  -- Placeholder for AI fields
  MIN(created_at) as oldest_item_date,
  MAX(created_at) as newest_item_date
FROM (
  SELECT 
    'photo' as content_type,
    p.created_at
  FROM photos p
  WHERE p.published = false  -- For now, show unpublished photos as needing review
  
  UNION ALL
  
  SELECT 
    'species' as content_type,
    s.created_at
  FROM species s
  WHERE s.published = false  -- For now, show unpublished species as needing review
) review_items;

-- 8. AI performance metrics - Performance tracking for AI decisions (placeholder)
CREATE OR REPLACE VIEW ai_performance_metrics AS
SELECT
  DATE_TRUNC('day', p.created_at) as date,
  COUNT(*) as total_photos_processed,
  0 as ai_tagged_photos,  -- Placeholder for AI fields
  0 as photos_flagged_for_review,  -- Placeholder for AI fields
  0.0 as avg_confidence,  -- Placeholder for AI fields
  0.0 as ai_tagging_rate,  -- Placeholder for AI fields
  0.0 as review_rate  -- Placeholder for AI fields
FROM photos p
WHERE p.created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', p.created_at)
ORDER BY date DESC;

-- Create indexes to optimize view performance
CREATE INDEX IF NOT EXISTS idx_photos_published_created_at ON photos(published, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_species_published_created_at ON species(published, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_photos_species_id ON photos(species_id);

-- Add comments for documentation
COMMENT ON VIEW photos_needing_review IS 'Photos that require human review (placeholder for AI fields)';
COMMENT ON VIEW ai_created_species IS 'Species records created by AI (placeholder for AI fields)';
COMMENT ON VIEW ai_assignment_stats IS 'Statistical overview of photo processing (placeholder for AI fields)';
COMMENT ON VIEW species_photo_counts IS 'Species ranked by photo count with processing statistics';
COMMENT ON VIEW ai_confidence_distribution IS 'Distribution of AI confidence scores (placeholder for AI fields)';
COMMENT ON VIEW recent_ai_activity IS 'Recent content processing activity (placeholder for AI fields)';
COMMENT ON VIEW review_queue_summary IS 'Summary of items needing review (placeholder for AI fields)';
COMMENT ON VIEW ai_performance_metrics IS 'Daily processing metrics (placeholder for AI fields)'; 