DROP FUNCTION IF EXISTS public.get_duplicate_photos();

CREATE OR REPLACE FUNCTION get_duplicate_photos()
RETURNS TABLE (
  hash_value text,
  duplicate_count bigint,
  photo_ids integer[]
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    hash,
    COUNT(*) as duplicate_count,
    array_agg(id) as photo_ids
  FROM
    public.photos
  WHERE
    hash IS NOT NULL
  GROUP BY
    hash
  HAVING
    COUNT(*) > 1
  ORDER BY
    duplicate_count DESC;
END;
$$ LANGUAGE plpgsql; 