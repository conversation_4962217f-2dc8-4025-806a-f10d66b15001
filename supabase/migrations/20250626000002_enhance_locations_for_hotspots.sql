-- Enhance locations table for comprehensive hotspot functionality
-- This migration extends the basic locations table with rich hotspot data

-- 1. Extend locations table with hotspot-specific fields
ALTER TABLE locations ADD COLUMN IF NOT EXISTS description TEXT;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS directions TEXT;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS facilities TEXT[];
ALTER TABLE locations ADD COLUMN IF NOT EXISTS best_time_to_visit TEXT;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS difficulty_level TEXT CHECK (difficulty_level IN ('easy', 'moderate', 'difficult', 'expert'));
ALTER TABLE locations ADD COLUMN IF NOT EXISTS access_type TEXT CHECK (access_type IN ('public', 'private', 'permit_required', 'restricted'));
ALTER TABLE locations ADD COLUMN IF NOT EXISTS parking_info TEXT;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS entrance_fee DECIMAL(8,2);
ALTER TABLE locations ADD COLUMN IF NOT EXISTS website_url TEXT;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS contact_info TEXT;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS featured BOOLEAN DEFAULT FALSE;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS photo_url TEXT;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS habitat_types TEXT[];
ALTER TABLE locations ADD COLUMN IF NOT EXISTS target_species TEXT[]; -- Common names of key species
ALTER TABLE locations ADD COLUMN IF NOT EXISTS seasonal_highlights JSONB; -- JSON object with seasonal information
ALTER TABLE locations ADD COLUMN IF NOT EXISTS visitor_tips TEXT;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS published BOOLEAN DEFAULT TRUE;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS google_maps_url TEXT;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS apple_maps_url TEXT;

-- 2. Create species_locations junction table for many-to-many relationships
CREATE TABLE IF NOT EXISTS species_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id UUID REFERENCES species_v2(id) ON DELETE CASCADE,
  location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
  abundance TEXT CHECK (abundance IN ('rare', 'uncommon', 'common', 'abundant', 'very_common')),
  seasonal_presence TEXT[], -- Array like ['spring', 'summer', 'fall', 'winter']
  breeding_status TEXT CHECK (breeding_status IN ('non_breeding', 'possible', 'probable', 'confirmed')),
  best_months INTEGER[], -- Array of month numbers (1-12)
  notes TEXT,
  confidence_level TEXT CHECK (confidence_level IN ('low', 'medium', 'high', 'confirmed')),
  last_observed DATE,
  observation_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(species_id, location_id)
);

-- 3. Create location_photos table for multiple photos per location
CREATE TABLE IF NOT EXISTS location_photos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
  photo_url TEXT NOT NULL,
  title TEXT,
  description TEXT,
  photographer TEXT,
  taken_date DATE,
  is_primary BOOLEAN DEFAULT FALSE,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_species_locations_species ON species_locations(species_id);
CREATE INDEX IF NOT EXISTS idx_species_locations_location ON species_locations(location_id);
CREATE INDEX IF NOT EXISTS idx_species_locations_abundance ON species_locations(abundance);
CREATE INDEX IF NOT EXISTS idx_species_locations_seasonal ON species_locations USING GIN(seasonal_presence);
CREATE INDEX IF NOT EXISTS idx_species_locations_months ON species_locations USING GIN(best_months);

CREATE INDEX IF NOT EXISTS idx_location_photos_location ON location_photos(location_id);
CREATE INDEX IF NOT EXISTS idx_location_photos_primary ON location_photos(is_primary);
CREATE INDEX IF NOT EXISTS idx_location_photos_sort ON location_photos(sort_order);

CREATE INDEX IF NOT EXISTS idx_locations_featured ON locations(featured);
CREATE INDEX IF NOT EXISTS idx_locations_published ON locations(published);
CREATE INDEX IF NOT EXISTS idx_locations_access_type ON locations(access_type);
CREATE INDEX IF NOT EXISTS idx_locations_difficulty ON locations(difficulty_level);
CREATE INDEX IF NOT EXISTS idx_locations_habitat_types ON locations USING GIN(habitat_types);

-- 5. Enable RLS for new tables
ALTER TABLE species_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE location_photos ENABLE ROW LEVEL SECURITY;

-- 6. Add RLS policies
-- Public read access for published locations and their relationships
CREATE POLICY "Public read access for species_locations" ON species_locations FOR SELECT USING (
  EXISTS (SELECT 1 FROM locations l WHERE l.id = location_id AND l.published = true)
);

CREATE POLICY "Public read access for location_photos" ON location_photos FOR SELECT USING (
  EXISTS (SELECT 1 FROM locations l WHERE l.id = location_id AND l.published = true)
);

-- Admin full access
CREATE POLICY "Admin full access for species_locations" ON species_locations FOR ALL USING (auth.email() = '<EMAIL>');
CREATE POLICY "Admin full access for location_photos" ON location_photos FOR ALL USING (auth.email() = '<EMAIL>');

-- 7. Create helpful views for location data
CREATE OR REPLACE VIEW location_species_summary AS
SELECT 
  l.id as location_id,
  l.name as location_name,
  l.latitude,
  l.longitude,
  l.state_province,
  l.country,
  l.habitat_types,
  l.featured,
  l.published,
  COUNT(sl.species_id) as total_species,
  COUNT(CASE WHEN sl.abundance IN ('common', 'abundant', 'very_common') THEN 1 END) as common_species,
  COUNT(CASE WHEN sl.abundance = 'rare' THEN 1 END) as rare_species,
  COUNT(CASE WHEN sl.breeding_status = 'confirmed' THEN 1 END) as breeding_species,
  ARRAY_AGG(DISTINCT s.category) FILTER (WHERE s.category IS NOT NULL) as categories_present,
  MAX(sl.last_observed) as last_observation_date,
  SUM(sl.observation_count) as total_observations
FROM locations l
LEFT JOIN species_locations sl ON l.id = sl.location_id
LEFT JOIN species_v2 s ON sl.species_id = s.id
WHERE l.published = true
GROUP BY l.id, l.name, l.latitude, l.longitude, l.state_province, l.country, l.habitat_types, l.featured, l.published;

CREATE OR REPLACE VIEW species_location_details AS
SELECT 
  s.id as species_id,
  s.name as species_name,
  s.common_name,
  s.scientific_name,
  s.category,
  s.conservation_status,
  l.id as location_id,
  l.name as location_name,
  l.state_province,
  l.country,
  l.latitude,
  l.longitude,
  sl.abundance,
  sl.seasonal_presence,
  sl.breeding_status,
  sl.best_months,
  sl.notes,
  sl.confidence_level,
  sl.last_observed,
  sl.observation_count,
  COUNT(p.id) as photo_count
FROM species_v2 s
JOIN species_locations sl ON s.id = sl.species_id
JOIN locations l ON sl.location_id = l.id
LEFT JOIN photos_v2 p ON s.id = p.species_id
WHERE s.published = true AND l.published = true
GROUP BY s.id, s.name, s.common_name, s.scientific_name, s.category, s.conservation_status,
         l.id, l.name, l.state_province, l.country, l.latitude, l.longitude,
         sl.abundance, sl.seasonal_presence, sl.breeding_status, sl.best_months,
         sl.notes, sl.confidence_level, sl.last_observed, sl.observation_count;

CREATE OR REPLACE VIEW hotspot_highlights AS
SELECT 
  l.*,
  lss.total_species,
  lss.common_species,
  lss.rare_species,
  lss.breeding_species,
  lss.categories_present,
  lss.total_observations,
  lp.photo_url as primary_photo,
  lp.title as primary_photo_title
FROM locations l
LEFT JOIN location_species_summary lss ON l.id = lss.location_id
LEFT JOIN location_photos lp ON l.id = lp.location_id AND lp.is_primary = true
WHERE l.published = true
ORDER BY l.featured DESC, lss.total_species DESC NULLS LAST;

-- 8. Add functions for location management
CREATE OR REPLACE FUNCTION update_location_species_count()
RETURNS TRIGGER AS $$
BEGIN
  -- Update observation count when observations are added/updated
  IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
    UPDATE species_locations 
    SET observation_count = (
      SELECT COUNT(*) 
      FROM observations o 
      WHERE o.species_id = NEW.species_id 
      AND o.location_id = NEW.location_id
    ),
    last_observed = (
      SELECT MAX(observation_date)
      FROM observations o 
      WHERE o.species_id = NEW.species_id 
      AND o.location_id = NEW.location_id
    )
    WHERE species_id = NEW.species_id AND location_id = NEW.location_id;
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update species-location counts
CREATE TRIGGER update_species_location_counts
  AFTER INSERT OR UPDATE OR DELETE ON observations
  FOR EACH ROW
  EXECUTE FUNCTION update_location_species_count();

-- 9. Add comments for documentation
COMMENT ON TABLE species_locations IS 'Many-to-many relationship between species and locations with abundance and seasonal data';
COMMENT ON TABLE location_photos IS 'Photo gallery for each location/hotspot';
COMMENT ON VIEW location_species_summary IS 'Summary statistics for each location showing species diversity and observation data';
COMMENT ON VIEW species_location_details IS 'Detailed view of species-location relationships with all metadata';
COMMENT ON VIEW hotspot_highlights IS 'Featured locations with summary statistics and primary photos for public display';

-- 10. Insert some sample data for testing (optional)
-- This can be removed in production
INSERT INTO locations (
  name, latitude, longitude, country, state_province, 
  description, directions, facilities, best_time_to_visit,
  difficulty_level, access_type, habitat_types, published, featured
) VALUES 
(
  'Central Park - The Ramble', 
  40.7794, -73.9632, 
  'United States', 'New York',
  'A 36-acre woodland area in Central Park known for excellent bird watching, especially during migration seasons.',
  'Enter Central Park at 79th Street and Central Park West. Walk east to The Ramble area near the Lake.',
  ARRAY['restrooms', 'benches', 'trails', 'visitor_center'],
  'Early morning (6-10 AM) during spring and fall migration (April-May, September-October)',
  'easy',
  'public',
  ARRAY['deciduous_forest', 'wetland', 'urban_park'],
  true,
  true
) ON CONFLICT DO NOTHING;
