-- Add AI review fields to photos table
ALTER TABLE photos 
ADD COLUMN IF NOT EXISTS ai_suggested_id UUID,
ADD COLUMN IF NOT EXISTS ai_confidence DECIMAL(3,2),
ADD COLUMN IF NOT EXISTS ai_reviewed BOOLEAN DEFAULT FALSE;

-- Create view for photos requiring AI review
CREATE OR REPLACE VIEW photos_requiring_review AS
SELECT 
  p.id,
  p.url,
  p.species_id,
  s.name as species_name,
  p.ai_suggested_id,
  ai_s.name as ai_suggested_name,
  p.ai_confidence,
  p.ai_reviewed,
  p.location,
  p.created_at as dateTaken
FROM photos p
LEFT JOIN species s ON p.species_id = s.id
LEFT JOIN species ai_s ON p.ai_suggested_id = ai_s.id
WHERE p.ai_reviewed = FALSE 
  AND p.ai_suggested_id IS NOT NULL
  AND p.published = TRUE
ORDER BY p.ai_confidence DESC NULLS LAST; 