-- Add AI-specific fields to photos table
ALTER TABLE public.photos 
ADD COLUMN IF NOT EXISTS ai_generated_metadata BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS needs_review BOOLEAN DEFAULT FALSE;

-- Add AI-specific fields to species table
ALTER TABLE public.species 
ADD COLUMN IF NOT EXISTS ai_generated BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS ai_confidence DECIMAL(3,2) CHECK (ai_confidence >= 0 AND ai_confidence <= 1),
ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}';

-- Add hash field to photos if it doesn't exist (for deduplication)
ALTER TABLE public.photos 
ADD COLUMN IF NOT EXISTS hash TEXT;

-- Create index on hash for faster duplicate detection
CREATE INDEX IF NOT EXISTS idx_photos_hash ON public.photos(hash);

-- Create index on ai_generated_metadata for filtering
CREATE INDEX IF NOT EXISTS idx_photos_ai_generated ON public.photos(ai_generated_metadata);

-- Create index on needs_review for review workflow
CREATE INDEX IF NOT EXISTS idx_photos_needs_review ON public.photos(needs_review);

-- Create index on species created_by_ai for filtering
CREATE INDEX IF NOT EXISTS idx_species_ai_generated ON public.species(ai_generated); 