-- Create the species_needing_review view in public schema
CREATE OR REPLACE VIEW public.species_needing_review AS
SELECT
  id,
  name,
  description,
  behavior,
  family,
  conservation_actions,
  size_description
FROM public.species
WHERE
  description IS NULL
  OR behavior IS NULL
  OR family IS NULL
  OR conservation_actions IS NULL
  OR size_description IS NULL;

-- Grant permissions to enable type generation
GRANT SELECT ON public.species_needing_review TO authenticated;
GRANT SELECT ON public.species_needing_review TO service_role;
GRANT SELECT ON public.species_needing_review TO anon; 