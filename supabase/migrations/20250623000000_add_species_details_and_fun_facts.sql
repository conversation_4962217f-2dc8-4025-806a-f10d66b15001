-- Add new informational columns to the existing species table
ALTER TABLE species ADD COLUMN IF NOT EXISTS habitat TEXT;
ALTER TABLE species ADD COLUMN IF NOT EXISTS regions TEXT;
ALTER TABLE species ADD COLUMN IF NOT EXISTS diet TEXT;
ALTER TABLE species ADD COLUMN IF NOT EXISTS behavior TEXT;

-- Create a new table to store individual fun facts related to a species
CREATE TABLE IF NOT EXISTS fun_facts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id UUID REFERENCES species(id) ON DELETE CASCADE NOT NULL,
  fact TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Optional: Add an index for faster lookups if it doesn't already exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_class c WHERE c.relname = 'idx_fun_facts_species_id' AND c.relkind = 'i') THEN
    CREATE INDEX idx_fun_facts_species_id ON fun_facts(species_id);
  END IF;
END$$; 