-- Data Cleanup and Auditing Views
-- This migration creates comprehensive views and utilities for managing data integrity

-- Add URL status column to photos table for tracking broken URLs
-- This must be done before creating views that might use it.
ALTER TABLE photos ADD COLUMN IF NOT EXISTS url_status text DEFAULT 'unknown' CHECK (url_status IN ('unknown', 'ok', 'broken', 'checking'));

-- Create index for efficient URL status queries
CREATE INDEX IF NOT EXISTS idx_photos_url_status ON photos(url_status);

-- 1. Orphaned Photos (photos with no valid species)
CREATE OR REPLACE VIEW orphaned_photos AS
SELECT 
  p.id, 
  p.url, 
  p.title, 
  p.species_id, 
  p.created_at,
  p.hash,
  'orphaned' as issue_type
FROM photos p
LEFT JOIN species s ON p.species_id = s.id
WHERE p.species_id IS NULL OR s.id IS NULL;

-- 2. Duplicate Photos by Hash
CREATE OR REPLACE VIEW duplicate_photos_by_hash AS
SELECT 
  hash, 
  array_agg(id ORDER BY created_at) as photo_ids, 
  count(*) as dup_count,
  'duplicate_hash' as issue_type
FROM photos
WHERE hash IS NOT NULL
GROUP BY hash
HAVING count(*) > 1;

-- 3. Duplicate Photos by URL
CREATE OR REPLACE VIEW duplicate_photos_by_url AS
SELECT 
  url, 
  array_agg(id ORDER BY created_at) as photo_ids, 
  count(*) as dup_count,
  'duplicate_url' as issue_type
FROM photos
WHERE url IS NOT NULL
GROUP BY url
HAVING count(*) > 1;

-- 4. Invalid Species References
CREATE OR REPLACE VIEW invalid_species_references AS
SELECT 
  p.id, 
  p.url, 
  p.species_id,
  p.title,
  p.created_at,
  'invalid_species' as issue_type
FROM photos p
LEFT JOIN species s ON p.species_id = s.id
WHERE p.species_id IS NOT NULL AND s.id IS NULL;

-- 5. Photos with Missing Files (placeholder for URL validation)
CREATE OR REPLACE VIEW photos_with_missing_files AS
SELECT 
  p.id,
  p.url,
  p.title,
  p.species_id,
  p.created_at,
  'missing_file' as issue_type
FROM photos p
WHERE p.url IS NULL OR p.url = '';

-- 6. Comprehensive Photo Issues Summary
CREATE OR REPLACE VIEW photo_issues_summary AS
SELECT 
  'orphaned' as issue_type,
  COUNT(*) as count
FROM orphaned_photos
UNION ALL
SELECT 
  'duplicate_hash' as issue_type,
  COUNT(*) as count
FROM duplicate_photos_by_hash
UNION ALL
SELECT 
  'duplicate_url' as issue_type,
  COUNT(*) as count
FROM duplicate_photos_by_url
UNION ALL
SELECT 
  'invalid_species' as issue_type,
  COUNT(*) as count
FROM invalid_species_references
UNION ALL
SELECT 
  'missing_file' as issue_type,
  COUNT(*) as count
FROM photos_with_missing_files;

-- 7. Species with No Photos
CREATE OR REPLACE VIEW species_with_no_photos AS
SELECT 
  s.id,
  s.name,
  s.common_name,
  s.scientific_name,
  s.published,
  s.created_at,
  'no_photos' as issue_type
FROM species s
LEFT JOIN photos p ON s.id = p.species_id
WHERE p.id IS NULL;

-- 8. Photos Cleanup Status Dashboard
CREATE OR REPLACE VIEW photos_cleanup_dashboard AS
SELECT 
  p.id,
  p.url,
  p.title,
  p.species_id,
  p.hash,
  p.created_at,
  s.name as species_name,
  CASE 
    WHEN op.id IS NOT NULL THEN 'orphaned'
    WHEN dp_hash.hash IS NOT NULL THEN 'duplicate_hash'
    WHEN dp_url.url IS NOT NULL THEN 'duplicate_url'
    WHEN isr.id IS NOT NULL THEN 'invalid_species'
    WHEN pmf.id IS NOT NULL THEN 'missing_file'
    ELSE 'ok'
  END as cleanup_status,
  CASE 
    WHEN op.id IS NOT NULL THEN 'Photo has no valid species reference'
    WHEN dp_hash.hash IS NOT NULL THEN 'Duplicate photo by hash'
    WHEN dp_url.url IS NOT NULL THEN 'Duplicate photo by URL'
    WHEN isr.id IS NOT NULL THEN 'Invalid species reference'
    WHEN pmf.id IS NOT NULL THEN 'Missing file at URL'
    ELSE 'No issues detected'
  END as issue_description
FROM photos p
LEFT JOIN species s ON p.species_id = s.id
LEFT JOIN orphaned_photos op ON p.id = op.id
LEFT JOIN duplicate_photos_by_hash dp_hash ON p.hash = dp_hash.hash
LEFT JOIN duplicate_photos_by_url dp_url ON p.url = dp_url.url
LEFT JOIN invalid_species_references isr ON p.id = isr.id
LEFT JOIN photos_with_missing_files pmf ON p.id = pmf.id;

-- Create a log table for cleanup operations
CREATE TABLE IF NOT EXISTS photo_cleanup_log (
  id SERIAL PRIMARY KEY,
  operation_type TEXT NOT NULL,
  photo_ids INTEGER[],
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Function to bulk delete duplicate photos (keeps the first one)
CREATE OR REPLACE FUNCTION cleanup_duplicate_photos()
RETURNS TABLE(deleted_count INTEGER, kept_photos INTEGER[]) AS $$
DECLARE
  duplicate_record RECORD;
  photo_ids_to_delete INTEGER[];
  kept_photo_id INTEGER;
BEGIN
  deleted_count := 0;
  
  -- Handle hash duplicates
  FOR duplicate_record IN 
    SELECT hash, photo_ids 
    FROM duplicate_photos_by_hash
  LOOP
    -- Keep the first photo (oldest), delete the rest
    kept_photo_id := duplicate_record.photo_ids[1];
    photo_ids_to_delete := duplicate_record.photo_ids[2:array_length(duplicate_record.photo_ids, 1)];
    
    -- Delete duplicate photos
    DELETE FROM photos WHERE id = ANY(photo_ids_to_delete);
    
    -- Log the operation
    INSERT INTO photo_cleanup_log (operation_type, photo_ids, details)
    VALUES (
      'delete_hash_duplicates',
      photo_ids_to_delete,
      jsonb_build_object(
        'hash', duplicate_record.hash,
        'kept_photo_id', kept_photo_id,
        'deleted_count', array_length(photo_ids_to_delete, 1)
      )
    );
    
    deleted_count := deleted_count + array_length(photo_ids_to_delete, 1);
    kept_photos := array_append(kept_photos, kept_photo_id);
  END LOOP;
  
  -- Handle URL duplicates
  FOR duplicate_record IN 
    SELECT url, photo_ids 
    FROM duplicate_photos_by_url
  LOOP
    -- Keep the first photo (oldest), delete the rest
    kept_photo_id := duplicate_record.photo_ids[1];
    photo_ids_to_delete := duplicate_record.photo_ids[2:array_length(duplicate_record.photo_ids, 1)];
    
    -- Delete duplicate photos
    DELETE FROM photos WHERE id = ANY(photo_ids_to_delete);
    
    -- Log the operation
    INSERT INTO photo_cleanup_log (operation_type, photo_ids, details)
    VALUES (
      'delete_url_duplicates',
      photo_ids_to_delete,
      jsonb_build_object(
        'url', duplicate_record.url,
        'kept_photo_id', kept_photo_id,
        'deleted_count', array_length(photo_ids_to_delete, 1)
      )
    );
    
    deleted_count := deleted_count + array_length(photo_ids_to_delete, 1);
    kept_photos := array_append(kept_photos, kept_photo_id);
  END LOOP;
  
  RETURN NEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cleanup orphaned photos
CREATE OR REPLACE FUNCTION cleanup_orphaned_photos()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
  orphaned_ids INTEGER[];
BEGIN
  -- Get all orphaned photo IDs
  SELECT array_agg(id) INTO orphaned_ids FROM orphaned_photos;
  
  IF orphaned_ids IS NULL THEN
    RETURN 0;
  END IF;
  
  -- Delete orphaned photos
  DELETE FROM photos WHERE id = ANY(orphaned_ids);
  
  -- Log the operation
  INSERT INTO photo_cleanup_log (operation_type, photo_ids, details)
  VALUES (
    'delete_orphaned_photos',
    orphaned_ids,
    jsonb_build_object('deleted_count', array_length(orphaned_ids, 1))
  );
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to unpublish species with no photos
CREATE OR REPLACE FUNCTION unpublish_empty_species()
RETURNS INTEGER AS $$
DECLARE
  updated_count INTEGER;
BEGIN
  UPDATE species
  SET published = false
  WHERE id IN (
    SELECT s.id 
    FROM species s
    LEFT JOIN photos p ON s.id = p.species_id
    WHERE p.id IS NULL AND s.published = true
  );
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update URL status for a photo
CREATE OR REPLACE FUNCTION update_photo_url_status(
  photo_id INTEGER,
  status TEXT
)
RETURNS VOID AS $$
BEGIN
  UPDATE photos 
  SET url_status = status
  WHERE id = photo_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_photos_hash ON photos(hash) WHERE hash IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_photos_url ON photos(url) WHERE url IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_photo_cleanup_log_created_at ON photo_cleanup_log(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_photo_cleanup_log_operation ON photo_cleanup_log(operation_type);

-- Grant permissions for the views
GRANT SELECT ON orphaned_photos TO anon, authenticated;
GRANT SELECT ON duplicate_photos_by_hash TO anon, authenticated;
GRANT SELECT ON duplicate_photos_by_url TO anon, authenticated;
GRANT SELECT ON invalid_species_references TO anon, authenticated;
GRANT SELECT ON photos_with_missing_files TO anon, authenticated;
GRANT SELECT ON photo_issues_summary TO anon, authenticated;
GRANT SELECT ON species_with_no_photos TO anon, authenticated;
GRANT SELECT ON photos_cleanup_dashboard TO anon, authenticated;

-- Grant permissions for the functions
GRANT EXECUTE ON FUNCTION cleanup_duplicate_photos() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION cleanup_orphaned_photos() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION unpublish_empty_species() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION update_photo_url_status(INTEGER, TEXT) TO anon, authenticated;
