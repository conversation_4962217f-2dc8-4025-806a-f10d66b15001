/*
  # Fix update_species_photo_counts function

  1. Problem
    - The UPDATE statement in update_species_photo_counts() function lacks a WHERE clause
    - This violates RLS policies that require explicit WHERE clauses

  2. Solution
    - Redefine the function with SECURITY DEFINER to bypass RLS
    - Add explicit WHERE clause to the UPDATE statement
    - Ensure proper permissions for the function
*/

-- Drop and recreate the function with proper security context
DROP FUNCTION IF EXISTS update_species_photo_counts();

CREATE OR REPLACE FUNCTION update_species_photo_counts()
RETURNS void 
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Update photo counts for all species with explicit WHERE clause
  UPDATE public.species 
  SET photo_count = (
    SELECT COUNT(*) 
    FROM public.photos 
    WHERE photos.species_id = species.id 
    AND photos.published = true
  )
  WHERE id IS NOT NULL; -- Explicit WHERE clause to satisfy RLS
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION update_species_photo_counts() TO authenticated;
GRANT EXECUTE ON FUNCTION update_species_photo_counts() TO anon;

-- Also fix the cleanup_orphaned_records function if it has similar issues
DROP FUNCTION IF EXISTS cleanup_orphaned_records();

CREATE OR REPLACE FUNCTION cleanup_orphaned_records()
RETURNS TABLE (
  orphaned_photos_removed bigint,
  empty_species_unpublished bigint
)
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  photos_removed bigint := 0;
  species_unpublished bigint := 0;
BEGIN
  -- Remove photos with invalid species_id references
  WITH deleted_photos AS (
    DELETE FROM public.photos 
    WHERE species_id IS NOT NULL 
    AND species_id NOT IN (SELECT id FROM public.species)
    RETURNING id
  )
  SELECT COUNT(*) INTO photos_removed FROM deleted_photos;
  
  -- Unpublish species without photos with explicit WHERE clause
  WITH unpublished_species AS (
    UPDATE public.species 
    SET published = false 
    WHERE (photo_count = 0 OR photo_count IS NULL)
    AND id IS NOT NULL -- Explicit WHERE clause
    RETURNING id
  )
  SELECT COUNT(*) INTO species_unpublished FROM unpublished_species;
  
  RETURN QUERY SELECT photos_removed, species_unpublished;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION cleanup_orphaned_records() TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_orphaned_records() TO anon;

-- Also fix the update_species_published_status function
DROP FUNCTION IF EXISTS update_species_published_status();

CREATE OR REPLACE FUNCTION update_species_published_status()
RETURNS void
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Unpublish species without photos
  UPDATE public.species 
  SET published = false 
  WHERE (photo_count = 0 OR photo_count IS NULL)
  AND id IS NOT NULL; -- Explicit WHERE clause
  
  -- Publish species with photos (if they weren't explicitly unpublished)
  UPDATE public.species 
  SET published = true 
  WHERE photo_count > 0 
  AND published IS NOT FALSE
  AND id IS NOT NULL; -- Explicit WHERE clause
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION update_species_published_status() TO authenticated;
GRANT EXECUTE ON FUNCTION update_species_published_status() TO anon;