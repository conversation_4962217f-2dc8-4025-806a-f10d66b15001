/*
  # Add missing columns to species table

  1. New Columns
    - `notes` (text) - General notes about the species
    - Additional fields that may be referenced in sync operations

  2. Security
    - Maintain existing RLS policies
    - Add appropriate indexes for performance
*/

-- Add the missing notes column and any other fields that might be needed
ALTER TABLE public.species 
ADD COLUMN IF NOT EXISTS notes text;

-- Add comment for documentation
COMMENT ON COLUMN public.species.notes IS 'General notes and additional information about the species';

-- Update the materialized view to include the new column if needed
DROP MATERIALIZED VIEW IF EXISTS species_photo_summary;
CREATE MATERIALIZED VIEW species_photo_summary AS
SELECT 
  s.id,
  s.name,
  s.common_name,
  s.category,
  s.conservation_status,
  COUNT(p.id) as photo_count,
  COUNT(p.id) FILTER (WHERE p.published = true) as published_photo_count,
  MAX(p.created_at) as latest_photo_date
FROM species s
LEFT JOIN photos p ON s.id = p.species_id
WHERE s.published = true
GROUP BY s.id, s.name, s.common_name, s.category, s.conservation_status;

-- Recreate the unique index
CREATE UNIQUE INDEX idx_species_photo_summary_id 
ON species_photo_summary(id);