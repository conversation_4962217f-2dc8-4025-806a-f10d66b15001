-- First, let's add some missing columns and improve the data structure
-- Add missing columns to species table if they don't exist
ALTER TABLE public.species 
ADD COLUMN IF NOT EXISTS size_cm numeric,
ADD COLUMN IF NOT EXISTS weight_g numeric,
ADD COLUMN IF NOT EXISTS lifespan_years integer,
ADD COLUMN IF NOT EXISTS migration_pattern text,
ADD COLUMN IF NOT EXISTS breeding_season text,
ADD COLUMN IF NOT EXISTS threat_level text,
ADD COLUMN IF NOT EXISTS population_trend text;

-- Add missing columns to photos table if they don't exist
ALTER TABLE public.photos 
ADD COLUMN IF NOT EXISTS camera_settings text,
ADD COLUMN IF NOT EXISTS weather_conditions text,
ADD COLUMN IF NOT EXISTS time_of_day text,
ADD COLUMN IF NOT EXISTS notes text;

-- Create a proper foreign key relationship between photos and species
ALTER TABLE public.photos 
DROP CONSTRAINT IF EXISTS photos_species_id_fkey;

ALTER TABLE public.photos 
ADD CONSTRAINT photos_species_id_fkey 
FOREIGN KEY (species_id) REFERENCES public.species(id) ON DELETE CASCADE;

-- Clean up orphaned photos (photos without valid species_id)
DELETE FROM public.photos 
WHERE species_id IS NOT NULL 
AND species_id NOT IN (SELECT id FROM public.species);

-- Update photo counts for each species
UPDATE public.species 
SET photo_count = (
  SELECT COUNT(*) 
  FROM public.photos 
  WHERE photos.species_id = species.id 
  AND photos.published = true
);

-- Clean up any species without proper categories
UPDATE public.species 
SET category = 'Birds' 
WHERE category IS NULL OR category = '';

-- Standardize conservation status values
UPDATE public.species 
SET conservation_status = CASE 
  WHEN LOWER(conservation_status) LIKE '%critical%' THEN 'Critically Endangered'
  WHEN LOWER(conservation_status) LIKE '%endanger%' THEN 'Endangered'
  WHEN LOWER(conservation_status) LIKE '%vulnerable%' THEN 'Vulnerable'
  WHEN LOWER(conservation_status) LIKE '%near%threat%' THEN 'Near Threatened'
  WHEN LOWER(conservation_status) LIKE '%least%concern%' THEN 'Least Concern'
  ELSE 'Least Concern'
END
WHERE conservation_status IS NOT NULL;

-- Insert some example species with proper data structure
INSERT INTO public.species (
  name, common_name, scientific_name, category, conservation_status,
  description, habitat, diet, behavior, published, featured
) VALUES 
  ('American Avocet', 'American Avocet', 'Recurvirostra americana', 'Birds', 'Least Concern',
   'American Avocet is a bird species found in various habitats. This species has been documented with photographs in our wildlife database.',
   'Forest, Woodland, Wetlands, Shallow lakes', 'Insects, crustaceans, worms', 'Flight, Nesting, Vocalization', true, true),
  
  ('Red-winged Blackbird', 'Red-winged Blackbird', 'Agelaius phoeniceus', 'Birds', 'Least Concern',
   'The Red-winged Blackbird is one of the most abundant birds across North America. Males are unmistakable with their red shoulder patches.',
   'Marshes, wetlands, agricultural areas', 'Seeds, insects, berries', 'Territorial, flocking in winter', true, true),
   
  ('Great Blue Heron', 'Great Blue Heron', 'Ardea herodias', 'Birds', 'Least Concern',
   'The Great Blue Heron is the largest heron in North America. These patient hunters can be found near water bodies.',
   'Wetlands, rivers, lakes, coastal areas', 'Fish, frogs, small mammals', 'Solitary hunter, wading', true, true),
   
  ('Monarch Butterfly', 'Monarch Butterfly', 'Danaus plexippus', 'Insects', 'Vulnerable',
   'The Monarch Butterfly is famous for its incredible migration across North America. Their populations have declined significantly.',
   'Fields, gardens, roadsides with milkweed', 'Nectar from flowers, milkweed', 'Long-distance migration, clustering', true, true),
   
  ('American Alligator', 'American Alligator', 'Alligator mississippiensis', 'Reptiles', 'Least Concern',
   'The American Alligator is a conservation success story, having recovered from near extinction in the 1960s.',
   'Freshwater wetlands, swamps, rivers', 'Fish, birds, mammals, reptiles', 'Ambush predator, territorial', true, true)
ON CONFLICT (name) DO UPDATE SET
  common_name = EXCLUDED.common_name,
  scientific_name = EXCLUDED.scientific_name,
  category = EXCLUDED.category,
  conservation_status = EXCLUDED.conservation_status,
  description = EXCLUDED.description,
  habitat = EXCLUDED.habitat,
  diet = EXCLUDED.diet,
  behavior = EXCLUDED.behavior,
  updated_at = now();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_species_category ON public.species(category);
CREATE INDEX IF NOT EXISTS idx_species_conservation_status ON public.species(conservation_status);
CREATE INDEX IF NOT EXISTS idx_species_published ON public.species(published);
CREATE INDEX IF NOT EXISTS idx_photos_species_id ON public.photos(species_id);
CREATE INDEX IF NOT EXISTS idx_photos_published ON public.photos(published);

-- Update the updated_at timestamp for all modified records
UPDATE public.species SET updated_at = now() WHERE updated_at < now() - interval '1 minute';
