-- Final V2 Table Migration - Fix ALL remaining references to old tables
-- This migration ensures ALL database views, functions, and references use photos_v2 and species_v2

-- Drop all views that might still reference old tables
DROP VIEW IF EXISTS orphaned_photos CASCADE;
DROP VIEW IF EXISTS duplicate_photos_by_hash CASCADE;
DROP VIEW IF EXISTS duplicate_photos_by_url CASCADE;
DROP VIEW IF EXISTS invalid_species_references CASCADE;
DROP VIEW IF EXISTS photos_with_missing_files CASCADE;
DROP VIEW IF EXISTS photo_data_quality_summary CASCADE;
DROP VIEW IF EXISTS photos_requiring_review CASCADE;
DROP VIEW IF EXISTS photos_needing_review CASCADE;
DROP VIEW IF EXISTS photos_with_missing_species CASCADE;
DROP VIEW IF EXISTS species_photo_counts CASCADE;
DROP VIEW IF EXISTS species_with_no_photos CASCADE;

-- Recreate ALL views using v2 tables
-- 1. Orphaned photos view
CREATE OR REPLACE VIEW orphaned_photos AS
SELECT 
  p.id, 
  p.url, 
  p.title, 
  p.species_id, 
  p.created_at,
  p.hash,
  'orphaned' as issue_type
FROM photos_v2 p
LEFT JOIN species_v2 s ON p.species_id = s.id
WHERE p.species_id IS NULL OR s.id IS NULL;

-- 2. Duplicate photos by hash
CREATE OR REPLACE VIEW duplicate_photos_by_hash AS
SELECT 
  hash, 
  array_agg(id ORDER BY created_at) as photo_ids, 
  count(*) as dup_count,
  'duplicate_hash' as issue_type
FROM photos_v2
WHERE hash IS NOT NULL
GROUP BY hash
HAVING count(*) > 1;

-- 3. Duplicate photos by URL
CREATE OR REPLACE VIEW duplicate_photos_by_url AS
SELECT 
  url, 
  array_agg(id ORDER BY created_at) as photo_ids, 
  count(*) as dup_count,
  'duplicate_url' as issue_type
FROM photos_v2
WHERE url IS NOT NULL AND url != ''
GROUP BY url
HAVING count(*) > 1;

-- 4. Invalid species references
CREATE OR REPLACE VIEW invalid_species_references AS
SELECT 
  p.id,
  p.url,
  p.title,
  p.species_id,
  p.created_at,
  'invalid_species_reference' as issue_type
FROM photos_v2 p
WHERE p.species_id IS NOT NULL 
  AND NOT EXISTS (SELECT 1 FROM species_v2 s WHERE s.id = p.species_id);

-- 5. Photos with missing files (placeholder - would need storage integration)
CREATE OR REPLACE VIEW photos_with_missing_files AS
SELECT 
  p.id,
  p.url,
  p.title,
  p.species_id,
  p.created_at,
  'missing_file' as issue_type
FROM photos_v2 p
WHERE p.url IS NULL OR p.url = '' OR p.url LIKE '%localhost%' OR p.url LIKE 'data:image%';

-- 6. Photo data quality summary
CREATE OR REPLACE VIEW photo_data_quality_summary AS
SELECT 
  p.id,
  p.url,
  p.title,
  p.species_id,
  p.created_at,
  CASE 
    WHEN op.id IS NOT NULL THEN 'Orphaned Photo'
    WHEN dp_hash.hash IS NOT NULL THEN 'Duplicate by Hash'
    WHEN dp_url.url IS NOT NULL THEN 'Duplicate by URL'
    WHEN isr.id IS NOT NULL THEN 'Invalid Species Reference'
    WHEN pmf.id IS NOT NULL THEN 'Missing File'
    ELSE 'No issues detected'
  END as issue_description
FROM photos_v2 p
LEFT JOIN species_v2 s ON p.species_id = s.id
LEFT JOIN orphaned_photos op ON p.id = op.id
LEFT JOIN duplicate_photos_by_hash dp_hash ON p.hash = dp_hash.hash
LEFT JOIN duplicate_photos_by_url dp_url ON p.url = dp_url.url
LEFT JOIN invalid_species_references isr ON p.id = isr.id
LEFT JOIN photos_with_missing_files pmf ON p.id = pmf.id;

-- 7. Photos requiring review
CREATE OR REPLACE VIEW photos_requiring_review AS
SELECT 
  p.id,
  p.url,
  p.species_id,
  p.needs_recovery,
  p.published,
  CASE
    WHEN p.needs_recovery = true THEN 'marked_for_recovery'
    WHEN p.species_id IS NOT NULL AND NOT (EXISTS (
        SELECT 1 FROM species_v2 s WHERE s.id = p.species_id
    )) THEN 'invalid_species_id'
    WHEN p.published = true AND (
        p.url IS NULL OR 
        p.url = '' OR 
        p.url LIKE '%localhost%' OR 
        p.url LIKE 'data:image%' OR 
        p.url LIKE '%\n%' OR 
        p.url LIKE '%\r%'
    ) THEN 'published_with_bad_url'
    ELSE 'unknown'
  END AS reason
FROM photos_v2 p
WHERE 
  p.needs_recovery = true 
  OR (p.species_id IS NOT NULL AND NOT (EXISTS (
      SELECT 1 FROM species_v2 s WHERE s.id = p.species_id
  )))
  OR (p.published = true AND (
      p.url IS NULL OR 
      p.url = '' OR 
      p.url LIKE '%localhost%' OR 
      p.url LIKE 'data:image%' OR 
      p.url LIKE '%\n%' OR 
      p.url LIKE '%\r%'
  ));

-- 8. Photos needing review
CREATE OR REPLACE VIEW photos_needing_review AS
SELECT
  p.id,
  p.url,
  p.title,
  p.description,
  p.location,
  p.species_id,
  p.published,
  p.created_at,
  s.name as species_name,
  s.scientific_name,
  s.common_name,
  COALESCE(p.ai_generated_metadata, false) as ai_generated_metadata,
  COALESCE(p.needs_review, false) as needs_review
FROM photos_v2 p
LEFT JOIN species_v2 s ON p.species_id = s.id
WHERE p.published = false OR COALESCE(p.needs_review, false) = true
ORDER BY p.created_at DESC;

-- 9. Photos with missing species
CREATE OR REPLACE VIEW photos_with_missing_species AS
SELECT 
    count(*) AS count,
    'photos_with_missing_species_id' AS issue_type
FROM photos_v2 
WHERE species_id IS NULL
UNION ALL
SELECT 
    count(*) AS count,
    'photos_with_invalid_species_id' AS issue_type
FROM photos_v2 p
WHERE p.species_id IS NOT NULL 
    AND NOT EXISTS (SELECT 1 FROM species_v2 s WHERE s.id = p.species_id);

-- 10. Species photo counts (CRITICAL for fixing the photo count display issue)
CREATE OR REPLACE VIEW species_photo_counts AS
SELECT 
    s.id,
    s.name,
    s.common_name,
    s.scientific_name,
    s.category,
    s.conservation_status,
    count(p.id) AS total_photos,
    sum(CASE WHEN p.published THEN 1 ELSE 0 END) AS published_photos,
    sum(CASE WHEN NOT p.published THEN 1 ELSE 0 END) AS unpublished_photos,
    0 AS ai_tagged_photos,
    0 AS photos_needing_review,
    s.created_at,
    s.updated_at
FROM species_v2 s
LEFT JOIN photos_v2 p ON s.id = p.species_id
GROUP BY 
    s.id, s.name, s.common_name, s.scientific_name, 
    s.category, s.conservation_status, s.created_at, s.updated_at
ORDER BY count(p.id) DESC;

-- 11. Species with no photos
CREATE OR REPLACE VIEW species_with_no_photos AS
SELECT 
  s.id,
  s.name,
  s.scientific_name,
  s.category,
  s.conservation_status,
  s.published,
  s.created_at,
  'no_photos' as issue_type
FROM species_v2 s
LEFT JOIN photos_v2 p ON s.id = p.species_id
WHERE p.id IS NULL
  AND s.published = true
ORDER BY s.created_at DESC;

-- Update all indexes to use v2 tables
CREATE INDEX IF NOT EXISTS idx_photos_v2_species_id ON photos_v2(species_id);
CREATE INDEX IF NOT EXISTS idx_photos_v2_published ON photos_v2(published);
CREATE INDEX IF NOT EXISTS idx_photos_v2_hash ON photos_v2(hash);
CREATE INDEX IF NOT EXISTS idx_photos_v2_url ON photos_v2(url);
CREATE INDEX IF NOT EXISTS idx_species_v2_published ON species_v2(published);
CREATE INDEX IF NOT EXISTS idx_species_v2_category ON species_v2(category);
CREATE INDEX IF NOT EXISTS idx_species_v2_conservation_status ON species_v2(conservation_status);

-- Grant permissions
GRANT SELECT ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon;

-- Add comments for documentation
COMMENT ON VIEW species_photo_counts IS 'Species with accurate photo counts using photos_v2 and species_v2 tables';
COMMENT ON VIEW orphaned_photos IS 'Photos with missing or invalid species_id references to species_v2';
COMMENT ON VIEW photos_with_missing_species IS 'Photos with missing or invalid species_id references to species_v2';
COMMENT ON VIEW species_with_no_photos IS 'Published species that have no associated photos in photos_v2';
COMMENT ON VIEW photos_needing_review IS 'Photos that need review using photos_v2 and species_v2 tables';
