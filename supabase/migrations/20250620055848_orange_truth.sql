/*
  # Create storage bucket for photos

  1. Storage Setup
    - Create 'species' bucket for photo storage
    - Set up public access policies
    - Configure file upload restrictions

  2. Security
    - Allow public read access for photos
    - Allow authenticated users to upload
    - Restrict file types and sizes
*/

-- Create the storage bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'species',
  'species',
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
) ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Allow public read access to photos
CREATE POLICY "Public read access for photos" ON storage.objects
  FOR SELECT USING (bucket_id = 'species');

-- Allow authenticated users to upload photos
CREATE POLICY "Authenticated users can upload photos" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'species' 
    AND auth.role() = 'authenticated'
  );

-- Allow authenticated users to delete their own uploads
CREATE POLICY "Authenticated users can delete photos" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'species' 
    AND auth.role() = 'authenticated'
  );

-- Allow authenticated users to update photo metadata
CREATE POLICY "Authenticated users can update photos" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'species' 
    AND auth.role() = 'authenticated'
  );