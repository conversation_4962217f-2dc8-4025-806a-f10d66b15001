-- supabase/migrations/20250622041607_create_execute_sql_function.sql

-- Creates a function that can execute arbitrary SQL.
-- This is useful for admin scripts that need to perform schema changes.
-- The function is secured to only be callable by users with the service_role key.
create or replace function execute_sql(sql text)
returns void
language plpgsql
security definer
set search_path = public
as $$
begin
  execute sql;
end;
$$;

grant execute on function public.execute_sql(text) to postgres;
