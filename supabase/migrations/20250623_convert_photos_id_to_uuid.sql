-- Migration: Convert photos.id from bigint to uuid
-- Enables UUID-based seeding and distributed workflows
-- This migration is idempotent and handles all dependencies

-- 1. Enable pgcrypto for UUID generation
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- 2. Drop dependent views (will be recreated at the end)
DROP VIEW IF EXISTS photos_requiring_review CASCADE;
DROP VIEW IF EXISTS species_photo_counts CASCADE;
DROP VIEW IF EXISTS ai_assignment_stats CASCADE;
DROP VIEW IF EXISTS recent_ai_overrides CASCADE;

-- 3. Drop triggers on photos (will be recreated)
DROP TRIGGER IF EXISTS update_photos_updated_at ON photos;
DROP TRIGGER IF EXISTS photos_update_species_count ON photos;
DROP TRIGGER IF EXISTS photos_ai_override_trigger ON photos;

-- 4. Drop indexes on photos (will be recreated)
DROP INDEX IF EXISTS idx_photos_species_id;
DROP INDEX IF EXISTS idx_photos_published;
DROP INDEX IF EXISTS idx_photos_created_at_desc;
DROP INDEX IF EXISTS idx_photos_species_published;
DROP INDEX IF EXISTS idx_photos_airtable_id;
DROP INDEX IF EXISTS idx_photos_hash;
DROP INDEX IF EXISTS idx_photos_url;
DROP INDEX IF EXISTS idx_photos_url_status;
DROP INDEX IF EXISTS idx_photos_ai_generated;
DROP INDEX IF EXISTS idx_photos_needs_review;
DROP INDEX IF EXISTS idx_photos_published_created_at;

-- 5. Alter photos.id to uuid
-- 5a. Add new UUID column
ALTER TABLE photos ADD COLUMN IF NOT EXISTS id_new uuid DEFAULT gen_random_uuid();
-- 5b. Populate id_new for all rows
UPDATE photos SET id_new = gen_random_uuid() WHERE id_new IS NULL;
-- 5c. Drop old PK constraint
DO $$ BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE table_name='photos' AND constraint_type='PRIMARY KEY') THEN
    ALTER TABLE photos DROP CONSTRAINT photos_pkey;
  END IF;
END $$;
-- 5d. Drop old id column, rename id_new to id
ALTER TABLE photos DROP COLUMN IF EXISTS id;
ALTER TABLE photos RENAME COLUMN id_new TO id;
-- 5e. Set new PK and not null
ALTER TABLE photos ALTER COLUMN id SET NOT NULL;
ALTER TABLE photos ADD PRIMARY KEY (id);
-- 5f. Set default for new inserts
ALTER TABLE photos ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- 6. Recreate indexes on photos
CREATE INDEX IF NOT EXISTS idx_photos_species_id ON photos(species_id);
CREATE INDEX IF NOT EXISTS idx_photos_published ON photos(published);
CREATE INDEX IF NOT EXISTS idx_photos_created_at_desc ON photos(created_at DESC) WHERE published = true;
CREATE INDEX IF NOT EXISTS idx_photos_species_published ON photos(species_id, published) WHERE published = true;
CREATE INDEX IF NOT EXISTS idx_photos_airtable_id ON photos(airtable_id) WHERE airtable_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_photos_hash ON photos(hash) WHERE hash IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_photos_url ON photos(url) WHERE url IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_photos_url_status ON photos(url_status);
CREATE INDEX IF NOT EXISTS idx_photos_ai_generated ON photos(ai_generated_metadata);
CREATE INDEX IF NOT EXISTS idx_photos_needs_review ON photos(needs_review);
CREATE INDEX IF NOT EXISTS idx_photos_published_created_at ON photos(published, created_at DESC);

-- 7. Recreate triggers on photos
-- update_photos_updated_at
CREATE TRIGGER update_photos_updated_at
  BEFORE UPDATE ON photos
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();
-- photos_update_species_count
CREATE TRIGGER photos_update_species_count
  AFTER INSERT OR UPDATE OR DELETE ON photos
  FOR EACH ROW
  EXECUTE FUNCTION trigger_update_species_photo_count();
-- photos_ai_override_trigger
CREATE TRIGGER photos_ai_override_trigger
  AFTER UPDATE ON photos
  FOR EACH ROW
  EXECUTE FUNCTION photos_ai_override_trigger();

-- 8. Recreate dropped views (definitions as before, but now referencing uuid id)
-- photos_requiring_review
CREATE VIEW photos_requiring_review AS
SELECT 
    p.id,
    p.url,
    p.species_id,
    p.needs_recovery,
    p.published,
    CASE
        WHEN p.needs_recovery = true THEN 'marked_for_recovery'
        WHEN p.species_id IS NOT NULL AND NOT (EXISTS (
            SELECT 1 FROM species s WHERE s.id = p.species_id
        )) THEN 'invalid_species_id'
        WHEN p.published = true AND (
            p.url IS NULL OR 
            p.url = '' OR 
            p.url LIKE '%localhost%' OR 
            p.url LIKE 'data:image%' OR 
            p.url LIKE '%\n%' OR 
            p.url LIKE '%\r%'
        ) THEN 'published_with_bad_url'
        ELSE 'unknown'
    END AS reason
FROM photos p
WHERE 
    p.needs_recovery = true 
    OR (p.species_id IS NOT NULL AND NOT (EXISTS (
        SELECT 1 FROM species s WHERE s.id = p.species_id
    )))
    OR (p.published = true AND (
        p.url IS NULL OR 
        p.url = '' OR 
        p.url LIKE '%localhost%' OR 
        p.url LIKE 'data:image%' OR 
        p.url LIKE '%\n%' OR 
        p.url LIKE '%\r%'
    ));
-- species_photo_counts
CREATE VIEW species_photo_counts AS
SELECT 
    s.id,
    s.name,
    s.common_name,
    s.scientific_name,
    s.category,
    s.conservation_status,
    count(p.id) AS total_photos,
    sum(CASE WHEN p.published THEN 1 ELSE 0 END) AS published_photos,
    sum(CASE WHEN NOT p.published THEN 1 ELSE 0 END) AS unpublished_photos,
    0 AS ai_tagged_photos,
    0 AS photos_needing_review,
    s.created_at,
    s.updated_at
FROM species s
LEFT JOIN photos p ON s.id = p.species_id
GROUP BY 
    s.id, s.name, s.common_name, s.scientific_name, 
    s.category, s.conservation_status, s.created_at, s.updated_at
ORDER BY count(p.id) DESC;
-- ai_assignment_stats
CREATE VIEW ai_assignment_stats AS
SELECT 
    count(*) AS total_photos,
    0 AS ai_tagged,
    0 AS flagged_for_review,
    0.0 AS avg_confidence,
    sum(CASE WHEN photos.published THEN 1 ELSE 0 END) AS published_photos,
    sum(CASE WHEN NOT photos.published THEN 1 ELSE 0 END) AS unpublished_photos
FROM photos;
-- recent_ai_overrides
CREATE VIEW recent_ai_overrides AS
SELECT 
    l.id,
    l.table_name,
    l.record_id,
    l.field_name,
    l.old_value,
    l.new_value,
    l.override_type,
    l.override_reason,
    l.created_at,
    CASE
        WHEN l.table_name = 'photos' THEN p.title
        WHEN l.table_name = 'species' THEN s.name
        ELSE 'Unknown'
    END AS record_title,
    CASE
        WHEN l.table_name = 'photos' THEN p.url
        ELSE NULL
    END AS photo_url
FROM ai_override_log l
LEFT JOIN photos p ON l.table_name = 'photos' AND l.record_id = p.id::text
LEFT JOIN species s ON l.table_name = 'species' AND l.record_id = s.id::text
ORDER BY l.created_at DESC
LIMIT 100;

-- 9. Add SQL comments for clarity
COMMENT ON COLUMN photos.id IS 'Primary key for photos, now UUID for distributed/AI workflows';
COMMENT ON TABLE photos IS 'Stores photo records, now with UUID primary key for distributed/AI workflows';
-- End of migration 