-- Update database views to use species_v2 instead of species table
-- This migration updates all views and functions to reference the new species_v2 table

-- Drop and recreate species_photo_counts view to use species_v2
DROP VIEW IF EXISTS species_photo_counts CASCADE;
CREATE VIEW species_photo_counts AS
SELECT 
    s.id,
    s.name,
    s.common_name,
    s.scientific_name,
    s.category,
    s.conservation_status,
    count(p.id) AS total_photos,
    sum(CASE WHEN p.published THEN 1 ELSE 0 END) AS published_photos,
    sum(CASE WHEN NOT p.published THEN 1 ELSE 0 END) AS unpublished_photos,
    0 AS ai_tagged_photos,
    0 AS photos_needing_review,
    s.created_at,
    s.updated_at
FROM species_v2 s
LEFT JOIN photos p ON s.id = p.species_id
GROUP BY 
    s.id, s.name, s.common_name, s.scientific_name, 
    s.category, s.conservation_status, s.created_at, s.updated_at
ORDER BY count(p.id) DESC;

-- Update recent_ai_overrides view to use species_v2
DROP VIEW IF EXISTS recent_ai_overrides CASCADE;
CREATE VIEW recent_ai_overrides AS
SELECT 
    l.id,
    l.table_name,
    l.record_id,
    l.field_name,
    l.old_value,
    l.new_value,
    l.override_type,
    l.override_reason,
    l.created_at,
    CASE
        WHEN l.table_name = 'photos' THEN p.title
        WHEN l.table_name = 'species' THEN s.name
        ELSE 'Unknown'
    END AS record_title,
    CASE
        WHEN l.table_name = 'photos' THEN p.url
        ELSE NULL
    END AS photo_url
FROM ai_override_log l
LEFT JOIN photos p ON l.table_name = 'photos' AND l.record_id = p.id::text
LEFT JOIN species_v2 s ON l.table_name = 'species' AND l.record_id = s.id::text
ORDER BY l.created_at DESC
LIMIT 100;

-- Update photos_with_missing_species view to use species_v2
DROP VIEW IF EXISTS photos_with_missing_species CASCADE;
CREATE VIEW photos_with_missing_species AS
SELECT 
    count(*) AS count,
    'photos_with_missing_species_id' AS issue_type
FROM photos 
WHERE species_id IS NULL
UNION ALL
SELECT 
    count(*) AS count,
    'photos_with_invalid_species_id' AS issue_type
FROM photos p
WHERE p.species_id IS NOT NULL 
    AND NOT EXISTS (SELECT 1 FROM species_v2 s WHERE s.id = p.species_id);

-- Update functions to use species_v2
CREATE OR REPLACE FUNCTION update_species_photo_counts()
RETURNS void AS $$
BEGIN
  UPDATE species_v2 
  SET photo_count = (
    SELECT COUNT(*) 
    FROM photos 
    WHERE photos.species_id = species_v2.id 
    AND photos.published = true
  );
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_species_published_status()
RETURNS void AS $$
BEGIN
  -- Unpublish species without photos
  UPDATE species_v2 
  SET published = false 
  WHERE photo_count = 0 OR photo_count IS NULL;
  
  -- Publish species with photos (if they weren't explicitly unpublished)
  UPDATE species_v2 
  SET published = true 
  WHERE photo_count > 0 AND published IS NOT FALSE;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_species_with_photo_stats()
RETURNS TABLE (
  species_id text,
  species_name text,
  total_photos bigint,
  published_photos bigint,
  airtable_photos bigint,
  uploaded_photos bigint
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.id::text as species_id,
    s.name as species_name,
    COUNT(p.id) as total_photos,
    COUNT(p.id) FILTER (WHERE p.published = true) as published_photos,
    COUNT(p.id) FILTER (WHERE p.airtable_id IS NOT NULL) as airtable_photos,
    COUNT(p.id) FILTER (WHERE p.airtable_id IS NULL) as uploaded_photos
  FROM species_v2 s
  LEFT JOIN photos p ON s.id = p.species_id
  GROUP BY s.id, s.name
  ORDER BY s.name;
END;
$$ LANGUAGE plpgsql;

-- Update fun_facts table foreign key to reference species_v2
-- First drop the existing constraint
ALTER TABLE fun_facts DROP CONSTRAINT IF EXISTS fun_facts_species_id_fkey;

-- Add new constraint pointing to species_v2
ALTER TABLE fun_facts 
ADD CONSTRAINT fun_facts_species_id_fkey 
FOREIGN KEY (species_id) REFERENCES species_v2(id) ON DELETE CASCADE;

-- Update add_species_fun_facts function to use species_v2
CREATE OR REPLACE FUNCTION add_species_fun_facts(
    p_scientific_name TEXT,
    p_facts TEXT[]
) RETURNS INTEGER AS $$
DECLARE
    species_uuid UUID;
    fact TEXT;
    inserted_count INTEGER := 0;
BEGIN
    -- Get the species UUID from species_v2
    SELECT id INTO species_uuid 
    FROM species_v2 
    WHERE scientific_name = p_scientific_name;
    
    IF species_uuid IS NULL THEN
        RAISE EXCEPTION 'Species % not found', p_scientific_name;
    END IF;
    
    -- Insert each fun fact
    FOREACH fact IN ARRAY p_facts
    LOOP
        INSERT INTO fun_facts (species_id, fact) VALUES (species_uuid, fact);
        inserted_count := inserted_count + 1;
    END LOOP;
    
    RAISE NOTICE 'Added % fun facts for species %', inserted_count, p_scientific_name;
    RETURN inserted_count;
END;
$$ LANGUAGE plpgsql;

-- Update species_needing_review view to use species_v2
DROP VIEW IF EXISTS species_needing_review CASCADE;
CREATE OR REPLACE VIEW public.species_needing_review AS
SELECT
  id,
  name,
  description,
  behavior,
  family,
  conservation_actions,
  size_description
FROM public.species_v2
WHERE
  description IS NULL
  OR behavior IS NULL
  OR family IS NULL
  OR conservation_actions IS NULL
  OR size_description IS NULL;

-- Grant necessary permissions
GRANT SELECT ON species_photo_counts TO authenticated;
GRANT SELECT ON species_photo_counts TO service_role;
GRANT SELECT ON species_photo_counts TO anon;

GRANT SELECT ON recent_ai_overrides TO authenticated;
GRANT SELECT ON recent_ai_overrides TO service_role;
GRANT SELECT ON recent_ai_overrides TO anon;

GRANT SELECT ON photos_with_missing_species TO authenticated;
GRANT SELECT ON photos_with_missing_species TO service_role;
GRANT SELECT ON photos_with_missing_species TO anon;

GRANT SELECT ON species_needing_review TO authenticated;
GRANT SELECT ON species_needing_review TO service_role;
GRANT SELECT ON species_needing_review TO anon;

-- Add helpful comments
COMMENT ON VIEW species_photo_counts IS 'Species with photo counts using species_v2 table';
COMMENT ON VIEW recent_ai_overrides IS 'Recent AI overrides with species_v2 references';
COMMENT ON VIEW photos_with_missing_species IS 'Photos with missing or invalid species_id references to species_v2';
COMMENT ON VIEW species_needing_review IS 'Species that need review using species_v2 table';
