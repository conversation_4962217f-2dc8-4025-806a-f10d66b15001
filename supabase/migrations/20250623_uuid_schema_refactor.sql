-- UUID Schema Migration
-- Converts photos.id from bigint to UUID and handles all dependent views
-- This migration is idempotent and handles the PostgreSQL limitation of not allowing
-- column type changes when views depend on them

-- Enable pgcrypto extension for gen_random_uuid()
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Drop dependent views that reference photos.id
-- These views will be recreated after the column type change
DROP VIEW IF EXISTS photos_requiring_review CASCADE;
DROP VIEW IF EXISTS species_photo_counts CASCADE;
DROP VIEW IF EXISTS ai_assignment_stats CASCADE;
DROP VIEW IF EXISTS recent_ai_overrides CASCADE;

-- Drop functions that reference photos.id as integer
-- These will be recreated with UUID support
DROP FUNCTION IF EXISTS cleanup_duplicate_photos() CASCADE;
DROP FUNCTION IF EXISTS cleanup_orphaned_photos() CASCADE;

-- Convert photos.id from bigint to UUID
-- First, add a new UUID column
ALTER TABLE photos ADD COLUMN id_new uuid DEFAULT gen_random_uuid();

-- Update the new column with UUIDs for existing records
-- We'll use gen_random_uuid() for all existing records
UPDATE photos SET id_new = gen_random_uuid() WHERE id_new IS NULL;

-- Drop the old primary key constraint
ALTER TABLE photos DROP CONSTRAINT photos_pkey;

-- Drop the old id column and rename the new one
ALTER TABLE photos DROP COLUMN id;
ALTER TABLE photos RENAME COLUMN id_new TO id;

-- Make the new id column NOT NULL and set it as primary key
ALTER TABLE photos ALTER COLUMN id SET NOT NULL;
ALTER TABLE photos ADD CONSTRAINT photos_pkey PRIMARY KEY (id);

-- Recreate the dropped views with updated column references

-- Recreate photos_requiring_review view
CREATE VIEW photos_requiring_review AS
SELECT 
    p.id,
    p.url,
    p.species_id,
    p.needs_recovery,
    p.published,
    CASE
        WHEN p.needs_recovery = true THEN 'marked_for_recovery'
        WHEN p.species_id IS NOT NULL AND NOT (EXISTS (
            SELECT 1 FROM species s WHERE s.id = p.species_id
        )) THEN 'invalid_species_id'
        WHEN p.published = true AND (
            p.url IS NULL OR 
            p.url = '' OR 
            p.url LIKE '%localhost%' OR 
            p.url LIKE 'data:image%' OR 
            p.url LIKE '%\n%' OR 
            p.url LIKE '%\r%'
        ) THEN 'published_with_bad_url'
        ELSE 'unknown'
    END AS reason
FROM photos p
WHERE 
    p.needs_recovery = true 
    OR (p.species_id IS NOT NULL AND NOT (EXISTS (
        SELECT 1 FROM species s WHERE s.id = p.species_id
    )))
    OR (p.published = true AND (
        p.url IS NULL OR 
        p.url = '' OR 
        p.url LIKE '%localhost%' OR 
        p.url LIKE 'data:image%' OR 
        p.url LIKE '%\n%' OR 
        p.url LIKE '%\r%'
    ));

-- Recreate species_photo_counts view
CREATE VIEW species_photo_counts AS
SELECT 
    s.id,
    s.name,
    s.common_name,
    s.scientific_name,
    s.category,
    s.conservation_status,
    count(p.id) AS total_photos,
    sum(CASE WHEN p.published THEN 1 ELSE 0 END) AS published_photos,
    sum(CASE WHEN NOT p.published THEN 1 ELSE 0 END) AS unpublished_photos,
    0 AS ai_tagged_photos,
    0 AS photos_needing_review,
    s.created_at,
    s.updated_at
FROM species s
LEFT JOIN photos p ON s.id = p.species_id
GROUP BY 
    s.id, s.name, s.common_name, s.scientific_name, 
    s.category, s.conservation_status, s.created_at, s.updated_at
ORDER BY count(p.id) DESC;

-- Recreate ai_assignment_stats view
CREATE VIEW ai_assignment_stats AS
SELECT 
    count(*) AS total_photos,
    0 AS ai_tagged,
    0 AS flagged_for_review,
    0.0 AS avg_confidence,
    sum(CASE WHEN photos.published THEN 1 ELSE 0 END) AS published_photos,
    sum(CASE WHEN NOT photos.published THEN 1 ELSE 0 END) AS unpublished_photos
FROM photos;

-- Recreate recent_ai_overrides view
CREATE VIEW recent_ai_overrides AS
SELECT 
    l.id,
    l.table_name,
    l.record_id,
    l.field_name,
    l.old_value,
    l.new_value,
    l.override_type,
    l.override_reason,
    l.created_at,
    CASE
        WHEN l.table_name = 'photos' THEN p.title
        WHEN l.table_name = 'species' THEN s.name
        ELSE 'Unknown'
    END AS record_title,
    CASE
        WHEN l.table_name = 'photos' THEN p.url
        ELSE NULL
    END AS photo_url
FROM ai_override_log l
LEFT JOIN photos p ON l.table_name = 'photos' AND l.record_id = p.id::text
LEFT JOIN species s ON l.table_name = 'species' AND l.record_id = s.id::text
ORDER BY l.created_at DESC
LIMIT 100;

-- Recreate functions with UUID support

-- Function to clean up duplicate photos (updated for UUID)
CREATE OR REPLACE FUNCTION cleanup_duplicate_photos()
RETURNS TABLE(deleted_count INTEGER, kept_photos UUID[]) AS $$
DECLARE
  duplicate_record RECORD;
  photo_ids_to_delete UUID[];
  kept_photo_id UUID;
BEGIN
  deleted_count := 0;
  kept_photos := ARRAY[]::UUID[];

  -- Handle hash duplicates
  FOR duplicate_record IN
    SELECT hash, photo_ids
    FROM duplicate_photos_by_hash
  LOOP
    -- Keep the first photo (oldest), delete the rest
    kept_photo_id := duplicate_record.photo_ids[1];
    photo_ids_to_delete := duplicate_record.photo_ids[2:array_length(duplicate_record.photo_ids, 1)];

    -- Delete duplicate photos
    DELETE FROM photos WHERE id = ANY(photo_ids_to_delete);

    -- Log the operation
    INSERT INTO photo_cleanup_log (operation_type, photo_ids, details)
    VALUES (
      'delete_hash_duplicates',
      photo_ids_to_delete,
      jsonb_build_object(
        'hash', duplicate_record.hash,
        'kept_photo_id', kept_photo_id,
        'deleted_count', array_length(photo_ids_to_delete, 1)
      )
    );

    deleted_count := deleted_count + array_length(photo_ids_to_delete, 1);
    kept_photos := array_append(kept_photos, kept_photo_id);
  END LOOP;

  -- Handle URL duplicates
  FOR duplicate_record IN
    SELECT url, photo_ids
    FROM duplicate_photos_by_url
  LOOP
    -- Keep the first photo (oldest), delete the rest
    kept_photo_id := duplicate_record.photo_ids[1];
    photo_ids_to_delete := duplicate_record.photo_ids[2:array_length(duplicate_record.photo_ids, 1)];

    -- Delete duplicate photos
    DELETE FROM photos WHERE id = ANY(photo_ids_to_delete);

    -- Log the operation
    INSERT INTO photo_cleanup_log (operation_type, photo_ids, details)
    VALUES (
      'delete_url_duplicates',
      photo_ids_to_delete,
      jsonb_build_object(
        'url', duplicate_record.url,
        'kept_photo_id', kept_photo_id,
        'deleted_count', array_length(photo_ids_to_delete, 1)
      )
    );

    deleted_count := deleted_count + array_length(photo_ids_to_delete, 1);
    kept_photos := array_append(kept_photos, kept_photo_id);
  END LOOP;

  RETURN NEXT;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up orphaned photos (updated for UUID)
CREATE OR REPLACE FUNCTION cleanup_orphaned_photos()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
  orphaned_ids UUID[];
BEGIN
  -- Get all orphaned photo IDs
  SELECT array_agg(id) INTO orphaned_ids FROM orphaned_photos;

  IF orphaned_ids IS NULL THEN
    RETURN 0;
  END IF;

  -- Delete orphaned photos
  DELETE FROM photos WHERE id = ANY(orphaned_ids);

  -- Log the operation
  INSERT INTO photo_cleanup_log (operation_type, photo_ids, details)
  VALUES (
    'delete_orphaned_photos',
    orphaned_ids,
    jsonb_build_object('deleted_count', array_length(orphaned_ids, 1))
  );

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create a new view to count photos with missing species_id
CREATE VIEW photos_with_missing_species AS
SELECT 
    count(*) AS count,
    'photos_with_missing_species_id' AS issue_type
FROM photos 
WHERE species_id IS NULL
UNION ALL
SELECT 
    count(*) AS count,
    'photos_with_invalid_species_id' AS issue_type
FROM photos p
WHERE p.species_id IS NOT NULL 
    AND NOT EXISTS (SELECT 1 FROM species s WHERE s.id = p.species_id);

-- Add helpful comment for future reference
COMMENT ON VIEW photos_with_missing_species IS 'Counts photos that have missing or invalid species_id references for data quality monitoring'; 