DROP VIEW IF EXISTS photos_requiring_review;

-- Final fix for the photos_requiring_review view to only show photos that actually need review
create or replace view "public"."photos_requiring_review" as
select
  p.id,
  p.url,
  p.species_id,
  p.needs_recovery,
  p.published,
  case
    when p.needs_recovery = true then 'marked_for_recovery'
    when p.species_id is not null
    and not exists (
      select
        1
      from
        species s
      where
        s.id = p.species_id
    ) then 'invalid_species_id'
    when p.published = true
    and (
      p.url is null
      or p.url = ''
      or p.url like '%localhost%'
      or p.url like 'data:image%'
      or p.url like E'%\\n%'
      or p.url like E'%\\r%'
    ) then 'published_with_bad_url'
    else 'unknown'
  end as reason
from
  photos p
where
  p.needs_recovery = true
  or (
    p.species_id is not null
    and not exists (
      select
        1
      from
        species s
      where
        s.id = p.species_id
    )
  )
  or (
    p.published = true
    and (
      p.url is null
      or p.url = ''
      or p.url like '%localhost%'
      or p.url like 'data:image%'
      or p.url like E'%\\n%'
      or p.url like E'%\\r%'
    )
  ); 