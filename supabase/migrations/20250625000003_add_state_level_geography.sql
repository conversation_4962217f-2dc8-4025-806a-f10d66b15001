-- Add state-level geographic granularity to species_v2 table
-- This allows for more precise location tagging while maintaining usability

-- Add new geographic fields to species_v2
ALTER TABLE species_v2 
ADD COLUMN IF NOT EXISTS countries TEXT[], -- Array of countries
ADD COLUMN IF NOT EXISTS states_provinces TEXT[], -- Array of states/provinces  
ADD COLUMN IF NOT EXISTS geographic_scope TEXT DEFAULT 'regional', -- 'global', 'continental', 'national', 'regional', 'local'
ADD COLUMN IF NOT EXISTS primary_region TEXT, -- Main geographic region for the species
ADD COLUMN IF NOT EXISTS habitat_specificity TEXT DEFAULT 'general'; -- 'endemic', 'specialized', 'general', 'widespread'

-- Create index for geographic searches
CREATE INDEX IF NOT EXISTS idx_species_v2_countries ON species_v2 USING gin(countries);
CREATE INDEX IF NOT EXISTS idx_species_v2_states_provinces ON species_v2 USING gin(states_provinces);
CREATE INDEX IF NOT EXISTS idx_species_v2_geographic_scope ON species_v2(geographic_scope);
CREATE INDEX IF NOT EXISTS idx_species_v2_primary_region ON species_v2(primary_region);

-- Update existing species with sample geographic data
UPDATE species_v2 SET 
  countries = CASE 
    WHEN regions ILIKE '%united states%' OR regions ILIKE '%usa%' OR regions ILIKE '%america%' THEN ARRAY['United States']
    WHEN regions ILIKE '%canada%' THEN ARRAY['Canada'] 
    WHEN regions ILIKE '%mexico%' THEN ARRAY['Mexico']
    WHEN regions ILIKE '%south america%' THEN ARRAY['Brazil', 'Argentina', 'Colombia', 'Peru', 'Venezuela']
    WHEN regions ILIKE '%europe%' THEN ARRAY['United Kingdom', 'France', 'Germany', 'Spain', 'Italy']
    WHEN regions ILIKE '%africa%' THEN ARRAY['Kenya', 'Tanzania', 'South Africa', 'Botswana']
    WHEN regions ILIKE '%asia%' THEN ARRAY['China', 'India', 'Japan', 'Thailand', 'Indonesia']
    WHEN regions ILIKE '%australia%' THEN ARRAY['Australia']
    ELSE ARRAY['Global']
  END,
  states_provinces = CASE 
    WHEN regions ILIKE '%california%' THEN ARRAY['California']
    WHEN regions ILIKE '%florida%' THEN ARRAY['Florida'] 
    WHEN regions ILIKE '%texas%' THEN ARRAY['Texas']
    WHEN regions ILIKE '%new york%' THEN ARRAY['New York']
    WHEN regions ILIKE '%alaska%' THEN ARRAY['Alaska']
    WHEN regions ILIKE '%ontario%' THEN ARRAY['Ontario']
    WHEN regions ILIKE '%british columbia%' THEN ARRAY['British Columbia']
    WHEN regions ILIKE '%eastern%' AND countries @> ARRAY['United States'] THEN ARRAY['New York', 'Pennsylvania', 'Virginia', 'North Carolina', 'South Carolina', 'Georgia', 'Florida']
    WHEN regions ILIKE '%western%' AND countries @> ARRAY['United States'] THEN ARRAY['California', 'Oregon', 'Washington', 'Nevada', 'Arizona', 'Utah', 'Colorado']
    WHEN regions ILIKE '%midwest%' AND countries @> ARRAY['United States'] THEN ARRAY['Illinois', 'Indiana', 'Iowa', 'Kansas', 'Michigan', 'Minnesota', 'Missouri', 'Nebraska', 'North Dakota', 'Ohio', 'South Dakota', 'Wisconsin']
    WHEN regions ILIKE '%southeast%' AND countries @> ARRAY['United States'] THEN ARRAY['Alabama', 'Arkansas', 'Florida', 'Georgia', 'Kentucky', 'Louisiana', 'Mississippi', 'North Carolina', 'South Carolina', 'Tennessee', 'Virginia', 'West Virginia']
    ELSE NULL
  END,
  geographic_scope = CASE 
    WHEN regions ILIKE '%global%' OR regions ILIKE '%worldwide%' THEN 'global'
    WHEN regions ILIKE '%north america%' OR regions ILIKE '%south america%' THEN 'continental'
    WHEN regions ILIKE '%united states%' OR regions ILIKE '%canada%' OR regions ILIKE '%mexico%' THEN 'national'
    WHEN regions ILIKE '%california%' OR regions ILIKE '%florida%' OR regions ILIKE '%texas%' THEN 'regional'
    ELSE 'regional'
  END,
  primary_region = CASE 
    WHEN regions ILIKE '%north america%' THEN 'North America'
    WHEN regions ILIKE '%south america%' THEN 'South America'
    WHEN regions ILIKE '%europe%' THEN 'Europe'
    WHEN regions ILIKE '%africa%' THEN 'Africa'
    WHEN regions ILIKE '%asia%' THEN 'Asia'
    WHEN regions ILIKE '%australia%' OR regions ILIKE '%oceania%' THEN 'Oceania'
    WHEN regions ILIKE '%arctic%' THEN 'Arctic'
    WHEN regions ILIKE '%antarctic%' THEN 'Antarctica'
    ELSE 'North America'
  END
WHERE regions IS NOT NULL;

-- Create a view for easy geographic filtering
CREATE OR REPLACE VIEW species_geographic_summary AS
SELECT 
  s.id,
  s.name,
  s.scientific_name,
  s.category,
  s.conservation_status,
  s.countries,
  s.states_provinces,
  s.geographic_scope,
  s.primary_region,
  s.habitat_specificity,
  s.regions as legacy_regions,
  s.published,
  COUNT(p.id) as photo_count
FROM species_v2 s
LEFT JOIN photos_v2 p ON s.id = p.species_id AND p.published = true
GROUP BY s.id, s.name, s.scientific_name, s.category, s.conservation_status, 
         s.countries, s.states_provinces, s.geographic_scope, s.primary_region, 
         s.habitat_specificity, s.regions, s.published
ORDER BY s.name;

-- Grant permissions
GRANT SELECT ON species_geographic_summary TO authenticated;
GRANT SELECT ON species_geographic_summary TO service_role;
GRANT SELECT ON species_geographic_summary TO anon;

-- Add helpful comments
COMMENT ON COLUMN species_v2.countries IS 'Array of countries where this species is found';
COMMENT ON COLUMN species_v2.states_provinces IS 'Array of states/provinces for more granular location data';
COMMENT ON COLUMN species_v2.geographic_scope IS 'Scope of species distribution: global, continental, national, regional, local';
COMMENT ON COLUMN species_v2.primary_region IS 'Primary geographic region where species is most commonly found';
COMMENT ON COLUMN species_v2.habitat_specificity IS 'How specific the species habitat requirements are';
