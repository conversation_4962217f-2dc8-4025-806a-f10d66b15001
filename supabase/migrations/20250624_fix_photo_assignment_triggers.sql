-- Fix Photo Assignment Triggers for V2 Tables
-- This migration creates proper triggers to automatically update species photo counts
-- when photos are assigned, reassigned, or deleted in the photos_v2 table

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS photos_v2_update_species_count ON photos_v2;
DROP FUNCTION IF EXISTS trigger_update_species_v2_photo_count();

-- Create function to update species_v2 photo counts
CREATE OR REPLACE FUNCTION trigger_update_species_v2_photo_count()
RETURNS trigger AS $$
BEGIN
  -- Handle INSERT and UPDATE
  IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
    -- Update new species count if species_id is not null
    IF NEW.species_id IS NOT NULL THEN
      UPDATE species_v2 
      SET photo_count = (
        SELECT COUNT(*) 
        FROM photos_v2 
        WHERE species_id = NEW.species_id 
        AND published = true
      )
      WHERE id = NEW.species_id;
    END IF;
    
    -- Handle UPDATE where species_id changed (photo reassignment)
    IF TG_OP = 'UPDATE' AND OLD.species_id IS DISTINCT FROM NEW.species_id THEN
      -- Update old species count if it had a species assigned
      IF OLD.species_id IS NOT NULL THEN
        UPDATE species_v2 
        SET photo_count = (
          SELECT COUNT(*) 
          FROM photos_v2 
          WHERE species_id = OLD.species_id 
          AND published = true
        )
        WHERE id = OLD.species_id;
      END IF;
    END IF;
    
    RETURN NEW;
  END IF;
  
  -- Handle DELETE
  IF TG_OP = 'DELETE' THEN
    -- Update species count if the deleted photo had a species assigned
    IF OLD.species_id IS NOT NULL THEN
      UPDATE species_v2 
      SET photo_count = (
        SELECT COUNT(*) 
        FROM photos_v2 
        WHERE species_id = OLD.species_id 
        AND published = true
      )
      WHERE id = OLD.species_id;
    END IF;
    
    RETURN OLD;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic photo count updates on photos_v2
CREATE TRIGGER photos_v2_update_species_count
  AFTER INSERT OR UPDATE OR DELETE ON photos_v2
  FOR EACH ROW
  EXECUTE FUNCTION trigger_update_species_v2_photo_count();

-- Also create a function to manually recalculate all photo counts
CREATE OR REPLACE FUNCTION recalculate_all_species_photo_counts()
RETURNS INTEGER AS $$
DECLARE
  updated_count INTEGER := 0;
  species_record RECORD;
BEGIN
  -- Update photo counts for all species
  FOR species_record IN 
    SELECT id FROM species_v2
  LOOP
    UPDATE species_v2 
    SET photo_count = (
      SELECT COUNT(*) 
      FROM photos_v2 
      WHERE species_id = species_record.id 
      AND published = true
    )
    WHERE id = species_record.id;
    
    updated_count := updated_count + 1;
  END LOOP;
  
  RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- Create a function to update a single species photo count
CREATE OR REPLACE FUNCTION update_species_photo_count(species_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE species_v2 
  SET photo_count = (
    SELECT COUNT(*) 
    FROM photos_v2 
    WHERE species_id = species_uuid 
    AND published = true
  )
  WHERE id = species_uuid;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION recalculate_all_species_photo_counts() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION update_species_photo_count(UUID) TO anon, authenticated;
