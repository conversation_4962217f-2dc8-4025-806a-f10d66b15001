
-- Clear all existing data from photos and species tables
-- This will help resolve any data conflicts during the Airtable sync

-- First, delete all photos (this will cascade due to foreign key relationships)
DELETE FROM public.photos;

-- Then delete all species data
DELETE FROM public.species;

-- Reset the auto-increment sequence for photos table
ALTER SEQUENCE photos_id_seq RESTART WITH 1;

-- Verify tables are empty (optional - just for confirmation)
-- SELECT COUNT(*) as photo_count FROM public.photos;
-- SELECT COUNT(*) as species_count FROM public.species;
