-- Fix Remaining V1 Table References
-- This migration updates all remaining database views and constraints to use photos_v2 instead of photos

-- Drop and recreate views that still reference the old photos table
DROP VIEW IF EXISTS orphaned_photos CASCADE;
DROP VIEW IF EXISTS duplicate_photos_by_hash CASCADE;
DROP VIEW IF EXISTS duplicate_photos_by_url CASCADE;
DROP VIEW IF EXISTS invalid_species_references CASCADE;
DROP VIEW IF EXISTS photos_with_missing_files CASCADE;
DROP VIEW IF EXISTS photo_data_quality_summary CASCADE;
DROP VIEW IF EXISTS photos_requiring_review CASCADE;
DROP VIEW IF EXISTS photos_needing_review CASCADE;
DROP VIEW IF EXISTS photos_with_missing_species CASCADE;

-- Recreate orphaned_photos view using photos_v2
CREATE OR REPLACE VIEW orphaned_photos AS
SELECT 
  p.id, 
  p.url, 
  p.title, 
  p.species_id, 
  p.created_at,
  p.hash,
  'orphaned' as issue_type
FROM photos_v2 p
LEFT JOIN species_v2 s ON p.species_id = s.id
WHERE p.species_id IS NULL OR s.id IS NULL;

-- Recreate duplicate_photos_by_hash view using photos_v2
CREATE OR REPLACE VIEW duplicate_photos_by_hash AS
SELECT 
  hash, 
  array_agg(id ORDER BY created_at) as photo_ids, 
  count(*) as dup_count,
  'duplicate_hash' as issue_type
FROM photos_v2
WHERE hash IS NOT NULL
GROUP BY hash
HAVING count(*) > 1;

-- Recreate duplicate_photos_by_url view using photos_v2
CREATE OR REPLACE VIEW duplicate_photos_by_url AS
SELECT 
  url, 
  array_agg(id ORDER BY created_at) as photo_ids, 
  count(*) as dup_count,
  'duplicate_url' as issue_type
FROM photos_v2
WHERE url IS NOT NULL AND url != ''
GROUP BY url
HAVING count(*) > 1;

-- Recreate invalid_species_references view using photos_v2
CREATE OR REPLACE VIEW invalid_species_references AS
SELECT 
  p.id,
  p.url,
  p.title,
  p.species_id,
  p.created_at,
  'invalid_species_reference' as issue_type
FROM photos_v2 p
WHERE p.species_id IS NOT NULL 
  AND NOT EXISTS (SELECT 1 FROM species_v2 s WHERE s.id = p.species_id);

-- Recreate photos_with_missing_files view using photos_v2
CREATE OR REPLACE VIEW photos_with_missing_files AS
SELECT 
  p.id,
  p.url,
  p.title,
  p.species_id,
  p.created_at,
  'missing_file' as issue_type
FROM photos_v2 p
WHERE p.url IS NULL OR p.url = '';

-- Recreate photo_data_quality_summary view using photos_v2
CREATE OR REPLACE VIEW photo_data_quality_summary AS
SELECT 
  p.id,
  p.url,
  p.title,
  p.species_id,
  p.created_at,
  CASE 
    WHEN op.id IS NOT NULL THEN 'Orphaned Photo'
    WHEN dp_hash.hash IS NOT NULL THEN 'Duplicate by Hash'
    WHEN dp_url.url IS NOT NULL THEN 'Duplicate by URL'
    WHEN isr.id IS NOT NULL THEN 'Invalid Species Reference'
    WHEN pmf.id IS NOT NULL THEN 'Missing File'
    ELSE 'No issues detected'
  END as issue_description
FROM photos_v2 p
LEFT JOIN species_v2 s ON p.species_id = s.id
LEFT JOIN orphaned_photos op ON p.id = op.id
LEFT JOIN duplicate_photos_by_hash dp_hash ON p.hash = dp_hash.hash
LEFT JOIN duplicate_photos_by_url dp_url ON p.url = dp_url.url
LEFT JOIN invalid_species_references isr ON p.id = isr.id
LEFT JOIN photos_with_missing_files pmf ON p.id = pmf.id;

-- Recreate photos_requiring_review view using photos_v2
CREATE OR REPLACE VIEW photos_requiring_review AS
SELECT 
  p.id,
  p.url,
  p.species_id,
  p.needs_recovery,
  p.published,
  CASE
    WHEN p.needs_recovery = true THEN 'marked_for_recovery'
    WHEN p.species_id IS NOT NULL AND NOT (EXISTS (
        SELECT 1 FROM species_v2 s WHERE s.id = p.species_id
    )) THEN 'invalid_species_id'
    WHEN p.published = true AND (
        p.url IS NULL OR 
        p.url = '' OR 
        p.url LIKE '%localhost%' OR 
        p.url LIKE 'data:image%' OR 
        p.url LIKE '%\n%' OR 
        p.url LIKE '%\r%'
    ) THEN 'published_with_bad_url'
    ELSE 'unknown'
  END AS reason
FROM photos_v2 p
WHERE 
  p.needs_recovery = true 
  OR (p.species_id IS NOT NULL AND NOT (EXISTS (
      SELECT 1 FROM species_v2 s WHERE s.id = p.species_id
  )))
  OR (p.published = true AND (
      p.url IS NULL OR 
      p.url = '' OR 
      p.url LIKE '%localhost%' OR 
      p.url LIKE 'data:image%' OR 
      p.url LIKE '%\n%' OR 
      p.url LIKE '%\r%'
  ));

-- Recreate photos_needing_review view using photos_v2
CREATE OR REPLACE VIEW photos_needing_review AS
SELECT
  p.id,
  p.url,
  p.title,
  p.description,
  p.location,
  p.species_id,
  p.published,
  p.created_at,
  s.name as species_name,
  s.scientific_name,
  s.common_name,
  COALESCE(p.ai_generated_metadata, false) as ai_generated_metadata,
  COALESCE(p.needs_review, false) as needs_review
FROM photos_v2 p
LEFT JOIN species_v2 s ON p.species_id = s.id
WHERE p.published = false OR COALESCE(p.needs_review, false) = true
ORDER BY p.created_at DESC;

-- Recreate photos_with_missing_species view using photos_v2
CREATE OR REPLACE VIEW photos_with_missing_species AS
SELECT 
    count(*) AS count,
    'photos_with_missing_species_id' AS issue_type
FROM photos_v2 
WHERE species_id IS NULL
UNION ALL
SELECT 
    count(*) AS count,
    'photos_with_invalid_species_id' AS issue_type
FROM photos_v2 p
WHERE p.species_id IS NOT NULL 
    AND NOT EXISTS (SELECT 1 FROM species_v2 s WHERE s.id = p.species_id);

-- Update species_photo_counts view to use photos_v2 if it exists
DROP VIEW IF EXISTS species_photo_counts CASCADE;
CREATE VIEW species_photo_counts AS
SELECT 
    s.id,
    s.name,
    s.common_name,
    s.scientific_name,
    s.category,
    s.conservation_status,
    count(p.id) AS total_photos,
    sum(CASE WHEN p.published THEN 1 ELSE 0 END) AS published_photos,
    sum(CASE WHEN NOT p.published THEN 1 ELSE 0 END) AS unpublished_photos,
    0 AS ai_tagged_photos,
    0 AS photos_needing_review,
    s.created_at,
    s.updated_at
FROM species_v2 s
LEFT JOIN photos_v2 p ON s.id = p.species_id
GROUP BY 
    s.id, s.name, s.common_name, s.scientific_name, 
    s.category, s.conservation_status, s.created_at, s.updated_at
ORDER BY count(p.id) DESC;

-- Add helpful comment
COMMENT ON VIEW orphaned_photos IS 'Photos that have no valid species_id reference in species_v2 table';
COMMENT ON VIEW photos_requiring_review IS 'Photos that need manual review due to various data quality issues';
COMMENT ON VIEW photos_with_missing_species IS 'Count of photos with missing or invalid species_id references';
