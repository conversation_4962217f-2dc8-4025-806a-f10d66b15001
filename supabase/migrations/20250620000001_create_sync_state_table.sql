
-- Create sync_state table to track sync progress
CREATE TABLE IF NOT EXISTS sync_state (
  id SERIAL PRIMARY KEY,
  sync_type TEXT NOT NULL,
  base_id TEXT NOT NULL,
  table_name TEXT NOT NULL,
  bucket_name TEXT,
  current_offset INTEGER DEFAULT 0,
  total_records INTEGER DEFAULT 0,
  photos_inserted INTEGER DEFAULT 0,
  error_count INTEGER DEFAULT 0,
  status TEXT DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed', 'failed', 'cancelled', 'partial')),
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  error_details JSONB,
  metadata JSONB
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_sync_state_lookup ON sync_state (sync_type, base_id, table_name, status);
CREATE INDEX IF NOT EXISTS idx_sync_state_status ON sync_state (status, started_at);

-- Enable RLS
ALTER TABLE sync_state ENABLE ROW LEVEL SECURITY;

-- Create RLS policy to allow all operations (adjust as needed for your security requirements)
CREATE POLICY "Allow all operations on sync_state" ON sync_state
  FOR ALL USING (true) WITH CHECK (true);
