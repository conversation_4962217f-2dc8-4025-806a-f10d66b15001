-- Add all the comprehensive fields mentioned by the user to the species table

-- Add missing fields to species table
ALTER TABLE public.species 
ADD COLUMN IF NOT EXISTS itis_tsn text,
ADD COLUMN IF NOT EXISTS gbif_id text,
ADD COLUMN IF NOT EXISTS ebird_code text,
ADD COLUMN IF NOT EXISTS inat_id text,
ADD COLUMN IF NOT EXISTS avibase_id text,
ADD COLUMN IF NOT EXISTS taxonomy_order text,
ADD COLUMN IF NOT EXISTS taxonomy_subfamily text,
ADD COLUMN IF NOT EXISTS taxonomy_genus text,
ADD COLUMN IF NOT EXISTS common_group text,
ADD COLUMN IF NOT EXISTS related_groups text,
ADD COLUMN IF NOT EXISTS conservation_actions text,
ADD COLUMN IF NOT EXISTS ai_fun_facts text,
ADD COLUMN IF NOT EXISTS fun_facts_field text;

-- Create indexes for the new fields that might be queried
CREATE INDEX IF NOT EXISTS idx_species_itis_tsn ON public.species(itis_tsn) WHERE itis_tsn IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_species_gbif_id ON public.species(gbif_id) WHERE gbif_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_species_ebird_code ON public.species(ebird_code) WHERE ebird_code IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_species_taxonomy_order ON public.species(taxonomy_order);
CREATE INDEX IF NOT EXISTS idx_species_common_group ON public.species(common_group);

-- Add comments to document the new fields
COMMENT ON COLUMN public.species.itis_tsn IS 'Integrated Taxonomic Information System Taxonomic Serial Number';
COMMENT ON COLUMN public.species.gbif_id IS 'Global Biodiversity Information Facility identifier';
COMMENT ON COLUMN public.species.ebird_code IS 'eBird species code for bird species';
COMMENT ON COLUMN public.species.inat_id IS 'iNaturalist species identifier';
COMMENT ON COLUMN public.species.avibase_id IS 'Avibase species identifier for birds';
COMMENT ON COLUMN public.species.taxonomy_order IS 'Taxonomic order classification';
COMMENT ON COLUMN public.species.taxonomy_subfamily IS 'Taxonomic subfamily classification';
COMMENT ON COLUMN public.species.taxonomy_genus IS 'Taxonomic genus classification';
COMMENT ON COLUMN public.species.common_group IS 'Common grouping for similar species';
COMMENT ON COLUMN public.species.related_groups IS 'Related species groups';
COMMENT ON COLUMN public.species.conservation_actions IS 'Conservation actions and efforts';
COMMENT ON COLUMN public.species.ai_fun_facts IS 'AI-generated fun facts about the species';
COMMENT ON COLUMN public.species.fun_facts_field IS 'Additional fun facts field';