-- Add missing AI fields to photos_v2 table
-- These fields exist in some queries but are missing from the table schema

-- Add AI-specific fields to photos_v2 table
ALTER TABLE photos_v2 
ADD COLUMN IF NOT EXISTS ai_generated_metadata BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS needs_review BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS ai_suggested_id UUID,
ADD COLUMN IF NOT EXISTS ai_confidence DECIMAL(3,2),
ADD COLUMN IF NOT EXISTS ai_reviewed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS hash TEXT;

-- Create index on hash for faster duplicate detection
CREATE INDEX IF NOT EXISTS idx_photos_v2_hash ON photos_v2(hash);

-- Create indexes for AI fields for better query performance
CREATE INDEX IF NOT EXISTS idx_photos_v2_ai_generated_metadata ON photos_v2(ai_generated_metadata);
CREATE INDEX IF NOT EXISTS idx_photos_v2_needs_review ON photos_v2(needs_review);
CREATE INDEX IF NOT EXISTS idx_photos_v2_ai_reviewed ON photos_v2(ai_reviewed);

-- Add helpful comments
COMMENT ON COLUMN photos_v2.ai_generated_metadata IS 'Whether photo metadata was generated by AI';
COMMENT ON COLUMN photos_v2.needs_review IS 'Whether photo needs manual review';
COMMENT ON COLUMN photos_v2.ai_suggested_id IS 'AI suggested species ID for unassigned photos';
COMMENT ON COLUMN photos_v2.ai_confidence IS 'AI confidence score for species identification (0-1)';
COMMENT ON COLUMN photos_v2.ai_reviewed IS 'Whether AI suggestions have been reviewed by admin';
COMMENT ON COLUMN photos_v2.metadata IS 'Additional metadata in JSON format';
COMMENT ON COLUMN photos_v2.hash IS 'File hash for duplicate detection';
