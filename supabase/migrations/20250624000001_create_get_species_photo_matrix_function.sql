-- supabase/migrations/20250624000001_create_get_species_photo_matrix_function.sql

create or replace function get_species_photo_matrix()
returns jsonb as $$
declare
  matrix jsonb;
begin
  with species_photos as (
    select
      s.id,
      jsonb_agg(
        jsonb_build_object(
          'id', p.id,
          'url', p.url,
          'title', p.title,
          'description', p.description,
          'published', p.published
          -- Add other photo fields you need here
        )
      ) filter (where p.id is not null) as photos
    from species_v2 s
    left join photos_v2 p on s.id = p.species_id
    group by s.id
  ),
  unassigned_photos as (
    select
      jsonb_agg(
        jsonb_build_object(
          'id', p.id,
          'url', p.url,
          'title', p.title,
          'description', p.description,
          'published', p.published
        )
      ) as photos
    from photos_v2 p
    where p.species_id is null
  )
  select
    jsonb_build_object(
      'species', (
        select
          jsonb_agg(
            jsonb_build_object(
              'species', to_jsonb(s),
              'photos', coalesce(sp.photos, '[]'::jsonb)
            )
          )
        from species_v2 s
        left join species_photos sp on s.id = sp.id
      ),
      'unassigned_photos', (select photos from unassigned_photos)
    )
  into matrix;

  return matrix;
end;
$$ language plpgsql; 