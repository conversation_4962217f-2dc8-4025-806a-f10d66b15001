/*
  # Database Optimization and Performance Improvements

  1. Indexes
    - Add performance indexes for common queries
    - Optimize existing indexes
    
  2. Functions
    - Create utility functions for maintenance
    - Add performance monitoring functions
    
  3. Triggers
    - Auto-update photo counts
    - Maintain data integrity
    
  4. RLS Policies
    - Optimize existing policies
    - Add missing policies
*/

-- Create indexes for better query performance (without CONCURRENTLY in migrations)
CREATE INDEX IF NOT EXISTS idx_photos_species_published 
ON photos(species_id, published) WHERE published = true;

CREATE INDEX IF NOT EXISTS idx_photos_created_at_desc 
ON photos(created_at DESC) WHERE published = true;

CREATE INDEX IF NOT EXISTS idx_photos_airtable_id 
ON photos(airtable_id) WHERE airtable_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_species_published_category 
ON species(published, category) WHERE published = true;

CREATE INDEX IF NOT EXISTS idx_species_featured 
ON species(featured) WHERE featured = true;

-- Function to update species photo counts
CREATE OR REPLACE FUNCTION update_species_photo_counts()
RETURNS void AS $$
BEGIN
  UPDATE species 
  SET photo_count = (
    SELECT COUNT(*) 
    FROM photos 
    WHERE photos.species_id = species.id 
    AND photos.published = true
  );
END;
$$ LANGUAGE plpgsql;

-- Function to update species published status based on photo availability
CREATE OR REPLACE FUNCTION update_species_published_status()
RETURNS void AS $$
BEGIN
  -- Unpublish species without photos
  UPDATE species 
  SET published = false 
  WHERE photo_count = 0 OR photo_count IS NULL;
  
  -- Publish species with photos (if they weren't explicitly unpublished)
  UPDATE species 
  SET published = true 
  WHERE photo_count > 0 AND published IS NOT FALSE;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update photo counts when photos are inserted/updated/deleted
CREATE OR REPLACE FUNCTION trigger_update_species_photo_count()
RETURNS trigger AS $$
BEGIN
  -- Handle INSERT and UPDATE
  IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
    IF NEW.species_id IS NOT NULL THEN
      UPDATE species 
      SET photo_count = (
        SELECT COUNT(*) 
        FROM photos 
        WHERE species_id = NEW.species_id 
        AND published = true
      )
      WHERE id = NEW.species_id;
    END IF;
    
    -- Handle UPDATE where species_id changed
    IF TG_OP = 'UPDATE' AND OLD.species_id IS DISTINCT FROM NEW.species_id THEN
      IF OLD.species_id IS NOT NULL THEN
        UPDATE species 
        SET photo_count = (
          SELECT COUNT(*) 
          FROM photos 
          WHERE species_id = OLD.species_id 
          AND published = true
        )
        WHERE id = OLD.species_id;
      END IF;
    END IF;
    
    RETURN NEW;
  END IF;
  
  -- Handle DELETE
  IF TG_OP = 'DELETE' THEN
    IF OLD.species_id IS NOT NULL THEN
      UPDATE species 
      SET photo_count = (
        SELECT COUNT(*) 
        FROM photos 
        WHERE species_id = OLD.species_id 
        AND published = true
      )
      WHERE id = OLD.species_id;
    END IF;
    
    RETURN OLD;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic photo count updates
DROP TRIGGER IF EXISTS photos_update_species_count ON photos;
CREATE TRIGGER photos_update_species_count
  AFTER INSERT OR UPDATE OR DELETE ON photos
  FOR EACH ROW
  EXECUTE FUNCTION trigger_update_species_photo_count();

-- Function to get species with photo statistics
CREATE OR REPLACE FUNCTION get_species_with_photo_stats()
RETURNS TABLE (
  species_id text,
  species_name text,
  total_photos bigint,
  published_photos bigint,
  airtable_photos bigint,
  uploaded_photos bigint
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.id as species_id,
    s.name as species_name,
    COUNT(p.id) as total_photos,
    COUNT(p.id) FILTER (WHERE p.published = true) as published_photos,
    COUNT(p.id) FILTER (WHERE p.airtable_id IS NOT NULL) as airtable_photos,
    COUNT(p.id) FILTER (WHERE p.airtable_id IS NULL) as uploaded_photos
  FROM species s
  LEFT JOIN photos p ON s.id = p.species_id
  GROUP BY s.id, s.name
  ORDER BY s.name;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up orphaned records
CREATE OR REPLACE FUNCTION cleanup_orphaned_records()
RETURNS TABLE (
  orphaned_photos_removed bigint,
  empty_species_unpublished bigint
) AS $$
DECLARE
  photos_removed bigint := 0;
  species_unpublished bigint := 0;
BEGIN
  -- Remove photos with invalid species_id references
  WITH deleted_photos AS (
    DELETE FROM photos 
    WHERE species_id IS NOT NULL 
    AND species_id NOT IN (SELECT id FROM species)
    RETURNING id
  )
  SELECT COUNT(*) INTO photos_removed FROM deleted_photos;
  
  -- Unpublish species without photos
  WITH unpublished_species AS (
    UPDATE species 
    SET published = false 
    WHERE photo_count = 0 OR photo_count IS NULL
    RETURNING id
  )
  SELECT COUNT(*) INTO species_unpublished FROM unpublished_species;
  
  RETURN QUERY SELECT photos_removed, species_unpublished;
END;
$$ LANGUAGE plpgsql;

-- Update existing photo counts
SELECT update_species_photo_counts();

-- Optimize RLS policies for better performance
DROP POLICY IF EXISTS "Photos are viewable by everyone" ON photos;
CREATE POLICY "Photos are viewable by everyone" ON photos
  FOR SELECT USING (published = true);

DROP POLICY IF EXISTS "Species are viewable by everyone" ON species;
CREATE POLICY "Species are viewable by everyone" ON species
  FOR SELECT USING (published = true);

-- Handle authenticated user policies with proper existence checking
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Authenticated users can manage photos" ON photos;
  DROP POLICY IF EXISTS "Authenticated users can manage species" ON species;
  
  -- Create new policies
  CREATE POLICY "Authenticated users can manage photos" ON photos
    FOR ALL USING (auth.role() = 'authenticated');
    
  CREATE POLICY "Authenticated users can manage species" ON species
    FOR ALL USING (auth.role() = 'authenticated');
END $$;

-- Create materialized view for performance-critical queries
DROP MATERIALIZED VIEW IF EXISTS species_photo_summary;
CREATE MATERIALIZED VIEW species_photo_summary AS
SELECT 
  s.id,
  s.name,
  s.common_name,
  s.category,
  s.conservation_status,
  COUNT(p.id) as photo_count,
  COUNT(p.id) FILTER (WHERE p.published = true) as published_photo_count,
  MAX(p.created_at) as latest_photo_date
FROM species s
LEFT JOIN photos p ON s.id = p.species_id
WHERE s.published = true
GROUP BY s.id, s.name, s.common_name, s.category, s.conservation_status;

-- Create index on materialized view
CREATE UNIQUE INDEX idx_species_photo_summary_id 
ON species_photo_summary(id);

-- Function to refresh materialized view
CREATE OR REPLACE FUNCTION refresh_species_photo_summary()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY species_photo_summary;
EXCEPTION
  WHEN OTHERS THEN
    -- If concurrent refresh fails, do a regular refresh
    REFRESH MATERIALIZED VIEW species_photo_summary;
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON FUNCTION update_species_photo_counts() IS 'Updates photo_count for all species based on published photos';
COMMENT ON FUNCTION cleanup_orphaned_records() IS 'Removes orphaned photos and unpublishes species without photos';
COMMENT ON FUNCTION get_species_with_photo_stats() IS 'Returns detailed photo statistics for each species';
COMMENT ON MATERIALIZED VIEW species_photo_summary IS 'Cached summary of species with photo counts for performance';