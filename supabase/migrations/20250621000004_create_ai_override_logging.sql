-- AI Override Logging and Triggers
-- This migration creates a logging system to track manual overrides of AI-generated content

-- Create the override log table
CREATE TABLE IF NOT EXISTS ai_override_log (
  id SERIAL PRIMARY KEY,
  table_name TEXT NOT NULL,
  record_id TEXT NOT NULL,
  field_name TEXT NOT NULL,
  old_value TEXT,
  new_value TEXT,
  override_type TEXT NOT NULL CHECK (override_type IN ('species_assignment', 'metadata_edit', 'review_status', 'confidence_override')),
  user_id UUID,
  override_reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_ai_override_log_table_record ON ai_override_log(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_ai_override_log_created_at ON ai_override_log(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ai_override_log_type ON ai_override_log(override_type);

-- Function to log <PERSON> overrides
CREATE OR REPLACE FUNCTION log_ai_override(
  p_table_name TEXT,
  p_record_id TEXT,
  p_field_name TEXT,
  p_old_value TEXT,
  p_new_value TEXT,
  p_override_type TEXT,
  p_user_id UUID DEFAULT NULL,
  p_override_reason TEXT DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
  INSERT INTO ai_override_log (
    table_name,
    record_id,
    field_name,
    old_value,
    new_value,
    override_type,
    user_id,
    override_reason
  ) VALUES (
    p_table_name,
    p_record_id,
    p_field_name,
    p_old_value,
    p_new_value,
    p_override_type,
    p_user_id,
    p_override_reason
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger function for photos table
CREATE OR REPLACE FUNCTION photos_ai_override_trigger() RETURNS TRIGGER AS $$
BEGIN
  -- Log species assignment changes
  IF OLD.species_id IS DISTINCT FROM NEW.species_id THEN
    PERFORM log_ai_override(
      'photos',
      NEW.id::TEXT,
      'species_id',
      OLD.species_id::TEXT,
      NEW.species_id::TEXT,
      'species_assignment',
      auth.uid(),
      'Manual species reassignment'
    );
  END IF;

  -- Log metadata changes (title, description, notes)
  IF OLD.title IS DISTINCT FROM NEW.title THEN
    PERFORM log_ai_override(
      'photos',
      NEW.id::TEXT,
      'title',
      OLD.title,
      NEW.title,
      'metadata_edit',
      auth.uid(),
      'Manual title edit'
    );
  END IF;

  IF OLD.description IS DISTINCT FROM NEW.description THEN
    PERFORM log_ai_override(
      'photos',
      NEW.id::TEXT,
      'description',
      OLD.description,
      NEW.description,
      'metadata_edit',
      auth.uid(),
      'Manual description edit'
    );
  END IF;

  IF OLD.notes IS DISTINCT FROM NEW.notes THEN
    PERFORM log_ai_override(
      'photos',
      NEW.id::TEXT,
      'notes',
      OLD.notes,
      NEW.notes,
      'metadata_edit',
      auth.uid(),
      'Manual notes edit'
    );
  END IF;

  -- Log review status changes (when AI fields are added)
  -- This will be enhanced when ai_generated_metadata and needs_review fields are available
  IF OLD.published IS DISTINCT FROM NEW.published THEN
    PERFORM log_ai_override(
      'photos',
      NEW.id::TEXT,
      'published',
      OLD.published::TEXT,
      NEW.published::TEXT,
      'review_status',
      auth.uid(),
      'Manual publication status change'
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger function for species table
CREATE OR REPLACE FUNCTION species_ai_override_trigger() RETURNS TRIGGER AS $$
BEGIN
  -- Log species metadata changes
  IF OLD.name IS DISTINCT FROM NEW.name THEN
    PERFORM log_ai_override(
      'species',
      NEW.id::TEXT,
      'name',
      OLD.name,
      NEW.name,
      'metadata_edit',
      auth.uid(),
      'Manual species name edit'
    );
  END IF;

  IF OLD.common_name IS DISTINCT FROM NEW.common_name THEN
    PERFORM log_ai_override(
      'species',
      NEW.id::TEXT,
      'common_name',
      OLD.common_name,
      NEW.common_name,
      'metadata_edit',
      auth.uid(),
      'Manual common name edit'
    );
  END IF;

  IF OLD.scientific_name IS DISTINCT FROM NEW.scientific_name THEN
    PERFORM log_ai_override(
      'species',
      NEW.id::TEXT,
      'scientific_name',
      OLD.scientific_name,
      NEW.scientific_name,
      'metadata_edit',
      auth.uid(),
      'Manual scientific name edit'
    );
  END IF;

  -- Log publication status changes
  IF OLD.published IS DISTINCT FROM NEW.published THEN
    PERFORM log_ai_override(
      'species',
      NEW.id::TEXT,
      'published',
      OLD.published::TEXT,
      NEW.published::TEXT,
      'review_status',
      auth.uid(),
      'Manual species publication status change'
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers
DROP TRIGGER IF EXISTS photos_ai_override_trigger ON photos;
CREATE TRIGGER photos_ai_override_trigger
  AFTER UPDATE ON photos
  FOR EACH ROW
  EXECUTE FUNCTION photos_ai_override_trigger();

DROP TRIGGER IF EXISTS species_ai_override_trigger ON species;
CREATE TRIGGER species_ai_override_trigger
  AFTER UPDATE ON species
  FOR EACH ROW
  EXECUTE FUNCTION species_ai_override_trigger();

-- Create a view for override analytics
CREATE OR REPLACE VIEW ai_override_analytics AS
SELECT
  DATE_TRUNC('day', created_at) as date,
  override_type,
  COUNT(*) as override_count,
  COUNT(DISTINCT record_id) as unique_records_affected,
  COUNT(DISTINCT user_id) as unique_users
FROM ai_override_log
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', created_at), override_type
ORDER BY date DESC, override_count DESC;

-- Create a view for recent overrides
CREATE OR REPLACE VIEW recent_ai_overrides AS
SELECT
  l.id,
  l.table_name,
  l.record_id,
  l.field_name,
  l.old_value,
  l.new_value,
  l.override_type,
  l.override_reason,
  l.created_at,
  CASE 
    WHEN l.table_name = 'photos' THEN p.title
    WHEN l.table_name = 'species' THEN s.name
    ELSE 'Unknown'
  END as record_title,
  CASE 
    WHEN l.table_name = 'photos' THEN p.url
    ELSE NULL
  END as photo_url
FROM ai_override_log l
LEFT JOIN photos p ON l.table_name = 'photos' AND l.record_id = p.id::TEXT
LEFT JOIN species s ON l.table_name = 'species' AND l.record_id = s.id::TEXT
ORDER BY l.created_at DESC
LIMIT 100;

-- Add RLS policies for the override log table
ALTER TABLE ai_override_log ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to view override logs
CREATE POLICY "Users can view override logs" ON ai_override_log
  FOR SELECT USING (auth.role() = 'authenticated');

-- Allow authenticated users to insert override logs (via triggers)
CREATE POLICY "System can insert override logs" ON ai_override_log
  FOR INSERT WITH CHECK (true);

-- Add comments for documentation
COMMENT ON TABLE ai_override_log IS 'Log of manual overrides to AI-generated content';
COMMENT ON FUNCTION log_ai_override IS 'Function to log AI content overrides';
COMMENT ON VIEW ai_override_analytics IS 'Analytics on AI override patterns';
COMMENT ON VIEW recent_ai_overrides IS 'Recent manual overrides of AI-generated content'; 