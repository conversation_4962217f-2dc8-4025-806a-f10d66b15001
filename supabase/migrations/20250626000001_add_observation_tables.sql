-- Add observation and location tracking tables for eBird/iNaturalist integration
-- This migration adds comprehensive observation tracking capabilities

-- 1. Locations table for standardized location data
CREATE TABLE IF NOT EXISTS locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  latitude DECIMAL(10, 8) NOT NULL,
  longitude DECIMAL(11, 8) NOT NULL,
  country TEXT,
  state_province TEXT,
  county TEXT,
  locality TEXT,
  ebird_location_id TEXT, -- eBird hotspot ID
  inat_place_id INTEGER, -- iNaturalist place ID
  elevation_m INTEGER,
  habitat_type TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(latitude, longitude, name)
);

-- 2. Observations table for tracking personal sightings
CREATE TABLE IF NOT EXISTS observations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id UUID REFERENCES species_v2(id) ON DELETE CASCADE,
  location_id UUID REFERENCES locations(id) ON DELETE SET NULL,
  observer_id TEXT, -- Your eBird/iNat user ID
  source TEXT NOT NULL CHECK (source IN ('ebird', 'inaturalist', 'manual')),
  external_id TEXT, -- eBird checklist ID or iNat observation ID
  observation_date DATE NOT NULL,
  observation_time TIME,
  count INTEGER DEFAULT 1,
  breeding_code TEXT, -- eBird breeding codes
  behavior TEXT,
  notes TEXT,
  confidence_level TEXT, -- 'certain', 'likely', 'possible'
  photo_url TEXT,
  audio_url TEXT,
  weather_conditions TEXT,
  temperature_c INTEGER,
  wind_speed_kmh INTEGER,
  visibility_km DECIMAL(4,2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(external_id, source) -- Prevent duplicate imports
);

-- 3. Checklists table for eBird checklist data
CREATE TABLE IF NOT EXISTS checklists (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ebird_checklist_id TEXT UNIQUE NOT NULL,
  location_id UUID REFERENCES locations(id) ON DELETE SET NULL,
  observer_id TEXT NOT NULL,
  checklist_date DATE NOT NULL,
  start_time TIME,
  duration_minutes INTEGER,
  distance_km DECIMAL(5,2),
  effort_type TEXT, -- 'traveling', 'stationary', 'casual'
  protocol TEXT, -- eBird protocol
  number_observers INTEGER DEFAULT 1,
  all_species_reported BOOLEAN DEFAULT FALSE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Species occurrence patterns (derived from observations)
CREATE TABLE IF NOT EXISTS species_occurrence (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id UUID REFERENCES species_v2(id) ON DELETE CASCADE,
  location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
  month INTEGER CHECK (month >= 1 AND month <= 12),
  frequency DECIMAL(5,4), -- 0.0 to 1.0 (percentage of checklists)
  abundance_category TEXT, -- 'rare', 'uncommon', 'common', 'abundant'
  first_observed DATE,
  last_observed DATE,
  total_observations INTEGER DEFAULT 0,
  breeding_evidence BOOLEAN DEFAULT FALSE,
  migration_status TEXT, -- 'resident', 'breeding', 'migrant', 'winter'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(species_id, location_id, month)
);

-- 5. Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_observations_species_id ON observations(species_id);
CREATE INDEX IF NOT EXISTS idx_observations_location_id ON observations(location_id);
CREATE INDEX IF NOT EXISTS idx_observations_date ON observations(observation_date);
CREATE INDEX IF NOT EXISTS idx_observations_source ON observations(source);
CREATE INDEX IF NOT EXISTS idx_observations_external_id ON observations(external_id);

CREATE INDEX IF NOT EXISTS idx_locations_coords ON locations(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_locations_ebird_id ON locations(ebird_location_id);
CREATE INDEX IF NOT EXISTS idx_locations_inat_id ON locations(inat_place_id);

CREATE INDEX IF NOT EXISTS idx_checklists_date ON checklists(checklist_date);
CREATE INDEX IF NOT EXISTS idx_checklists_location ON checklists(location_id);
CREATE INDEX IF NOT EXISTS idx_checklists_observer ON checklists(observer_id);

CREATE INDEX IF NOT EXISTS idx_species_occurrence_species ON species_occurrence(species_id);
CREATE INDEX IF NOT EXISTS idx_species_occurrence_location ON species_occurrence(location_id);
CREATE INDEX IF NOT EXISTS idx_species_occurrence_month ON species_occurrence(month);

-- 6. Add RLS policies (Row Level Security)
ALTER TABLE locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE observations ENABLE ROW LEVEL SECURITY;
ALTER TABLE checklists ENABLE ROW LEVEL SECURITY;
ALTER TABLE species_occurrence ENABLE ROW LEVEL SECURITY;

-- Allow public read access to locations and species occurrence
CREATE POLICY "Public read access for locations" ON locations FOR SELECT USING (true);
CREATE POLICY "Public read access for species occurrence" ON species_occurrence FOR SELECT USING (true);

-- Admin-only write access for all tables
CREATE POLICY "Admin full access for locations" ON locations FOR ALL USING (auth.email() = '<EMAIL>');
CREATE POLICY "Admin full access for observations" ON observations FOR ALL USING (auth.email() = '<EMAIL>');
CREATE POLICY "Admin full access for checklists" ON checklists FOR ALL USING (auth.email() = '<EMAIL>');
CREATE POLICY "Admin full access for species occurrence" ON species_occurrence FOR ALL USING (auth.email() = '<EMAIL>');

-- 7. Add helpful views
CREATE OR REPLACE VIEW species_location_summary AS
SELECT 
  s.id as species_id,
  s.name,
  s.scientific_name,
  s.common_name,
  l.id as location_id,
  l.name as location_name,
  l.state_province,
  l.country,
  COUNT(o.id) as observation_count,
  MIN(o.observation_date) as first_seen,
  MAX(o.observation_date) as last_seen,
  ARRAY_AGG(DISTINCT EXTRACT(month FROM o.observation_date)::INTEGER ORDER BY EXTRACT(month FROM o.observation_date)::INTEGER) as months_observed
FROM species_v2 s
JOIN observations o ON s.id = o.species_id
JOIN locations l ON o.location_id = l.id
GROUP BY s.id, s.name, s.scientific_name, s.common_name, l.id, l.name, l.state_province, l.country;

-- 8. Add comments for documentation
COMMENT ON TABLE locations IS 'Standardized location data for observations, linked to eBird hotspots and iNaturalist places';
COMMENT ON TABLE observations IS 'Individual species observations from eBird, iNaturalist, or manual entry';
COMMENT ON TABLE checklists IS 'eBird checklist metadata for context and effort tracking';
COMMENT ON TABLE species_occurrence IS 'Derived occurrence patterns showing when/where species are typically found';
COMMENT ON VIEW species_location_summary IS 'Summary of species observations by location with temporal patterns';
