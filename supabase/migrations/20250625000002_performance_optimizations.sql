-- Performance Optimization Migration
-- This migration adds indexes and optimizations to improve query performance

-- Step 1: Add performance indexes for species_v2 table
CREATE INDEX IF NOT EXISTS idx_species_v2_published_name ON species_v2(published, name) WHERE published = true;
CREATE INDEX IF NOT EXISTS idx_species_v2_category_published ON species_v2(category, published) WHERE published = true;
CREATE INDEX IF NOT EXISTS idx_species_v2_conservation_status ON species_v2(conservation_status) WHERE conservation_status IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_species_v2_featured ON species_v2(featured) WHERE featured = true;
CREATE INDEX IF NOT EXISTS idx_species_v2_photo_count ON species_v2(photo_count) WHERE photo_count > 0;

-- Step 2: Add performance indexes for photos table
CREATE INDEX IF NOT EXISTS idx_photos_species_id_published ON photos(species_id, published) WHERE published = true;
CREATE INDEX IF NOT EXISTS idx_photos_published_created_at ON photos(published, created_at DESC) WHERE published = true;
CREATE INDEX IF NOT EXISTS idx_photos_species_id_created_at ON photos(species_id, created_at DESC) WHERE species_id IS NOT NULL;

-- Step 3: Add composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_species_v2_search ON species_v2 USING gin(to_tsvector('english', coalesce(name, '') || ' ' || coalesce(scientific_name, '')));
CREATE INDEX IF NOT EXISTS idx_photos_location ON photos(location) WHERE location IS NOT NULL;

-- Step 4: Optimize existing views for performance
-- Update species_photo_counts view with better performance
DROP VIEW IF EXISTS species_photo_counts CASCADE;
CREATE VIEW species_photo_counts AS
SELECT 
    s.id,
    s.name,
    s.scientific_name,
    s.category,
    s.conservation_status,
    s.photo_count as cached_photo_count,
    count(p.id) AS actual_photo_count,
    sum(CASE WHEN p.published THEN 1 ELSE 0 END) AS published_photos,
    sum(CASE WHEN NOT p.published THEN 1 ELSE 0 END) AS unpublished_photos,
    s.created_at,
    s.updated_at
FROM species_v2 s
LEFT JOIN photos p ON s.id = p.species_id
WHERE s.published = true
GROUP BY 
    s.id, s.name, s.scientific_name, 
    s.category, s.conservation_status, s.photo_count, s.created_at, s.updated_at
ORDER BY s.photo_count DESC NULLS LAST;

-- Step 5: Create materialized view for expensive aggregations (if needed)
-- This can be refreshed periodically instead of computed on every request
CREATE MATERIALIZED VIEW IF NOT EXISTS species_stats AS
SELECT 
    category,
    conservation_status,
    count(*) as species_count,
    sum(photo_count) as total_photos,
    avg(photo_count) as avg_photos_per_species,
    max(photo_count) as max_photos,
    min(photo_count) as min_photos
FROM species_v2 
WHERE published = true
GROUP BY category, conservation_status
ORDER BY species_count DESC;

-- Create index on materialized view
CREATE INDEX IF NOT EXISTS idx_species_stats_category ON species_stats(category);
CREATE INDEX IF NOT EXISTS idx_species_stats_conservation ON species_stats(conservation_status);

-- Step 6: Add function to refresh materialized view
CREATE OR REPLACE FUNCTION refresh_species_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW species_stats;
END;
$$ LANGUAGE plpgsql;

-- Step 7: Optimize RLS policies for performance
-- Add indexes to support RLS policies if they exist
-- (These would be specific to your RLS setup)

-- Step 8: Add table statistics update
-- This helps the query planner make better decisions
ANALYZE species_v2;
ANALYZE photos;

-- Step 9: Add comments for documentation
COMMENT ON INDEX idx_species_v2_published_name IS 'Optimizes queries for published species ordered by name';
COMMENT ON INDEX idx_species_v2_category_published IS 'Optimizes category filtering for published species';
COMMENT ON INDEX idx_photos_species_id_published IS 'Optimizes photo queries by species and publication status';
COMMENT ON INDEX idx_photos_published_created_at IS 'Optimizes recent published photos queries';
COMMENT ON MATERIALIZED VIEW species_stats IS 'Cached aggregation statistics for dashboard performance';

-- Step 10: Create function to monitor query performance
CREATE OR REPLACE FUNCTION get_slow_queries()
RETURNS TABLE(
    query text,
    calls bigint,
    total_time double precision,
    mean_time double precision
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pg_stat_statements.query,
        pg_stat_statements.calls,
        pg_stat_statements.total_exec_time,
        pg_stat_statements.mean_exec_time
    FROM pg_stat_statements
    WHERE pg_stat_statements.mean_exec_time > 100 -- queries taking more than 100ms on average
    ORDER BY pg_stat_statements.mean_exec_time DESC
    LIMIT 20;
END;
$$ LANGUAGE plpgsql;

-- Step 11: Grant necessary permissions
GRANT SELECT ON species_stats TO authenticated;
GRANT SELECT ON species_stats TO anon;
GRANT EXECUTE ON FUNCTION refresh_species_stats() TO authenticated;
GRANT EXECUTE ON FUNCTION get_slow_queries() TO authenticated;
