-- Seed file for Fauna Focus Database (UUID version)
-- Truncate tables for a clean slate
TRUNCATE TABLE photos CASCADE;
TRUNCATE TABLE fun_facts CASCADE;
TRUNCATE TABLE species CASCADE;

-- Insert 2 sample species
INSERT INTO species (
    id, name, common_name, scientific_name, description, category, conservation_status, habitat, diet, behavior, family, size_cm, weight_g, lifespan_years, migration_pattern, breeding_season, threat_level, population_trend, published, featured, ai_generated, ai_confidence, tags, regions, created_at, updated_at
) VALUES
(
    gen_random_uuid(),
    'Bald Eagle',
    'American Eagle',
    'Haliaeetus leucocephalus',
    'The Bald Eagle is a majestic bird of prey and the national bird of the United States. These powerful raptors are known for their distinctive white head and tail feathers, which develop when they reach maturity at around 5 years of age. Fun fact: Despite their name, Bald Eagles are not actually bald - the term "bald" comes from an old English word meaning "white-headed."',
    'Birds of Prey',
    'Least Concern',
    'Coastal areas, lakes, rivers, and large bodies of water across North America',
    'Primarily fish, but also waterfowl, small mammals, and carrion',
    'Solitary hunters that mate for life and are highly territorial during breeding season',
    'Accipitridae',
    70.0,
    3000.0,
    20,
    'Partial migrant - northern populations migrate south in winter',
    'Late winter to early spring',
    'Low',
    'Increasing',
    true,
    true,
    false,
    0.95,
    ARRAY['raptor', 'fish-eater', 'national-symbol', 'coastal'],
    'North America',
    now(),
    now()
),
(
    gen_random_uuid(),
    'Gray Wolf',
    'Timber Wolf',
    'Canis lupus',
    'The Gray Wolf is one of the most iconic and misunderstood predators in North America. These highly social animals live in complex family groups called packs, which are led by an alpha male and female breeding pair. Fun fact: Wolves can hear sounds up to 6 miles away in forested areas and up to 10 miles away in open terrain.',
    'Mammals',
    'Least Concern',
    'Forests, tundra, grasslands, and mountainous regions across North America, Europe, and Asia',
    'Large ungulates like deer, elk, and moose, as well as smaller mammals and carrion',
    'Highly social pack animals with complex hierarchies and cooperative hunting strategies',
    'Canidae',
    120.0,
    45000.0,
    8,
    'Some populations migrate seasonally following prey',
    'Late winter',
    'Low to Medium',
    'Stable',
    true,
    false,
    false,
    0.92,
    ARRAY['pack-hunter', 'apex-predator', 'social', 'endangered-recovery'],
    'North America, Europe, Asia',
    now(),
    now()
);

-- Insert 3 sample photos referencing the above species
INSERT INTO photos (
    id, species_id, url, title, description, photographer, location, published, created_at, updated_at
) VALUES
(
    gen_random_uuid(),
    (SELECT id FROM species WHERE name = 'Bald Eagle' LIMIT 1),
    'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop',
    'Bald Eagle in Flight',
    'A majestic Bald Eagle soaring over a pristine lake, showcasing its impressive wingspan and distinctive white head and tail feathers.',
    'Nature Photographer',
    'Alaska, USA',
    true,
    now(),
    now()
),
(
    gen_random_uuid(),
    (SELECT id FROM species WHERE name = 'Bald Eagle' LIMIT 1),
    'https://images.unsplash.com/photo-1549366021-9f761d450615?w=800&h=600&fit=crop',
    'Bald Eagle Perched',
    'A Bald Eagle perched on a weathered branch, surveying its territory with keen eyesight that can spot fish from great distances.',
    'Wildlife Enthusiast',
    'British Columbia, Canada',
    true,
    now(),
    now()
),
(
    gen_random_uuid(),
    (SELECT id FROM species WHERE name = 'Gray Wolf' LIMIT 1),
    'https://images.unsplash.com/photo-1516550135137-4c3c07aaab4b?w=800&h=600&fit=crop',
    'Gray Wolf Pack',
    'A family of Gray Wolves in their natural habitat, demonstrating the strong social bonds that characterize these intelligent pack animals.',
    'Conservation Photographer',
    'Yellowstone National Park, USA',
    true,
    now(),
    now()
);

-- Update photo counts for species
UPDATE species 
SET photo_count = (
    SELECT COUNT(*) 
    FROM photos 
    WHERE photos.species_id = species.id
);

-- Verify the data was inserted correctly
SELECT 
    'Species count:' as info,
    COUNT(*) as count 
FROM species
UNION ALL
SELECT 
    'Photos count:' as info,
    COUNT(*) as count 
FROM photos
UNION ALL
SELECT 
    'Fun facts count:' as info,
    COUNT(*) as count 
FROM fun_facts; 