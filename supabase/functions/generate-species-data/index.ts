import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { OpenAI } from 'https://deno.land/x/openai/mod.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { speciesName } = await req.json()
    if (!speciesName) {
      throw new Error('speciesName is required')
    }

    const openAIKey = Deno.env.get('OPENAI_API_KEY')
    if (!openAIKey) {
        console.error('OPENAI_API_KEY is not set in environment variables.')
        return new Response(JSON.stringify({ error: 'OpenAI API key is not configured.' }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
        })
    }

    const openai = new OpenAI(openAIKey);

    const prompt = `
      Generate a detailed data profile for the animal species: "${speciesName}".
      Provide the output in a valid JSON format. The JSON object should contain the following keys:
      - "scientific_name": The scientific name.
      - "category": The general category (e.g., "Birds", "Mammals", "Reptiles", "Insects").
      - "conservation_status": The IUCN conservation status (e.g., "Least Concern", "Vulnerable", "Endangered").
      - "description": A brief, engaging paragraph describing the species.
      - "habitat": A description of its natural habitat.
      - "diet": A description of its primary diet.
      - "behavior": A description of its common behaviors.
      - "family": The taxonomic family.
      - "size_cm": The average size in centimeters (provide a single numeric value, not a range).
      - "weight_g": The average weight in grams (provide a single numeric value, not a range).
      - "lifespan_years": The average lifespan in years (provide a single integer value).
      - "migration_pattern": A brief description of its migration pattern, or "Non-migratory".
      - "breeding_season": A brief description of its breeding season.
      - "threat_level": A summary of threats, or "Low".
      - "population_trend": The population trend (e.g., "Stable", "Decreasing", "Increasing").

      Ensure all fields are filled with accurate information. The entire output must be only the JSON object, with no additional text or explanations.
    `

    const chatCompletion = await openai.chat.completions.create({
      messages: [{ role: 'user', content: prompt }],
      model: 'gpt-4o',
      response_format: { type: 'json_object' },
    })

    const aiResponse = chatCompletion.choices[0].message.content
    const jsonData = JSON.parse(aiResponse)

    return new Response(JSON.stringify(jsonData), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })

  } catch (error) {
    console.error('Error generating species data:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
}) 