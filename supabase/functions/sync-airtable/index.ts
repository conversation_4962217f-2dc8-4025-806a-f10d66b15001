import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Retry mechanism with exponential backoff
async function fetchWithRetry(url: string, options: RequestInit, maxRetries = 3): Promise<Response> {
  let lastError: Error | null = null
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Fetch attempt ${attempt + 1}/${maxRetries + 1} for URL: ${url}`)
      
      const response = await fetch(url, options)
      
      // Handle rate limiting specifically
      if (response.status === 429) {
        const retryAfter = response.headers.get('Retry-After')
        const waitTime = retryAfter ? parseInt(retryAfter) * 1000 : Math.pow(2, attempt) * 1000
        console.log(`Rate limited (429), waiting ${waitTime}ms before retry...`)
        await delay(waitTime)
        continue
      }
      
      // If we get a response (even if it's an error), return it
      return response
      
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error))
      console.error(`Fetch attempt ${attempt + 1} failed:`, lastError.message)
      
      // If this is our last attempt, throw the error
      if (attempt === maxRetries) {
        throw lastError
      }
      
      // Wait before retrying with exponential backoff
      const waitTime = Math.pow(2, attempt) * 1000 + Math.random() * 1000
      console.log(`Waiting ${waitTime}ms before retry...`)
      await delay(waitTime)
    }
  }
  
  // This should never be reached, but just in case
  throw lastError || new Error('Unknown fetch error')
}

serve(async (req) => {
  console.log('Sync function called with method:', req.method)
  
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    )

    const airtableApiKey = Deno.env.get('AIRTABLE_API_KEY')
    console.log('Airtable API key configured:', !!airtableApiKey)
    
    if (!airtableApiKey) {
      throw new Error('AIRTABLE_API_KEY not configured')
    }

    let requestBody
    try {
      requestBody = await req.json()
      console.log('Request body received:', requestBody)
    } catch (error) {
      console.error('Error parsing request body:', error)
      throw new Error('Invalid JSON in request body')
    }

    const { baseId, photosTable, speciesTable = 'Species Registry', bucketName = 'species', resumeFromOffset = 0, clearExisting = false, syncType = 'photos' } = requestBody
    
    if (!baseId) {
      throw new Error('Missing required parameter: baseId')
    }

    console.log('Starting Airtable sync:', { baseId, syncType, photosTable, speciesTable, bucketName, resumeFromOffset })

    let syncResults = {
      photosInserted: 0,
      photosUpdated: 0,
      speciesInserted: 0,
      speciesUpdated: 0,
      errors: [],
      detailedErrors: [],
      totalRecords: 0,
      currentOffset: resumeFromOffset,
      completed: false,
      nextOffset: resumeFromOffset,
      processedRecords: 0
    }

    // Determine which table to sync
    const tableName = syncType === 'species' ? speciesTable : photosTable
    if (!tableName) {
      throw new Error(`Missing required parameter: ${syncType === 'species' ? 'speciesTable' : 'photosTable'}`)
    }

    // Create or update sync state record
    let syncStateId = null
    if (resumeFromOffset === 0) {
      const { data: syncState, error: syncStateError } = await supabaseClient
        .from('sync_state')
        .insert({
          sync_type: `airtable_${syncType}`,
          base_id: baseId,
          table_name: tableName,
          bucket_name: bucketName,
          current_offset: 0,
          total_records: 0,
          status: 'in_progress',
          started_at: new Date().toISOString()
        })
        .select()
        .single()

      if (syncStateError) {
        console.log('Could not create sync state record:', syncStateError)
      } else {
        syncStateId = syncState.id
      }
    } else {
      // Find existing sync state
      const { data: existingSyncState } = await supabaseClient
        .from('sync_state')
        .select('id')
        .eq('sync_type', `airtable_${syncType}`)
        .eq('base_id', baseId)
        .eq('table_name', tableName)
        .order('started_at', { ascending: false })
        .limit(1)
        .maybeSingle()

      if (existingSyncState) {
        syncStateId = existingSyncState.id
      }
    }

    // Clear existing data if requested and starting fresh
    if (clearExisting && resumeFromOffset === 0) {
      console.log(`Clearing existing ${syncType} data...`)
      if (syncType === 'species') {
        const { error: deleteError } = await supabaseClient
          .from('species')
          .delete()
          .neq('id', '')
        if (deleteError) {
          console.error('Error clearing species data:', deleteError)
        } else {
          console.log('Successfully cleared existing species data')
        }
      } else {
        const { error: deleteError } = await supabaseClient
          .from('photos')
          .delete()
          .neq('id', 0)
        if (deleteError) {
          console.error('Error clearing photos data:', deleteError)
        }
      }
    }

    // Fetch records from Airtable with improved pagination and retry logic
    console.log(`Fetching ${syncType} from Airtable table: ${tableName}`)
    
    const pageSize = 100 // Airtable's max page size
    const maxRecordsPerBatch = syncType === 'species' ? 500 : 200 // Larger batch for species
    let allRecords = []
    let offset = null
    let totalFetched = 0
    let recordsToSkip = resumeFromOffset

    // Add delay function to handle rate limiting
    const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

    // Fetch records until we have enough for this batch or no more records
    do {
      let url = `https://api.airtable.com/v0/${baseId}/${encodeURIComponent(tableName)}?pageSize=${pageSize}`
      
      if (offset) {
        url += `&offset=${offset}`
      }

      console.log(`Fetching page from Airtable... (total so far: ${totalFetched})`)

      // Add delay to prevent rate limiting
      await delay(200)

      try {
        const response = await fetchWithRetry(url, {
          headers: {
            'Authorization': `Bearer ${airtableApiKey}`,
          },
        }, 3) // Retry up to 3 times

        if (!response.ok) {
          const errorText = await response.text()
          console.error('Airtable fetch error:', errorText)
          throw new Error(`Failed to fetch ${syncType}: ${response.status} - ${errorText}`)
        }

        const pageData = await response.json()
        const pageRecords = pageData.records || []
        
        console.log(`Fetched ${pageRecords.length} records from this page`)
        
        // If we're resuming, skip records we've already processed
        if (recordsToSkip > 0) {
          const recordsToAdd = pageRecords.slice(recordsToSkip)
          allRecords = allRecords.concat(recordsToAdd)
          recordsToSkip = Math.max(0, recordsToSkip - pageRecords.length)
          console.log(`Skipped ${pageRecords.length - recordsToAdd.length} already processed records, added ${recordsToAdd.length} new records`)
        } else {
          allRecords = allRecords.concat(pageRecords)
        }
        
        totalFetched += pageRecords.length
        offset = pageData.offset
        
        // Break if we have enough records for this batch
        if (allRecords.length >= maxRecordsPerBatch) {
          break
        }
        
      } catch (fetchError) {
        const errorMessage = fetchError instanceof Error ? fetchError.message : String(fetchError)
        console.error('Failed to fetch from Airtable after retries:', errorMessage)
        throw new Error(`Airtable fetch failed: ${errorMessage}`)
      }
      
    } while (offset) // Continue until no more pages or we have enough records

    console.log(`Processing ${allRecords.length} ${syncType} records in this batch...`)
    
    // Process the records we've fetched
    if (allRecords.length > 0) {
      if (syncType === 'species') {
        await processSpeciesBatch(allRecords, supabaseClient, syncResults, syncStateId, resumeFromOffset)
      } else {
        await processPhotosBatch(allRecords, supabaseClient, bucketName, syncResults, syncStateId, resumeFromOffset)
      }
    }

    // Determine if we're completed
    syncResults.totalRecords = totalFetched
    syncResults.processedRecords = allRecords.length
    syncResults.currentOffset = resumeFromOffset + allRecords.length
    syncResults.completed = !offset // If no more offset, we're done
    syncResults.nextOffset = syncResults.completed ? 0 : syncResults.currentOffset

    // Update sync state
    if (syncStateId) {
      const updateData = { 
        status: syncResults.completed ? 'completed' : 'partial',
        current_offset: syncResults.currentOffset,
        error_count: syncResults.errors.length,
        completed_at: syncResults.completed ? new Date().toISOString() : null,
        last_updated: new Date().toISOString()
      }

      if (syncType === 'species') {
        updateData.photos_inserted = syncResults.speciesInserted // Reuse field for species count
      } else {
        updateData.photos_inserted = syncResults.photosInserted
      }

      await supabaseClient
        .from('sync_state')
        .update(updateData)
        .eq('id', syncStateId)
    }

    console.log(`${syncType} sync batch completed:`, syncResults)

    return new Response(
      JSON.stringify(syncResults),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    // Ensure error message is always a string to prevent 'undefined' responses
    const errorMessage = error instanceof Error ? error.message : String(error || 'Unknown error occurred')
    console.error('Sync error:', errorMessage)
    
    const errorResponse = {
      error: errorMessage,
      photosInserted: 0,
      photosUpdated: 0,
      speciesInserted: 0,
      speciesUpdated: 0,
      errors: [errorMessage],
      detailedErrors: [{
        type: 'system',
        error: errorMessage,
        stack: error instanceof Error ? error.stack : undefined
      }],
      totalRecords: 0,
      completed: false,
      currentOffset: 0,
      nextOffset: 0
    }
    
    return new Response(
      JSON.stringify(errorResponse),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200  // Always return 200 to prevent client-side errors
      }
    )
  }
})

// Helper function to process species batches with ALL comprehensive fields
async function processSpeciesBatch(records, supabaseClient, syncResults, syncStateId, currentOffset) {
  console.log(`Processing ${records.length} species records with comprehensive field mapping...`)
  
  const batchSize = 50 // Process species in smaller batches
  
  for (let i = 0; i < records.length; i += batchSize) {
    const batch = records.slice(i, i + batchSize)
    console.log(`Processing species batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(records.length/batchSize)}`)
    
    const batchData = []
    
    for (const record of batch) {
      try {
        const fields = record.fields
        console.log(`Processing species record ${record.id}:`, fields.species_id || fields.common_name || 'unnamed')
        console.log('Available fields:', Object.keys(fields))
        
        // Log the fun_facts field specifically to debug
        if (fields.fun_facts) {
          console.log('Found fun_facts field:', fields.fun_facts);
        }
        
        // Extract ALL comprehensive species data from Airtable fields
        const speciesData = {
          // Core identification fields
          id: fields.species_id || record.id,
          name: fields.common_name || fields.name || 'Unknown Species',
          common_name: fields.common_name || null,
          scientific_name: fields.scientific_name || null,
          
          // Classification fields
          category: fields.category || 'Wildlife',
          family: fields.taxonomy_family || fields.family || null,
          
          // Conservation and status
          conservation_status: fields.conservation_status || 'Least Concern',
          
          // Habitat and distribution
          habitat: extractHabitatInfo(fields),
          
          // Physical characteristics
          size_cm: parseNumeric(fields.size_cm),
          weight_g: parseNumeric(fields.weight_g),
          lifespan_years: parseInt(fields.lifespan || fields.lifespan_years) || null,
          size_description: fields.size_description || null,
          
          // Behavior and ecology
          diet: fields.diet || null,
          behavior: extractBehaviorInfo(fields),
          migration_pattern: fields.migration_pattern || null,
          breeding_season: fields.breeding_season || null,
          
          // Threats and conservation
          threat_level: fields.threat_level || fields.threats || null,
          population_trend: fields.population_trend || null,
          
          // Description and notes
          description: extractComprehensiveDescription(fields),
          notes: extractComprehensiveNotes(fields),
          
          // External IDs and references
          airtable_id: record.id,
          itis_tsn: fields.itis_tsn || null,
          gbif_id: fields.gbif_id || null,
          ebird_code: fields.ebird_code || null,
          inat_id: fields.inat_id || null,
          avibase_id: fields.avibase_id || null,
          
          // Taxonomy fields
          taxonomy_order: fields.taxonomy_order || null,
          taxonomy_subfamily: fields.taxonomy_subfamily || null,
          taxonomy_genus: fields.taxonomy_genus || null,
          
          // Grouping and relationships
          common_group: fields.common_group || null,
          related_groups: fields.related_groups || null,
          
          // Conservation actions
          conservation_actions: fields.conservation_actions || null,
          
          // Fun facts - CRITICAL: Extract ALL fun fact fields
          ai_fun_facts: fields.ai_fun_facts || null,
          fun_facts_field: fields.fun_facts || fields.fun_facts_field || null,
          
          // Status and metadata
          published: determinePublishedStatus(fields),
          featured: fields.featured === true || fields.featured === 'Yes',
          photo_count: parseInt(fields.photo_count) || 0,
          
          // Tags and groupings
          tags: extractComprehensiveTags(fields),
          
          // Timestamps
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }

        // Validate required fields
        if (!speciesData.id || !speciesData.name) {
          throw new Error(`Missing required fields: id=${speciesData.id}, name=${speciesData.name}`)
        }

        batchData.push(speciesData)
        
      } catch (err) {
        const errorMsg = `Species record ${record.id}: ${err.message}`
        console.error('Species record processing error:', err)
        syncResults.errors.push(errorMsg)
        syncResults.detailedErrors.push({
          type: 'species',
          recordId: record.id,
          error: err.message,
          availableFields: Object.keys(record.fields || {})
        })
      }
    }

    // Insert species batch with upsert
    if (batchData.length > 0) {
      console.log(`Inserting species batch of ${batchData.length} records with comprehensive data...`)
      
      try {
        const { error: batchError } = await supabaseClient
          .from('species')
          .upsert(batchData, { onConflict: 'id' })

        if (batchError) {
          const errorMsg = `Species batch insert error: ${batchError.message}`
          console.error('Species batch insert error details:', batchError)
          syncResults.errors.push(errorMsg)
          syncResults.detailedErrors.push({
            type: 'species_batch',
            error: batchError.message,
            batchSize: batchData.length,
            details: batchError.details || 'No additional details'
          })
          
          // Try inserting records one by one to identify problematic records
          console.log('Attempting individual record insertion...')
          let individualInserts = 0
          for (const speciesRecord of batchData) {
            try {
              const { error: individualError } = await supabaseClient
                .from('species')
                .upsert([speciesRecord], { onConflict: 'id' })
              
              if (!individualError) {
                individualInserts++
              } else {
                console.error(`Individual insert failed for species ${speciesRecord.id}:`, individualError)
                syncResults.detailedErrors.push({
                  type: 'species_individual',
                  recordId: speciesRecord.id,
                  error: individualError.message
                })
              }
            } catch (individualErr) {
              console.error(`Individual insert exception for species ${speciesRecord.id}:`, individualErr)
            }
          }
          
          syncResults.speciesInserted += individualInserts
          console.log(`Successfully inserted ${individualInserts} species records individually`)
        } else {
          syncResults.speciesInserted += batchData.length
          console.log(`Successfully inserted species batch of ${batchData.length} records with comprehensive data`)
        }
      } catch (insertException) {
        console.error('Exception during species batch insert:', insertException)
        syncResults.errors.push(`Species batch insert exception: ${insertException.message}`)
      }
    }

    syncResults.currentOffset = currentOffset + (i + batch.length)

    // Update sync state progress
    if (syncStateId) {
      await supabaseClient
        .from('sync_state')
        .update({ 
          current_offset: syncResults.currentOffset,
          photos_inserted: syncResults.speciesInserted, // Reuse field for species count
          error_count: syncResults.errors.length,
          last_updated: new Date().toISOString()
        })
        .eq('id', syncStateId)
    }
  }
}

// Helper function to extract comprehensive habitat information
function extractHabitatInfo(fields) {
  const habitatParts = []
  
  if (fields.habitat_primary) {
    if (Array.isArray(fields.habitat_primary)) {
      habitatParts.push(...fields.habitat_primary)
    } else {
      habitatParts.push(fields.habitat_primary)
    }
  }
  
  if (fields.habitat) {
    habitatParts.push(fields.habitat)
  }
  
  if (fields.regions) {
    if (Array.isArray(fields.regions)) {
      habitatParts.push(`Regions: ${fields.regions.join(', ')}`)
    } else {
      habitatParts.push(`Regions: ${fields.regions}`)
    }
  }
  
  return habitatParts.length > 0 ? habitatParts.join('; ') : null
}

// Helper function to extract behavior information
function extractBehaviorInfo(fields) {
  const behaviorParts = []
  
  if (fields.behavior) behaviorParts.push(fields.behavior)
  if (fields.migration_pattern) behaviorParts.push(`Migration: ${fields.migration_pattern}`)
  if (fields.breeding_season) behaviorParts.push(`Breeding: ${fields.breeding_season}`)
  if (fields.common_group) behaviorParts.push(`Group: ${fields.common_group}`)
  if (fields.related_groups) behaviorParts.push(`Related: ${fields.related_groups}`)
  
  return behaviorParts.length > 0 ? behaviorParts.join('; ') : null
}

// Helper function to extract comprehensive description with fun facts
function extractComprehensiveDescription(fields) {
  const descParts = []
  
  // Primary description sources
  if (fields.description) descParts.push(fields.description)
  if (fields.notes) descParts.push(fields.notes)
  
  // CRITICAL: Include fun facts in the description
  if (fields.fun_facts) {
    descParts.push(`🎯 FUN FACTS: ${fields.fun_facts}`)
  }
  
  if (fields.ai_fun_facts) {
    descParts.push(`AI Facts: ${fields.ai_fun_facts}`)
  }
  
  // If no description, create a basic one
  if (descParts.length === 0) {
    const name = fields.common_name || fields.name || 'This species'
    const category = fields.category || 'wildlife'
    const status = fields.conservation_status || 'conservation status unknown'
    descParts.push(`${name} is a ${category.toLowerCase()} species with ${status.toLowerCase()} conservation status.`)
  }
  
  return descParts.join('\n\n')
}

// Helper function to extract comprehensive notes
function extractComprehensiveNotes(fields) {
  const noteParts = []
  
  if (fields.conservation_actions) noteParts.push(`Conservation: ${fields.conservation_actions}`)
  if (fields.threats) noteParts.push(`Threats: ${fields.threats}`)
  if (fields.taxonomy_order) noteParts.push(`Order: ${fields.taxonomy_order}`)
  if (fields.taxonomy_subfamily) noteParts.push(`Subfamily: ${fields.taxonomy_subfamily}`)
  if (fields.taxonomy_genus) noteParts.push(`Genus: ${fields.taxonomy_genus}`)
  
  // Add external IDs as notes
  const externalIds = []
  if (fields.itis_tsn) externalIds.push(`ITIS: ${fields.itis_tsn}`)
  if (fields.gbif_id) externalIds.push(`GBIF: ${fields.gbif_id}`)
  if (fields.ebird_code) externalIds.push(`eBird: ${fields.ebird_code}`)
  if (fields.inat_id) externalIds.push(`iNaturalist: ${fields.inat_id}`)
  if (fields.avibase_id) externalIds.push(`Avibase: ${fields.avibase_id}`)
  
  if (externalIds.length > 0) {
    noteParts.push(`External IDs: ${externalIds.join(', ')}`)
  }
  
  // Include any other fields that might contain notes
  if (fields.Each_Conservation_Action) {
    noteParts.push(`Conservation Action: ${fields.Each_Conservation_Action}`)
  }
  
  return noteParts.length > 0 ? noteParts.join(' | ') : null
}

// Helper function to extract comprehensive tags
function extractComprehensiveTags(fields) {
  const allTags = []
  
  // Geographic and habitat tags
  if (fields.regions) {
    if (Array.isArray(fields.regions)) {
      allTags.push(...fields.regions)
    } else {
      allTags.push(fields.regions)
    }
  }
  
  if (fields.habitat_primary) {
    if (Array.isArray(fields.habitat_primary)) {
      allTags.push(...fields.habitat_primary)
    } else {
      allTags.push(fields.habitat_primary)
    }
  }
  
  // Classification tags
  if (fields.category) allTags.push(fields.category)
  if (fields.common_group) allTags.push(fields.common_group)
  if (fields.taxonomy_family) allTags.push(fields.taxonomy_family)
  if (fields.taxonomy_order) allTags.push(fields.taxonomy_order)
  
  // Conservation tags
  if (fields.conservation_status) allTags.push(fields.conservation_status)
  if (fields.threat_level) allTags.push(fields.threat_level)
  
  // Remove duplicates and ensure all are strings
  const uniqueTags = [...new Set(allTags)]
  return uniqueTags.filter(tag => typeof tag === 'string' && tag.trim().length > 0)
}

// Helper function to parse numeric values safely
function parseNumeric(value) {
  if (value === null || value === undefined || value === '') return null
  const parsed = parseFloat(value)
  return isNaN(parsed) ? null : parsed
}

// Helper function to determine published status
function determinePublishedStatus(fields) {
  // Published if status is 'Verified' or 'Published'
  if (fields.status === 'Verified' || fields.status === 'Published') return true
  // Unpublished if status is 'Needs Review' or 'Draft'
  if (fields.status === 'Needs Review' || fields.status === 'Draft') return false
  // Default to true for other statuses
  return true
}

// Helper function to process photos batches
async function processPhotosBatch(records, supabaseClient, bucketName, syncResults, syncStateId, currentOffset) {
  // Get Supabase project URL for constructing storage URLs
  const supabaseUrl = Deno.env.get('SUPABASE_URL')
  
  // Process photos in smaller batches
  const batchSize = 20 // Smaller batches for better reliability
  console.log(`Processing ${records.length} photo records in batches of ${batchSize}...`)
  
  for (let i = 0; i < records.length; i += batchSize) {
    const batch = records.slice(i, i + batchSize)
    console.log(`Processing photos batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(records.length/batchSize)}`)
    
    const batchData = []
    
    for (const record of batch) {
      try {
        const fields = record.fields
        console.log(`Processing photo record ${record.id}:`, Object.keys(fields))
        
        // Get the s3_key (storage path) from the record
        let s3Key = fields.s3_key
        
        if (!s3Key) {
          const errorMsg = `Photo record ${record.id}: No s3_key found`
          console.log(errorMsg)
          syncResults.errors.push(errorMsg)
          syncResults.detailedErrors.push({
            type: 'photo',
            recordId: record.id,
            error: 'No s3_key found',
            availableFields: Object.keys(fields)
          })
          continue
        }

        // Clean and construct the storage URL
        s3Key = s3Key.replace(/^\/+/, '').replace(/^species\//, '')
        const finalUrl = `${supabaseUrl}/storage/v1/object/public/${bucketName}/${s3Key}`
        
        console.log(`Constructed URL for ${record.id}: ${finalUrl}`)

        // Map ALL available fields from Airtable - ensure all values are properly formatted
        const photoData = {
          airtable_id: record.id,
          url: finalUrl,
          title: fields.photo_id || fields.filename || null,
          description: extractPhotoDescription(fields),
          photographer: fields.photographer || null,
          location: fields.location || null,
          camera_settings: extractCameraSettings(fields),
          weather_conditions: fields.weather_conditions || null,
          time_of_day: fields.date_taken || null,
          notes: extractPhotoNotes(fields),
          species_id: fields.species_id || null,
          published: determinePhotoPublishedStatus(fields),
          tags: extractPhotoTags(fields) // Ensure this returns a proper array
        }

        // Validate data before adding to batch
        if (Array.isArray(photoData.tags) && photoData.tags.length > 0) {
          // Make sure all tags are strings
          photoData.tags = photoData.tags.filter(tag => typeof tag === 'string' && tag.trim().length > 0)
        } else {
          photoData.tags = []
        }

        batchData.push(photoData)
        
      } catch (err) {
        const errorMsg = `Photo record ${record.id}: ${err.message}`
        console.error('Photo record processing error:', err)
        syncResults.errors.push(errorMsg)
        syncResults.detailedErrors.push({
          type: 'photo',
          recordId: record.id,
          error: err.message
        })
      }
    }

    // Insert photos batch - with better error handling
    if (batchData.length > 0) {
      console.log(`Inserting photos batch of ${batchData.length} records...`)
      
      try {
        const { error: batchError } = await supabaseClient
          .from('photos')
          .insert(batchData)

        if (batchError) {
          const errorMsg = `Photos batch insert error: ${batchError.message}`
          console.error('Photos batch insert error details:', batchError)
          syncResults.errors.push(errorMsg)
          syncResults.detailedErrors.push({
            type: 'photos_batch',
            error: batchError.message,
            batchSize: batchData.length,
            details: batchError.details || 'No additional details'
          })
          
          // Try inserting records one by one to identify problematic records
          console.log('Attempting individual record insertion...')
          let individualInserts = 0
          for (const photoRecord of batchData) {
            try {
              const { error: individualError } = await supabaseClient
                .from('photos')
                .insert([photoRecord])
              
              if (!individualError) {
                individualInserts++
              } else {
                console.error(`Individual insert failed for record:`, individualError)
              }
            } catch (individualErr) {
              console.error(`Individual insert exception:`, individualErr)
            }
          }
          syncResults.photosInserted += individualInserts
          console.log(`Successfully inserted ${individualInserts} records individually`)
        } else {
          syncResults.photosInserted += batchData.length
          console.log(`Successfully inserted photos batch of ${batchData.length} records`)
        }
      } catch (insertException) {
        console.error('Exception during batch insert:', insertException)
        syncResults.errors.push(`Batch insert exception: ${insertException.message}`)
      }
    }

    syncResults.currentOffset = currentOffset + (i + batch.length)

    // Update sync state progress
    if (syncStateId) {
      await supabaseClient
        .from('sync_state')
        .update({ 
          current_offset: syncResults.currentOffset,
          photos_inserted: syncResults.photosInserted,
          error_count: syncResults.errors.length,
          last_updated: new Date().toISOString()
        })
        .eq('id', syncStateId)
    }
  }
}

// Helper function to extract photo description from various fields
function extractPhotoDescription(fields) {
  const descFields = ['species', 'common_name', 'identification_method']
  for (const field of descFields) {
    if (fields[field]) {
      return Array.isArray(fields[field]) ? JSON.stringify(fields[field]) : String(fields[field])
    }
  }
  return null
}

// Helper function to extract camera settings with ALL the camera fields
function extractCameraSettings(fields) {
  const parts = []
  if (fields.camera_make) parts.push(fields.camera_make)
  if (fields.camera_model) parts.push(fields.camera_model)
  if (fields.lens_model) parts.push(`Lens: ${fields.lens_model}`)
  if (fields.focal_length) parts.push(`${fields.focal_length}mm`)
  if (fields.aperture) parts.push(`f/${fields.aperture}`)
  if (fields.shutter_speed) parts.push(fields.shutter_speed)
  if (fields.iso) parts.push(`ISO ${fields.iso}`)
  if (fields.flash) parts.push(`Flash: ${fields.flash}`)
  if (fields.white_balance) parts.push(`WB: ${fields.white_balance}`)
  if (fields.exposure_mode) parts.push(`Exp: ${fields.exposure_mode}`)
  if (fields.metering_mode) parts.push(`Meter: ${fields.metering_mode}`)
  if (fields.color_space) parts.push(`Color: ${fields.color_space}`)
  
  return parts.length > 0 ? parts.join(', ') : null
}

// Helper function to extract photo notes from various fields
function extractPhotoNotes(fields) {
  const noteFields = ['processing_status', 'location_ref', 'bing_tags']
  const noteParts = []
  
  for (const field of noteFields) {
    if (fields[field]) {
      const value = Array.isArray(fields[field]) ? fields[field].join(', ') : String(fields[field])
      noteParts.push(`${field}: ${value}`)
    }
  }
  
  // Add GPS data if available
  if (fields.gps_latitude && fields.gps_longitude) {
    noteParts.push(`GPS: ${fields.gps_latitude}, ${fields.gps_longitude}`)
    if (fields.gps_altitude) {
      noteParts.push(`Altitude: ${fields.gps_altitude}`)
    }
  }
  
  // Add image dimensions if available
  if (fields.image_width && fields.image_height) {
    noteParts.push(`Dimensions: ${fields.image_width}x${fields.image_height}`)
  }
  
  // Add featured/starred status
  if (fields.featured) noteParts.push('Featured: true')
  if (fields.starred) noteParts.push('Starred: true')
  
  return noteParts.length > 0 ? noteParts.join(' | ') : null
}

// Helper function to determine photo published status
function determinePhotoPublishedStatus(fields) {
  // Default to published unless explicitly marked as draft or needs review
  if (fields.processing_status === 'Draft') return false
  if (fields.needs_review === 'checked') return false
  return true
}

// Helper function to extract photo tags
function extractPhotoTags(fields) {
  const tagFields = ['tags', 'bing_tags']
  const allTags = []
  
  for (const field of tagFields) {
    if (fields[field]) {
      if (Array.isArray(fields[field])) {
        allTags.push(...fields[field])
      } else if (typeof fields[field] === 'string') {
        // Try to split by common delimiters
        const splitTags = fields[field].split(/[,;|]/).map(tag => tag.trim()).filter(tag => tag.length > 0)
        allTags.push(...splitTags)
      }
    }
  }
  
  // Add species and common_name as tags if they exist
  if (fields.species) allTags.push(fields.species)
  if (fields.common_name) allTags.push(fields.common_name)
  
  // Remove duplicates and ensure all are strings
  const uniqueTags = [...new Set(allTags)]
  return uniqueTags.filter(tag => typeof tag === 'string' && tag.trim().length > 0)
}