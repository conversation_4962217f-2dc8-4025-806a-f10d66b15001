import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';

const SUPABASE_URL = Deno.env.get('SUPABASE_URL');
const SUPABASE_ANON_KEY = Deno.env.get('SUPABASE_ANON_KEY');

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

serve(async (req) => {
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method Not Allowed' }), {
      status: 405,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  try {
    const { data: photos, error: fetchError } = await supabase
      .from('photos_v2')
      .select('id, url');

    if (fetchError) throw fetchError;

    if (!photos || photos.length === 0) {
      return new Response(JSON.stringify({ message: 'No photos found to process.' }), {
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const updates = photos.map(photo => {
      const sanitizedUrl = photo.url ? photo.url.replace(/\s/g, '') : photo.url;
      return {
        id: photo.id,
        url: sanitizedUrl,
        changed: photo.url !== sanitizedUrl
      };
    }).filter(p => p.changed);
    
    if (updates.length === 0) {
      return new Response(JSON.stringify({ message: 'No URLs needed fixing.' }), {
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const updatesToUpsert = updates.map(({ id, url }) => ({ id, url }));

    const { error: updateError } = await supabase.from('photos').upsert(updatesToUpsert);
    if (updateError) throw updateError;

    return new Response(JSON.stringify({ message: `Successfully fixed ${updates.length} photo URLs.` }), {
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}); 