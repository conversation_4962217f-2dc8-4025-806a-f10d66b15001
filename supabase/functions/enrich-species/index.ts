/// <reference types="https://esm.sh/v135/@supabase/functions-js@2.4.1/src/edge-runtime.d.ts" />

import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";
import OpenAI from "https://esm.sh/openai";

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: Deno.env.get("OPENAI_API_KEY"),
});

interface EnrichRequest {
  commonName: string;
  scientificName: string;
  currentDescription?: string;
}

async function generateSpeciesDescription(
  commonName: string, 
  scientificName: string, 
  currentDescription?: string
): Promise<string> {
  const context = currentDescription 
    ? `Current description: "${currentDescription}". Please improve and expand this description.`
    : '';

  const prompt = `Generate a detailed, educational description for ${commonName} (${scientificName}). 

${context}

The description should be written in a natural, flowing paragraph that includes:
- Habitat information
- Diet details  
- Behavioral characteristics
- At least 1 interesting fun fact

Write in a tone suitable for a nature field guide - factual, educational, and engaging. Aim for 3-4 sentences that naturally weave together habitat, diet, behavior, and fun facts.

Example format:
"The [species] inhabits [habitat description], where it [behavior]. This species primarily feeds on [diet], and [fun fact about behavior/adaptation/characteristic]."

Generate only the description paragraph, no additional formatting or labels.`;

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',  // Updated to newer, more cost-effective model
      messages: [
        {
          role: 'system',
          content: 'You are a wildlife expert writing educational content for a nature field guide. Be factual, engaging, and include specific details about habitat, diet, behavior, and interesting facts.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 250,
      temperature: 0.7,
    });

    return completion.choices[0]?.message?.content?.trim() || '';
  } catch (error) {
    console.error(`Error generating description for ${commonName}:`, error);
    throw new Error(`Failed to generate description: ${error}`);
  }
}

serve(async (req) => {
  // This is needed if you're planning to invoke your function from a browser.
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { commonName, scientificName, currentDescription } = await req.json() as EnrichRequest;

    const description = await generateSpeciesDescription(
      commonName,
      scientificName,
      currentDescription
    );

    if (!description) {
      return new Response(JSON.stringify({ error: 'Failed to generate description' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      });
    }

    return new Response(JSON.stringify({ description }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
}); 