import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'GET') {
    return new Response(JSON.stringify({
      success: false,
      error: { message: 'Method not allowed' }
    }), {
      status: 405,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }

  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? ''
  )

  try {
    return await handleSearch(req, supabase)
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: { message: error.message }
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

async function handleSearch(req: Request, supabase: any) {
  const url = new URL(req.url)
  const query = url.searchParams.get('q') || ''
  const type = url.searchParams.get('type') || 'all' // 'species', 'photos', 'all'
  const page = parseInt(url.searchParams.get('page') || '1')
  const limit = parseInt(url.searchParams.get('limit') || '20')
  const category = url.searchParams.get('category')
  const conservation_status = url.searchParams.get('conservation_status')

  if (!query.trim()) {
    return new Response(JSON.stringify({
      success: false,
      error: { message: 'Search query is required' }
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }

  const results: any = {
    species: [],
    photos: [],
    meta: {
      query,
      type,
      page,
      limit,
      totalSpecies: 0,
      totalPhotos: 0
    }
  }

  // Search species
  if (type === 'species' || type === 'all') {
    let speciesQuery = supabase
      .from('species_v2')
      .select('*', { count: 'exact' })
      .eq('published', true)
      .or(`name.ilike.%${query}%,common_name.ilike.%${query}%,scientific_name.ilike.%${query}%,description.ilike.%${query}%`)

    if (category) speciesQuery = speciesQuery.eq('category', category)
    if (conservation_status) speciesQuery = speciesQuery.eq('conservation_status', conservation_status)

    const from = (page - 1) * limit
    const to = from + limit - 1
    speciesQuery = speciesQuery.range(from, to).order('name')

    const { data: speciesData, error: speciesError, count: speciesCount } = await speciesQuery

    if (speciesError) throw speciesError

    results.species = speciesData || []
    results.meta.totalSpecies = speciesCount || 0
  }

  // Search photos
  if (type === 'photos' || type === 'all') {
    let photosQuery = supabase
      .from('photos_v2')
      .select(`
        *,
        species:species_id (
          id,
          name,
          common_name,
          category,
          conservation_status
        )
      `, { count: 'exact' })
      .eq('published', true)
      .or(`title.ilike.%${query}%,description.ilike.%${query}%,photographer.ilike.%${query}%,location.ilike.%${query}%`)

    if (category) photosQuery = photosQuery.eq('species.category', category)
    if (conservation_status) photosQuery = photosQuery.eq('species.conservation_status', conservation_status)

    const from = (page - 1) * limit
    const to = from + limit - 1
    photosQuery = photosQuery.range(from, to).order('created_at', { ascending: false })

    const { data: photosData, error: photosError, count: photosCount } = await photosQuery

    if (photosError) throw photosError

    results.photos = photosData || []
    results.meta.totalPhotos = photosCount || 0
  }

  return new Response(JSON.stringify({
    success: true,
    data: results
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
} 