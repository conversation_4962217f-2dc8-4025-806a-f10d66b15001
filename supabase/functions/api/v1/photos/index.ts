import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? ''
  )

  const { method, url } = req
  const urlObj = new URL(url)
  const path = urlObj.pathname.replace('/api/v1/photos', '')
  
  try {
    switch (method) {
      case 'GET':
        if (path === '/' || path === '') {
          return await handleListPhotos(req, supabase)
        } else {
          return await handleGetPhoto(path.slice(1), supabase)
        }
      case 'POST':
        return await handleCreatePhoto(req, supabase)
      case 'PUT':
        if (path && path !== '/') {
          return await handleUpdatePhoto(path.slice(1), req, supabase)
        }
        break
      case 'DELETE':
        if (path && path !== '/') {
          return await handleDeletePhoto(path.slice(1), supabase)
        }
        break
    }

    return new Response(JSON.stringify({
      success: false,
      error: { message: 'Method not allowed' }
    }), {
      status: 405,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })

  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: { message: error.message }
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

async function handleListPhotos(req: Request, supabase: any) {
  const url = new URL(req.url)
  const page = parseInt(url.searchParams.get('page') || '1')
  const limit = parseInt(url.searchParams.get('limit') || '20')
  const species_id = url.searchParams.get('species_id')
  const photographer = url.searchParams.get('photographer')
  const location = url.searchParams.get('location')
  const published = url.searchParams.get('published') !== 'false' // default to true

  let query = supabase
    .from('photos_v2')
    .select(`
      *,
      species:species_id (
        id,
        name,
        common_name,
        category,
        conservation_status
      )
    `, { count: 'exact' })

  // Apply filters
  if (published !== null) query = query.eq('published', published === 'true')
  if (species_id) query = query.eq('species_id', species_id)
  if (photographer) query = query.ilike('photographer', `%${photographer}%`)
  if (location) query = query.ilike('location', `%${location}%`)

  // Apply pagination
  const from = (page - 1) * limit
  const to = from + limit - 1
  query = query.range(from, to).order('created_at', { ascending: false })

  const { data, error, count } = await query

  if (error) throw error

  return new Response(JSON.stringify({
    success: true,
    data,
    meta: {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    }
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
}

async function handleGetPhoto(photoId: string, supabase: any) {
  const { data, error } = await supabase
    .from('photos_v2')
    .select(`
      *,
      species:species_id (
        id,
        name,
        common_name,
        scientific_name,
        category,
        conservation_status,
        description,
        habitat,
        diet,
        behavior
      )
    `)
    .eq('id', photoId)
    .single()

  if (error) {
    if (error.code === 'PGRST116') {
      return new Response(JSON.stringify({
        success: false,
        error: { message: 'Photo not found' }
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    throw error
  }

  return new Response(JSON.stringify({
    success: true,
    data
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
}

async function handleCreatePhoto(req: Request, supabase: any) {
  const body = await req.json()
  
  // Validate required fields
  if (!body.url) {
    return new Response(JSON.stringify({
      success: false,
      error: { message: 'URL is required' }
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }

  const { data, error } = await supabase
    .from('photos_v2')
    .insert({
      ...body,
      published: body.published ?? true,
      created_at: new Date().toISOString()
    })
    .select()
    .single()

  if (error) throw error

  return new Response(JSON.stringify({
    success: true,
    data
  }), {
    status: 201,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
}

async function handleUpdatePhoto(photoId: string, req: Request, supabase: any) {
  const body = await req.json()

  const { data, error } = await supabase
    .from('photos_v2')
    .update({
      ...body,
      updated_at: new Date().toISOString()
    })
    .eq('id', photoId)
    .select()
    .single()

  if (error) {
    if (error.code === 'PGRST116') {
      return new Response(JSON.stringify({
        success: false,
        error: { message: 'Photo not found' }
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    throw error
  }

  return new Response(JSON.stringify({
    success: true,
    data
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
}

async function handleDeletePhoto(photoId: string, supabase: any) {
  const { error } = await supabase
    .from('photos_v2')
    .delete()
    .eq('id', photoId)

  if (error) {
    if (error.code === 'PGRST116') {
      return new Response(JSON.stringify({
        success: false,
        error: { message: 'Photo not found' }
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    throw error
  }

  return new Response(JSON.stringify({
    success: true,
    message: 'Photo deleted successfully'
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
} 