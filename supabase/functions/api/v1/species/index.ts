import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? ''
  )

  const { method, url } = req
  const urlObj = new URL(url)
  const path = urlObj.pathname.replace('/api/v1/species', '')
  
  try {
    switch (method) {
      case 'GET':
        if (path === '/' || path === '') {
          return await handleListSpecies(req, supabase)
        } else {
          return await handleGetSpecies(path.slice(1), supabase)
        }
      case 'POST':
        return await handleCreateSpecies(req, supabase)
      case 'PUT':
        if (path && path !== '/') {
          return await handleUpdateSpecies(path.slice(1), req, supabase)
        }
        break
      case 'DELETE':
        if (path && path !== '/') {
          return await handleDeleteSpecies(path.slice(1), supabase)
        }
        break
    }

    return new Response(JSON.stringify({
      success: false,
      error: { message: 'Method not allowed' }
    }), {
      status: 405,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })

  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: { message: error.message }
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

async function handleListSpecies(req: Request, supabase: any) {
  const url = new URL(req.url)
  const page = parseInt(url.searchParams.get('page') || '1')
  const limit = parseInt(url.searchParams.get('limit') || '20')
  const category = url.searchParams.get('category')
  const conservation_status = url.searchParams.get('conservation_status')
  const featured = url.searchParams.get('featured')
  const published = url.searchParams.get('published') !== 'false' // default to true

  let query = supabase
    .from('species_v2')
    .select('*', { count: 'exact' })

  // Apply filters
  if (published !== null) query = query.eq('published', published === 'true')
  if (category) query = query.eq('category', category)
  if (conservation_status) query = query.eq('conservation_status', conservation_status)
  if (featured) query = query.eq('featured', featured === 'true')

  // Apply pagination
  const from = (page - 1) * limit
  const to = from + limit - 1
  query = query.range(from, to).order('name')

  const { data, error, count } = await query

  if (error) throw error

  return new Response(JSON.stringify({
    success: true,
    data,
    meta: {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    }
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
}

async function handleGetSpecies(speciesId: string, supabase: any) {
  // Get species, photos, and fun facts separately to avoid join issues
  const [speciesData, photosData, funFactsData] = await Promise.all([
    supabase.from('species_v2').select('*').eq('id', speciesId).single(),
    supabase.from('photos_v2').select(`
      id,
      url,
      title,
      description,
      photographer,
      location,
      camera_settings,
      weather_conditions,
      time_of_day,
      tags,
      published,
      created_at
    `).eq('species_id', speciesId).eq('published', true).order('created_at', { ascending: false }),
    supabase.from('fun_facts').select('fact').eq('species_id', speciesId).order('created_at', { ascending: true })
  ]);

  if (speciesData.error) {
    if (speciesData.error.code === 'PGRST116') {
      return new Response(JSON.stringify({
        success: false,
        error: { message: 'Species not found' }
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    throw speciesData.error;
  }

  if (photosData.error) throw photosData.error;
  if (funFactsData.error) throw funFactsData.error;

  const data = {
    ...speciesData.data,
    photos: photosData.data || [],
    fun_facts: funFactsData.data?.map(item => item.fact) || []
  };

  return new Response(JSON.stringify({
    success: true,
    data
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
}

async function handleCreateSpecies(req: Request, supabase: any) {
  const body = await req.json()
  
  // Validate required fields
  if (!body.name) {
    return new Response(JSON.stringify({
      success: false,
      error: { message: 'Name is required' }
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }

  const { data, error } = await supabase
    .from('species_v2')
    .insert({
      ...body,
      published: body.published ?? true,
      created_at: new Date().toISOString()
    })
    .select()
    .single()

  if (error) throw error

  return new Response(JSON.stringify({
    success: true,
    data
  }), {
    status: 201,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
}

async function handleUpdateSpecies(speciesId: string, req: Request, supabase: any) {
  const body = await req.json()

  const { data, error } = await supabase
    .from('species_v2')
    .update({
      ...body,
      updated_at: new Date().toISOString()
    })
    .eq('id', speciesId)
    .select()
    .single()

  if (error) {
    if (error.code === 'PGRST116') {
      return new Response(JSON.stringify({
        success: false,
        error: { message: 'Species not found' }
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    throw error
  }

  return new Response(JSON.stringify({
    success: true,
    data
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
}

async function handleDeleteSpecies(speciesId: string, supabase: any) {
  const { error } = await supabase
    .from('species_v2')
    .delete()
    .eq('id', speciesId)

  if (error) {
    if (error.code === 'PGRST116') {
      return new Response(JSON.stringify({
        success: false,
        error: { message: 'Species not found' }
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    throw error
  }

  return new Response(JSON.stringify({
    success: true,
    message: 'Species deleted successfully'
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
} 