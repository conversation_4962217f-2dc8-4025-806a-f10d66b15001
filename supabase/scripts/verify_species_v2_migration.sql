-- Verification Script for species_v2 Migration
-- Run this after the main migration to verify results

-- 1. Check row counts
SELECT 
  'Row Count Verification' as check_type,
  (SELECT COUNT(*) FROM species) as species_count,
  (SELECT COUNT(*) FROM species_v2) as species_v2_count,
  CASE 
    WHEN (SELECT COUNT(*) FROM species) = (SELECT COUNT(*) FROM species_v2) 
    THEN '✅ MATCH' 
    ELSE '❌ MISMATCH' 
  END as status;

-- 2. Check sample records
SELECT 
  'Sample Records' as check_type,
  s.id as original_id,
  s.name as original_name,
  s2.id as new_uuid,
  s2.legacy_id,
  s2.name as new_name,
  CASE 
    WHEN s.id = s2.legacy_id AND s.name = s2.name 
    THEN '✅ CORRECT' 
    ELSE '❌ ERROR' 
  END as verification
FROM species s 
JOIN species_v2 s2 ON s.id = s2.legacy_id 
ORDER BY s.name
LIMIT 5;

-- 3. Check for any missing records
SELECT 
  'Missing Records Check' as check_type,
  COUNT(*) as missing_count
FROM species s 
LEFT JOIN species_v2 s2 ON s.id = s2.legacy_id 
WHERE s2.legacy_id IS NULL;

-- 4. Check table structure
SELECT 
  'Table Structure' as check_type,
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns 
WHERE table_name = 'species_v2' 
ORDER BY ordinal_position;

-- 5. Check indexes
SELECT 
  'Indexes' as check_type,
  indexname,
  indexdef
FROM pg_indexes 
WHERE tablename = 'species_v2'; 