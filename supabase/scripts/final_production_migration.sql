-- Final Production Migration Script for Supabase
-- Project: wildlife-explorer
-- Target: Migrate 447 species records to species_v2 table
-- This script is fully dynamic and handles column mismatches.

-- Step 1: Drop the existing species_v2 table to start fresh
DROP TABLE IF EXISTS species_v2 CASCADE;

-- Step 2: Create the definitive species_v2 table with the complete schema
CREATE TABLE species_v2 (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  legacy_id TEXT,
  name TEXT,
  common_name TEXT,
  scientific_name TEXT,
  family TEXT,
  habitat TEXT,
  diet TEXT,
  behavior TEXT,
  conservation_status TEXT,
  description TEXT,
  category TEXT,
  tags TEXT[],
  published BOOLEAN,
  featured BOOLEAN,
  photo_count INTEGER,
  airtable_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  size_cm TEXT,
  weight_g TEXT,
  lifespan_years INTEGER,
  migration_pattern TEXT,
  breeding_season TEXT,
  threat_level TEXT,
  population_trend TEXT,
  size_description TEXT,
  itis_tsn TEXT,
  gbif_id TEXT,
  ebird_code TEXT,
  inat_id TEXT,
  avibase_id TEXT,
  taxonomy_order TEXT,
  taxonomy_subfamily TEXT,
  taxonomy_genus TEXT,
  common_group TEXT,
  related_groups TEXT[],
  conservation_actions TEXT,
  ai_fun_facts JSONB,
  fun_facts_field JSONB,
  notes TEXT,
  ai_generated BOOLEAN DEFAULT FALSE,
  ai_confidence FLOAT DEFAULT 0,
  regions TEXT
);

-- Step 3: Dynamically migrate data by matching columns
DO $$
DECLARE
  -- Find columns that exist in BOTH tables
  shared_columns TEXT := (
    SELECT string_agg(quote_ident(column_name), ', ')
    FROM (
      SELECT column_name FROM information_schema.columns WHERE table_name = 'species'
      INTERSECT
      SELECT column_name FROM information_schema.columns WHERE table_name = 'species_v2' AND column_name != 'id'
    ) AS shared
  );
  
  -- The original id from 'species' will be mapped to 'legacy_id'
  dynamic_query TEXT;
BEGIN
  -- Construct the dynamic INSERT statement
  dynamic_query := 'INSERT INTO species_v2 (legacy_id, ' || shared_columns || ') ' ||
                   'SELECT id::text, ' || shared_columns || ' FROM species;';

  -- Execute the query
  RAISE NOTICE 'Executing dynamic migration: %', dynamic_query;
  EXECUTE dynamic_query;
END;
$$;


-- Step 4: Add indexes and constraints
CREATE INDEX IF NOT EXISTS idx_species_v2_name ON species_v2(name);
CREATE INDEX IF NOT EXISTS idx_species_v2_legacy_id ON species_v2(legacy_id);
ALTER TABLE species_v2 ADD CONSTRAINT species_v2_name_key UNIQUE (name);

-- Step 5: Verification queries
SELECT
  'Migration Complete' as status,
  (SELECT COUNT(*) FROM species) as original_count,
  (SELECT COUNT(*) FROM species_v2) as new_count;

SELECT * FROM species_v2 LIMIT 5; 