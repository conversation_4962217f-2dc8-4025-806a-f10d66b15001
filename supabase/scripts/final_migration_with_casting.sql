-- Final Production Migration Script for Supabase (with Dynamic Casting)
-- Project: wildlife-explorer
-- This script is fully dynamic and handles column and data type mismatches.

-- Step 1: Drop the existing species_v2 table to start fresh
DROP TABLE IF EXISTS species_v2 CASCADE;

-- Step 2: Create the definitive species_v2 table with the complete schema
CREATE TABLE species_v2 (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  legacy_id TEXT,
  name TEXT,
  common_name TEXT,
  scientific_name TEXT,
  family TEXT,
  habitat TEXT,
  diet TEXT,
  behavior TEXT,
  conservation_status TEXT,
  description TEXT,
  category TEXT,
  tags TEXT[],
  published BOOLEAN,
  featured BOOLEAN,
  photo_count INTEGER,
  airtable_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  size_cm TEXT,
  weight_g TEXT,
  lifespan_years INTEGER,
  migration_pattern TEXT,
  breeding_season TEXT,
  threat_level TEXT,
  population_trend TEXT,
  size_description TEXT,
  itis_tsn TEXT,
  gbif_id TEXT,
  ebird_code TEXT,
  inat_id TEXT,
  avibase_id TEXT,
  taxonomy_order TEXT,
  taxonomy_subfamily TEXT,
  taxonomy_genus TEXT,
  common_group TEXT,
  related_groups TEXT[],
  conservation_actions TEXT,
  ai_fun_facts JSONB,
  fun_facts_field JSONB,
  notes TEXT,
  ai_generated BOOLEAN DEFAULT FALSE,
  ai_confidence FLOAT DEFAULT 0,
  regions TEXT
);

-- Step 3: Dynamically migrate data by matching columns and casting where needed
DO $$
DECLARE
  insert_columns TEXT;
  select_expressions TEXT;
  dynamic_query TEXT;
BEGIN
  -- 1. Get the list of columns for the INSERT statement (names are the same)
  SELECT string_agg(quote_ident(c.column_name), ', ')
  INTO insert_columns
  FROM (
      SELECT column_name FROM information_schema.columns WHERE table_name = 'species'
      INTERSECT
      SELECT column_name FROM information_schema.columns WHERE table_name = 'species_v2' AND column_name NOT IN ('id', 'legacy_id')
  ) AS c;

  -- 2. Get the list of expressions for the SELECT statement, with conditional casting
  SELECT string_agg(
    CASE 
      WHEN c.column_name IN ('ai_fun_facts', 'fun_facts_field') THEN 'NULLIF(' || quote_ident(c.column_name) || ', '''')::jsonb'
      ELSE quote_ident(c.column_name)
    END,
    ', '
  )
  INTO select_expressions
  FROM (
      SELECT column_name FROM information_schema.columns WHERE table_name = 'species'
      INTERSECT
      SELECT column_name FROM information_schema.columns WHERE table_name = 'species_v2' AND column_name NOT IN ('id', 'legacy_id')
  ) AS c;

  -- 3. Construct the full dynamic query, mapping original 'id' to 'legacy_id'
  dynamic_query := 'INSERT INTO species_v2 (legacy_id, ' || insert_columns || ') ' ||
                   'SELECT id::text, ' || select_expressions || ' FROM species;';

  RAISE NOTICE 'Executing dynamic migration: %', dynamic_query;
  EXECUTE dynamic_query;
END;
$$;


-- Step 4: Add indexes and constraints
CREATE INDEX IF NOT EXISTS idx_species_v2_name ON species_v2(name);
CREATE INDEX IF NOT EXISTS idx_species_v2_legacy_id ON species_v2(legacy_id);
ALTER TABLE species_v2 ADD CONSTRAINT species_v2_name_key UNIQUE (name);

-- Step 5: Verification queries
SELECT
  'Migration Complete' as status,
  (SELECT COUNT(*) FROM species) as original_count,
  (SELECT COUNT(*) FROM species_v2) as new_count;

SELECT id, legacy_id, name, ai_fun_facts, ai_generated, regions FROM species_v2 LIMIT 5; 