-- Check for duplicate photos in photos_v2 table
-- This will help identify if the same photo appears multiple times

-- 1. Check for duplicates by URL
SELECT 
    url,
    COUNT(*) as duplicate_count,
    array_agg(id) as photo_ids
FROM photos_v2 
WHERE url IS NOT NULL
GROUP BY url 
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- 2. Check for duplicates by title
SELECT 
    title,
    COUNT(*) as duplicate_count,
    array_agg(id) as photo_ids
FROM photos_v2 
WHERE title IS NOT NULL
GROUP BY title 
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- 3. Check for duplicates by hash (if hash field exists)
SELECT 
    hash,
    COUNT(*) as duplicate_count,
    array_agg(id) as photo_ids
FROM photos_v2 
WHERE hash IS NOT NULL
GROUP BY hash 
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- 4. Get total count of photos
SELECT COUNT(*) as total_photos FROM photos_v2;

-- 5. Check for photos with same URL and title
SELECT 
    url,
    title,
    COUNT(*) as duplicate_count,
    array_agg(id) as photo_ids
FROM photos_v2 
WHERE url IS NOT NULL AND title IS NOT NULL
GROUP BY url, title 
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;
