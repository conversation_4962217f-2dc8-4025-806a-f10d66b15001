<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wildlife API Example - External Site Usage</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .species-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .species-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            background: white;
        }
        .species-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .search-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .search-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .error {
            color: #dc3545;
            padding: 10px;
            background: #f8d7da;
            border-radius: 4px;
            margin: 10px 0;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌿 Wildlife Explorer - API Integration Example</h1>
        <p>This page demonstrates how external websites can integrate with the Wildlife API to display species and photo data.</p>

        <div class="search-section">
            <h2>Search Wildlife</h2>
            <input type="text" id="searchInput" class="search-input" placeholder="Search for species (e.g., 'goldfinch', 'eagle', 'owl')">
            <button onclick="searchSpecies()" class="btn">Search</button>
        </div>

        <div class="stats">
            <div class="stat">
                <div class="stat-number" id="totalSpecies">-</div>
                <div>Total Species</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="totalPhotos">-</div>
                <div>Total Photos</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="categories">-</div>
                <div>Categories</div>
            </div>
        </div>

        <div id="results" class="species-grid">
            <div class="loading">Loading featured species...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'https://raqxptgrugnmxdunbuty.supabase.co/functions/v1/api/v1';

        // Load featured species on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadFeaturedSpecies();
            loadStats();
        });

        async function loadFeaturedSpecies() {
            try {
                const response = await fetch(`${API_BASE}/species?featured=true&limit=6`);
                const data = await response.json();
                
                if (data.success && data.data) {
                    displaySpecies(data.data);
                } else {
                    showError('Failed to load featured species');
                }
            } catch (error) {
                showError('Error loading featured species: ' + error.message);
            }
        }

        async function searchSpecies() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) {
                loadFeaturedSpecies();
                return;
            }

            document.getElementById('results').innerHTML = '<div class="loading">Searching...</div>';

            try {
                const response = await fetch(`${API_BASE}/search?q=${encodeURIComponent(query)}&type=species&limit=20`);
                const data = await response.json();
                
                if (data.success && data.data) {
                    displaySpecies(data.data.species);
                } else {
                    showError('Search failed: ' + (data.error?.message || 'Unknown error'));
                }
            } catch (error) {
                showError('Search error: ' + error.message);
            }
        }

        async function loadStats() {
            try {
                // Load categories for stats
                const categoriesResponse = await fetch(`${API_BASE}/categories`);
                const categoriesData = await categoriesResponse.json();
                
                if (categoriesData.success) {
                    document.getElementById('categories').textContent = categoriesData.data.length;
                }

                // Load sample species for total count
                const speciesResponse = await fetch(`${API_BASE}/species?limit=1`);
                const speciesData = await speciesResponse.json();
                
                if (speciesData.success && speciesData.meta) {
                    document.getElementById('totalSpecies').textContent = speciesData.meta.total || '?';
                }

                // Load sample photos for total count
                const photosResponse = await fetch(`${API_BASE}/photos?limit=1`);
                const photosData = await photosResponse.json();
                
                if (photosData.success && photosData.meta) {
                    document.getElementById('totalPhotos').textContent = photosData.meta.total || '?';
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        function displaySpecies(species) {
            const resultsDiv = document.getElementById('results');
            
            if (!species || species.length === 0) {
                resultsDiv.innerHTML = '<div class="loading">No species found</div>';
                return;
            }

            resultsDiv.innerHTML = species.map(spec => `
                <div class="species-card">
                    <img src="${getSpeciesImage(spec)}" alt="${spec.name}" onerror="this.src='https://via.placeholder.com/300x200?text=No+Image'">
                    <h3>${spec.name}</h3>
                    ${spec.common_name && spec.common_name !== spec.name ? `<p><em>${spec.common_name}</em></p>` : ''}
                    ${spec.scientific_name ? `<p><strong>Scientific:</strong> ${spec.scientific_name}</p>` : ''}
                    ${spec.category ? `<p><strong>Category:</strong> ${spec.category}</p>` : ''}
                    ${spec.conservation_status ? `<p><strong>Status:</strong> ${spec.conservation_status}</p>` : ''}
                    ${spec.description ? `<p>${spec.description.substring(0, 150)}${spec.description.length > 150 ? '...' : ''}</p>` : ''}
                    ${spec.photo_count ? `<p><strong>Photos:</strong> ${spec.photo_count}</p>` : ''}
                    <button onclick="viewSpeciesDetails('${spec.id}')" class="btn">View Details</button>
                </div>
            `).join('');
        }

        function getSpeciesImage(species) {
            // In a real implementation, you'd get the first photo for this species
            // For now, we'll use a placeholder
            return `https://via.placeholder.com/300x200?text=${encodeURIComponent(species.name)}`;
        }

        async function viewSpeciesDetails(speciesId) {
            try {
                const response = await fetch(`${API_BASE}/species/${speciesId}`);
                const data = await response.json();
                
                if (data.success && data.data) {
                    const species = data.data;
                    alert(`
Species Details:
Name: ${species.name}
Scientific Name: ${species.scientific_name || 'N/A'}
Category: ${species.category || 'N/A'}
Conservation Status: ${species.conservation_status || 'N/A'}
Description: ${species.description || 'No description available'}
Photos: ${species.photos ? species.photos.length : 0}
                    `);
                } else {
                    alert('Failed to load species details');
                }
            } catch (error) {
                alert('Error loading species details: ' + error.message);
            }
        }

        function showError(message) {
            document.getElementById('results').innerHTML = `
                <div class="error">
                    <strong>Error:</strong> ${message}
                </div>
            `;
        }

        // Allow Enter key to trigger search
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchSpecies();
            }
        });
    </script>
</body>
</html> 