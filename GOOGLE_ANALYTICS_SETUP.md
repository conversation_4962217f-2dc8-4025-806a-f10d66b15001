# Google Analytics Setup Guide

This guide will help you set up Google Analytics 4 (GA4) for the Fauna Focus application.

## Prerequisites

- A Google account
- Access to Google Analytics (https://analytics.google.com)

## Step 1: Create a Google Analytics Property

1. Go to [Google Analytics](https://analytics.google.com)
2. Sign in with your Google account
3. Click "Start measuring" or "Create Property" if you already have an account
4. Fill in the property details:
   - **Property name**: "Fauna Focus" (or your preferred name)
   - **Reporting time zone**: Select your time zone
   - **Currency**: Select your preferred currency
5. Click "Next"

## Step 2: Set Up Data Stream

1. Choose "Web" as your platform
2. Enter your website details:
   - **Website URL**: Your production domain (e.g., `https://fauna-focus-frontend.vercel.app`)
   - **Stream name**: "Fauna Focus Website" (or your preferred name)
3. Click "Create stream"

## Step 3: Get Your Measurement ID

1. After creating the stream, you'll see your **Measurement ID** (format: `G-XXXXXXXXXX`)
2. Copy this ID - you'll need it for the environment configuration

## Step 4: Configure Environment Variables

1. Open your `.env` file in the project root
2. Add your Measurement ID:
   ```
   VITE_GA_MEASUREMENT_ID="G-XXXXXXXXXX"
   ```
   Replace `G-XXXXXXXXXX` with your actual Measurement ID

3. For production deployment, make sure to add this environment variable to your hosting platform (Vercel, Netlify, etc.)

## Step 5: Verify Installation

1. Deploy your application with the new environment variable
2. Visit your website
3. In Google Analytics, go to "Realtime" reports
4. You should see your visit appear in real-time data

## Features Included

The Fauna Focus application includes comprehensive analytics tracking:

### Automatic Tracking
- **Page Views**: All page navigation is automatically tracked
- **Time on Page**: How long users spend on each page
- **Scroll Depth**: How far users scroll on pages (25%, 50%, 75%, 100%)

### Wildlife-Specific Tracking
- **Species Views**: When users view individual species pages
- **Species Searches**: Search queries and result counts
- **Species Filtering**: Filter usage (category, conservation status, location)
- **Photo Views**: Individual photo interactions
- **Photo Sharing**: When users share photos
- **Hotspot Views**: Location/hotspot page visits
- **Navigation**: Tab and section navigation

### Admin Tracking
- **Admin Login/Logout**: Administrative access (non-sensitive)

## Privacy and Compliance

### GDPR Compliance
The analytics implementation includes consent management:
- Analytics storage is granted by default (you may want to modify this)
- Ad storage is denied by default
- You can implement a cookie consent banner to update these settings

### Data Privacy
- No personally identifiable information (PII) is tracked
- Admin actions are tracked at a high level only (login/logout)
- All tracking respects user privacy

## Customization

### Adding Custom Events
To track additional events, use the analytics service:

```typescript
import { trackWildlifeEvent } from '@/services/analytics';

// Track a custom wildlife interaction
trackWildlifeEvent.viewSpecies('Bald Eagle', 'species-123');

// Track custom events
import { trackEvent } from '@/services/analytics';
trackEvent('custom_action', 'category', 'label', 123);
```

### Modifying Consent Settings
To update consent settings (e.g., after user accepts cookies):

```typescript
import { updateConsentSettings } from '@/services/analytics';

// Grant both analytics and ad storage
updateConsentSettings(true, true);

// Deny ad storage but allow analytics
updateConsentSettings(false, true);
```

## Useful Google Analytics Reports

Once data starts flowing, check these reports:

### Audience Reports
- **Realtime**: See current visitors
- **Demographics**: Age and gender data (if available)
- **Geo**: Geographic distribution of users
- **Technology**: Devices, browsers, operating systems

### Behavior Reports
- **Pages and Screens**: Most popular pages
- **Events**: Custom event tracking (species views, searches, etc.)
- **Site Search**: Search behavior analysis

### Acquisition Reports
- **Traffic Sources**: How users find your site
- **Campaigns**: Track marketing campaign effectiveness

## Troubleshooting

### Analytics Not Working
1. Check that `VITE_GA_MEASUREMENT_ID` is set correctly
2. Verify the Measurement ID format (should start with `G-`)
3. Check browser console for any JavaScript errors
4. Ensure the domain in GA4 matches your actual domain

### No Real-time Data
1. Wait a few minutes - data can take time to appear
2. Check that you're visiting the correct domain
3. Verify that ad blockers aren't blocking analytics
4. Check browser developer tools for network requests to `googletagmanager.com`

### Development vs Production
- Analytics will work in both development and production
- Consider using different GA4 properties for development and production
- The current setup uses the same property for both environments

## Security Notes

- The Measurement ID is safe to expose in client-side code
- No sensitive data is tracked
- All analytics data is processed by Google Analytics
- Consider implementing a privacy policy that mentions analytics usage

## Support

For issues with Google Analytics setup:
1. Check the [Google Analytics Help Center](https://support.google.com/analytics)
2. Review the [GA4 documentation](https://developers.google.com/analytics/devguides/collection/ga4)
3. Check the browser console for any error messages

For issues with the Fauna Focus analytics implementation:
1. Check the browser console for errors
2. Verify environment variables are set correctly
3. Test in both development and production environments
