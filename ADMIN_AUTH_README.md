# Admin Authentication System

## Overview

The Fauna Focus application now includes a comprehensive admin authentication system that restricts all content editing actions to authenticated admin users only. This system provides secure access to administrative functions while keeping the public viewing experience unrestricted.

## Features

### 🔐 Authentication Methods
- **Email/Password Login**: Traditional username/password authentication
- **Magic Link Login**: Passwordless authentication via email link
- **Session Management**: Persistent login sessions with automatic logout
- **Admin Verification**: Email-based admin verification against environment variable

### 🛡️ Security Features
- **Route Protection**: All admin routes are protected with authentication checks
- **Component-Level Security**: Editing controls are hidden for non-admin users
- **Session Validation**: Real-time session validation and automatic redirects
- **Secure Logout**: Proper session cleanup on logout

### 🎯 Protected Functionality
- **Species Management**: Add, edit, and delete species
- **Photo Upload**: Upload and assign photos to species
- **Photo Recovery**: Recover and manage broken photos
- **Data Import**: AI-powered species import functionality
- **Admin Dashboard**: Centralized admin interface
- **Data Consistency**: Database maintenance and cleanup tools

## Setup

### 1. Environment Configuration

Add the following to your `.env` file:

```bash
# Admin Authentication
VITE_ADMIN_EMAIL=<EMAIL>

# Supabase Configuration (required)
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 2. Supabase Auth Setup

1. **Enable Email Auth** in your Supabase project:
   - Go to Authentication > Settings
   - Enable "Email" provider
   - Configure email templates if desired

2. **Create Admin User**:
   - Use the Supabase dashboard or API to create a user with your admin email
   - Set a secure password
   - Verify the email address

3. **Optional: Enable Magic Links**:
   - In Authentication > Settings > Email
   - Enable "Enable magic links"
   - Configure redirect URLs

### 3. Email Configuration

The admin email specified in `VITE_ADMIN_EMAIL` must match exactly with the email used to create the Supabase user account.

## Usage

### Admin Login

1. **Navigate to Admin Login**: Click "Admin Login" in the navigation or go to `/admin/login`
2. **Choose Authentication Method**:
   - **Password Login**: Enter email and password
   - **Magic Link**: Enter email and check your inbox for login link
3. **Access Admin Dashboard**: Upon successful login, you'll be redirected to `/admin/dashboard`

### Admin Dashboard

The admin dashboard provides:
- **Quick Stats**: Overview of species and photo counts
- **Tool Access**: Direct links to all admin tools
- **System Status**: Database and service health indicators
- **User Info**: Current admin user details and logout option

### Protected Routes

All admin functionality is now under `/admin/*` routes:
- `/admin/dashboard` - Main admin interface
- `/admin/species-import` - AI-powered species import
- `/admin/photo-assignment` - Photo assignment tool
- `/admin/photo-recovery` - Photo recovery and management
- `/admin/species-photo-matrix` - Comprehensive data viewer
- `/admin/sync` - External data synchronization

### Component-Level Protection

Editing controls are automatically hidden for non-admin users:
- **Add Species Button**: Only visible to admins
- **Photo Upload Zones**: Only visible to admins
- **Edit Controls**: Hidden for non-admin users
- **Admin Navigation**: Admin links only show for authenticated users

## Architecture

### Core Components

#### `useAdminAuth` Hook
```typescript
const { isAdmin, adminUser, loading, login, logout } = useAdminAuth();
```

Provides:
- `isAdmin`: Boolean indicating admin status
- `adminUser`: Current admin user object
- `loading`: Authentication state loading indicator
- `login`: Function to authenticate user
- `logout`: Function to sign out user

#### `RequireAdmin` Component
```typescript
<RequireAdmin>
  <AdminComponent />
</RequireAdmin>
```

Wraps admin components and:
- Checks authentication status
- Redirects non-admin users to login
- Shows loading states during verification
- Provides access denied fallback

#### Auth Utilities (`src/utils/auth.ts`)
- `isAdminUser()`: Check if current user is admin
- `getCurrentAdminUser()`: Get admin user details
- `signInWithPassword()`: Password authentication
- `signInWithMagicLink()`: Magic link authentication
- `signOut()`: User logout
- `onAuthStateChange()`: Auth state subscription

### File Structure

```
src/
├── hooks/
│   └── useAdminAuth.tsx          # Admin authentication hook
├── components/
│   └── RequireAdmin.tsx          # Admin route protection
├── utils/
│   └── auth.ts                   # Authentication utilities
├── pages/
│   ├── AdminLogin.tsx            # Login page
│   └── AdminDashboard.tsx        # Admin dashboard
└── App.tsx                       # Updated with admin routes
```

## Security Considerations

### Environment Variables
- `VITE_ADMIN_EMAIL` is exposed to the client but only used for verification
- Admin status is verified server-side through Supabase Auth
- No sensitive credentials are stored in client-side code

### Session Management
- Sessions are managed by Supabase Auth
- Automatic session refresh and validation
- Secure logout with proper cleanup

### Route Protection
- All admin routes are wrapped with `RequireAdmin`
- Automatic redirects for unauthenticated users
- Graceful fallbacks for access denied scenarios

## Troubleshooting

### Common Issues

1. **"Not authorized as admin" Error**:
   - Verify `VITE_ADMIN_EMAIL` matches your Supabase user email exactly
   - Ensure the user exists in Supabase Auth
   - Check that the email is verified

2. **Login Not Working**:
   - Verify Supabase configuration in environment variables
   - Check Supabase Auth settings
   - Ensure email provider is enabled

3. **Magic Link Not Received**:
   - Check spam folder
   - Verify email configuration in Supabase
   - Check redirect URL settings

4. **Admin Controls Not Showing**:
   - Verify you're logged in as admin
   - Check browser console for errors
   - Ensure `useAdminAuth` hook is properly imported

### Debug Mode

Enable debug logging by adding to your environment:
```bash
VITE_DEBUG_AUTH=true
```

This will log authentication state changes to the console.

## Best Practices

### Security
- Use strong passwords for admin accounts
- Regularly rotate admin credentials
- Monitor authentication logs
- Use HTTPS in production

### User Management
- Limit admin accounts to necessary personnel only
- Use dedicated admin email addresses
- Implement proper password policies
- Consider multi-factor authentication for additional security

### Development
- Use different admin emails for development and production
- Test authentication flows thoroughly
- Implement proper error handling
- Add comprehensive logging for debugging

## Future Enhancements

### Planned Features
- **Role-Based Access**: Multiple admin roles with different permissions
- **Two-Factor Authentication**: Additional security layer
- **Session Timeout**: Configurable session expiration
- **Audit Logging**: Track admin actions for compliance
- **Bulk Operations**: Admin tools for bulk data management

### Integration Opportunities
- **SSO Integration**: Single sign-on with external providers
- **LDAP Integration**: Enterprise directory integration
- **API Authentication**: Secure API access for admin functions
- **Webhook Notifications**: Real-time admin activity notifications

## Support

For issues with the admin authentication system:
1. Check the troubleshooting section above
2. Review Supabase Auth documentation
3. Check browser console for error messages
4. Verify environment configuration
5. Test with a fresh browser session

The admin authentication system provides a secure foundation for managing the wildlife database while maintaining a seamless experience for public users. 