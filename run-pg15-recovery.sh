#!/bin/bash

set -e

DATA_DIR="supabase/.volumes/db/data"
CONTAINER_NAME="pg15-recovery"
PG_PORT=5433
PG_PASSWORD="postgres"

# 1. Check if postgres:15 image exists, pull if not
if ! docker image inspect postgres:15 &>/dev/null; then
  echo "Pulling postgres:15 Docker image..."
  docker pull postgres:15
fi

# 2. Run the container
echo "Starting temporary Postgres 15 container..."
docker run -d \
  --name $CONTAINER_NAME \
  -e POSTGRES_PASSWORD=$PG_PASSWORD \
  -p $PG_PORT:5432 \
  -v "$(pwd)/$DATA_DIR":/var/lib/postgresql/data \
  postgres:15

echo ""
echo "✅ Postgres 15 container is running."
echo "Connection string:"
echo "  postgres://postgres:postgres@localhost:$PG_PORT/postgres"
echo ""
echo "To connect with psql:"
echo "  PGPASSWORD=postgres psql -h localhost -p $PG_PORT -U postgres"
echo ""
echo "To connect with TablePlus/DBeaver, use:"
echo "  Host: localhost"
echo "  Port: $PG_PORT"
echo "  User: postgres"
echo "  Password: postgres"
echo "  Database: postgres"
echo ""
echo "To export all data to backup.dump (from your host):"
echo "  PGPASSWORD=postgres pg_dump -h localhost -p $PG_PORT -U postgres -F c -b -v -f backup.dump postgres"
echo ""
echo "When done, stop and remove the container:"
echo "  docker stop $CONTAINER_NAME && docker rm $CONTAINER_NAME"
