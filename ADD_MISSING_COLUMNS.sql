-- ADD MISSING COLUMNS TO EXISTING TABLES
-- The species_locations table exists but is missing key columns

-- 1. First, let's see what columns currently exist
SELECT 'Current species_locations table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'species_locations' 
ORDER BY ordinal_position;

-- 2. Add missing columns to species_locations table
ALTER TABLE species_locations ADD COLUMN IF NOT EXISTS abundance TEXT CHECK (abundance IN ('rare', 'uncommon', 'common', 'abundant', 'very_common'));
ALTER TABLE species_locations ADD COLUMN IF NOT EXISTS seasonal_presence TEXT[];
ALTER TABLE species_locations ADD COLUMN IF NOT EXISTS breeding_status TEXT CHECK (breeding_status IN ('non_breeding', 'possible', 'probable', 'confirmed'));
ALTER TABLE species_locations ADD COLUMN IF NOT EXISTS best_months INTEGER[];
ALTER TABLE species_locations ADD COLUMN IF NOT EXISTS notes TEXT;
ALTER TABLE species_locations ADD COLUMN IF NOT EXISTS confidence_level TEXT CHECK (confidence_level IN ('low', 'medium', 'high', 'confirmed'));
ALTER TABLE species_locations ADD COLUMN IF NOT EXISTS last_observed DATE;
ALTER TABLE species_locations ADD COLUMN IF NOT EXISTS observation_count INTEGER DEFAULT 0;
ALTER TABLE species_locations ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
ALTER TABLE species_locations ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- 3. Add unique constraint if it doesn't exist
DO $$
BEGIN
    -- Check if the constraint already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'species_locations_species_id_location_id_key' 
        AND table_name = 'species_locations'
    ) THEN
        ALTER TABLE species_locations ADD CONSTRAINT species_locations_species_id_location_id_key UNIQUE(species_id, location_id);
    END IF;
END $$;

-- 4. Create missing tables (only if they don't exist)
CREATE TABLE IF NOT EXISTS location_photos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
  photo_url TEXT NOT NULL,
  title TEXT,
  description TEXT,
  photographer TEXT,
  taken_date DATE,
  is_primary BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS observations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  species_id UUID REFERENCES species_v2(id) ON DELETE CASCADE,
  location_id UUID REFERENCES locations(id) ON DELETE CASCADE,
  observer_name TEXT,
  observation_date DATE NOT NULL,
  count INTEGER DEFAULT 1,
  notes TEXT,
  confidence_level TEXT CHECK (confidence_level IN ('low', 'medium', 'high', 'confirmed')),
  breeding_behavior BOOLEAN DEFAULT FALSE,
  photo_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Add missing indexes
CREATE INDEX IF NOT EXISTS idx_species_locations_species ON species_locations(species_id);
CREATE INDEX IF NOT EXISTS idx_species_locations_location ON species_locations(location_id);
CREATE INDEX IF NOT EXISTS idx_species_locations_abundance ON species_locations(abundance);
CREATE INDEX IF NOT EXISTS idx_location_photos_location ON location_photos(location_id);
CREATE INDEX IF NOT EXISTS idx_observations_species ON observations(species_id);
CREATE INDEX IF NOT EXISTS idx_observations_location ON observations(location_id);
CREATE INDEX IF NOT EXISTS idx_observations_date ON observations(observation_date);

-- 6. Enable RLS
ALTER TABLE species_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE location_photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE observations ENABLE ROW LEVEL SECURITY;

-- 7. Drop and recreate policies
DROP POLICY IF EXISTS "Public read access for species_locations" ON species_locations;
DROP POLICY IF EXISTS "Admin full access for species_locations" ON species_locations;
DROP POLICY IF EXISTS "Public read access for location_photos" ON location_photos;
DROP POLICY IF EXISTS "Admin full access for location_photos" ON location_photos;
DROP POLICY IF EXISTS "Public read access for observations" ON observations;
DROP POLICY IF EXISTS "Admin full access for observations" ON observations;

CREATE POLICY "Public read access for species_locations" ON species_locations FOR SELECT USING (
  EXISTS (SELECT 1 FROM locations l WHERE l.id = location_id AND l.published = true)
);

CREATE POLICY "Admin full access for species_locations" ON species_locations FOR ALL USING (auth.email() = '<EMAIL>');

CREATE POLICY "Public read access for location_photos" ON location_photos FOR SELECT USING (
  EXISTS (SELECT 1 FROM locations l WHERE l.id = location_id AND l.published = true)
);

CREATE POLICY "Admin full access for location_photos" ON location_photos FOR ALL USING (auth.email() = '<EMAIL>');

CREATE POLICY "Public read access for observations" ON observations FOR SELECT USING (
  EXISTS (SELECT 1 FROM locations l WHERE l.id = location_id AND l.published = true)
);

CREATE POLICY "Admin full access for observations" ON observations FOR ALL USING (auth.email() = '<EMAIL>');

-- 8. Drop and recreate views
DROP VIEW IF EXISTS species_location_details CASCADE;
DROP VIEW IF EXISTS location_species_summary CASCADE;
DROP VIEW IF EXISTS hotspot_highlights CASCADE;
DROP VIEW IF EXISTS unassigned_species CASCADE;
DROP VIEW IF EXISTS locations_without_species CASCADE;

-- Create location_species_summary view
CREATE VIEW location_species_summary AS
SELECT 
  l.id as location_id,
  l.name as location_name,
  l.latitude,
  l.longitude,
  l.state_province,
  l.country,
  l.habitat_types,
  l.featured,
  l.published,
  COALESCE(COUNT(sl.species_id), 0) as total_species,
  COALESCE(COUNT(CASE WHEN sl.abundance IN ('common', 'abundant', 'very_common') THEN 1 END), 0) as common_species,
  COALESCE(COUNT(CASE WHEN sl.abundance = 'rare' THEN 1 END), 0) as rare_species,
  COALESCE(COUNT(CASE WHEN sl.breeding_status = 'confirmed' THEN 1 END), 0) as breeding_species,
  COALESCE(ARRAY_AGG(DISTINCT s.category) FILTER (WHERE s.category IS NOT NULL), ARRAY[]::text[]) as categories_present,
  MAX(sl.last_observed) as last_observation_date,
  COALESCE(SUM(sl.observation_count), 0) as total_observations
FROM locations l
LEFT JOIN species_locations sl ON l.id = sl.location_id
LEFT JOIN species_v2 s ON sl.species_id = s.id
WHERE l.published = true
GROUP BY l.id, l.name, l.latitude, l.longitude, l.state_province, l.country, l.habitat_types, l.featured, l.published;

-- Create species_location_details view
CREATE VIEW species_location_details AS
SELECT 
  s.id as species_id,
  s.name as species_name,
  s.common_name,
  s.scientific_name,
  s.category,
  s.conservation_status,
  l.id as location_id,
  l.name as location_name,
  l.state_province,
  l.country,
  l.latitude,
  l.longitude,
  sl.abundance,
  sl.seasonal_presence,
  sl.breeding_status,
  sl.best_months,
  sl.notes,
  sl.confidence_level,
  sl.last_observed,
  sl.observation_count,
  COUNT(p.id) as photo_count
FROM species_v2 s
JOIN species_locations sl ON s.id = sl.species_id
JOIN locations l ON sl.location_id = l.id
LEFT JOIN photos_v2 p ON s.id = p.species_id
WHERE s.published = true AND l.published = true
GROUP BY s.id, s.name, s.common_name, s.scientific_name, s.category, s.conservation_status,
         l.id, l.name, l.state_province, l.country, l.latitude, l.longitude,
         sl.abundance, sl.seasonal_presence, sl.breeding_status, sl.best_months,
         sl.notes, sl.confidence_level, sl.last_observed, sl.observation_count;

-- Create hotspot_highlights view
CREATE VIEW hotspot_highlights AS
SELECT 
  l.*,
  COALESCE(lss.total_species, 0) as total_species,
  COALESCE(lss.common_species, 0) as common_species,
  COALESCE(lss.rare_species, 0) as rare_species,
  COALESCE(lss.breeding_species, 0) as breeding_species,
  COALESCE(lss.categories_present, ARRAY[]::text[]) as categories_present,
  COALESCE(lss.total_observations, 0) as total_observations,
  l.photo_url as primary_photo,
  NULL as primary_photo_title
FROM locations l
LEFT JOIN location_species_summary lss ON l.id = lss.location_id
WHERE l.published = true
ORDER BY l.featured DESC, COALESCE(lss.total_species, 0) DESC, l.name;

-- Create unassigned_species view
CREATE VIEW unassigned_species AS
SELECT 
    s.id,
    s.name,
    s.common_name,
    s.scientific_name,
    s.category,
    s.conservation_status,
    COUNT(p.id) as photo_count
FROM species_v2 s
LEFT JOIN photos_v2 p ON s.id = p.species_id
WHERE s.published = true 
AND s.id NOT IN (SELECT DISTINCT species_id FROM species_locations WHERE species_id IS NOT NULL)
GROUP BY s.id, s.name, s.common_name, s.scientific_name, s.category, s.conservation_status
ORDER BY s.name;

-- Create locations_without_species view
CREATE VIEW locations_without_species AS
SELECT 
    l.id,
    l.name,
    l.state_province,
    l.country,
    l.latitude,
    l.longitude,
    l.habitat_types
FROM locations l
WHERE l.published = true 
AND l.id NOT IN (SELECT DISTINCT location_id FROM species_locations WHERE location_id IS NOT NULL)
ORDER BY l.name;

-- 9. Add sample data for testing (only if table is empty)
DO $$
DECLARE
    central_park_id UUID;
    species_ids UUID[];
    species_id UUID;
    current_count INTEGER;
BEGIN
    -- Check if we already have data
    SELECT COUNT(*) INTO current_count FROM species_locations;
    
    -- Only add sample data if table is empty
    IF current_count = 0 THEN
        -- Get Central Park location ID
        SELECT id INTO central_park_id FROM locations WHERE name ILIKE '%central park%' LIMIT 1;
        
        -- Get some species IDs (first 3 published species)
        SELECT ARRAY(SELECT id FROM species_v2 WHERE published = true LIMIT 3) INTO species_ids;
        
        -- Only proceed if we have both location and species
        IF central_park_id IS NOT NULL AND array_length(species_ids, 1) > 0 THEN
            -- Add sample species-location relationships
            FOREACH species_id IN ARRAY species_ids
            LOOP
                INSERT INTO species_locations (
                    species_id, 
                    location_id, 
                    abundance, 
                    seasonal_presence, 
                    breeding_status, 
                    best_months, 
                    confidence_level, 
                    observation_count
                ) VALUES (
                    species_id,
                    central_park_id,
                    CASE (random() * 4)::int 
                        WHEN 0 THEN 'rare'
                        WHEN 1 THEN 'uncommon' 
                        WHEN 2 THEN 'common'
                        WHEN 3 THEN 'abundant'
                        ELSE 'very_common'
                    END,
                    ARRAY['spring', 'summer', 'fall'],
                    CASE (random() * 3)::int
                        WHEN 0 THEN 'non_breeding'
                        WHEN 1 THEN 'possible'
                        WHEN 2 THEN 'probable'
                        ELSE 'confirmed'
                    END,
                    ARRAY[4, 5, 6, 7, 8, 9, 10], -- April through October
                    'medium',
                    (random() * 20)::int + 1
                ) ON CONFLICT (species_id, location_id) DO NOTHING;
            END LOOP;
        END IF;
    END IF;
END $$;

-- 10. Show results
SELECT 'Columns added and views created successfully!' as status;

SELECT 'Updated species_locations table structure:' as info;
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'species_locations' 
ORDER BY ordinal_position;

SELECT 'Current species-location relationships:' as info;
SELECT COUNT(*) as count FROM species_locations;

SELECT 'Views created:' as info;
SELECT table_name 
FROM information_schema.views 
WHERE table_schema = 'public' 
AND table_name IN ('species_location_details', 'location_species_summary', 'hotspot_highlights', 'unassigned_species', 'locations_without_species');

-- 11. Test the views
SELECT 'Testing species_location_details view:' as test;
SELECT COUNT(*) as view_row_count FROM species_location_details;
