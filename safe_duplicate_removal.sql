-- Safe duplicate removal with backup
-- This approach creates a backup table before deletion

-- Step 1: Create a backup table
CREATE TABLE photos_v2_backup AS
SELECT * FROM photos_v2;

-- Step 2: Verify backup was created
SELECT COUNT(*) as backup_count FROM photos_v2_backup;

-- Step 3: Identify duplicates and show details
WITH duplicate_analysis AS (
    SELECT 
        id,
        url,
        title,
        species_id,
        created_at,
        ROW_NUMBER() OVER (PARTITION BY url, title ORDER BY id) as rn,
        COUNT(*) OVER (PARTITION BY url, title) as duplicate_count
    FROM photos_v2 
    WHERE url IS NOT NULL AND title IS NOT NULL
)
SELECT 
    duplicate_count,
    COUNT(*) as groups_with_this_count
FROM duplicate_analysis
WHERE duplicate_count > 1
GROUP BY duplicate_count
ORDER BY duplicate_count;

-- Step 4: Show sample duplicates (first 20 groups)
WITH duplicate_groups AS (
    SELECT
        url,
        title,
        COUNT(*) as duplicate_count,
        array_agg(id ORDER BY created_at) as all_ids,
        (array_agg(id ORDER BY created_at))[1] as keep_id
    FROM photos_v2
    WHERE url IS NOT NULL AND title IS NOT NULL
    GROUP BY url, title
    HAVING COUNT(*) > 1
    ORDER BY duplicate_count DESC
    LIMIT 20
)
SELECT * FROM duplicate_groups;

-- Step 5: Create a clean table with unique records only
CREATE TABLE photos_v2_clean AS
WITH ranked_photos AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY url, title ORDER BY created_at) as rn
    FROM photos_v2
)
SELECT
    id, url, title, description, photographer, location,
    camera_settings, weather_conditions, time_of_day, notes,
    published, created_at, species_id, hash, updated_at
FROM ranked_photos
WHERE rn = 1;

-- Step 6: Verify the clean table
SELECT COUNT(*) as clean_count FROM photos_v2_clean;

-- Step 7: Check for duplicates in clean table (should be 0)
SELECT 
    url,
    title,
    COUNT(*) as duplicate_count
FROM photos_v2_clean 
WHERE url IS NOT NULL AND title IS NOT NULL
GROUP BY url, title 
HAVING COUNT(*) > 1;

-- Step 8: When ready, replace the original table
-- DROP TABLE photos_v2;
-- ALTER TABLE photos_v2_clean RENAME TO photos_v2;

-- Step 9: Recreate any indexes or constraints that were on the original table
-- (You'll need to check what indexes exist on photos_v2 first)
