-- Add missing columns for AI-generated content to locations table

-- First, check what columns currently exist
SELECT 'Current locations table columns:' as info;
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'locations' 
ORDER BY ordinal_position;

-- Add the missing columns for AI-generated content
ALTER TABLE locations ADD COLUMN IF NOT EXISTS description TEXT;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS visitor_tips TEXT;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS best_times TEXT;

-- Also add some other useful columns if they don't exist
ALTER TABLE locations ADD COLUMN IF NOT EXISTS website_url TEXT;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS contact_info TEXT;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS featured BOOLEAN DEFAULT FALSE;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS photo_url TEXT;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS habitat_types TEXT[];
ALTER TABLE locations ADD COLUMN IF NOT EXISTS published BOOLEAN DEFAULT TRUE;
ALTER TABLE locations ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
ALTER TABLE locations ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Update the existing Central Park record with the AI-generated content
UPDATE locations
SET
  description = 'Central Park''s Ramble, located in the heart of New York City, offers a surprisingly rich wildlife viewing experience despite its urban setting. This haven of mixed habitats, including woodlands, meadows, and rocky outcrops, provides crucial refuge for a variety of species. The carefully managed and diverse vegetation supports a thriving ecosystem, making it a valuable green space for both wildlife and city dwellers.',
  visitor_tips = 'To maximize your wildlife viewing experience in the Ramble, plan your visit during the cooler hours of the day, dawn or dusk, when animals are most active. Bring binoculars for optimal bird watching and a field guide to help identify species. Stay on marked trails to avoid disturbing wildlife and their habitats. Keep a safe distance from any animals you encounter; never approach or feed them.',
  best_times = 'The best time to visit the Ramble for wildlife viewing varies depending on the species you hope to see. Spring and fall offer pleasant weather and peak bird migration, with many species passing through or nesting. Summer months can be hot and humid, and some animals may be less active during the midday heat. Winter presents quieter, more intimate encounters.',
  habitat_types = ARRAY['deciduous_forest', 'urban_park', 'mixed_woodland'],
  featured = true
WHERE name ILIKE '%central park%';

-- Show the updated structure
SELECT 'Updated locations table columns:' as info;
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'locations' 
ORDER BY ordinal_position;

-- Show the updated Central Park record
SELECT 'Updated Central Park record:' as info;
SELECT name, description, visitor_tips, best_times, habitat_types, featured
FROM locations 
WHERE name ILIKE '%central park%';
