# Photo Recovery System

A comprehensive system for detecting, managing, and recovering missing or broken photo URLs in the Fauna Focus application.

## Overview

The photo recovery system consists of:
- **Backend Scripts**: Audit, analyze, and fix broken photo URLs
- **UI Components**: Admin interface for managing missing photos
- **Utility Functions**: Shared logic for photo recovery operations

## Quick Start

### 1. Audit Missing Photos
```bash
# Run a full audit to identify broken photo URLs
npx tsx src/scripts/manualPhotoRecovery.ts --limit 1000

# Check results in logs/photos-to-recover.json
```

### 2. Analyze and Attempt Fixes
```bash
# Try different bucket names and path variations
npx tsx src/scripts/analyzeAndFixUrls.ts --limit 50

# Check results in logs/url-analysis-summary.json
```

### 3. Soft Delete Missing Photos
```bash
# Mark broken photos as unavailable (dry run first)
npx tsx src/scripts/softDeleteMissingPhotos.ts --dry-run

# Apply the changes
npx tsx src/scripts/softDeleteMissingPhotos.ts
```

### 4. Use Admin UI
Navigate to `/admin-photo-recovery` in your browser to:
- View all missing photos
- Edit URLs inline
- Mark photos as recovered
- Delete permanently if needed

## Scripts Reference

### manualPhotoRecovery.ts
Audits all photo URLs and identifies broken ones.

**Usage:**
```bash
npx tsx src/scripts/manualPhotoRecovery.ts [options]
```

**Options:**
- `--limit <number>`: Limit number of photos to check (default: 100)
- `--dry-run`: Show what would be done without making changes
- `--check`: Only check URLs, don't save results

**Output:**
- `logs/photos-to-recover.json`: List of broken photos
- `logs/photos-not-found.json`: Photos that couldn't be checked

### analyzeAndFixUrls.ts
Attempts to fix broken URLs by trying different bucket names and path variations.

**Usage:**
```bash
npx tsx src/scripts/analyzeAndFixUrls.ts [options]
```

**Options:**
- `--limit <number>`: Limit number of photos to analyze (default: 50)

**Output:**
- `logs/url-analysis-summary.json`: Analysis results
- `logs/fixed-urls.json`: Successfully fixed URLs (if any)

### softDeleteMissingPhotos.ts
Marks missing photos as unavailable by setting `published = false`.

**Usage:**
```bash
npx tsx src/scripts/softDeleteMissingPhotos.ts [options]
```

**Options:**
- `--dry-run`: Show what would be done without making changes
- `--check`: Only check, don't update

**Output:**
- `logs/soft-deletion-results.json`: Results of the operation

### updatePhotoUrls.ts
Updates photo URLs from a mapping file.

**Usage:**
```bash
npx tsx src/scripts/updatePhotoUrls.ts <mapping-file> [options]
```

**Options:**
- `--dry-run`: Show what would be done without making changes
- `--check`: Validate URLs before updating

### photoRecoveryWorkflow.ts
Orchestrates the entire photo recovery process.

**Usage:**
```bash
npx tsx src/scripts/photoRecoveryWorkflow.ts [step]
```

**Steps:**
- `audit`: Run photo audit
- `analyze`: Analyze broken URLs
- `fix`: Apply fixes
- `cleanup`: Soft delete remaining broken photos

## UI Components

### AdminPhotoRecovery
Located at `/admin-photo-recovery`, this page provides:

**Features:**
- View all missing photos (published = false)
- Search and filter by ID, species, location
- Inline URL editing
- Mark photos as recovered
- Permanently delete photos
- Real-time statistics

**Actions:**
- **Edit URL**: Click the edit icon to modify a photo's URL
- **Mark as Recovered**: Click the checkmark to set published = true
- **Delete**: Click the trash icon to permanently remove

### PhotoAssignment Updates
The Photo Assignment page now includes:
- Visual indicators for missing photos (red border, "Unavailable" label)
- Filter options for published/unpublished photos
- Enhanced drag-and-drop experience

## Utility Functions

### photoRecovery.ts
Shared utility functions for photo recovery operations:

**Functions:**
- `fetchMissingPhotos()`: Get all unpublished photos
- `updatePhotoUrl()`: Update a photo's URL
- `markPhotoAsRecovered()`: Mark photo as published
- `deletePhoto()`: Permanently delete a photo
- `validatePhotoUrl()`: Check if a URL is accessible
- `getPhotoRecoveryStats()`: Get recovery statistics

## Database Schema

### Photos Table
```sql
CREATE TABLE photos (
  id SERIAL PRIMARY KEY,
  url TEXT,
  title TEXT,
  description TEXT,
  species_id UUID REFERENCES species(id),
  location TEXT,
  published BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

**Key Fields:**
- `published`: Boolean indicating if photo is available (false = missing/broken)
- `url`: Photo URL (can be null or broken)
- `species_id`: Reference to species (can be null)

## Recovery Strategies

### 1. URL Fixing
- Try different bucket names (species → photos)
- Fix path variations
- Remove extra characters/newlines

### 2. Re-upload
- Upload original files to Supabase Storage
- Update database URLs
- Mark as published

### 3. Soft Delete
- Set `published = false` for missing photos
- Keep records for audit purposes
- Allow manual recovery later

### 4. Hard Delete
- Permanently remove broken records
- Use only when photos are confirmed lost

## Best Practices

### Prevention
1. **Validate URLs** before saving to database
2. **Use consistent** storage bucket naming
3. **Implement retry logic** for uploads
4. **Monitor storage** usage and limits

### Recovery
1. **Always audit first** to understand the scope
2. **Use dry-run mode** before making changes
3. **Backup data** before bulk operations
4. **Test fixes** on a small subset first

### Maintenance
1. **Regular audits** to catch issues early
2. **Monitor error rates** in photo loading
3. **Keep logs** of recovery operations
4. **Document patterns** in broken URLs

## Troubleshooting

### Common Issues

**Scripts not running:**
- Check environment variables in `.env`
- Ensure Supabase client is properly configured
- Verify TypeScript compilation

**UI not loading:**
- Check browser console for errors
- Verify route is properly configured
- Ensure all dependencies are installed

**Database errors:**
- Check Supabase connection
- Verify table schema matches expectations
- Check RLS policies if applicable

### Debug Mode
Enable debug logging by setting:
```bash
export DEBUG=photo-recovery:*
```

## Contributing

When adding new recovery features:

1. **Add tests** for new utility functions
2. **Update documentation** for new scripts
3. **Follow existing patterns** for consistency
4. **Add error handling** for robustness

## Support

For issues or questions:
1. Check the logs in `logs/` directory
2. Review this README
3. Check Supabase dashboard for errors
4. Test with a small dataset first 