# AI Wildlife Photo Dashboard

A comprehensive monitoring and review system for AI-generated wildlife photo content, built with Supabase and React.

## 🎯 Overview

The AI Dashboard provides real-time monitoring, review workflows, and audit trails for AI-tagged wildlife photos and species. It enables quality control through manual overrides while maintaining full accountability.

## 🏗️ Architecture

### Database Schema

#### Core Tables
- `photos` - Wildlife photos with AI-generated metadata
- `species` - Wildlife species information
- `ai_override_log` - Audit trail for manual overrides

#### Monitoring Views
- `photos_needing_review` - Photos flagged for human review
- `ai_created_species` - Species created by AI agent
- `ai_assignment_stats` - Statistical overview of AI activity
- `species_photo_counts` - Species ranked by photo count
- `recent_ai_activity` - Recent AI-generated content
- `review_queue_summary` - Summary of items needing review
- `ai_performance_metrics` - Daily AI performance tracking

#### Override Logging Views
- `ai_override_analytics` - Override pattern analytics
- `recent_ai_overrides` - Recent manual overrides with details

### Override Log Schema

```sql
CREATE TABLE ai_override_log (
  id SERIAL PRIMARY KEY,
  table_name TEXT NOT NULL,                    -- 'photos' or 'species'
  record_id TEXT NOT NULL,                     -- ID of the modified record
  field_name TEXT NOT NULL,                    -- Field that was changed
  old_value TEXT,                              -- Previous value
  new_value TEXT,                              -- New value
  override_type TEXT NOT NULL CHECK (          -- Type of override
    override_type IN ('species_assignment', 'metadata_edit', 'review_status', 'confidence_override')
  ),
  user_id UUID,                                -- User who made the change
  override_reason TEXT,                        -- Reason for the override
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🚀 Getting Started

### Prerequisites
- Supabase project with the AI monitoring migrations applied
- React application with the dashboard components

### Installation

1. **Apply Database Migrations**
   ```bash
   npx supabase db reset
   ```

2. **Access the Dashboard**
   - Navigate to `/ai-dashboard` in your application
   - Or click "AI Dashboard" from the main navigation

3. **Configure Supabase Studio**
   - Go to Supabase Studio → Tables & Views
   - Pin the monitoring views for quick access
   - Enable Row Level Security on `ai_override_log`

## 📊 Dashboard Features

### Overview Tab
- **Statistics Cards**: Total photos, AI-tagged count, review queue, average confidence
- **Quick Actions**: Direct navigation to review queues
- **Recent Activity**: AI processing activity (last 7 days)

### Review Queue Tab
- **Inline Editing**: Edit titles, descriptions, locations, species assignments
- **Publish/Unpublish**: Toggle photo publication status
- **Batch Operations**: Review multiple items efficiently

### AI Species Tab
- **Species Management**: Review and edit AI-created species
- **Metadata Editing**: Update names, scientific names, common names
- **Publication Control**: Approve or reject AI-generated species

### Overrides Tab
- **Audit Trail**: View all manual overrides with details
- **Override Analytics**: Track override patterns and reasons
- **User Accountability**: See who made changes and when

## 🔧 Usage Examples

### Reviewing AI-Tagged Photos

1. Navigate to the **Review Queue** tab
2. Click the edit button (✏️) on any photo
3. Modify title, description, location, or species assignment
4. Click save (💾) to apply changes
5. Toggle publish status with the eye icon (👁️)

### Managing AI-Created Species

1. Go to the **AI Species** tab
2. Review species metadata and confidence scores
3. Edit names or scientific classifications
4. Approve by publishing or reject by keeping as draft

### Auditing Overrides

```sql
-- View recent overrides
SELECT * FROM recent_ai_overrides ORDER BY created_at DESC LIMIT 10;

-- Override analytics by type
SELECT override_type, COUNT(*) as count 
FROM ai_override_analytics 
GROUP BY override_type;

-- User override activity
SELECT user_id, COUNT(*) as override_count 
FROM ai_override_log 
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY user_id;
```

## 🔔 Real-time Notifications

The dashboard includes real-time notifications for:
- New photos needing review
- AI-created species requiring approval
- Manual overrides by team members

Enable real-time subscriptions in your React components:

```typescript
import { useRealtimeOverrides } from '@/hooks/useRealtimeOverrides';

function Dashboard() {
  const { overrides, loading } = useRealtimeOverrides();
  
  // Real-time override notifications
  useEffect(() => {
    if (overrides.length > 0) {
      // Show notification for new overrides
    }
  }, [overrides]);
}
```

## 🛠️ Development

### Regenerating Types

When schema changes, regenerate TypeScript types:

```bash
npx supabase gen types typescript --project-id YOUR_PROJECT_ID > src/integrations/supabase/types.ts
```

### Adding New Override Types

1. Update the `override_type` CHECK constraint in the migration
2. Add corresponding trigger logic in the override functions
3. Update the frontend override type icons and descriptions

### Customizing Views

Modify the monitoring views in `supabase/migrations/20250621000003_create_ai_monitoring_views.sql`:

```sql
-- Example: Add custom filtering
CREATE OR REPLACE VIEW custom_ai_view AS
SELECT * FROM photos_needing_review 
WHERE created_at >= NOW() - INTERVAL '24 hours';
```

## 📈 Monitoring & Analytics

### Key Metrics to Track

- **AI Confidence Distribution**: Monitor confidence score trends
- **Override Rate**: Track how often AI decisions are manually changed
- **Review Queue Size**: Ensure timely review of flagged content
- **User Activity**: Monitor who's making overrides and why

### Sample Queries

```sql
-- Daily AI performance
SELECT * FROM ai_performance_metrics 
WHERE date >= NOW() - INTERVAL '30 days';

-- Species with most photos
SELECT * FROM species_photo_counts 
ORDER BY total_photos DESC LIMIT 10;

-- Override patterns
SELECT override_type, COUNT(*) as count,
       AVG(EXTRACT(EPOCH FROM (NOW() - created_at))/3600) as avg_hours_ago
FROM ai_override_log 
GROUP BY override_type;
```

## 🔒 Security & Permissions

### Row Level Security (RLS)

The `ai_override_log` table has RLS enabled with policies:
- **View Policy**: Authenticated users can view override logs
- **Insert Policy**: System can insert logs via triggers

### User Permissions

- **Reviewers**: Can edit and approve AI-generated content
- **Admins**: Can view override analytics and audit trails
- **System**: Automatically logs all manual changes

## 🚨 Troubleshooting

### Common Issues

1. **Views Not Loading**
   - Ensure migrations are applied: `npx supabase db reset`
   - Check Supabase Studio for view existence

2. **Override Logs Missing**
   - Verify triggers are active: Check `photos_ai_override_trigger` and `species_ai_override_trigger`
   - Ensure RLS policies allow insert operations

3. **Real-time Notifications Not Working**
   - Check Supabase Realtime is enabled
   - Verify subscription channels are correct

### Debug Queries

```sql
-- Check trigger status
SELECT trigger_name, event_manipulation, action_statement 
FROM information_schema.triggers 
WHERE trigger_name LIKE '%ai_override%';

-- Verify override log entries
SELECT COUNT(*) as override_count, 
       MIN(created_at) as earliest_override,
       MAX(created_at) as latest_override
FROM ai_override_log;
```

## 🔮 Future Enhancements

- **Bulk Operations**: Batch approve/reject multiple items
- **Advanced Filtering**: Filter by confidence score, date range, user
- **Export Functionality**: Export override logs and analytics
- **Integration APIs**: Webhook notifications for external systems
- **Machine Learning**: Use override patterns to improve AI accuracy

## 📝 Contributing

When contributing to the AI Dashboard:

1. Follow the existing migration naming convention
2. Add comprehensive comments to SQL views and functions
3. Update this README with new features
4. Include TypeScript types for new database changes
5. Test override logging with new features

---

**Need Help?** Check the main project README or open an issue for support. 