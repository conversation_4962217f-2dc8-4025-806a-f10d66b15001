# Data Cleanup Guide for Fauna Focus

This guide explains how to use the comprehensive data cleanup system to maintain data integrity in your wildlife database.

## Overview

The data cleanup system provides tools to identify and fix common data integrity issues:

- **Orphaned Photos**: Photos with no valid species reference
- **Duplicate Photos**: Photos with identical hash or URL
- **Invalid Species References**: Photos pointing to non-existent species
- **Broken URLs**: Photos with inaccessible image files
- **Empty Species**: Published species with no associated photos

## Quick Start

### 1. Run Data Analysis

First, analyze your current data issues:

```bash
# Run a dry run to see what issues exist
npm run cleanup-data --dry-run

# Or use the short form
npm run cleanup-data -d
```

### 2. Validate Photo URLs

Check for broken image URLs:

```bash
# Validate all photo URLs
npm run validate-urls

# Validate a specific photo
npm run validate-urls 123
```

### 3. Run Cleanup Operations

After reviewing the issues, run the actual cleanup:

```bash
# Run full cleanup (deletes orphaned/duplicate photos, unpublishes empty species)
npm run cleanup-data
```

## Database Views

The system creates several SQL views for monitoring data integrity:

### 1. `orphaned_photos`
Shows photos with no valid species reference:
```sql
SELECT * FROM orphaned_photos;
```

### 2. `duplicate_photos_by_hash`
Shows photos with identical content hashes:
```sql
SELECT * FROM duplicate_photos_by_hash;
```

### 3. `duplicate_photos_by_url`
Shows photos with identical URLs:
```sql
SELECT * FROM duplicate_photos_by_url;
```

### 4. `invalid_species_references`
Shows photos pointing to non-existent species:
```sql
SELECT * FROM invalid_species_references;
```

### 5. `photos_with_missing_files`
Shows photos with broken URLs (after URL validation):
```sql
SELECT * FROM photos_with_missing_files;
```

### 6. `species_with_no_photos`
Shows species that have no associated photos:
```sql
SELECT * FROM species_with_no_photos;
```

### 7. `photo_issues_summary`
Provides a count of all issue types:
```sql
SELECT * FROM photo_issues_summary;
```

### 8. `photos_cleanup_dashboard`
Comprehensive view showing cleanup status for all photos:
```sql
SELECT * FROM photos_cleanup_dashboard;
```

## Cleanup Functions

### `cleanup_orphaned_photos()`
Deletes photos with no valid species reference.

### `cleanup_duplicate_photos()`
Removes duplicate photos, keeping the oldest one from each group.

### `unpublish_empty_species()`
Sets `published = false` for species with no photos.

### `update_photo_url_status(photo_id, status)`
Updates the URL status for a specific photo.

## Admin Dashboard

The Data Consistency Dashboard provides a web interface for:

1. **Viewing Issue Summary**: See counts of all data issues
2. **Photo Management**: Reassign orphaned photos to species or delete them
3. **Species Management**: View and manage species without photos
4. **Cleanup Operations**: Run dry runs and actual cleanup operations

### Accessing the Dashboard

Navigate to the admin section and look for "Data Consistency Dashboard".

## URL Validation

The URL validation system:

1. **Checks HTTP Status**: Verifies images return 200 OK
2. **Handles Timeouts**: 10-second timeout per URL
3. **Batch Processing**: Processes URLs in batches to avoid overwhelming servers
4. **Status Tracking**: Updates `url_status` field in the database
5. **Detailed Reporting**: Generates JSON reports with validation results

### URL Status Values

- `unknown`: Not yet validated
- `ok`: URL is accessible (200 OK)
- `broken`: URL returns error status
- `checking`: Currently being validated

## Cleanup Logging

All cleanup operations are logged in the `photo_cleanup_log` table:

```sql
SELECT * FROM photo_cleanup_log ORDER BY created_at DESC;
```

Log entries include:
- Operation type
- Affected photo IDs
- Details about the operation
- Timestamp

## Best Practices

### 1. Always Run Dry Runs First
```bash
npm run cleanup-data --dry-run
```

### 2. Backup Before Major Cleanup
```sql
-- Create backup tables
CREATE TABLE photos_backup AS SELECT * FROM photos;
CREATE TABLE species_backup AS SELECT * FROM species;
```

### 3. Monitor Cleanup Logs
```sql
-- Check recent cleanup operations
SELECT * FROM photo_cleanup_log 
WHERE created_at > NOW() - INTERVAL '1 day'
ORDER BY created_at DESC;
```

### 4. Regular Maintenance
- Run URL validation weekly
- Run data cleanup monthly
- Monitor the dashboard for new issues

## Troubleshooting

### Common Issues

1. **Permission Errors**
   - Ensure your database user has execute permissions on cleanup functions
   - Check RLS policies if using row-level security

2. **URL Validation Timeouts**
   - Some image servers may be slow to respond
   - The system handles timeouts gracefully and marks them as failed

3. **Duplicate Detection**
   - Hash-based duplicates are more reliable than URL-based
   - Check both views to understand the full scope

### Recovery

If cleanup operations cause issues:

1. **Restore from Backup**:
   ```sql
   -- Restore photos table
   DROP TABLE photos;
   ALTER TABLE photos_backup RENAME TO photos;
   ```

2. **Check Cleanup Logs**:
   ```sql
   SELECT * FROM photo_cleanup_log 
   WHERE operation_type = 'delete_orphaned_photos'
   ORDER BY created_at DESC;
   ```

## Advanced Usage

### Custom Cleanup Scripts

You can import the cleanup functions in your own scripts:

```typescript
import { runFullCleanup, getDataIssuesSummary } from './scripts/data-cleanup';

// Get current issues
const issues = await getDataIssuesSummary();
console.log('Current issues:', issues);

// Run cleanup
const summary = await runFullCleanup(false);
console.log('Cleanup summary:', summary);
```

### URL Validation Integration

```typescript
import { validateAllPhotoUrls, validateSpecificPhoto } from './scripts/url-validator';

// Validate all URLs
await validateAllPhotoUrls();

// Validate specific photo
await validateSpecificPhoto(123);
```

### Database Monitoring

Set up regular monitoring queries:

```sql
-- Daily issue count
SELECT 
  issue_type,
  count,
  DATE(created_at) as date
FROM photo_issues_summary
WHERE created_at > NOW() - INTERVAL '7 days';

-- Recent cleanup activity
SELECT 
  operation_type,
  COUNT(*) as operation_count,
  DATE(created_at) as date
FROM photo_cleanup_log
WHERE created_at > NOW() - INTERVAL '30 days'
GROUP BY operation_type, DATE(created_at)
ORDER BY date DESC;
```

## Type Generation

After running migrations, regenerate TypeScript types:

```bash
npm run gen-types
```

This updates `src/integrations/supabase/types.ts` with the new views and functions.

## Support

For issues or questions about the data cleanup system:

1. Check the cleanup logs for error details
2. Review the database views to understand current state
3. Run dry runs to preview changes before applying them
4. Use the admin dashboard for interactive management

---

**Note**: Always backup your data before running cleanup operations, especially in production environments. 